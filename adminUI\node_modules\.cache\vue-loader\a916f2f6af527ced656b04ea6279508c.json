{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue?vue&type=template&id=0a5856ef&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue", "mtime": 1754275430525}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"}),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData.data,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"id\",\"label\":\"ID\",\"min-width\":\"50\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.grade.levelIcon'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.icon,\"preview-src-list\":[scope.row.icon]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":_vm.$t('user.grade.levelName'),\"min-width\":\"100\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"experience\",\"label\":_vm.$t('user.grade.experience'),\"min-width\":\"100\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"discount\",\"label\":_vm.$t('user.grade.discount') + '(%)',\"min-width\":\"100\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"commissionRate\",\"label\":_vm.$t('user.grade.commissionRate') + '(%)',\"min-width\":\"120\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"upgradeType\",\"label\":_vm.$t('user.grade.upgradeType'),\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getUpgradeTypeColor(scope.row.upgradeType)}},[_vm._v(\"\\n            \"+_vm._s(_vm.getUpgradeTypeName(scope.row.upgradeType))+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"upgradePrice\",\"label\":_vm.$t('user.grade.upgradeFee'),\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.upgradeType === 1)?_c('span',[_vm._v(\"Rp \"+_vm._s(scope.row.upgradePrice))]):_c('span',[_vm._v(_vm._s(_vm.$t('user.grade.free')))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"isAvailable\",\"label\":_vm.$t('user.grade.availableStatus'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.isAvailable ? 'success' : 'danger'}},[_vm._v(\"\\n            \"+_vm._s(scope.row.isAvailable ? _vm.$t('user.grade.available') : _vm.$t('user.grade.unavailable'))+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.grade.status'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return (_vm.checkPermi(['admin:system:user:level:use']))?[_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false,\"active-text\":_vm.$t('user.grade.enable'),\"inactive-text\":_vm.$t('user.grade.disable'),\"disabled\":\"\"},nativeOn:{\"click\":function($event){return _vm.onchangeIsShow(scope.row)}},model:{value:(scope.row.isShow),callback:function ($$v) {_vm.$set(scope.row, \"isShow\", $$v)},expression:\"scope.row.isShow\"}})]:undefined}}],null,true)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.grade.operation'),\"min-width\":\"120\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:system:user:level:update']),expression:\"['admin:system:user:level:update']\"}],staticClass:\"mr10\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.edit(scope.row)}}},[_vm._v(_vm._s(_vm.$t('user.grade.edit')))]),_vm._v(\" \"),_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:system:user:level:delete']),expression:\"['admin:system:user:level:delete']\"}],attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row.id, scope.$index)}}},[_vm._v(_vm._s(_vm.$t('user.grade.delete')))])]}}])})],1)],1),_vm._v(\" \"),_c('creat-grade',{ref:\"grades\",attrs:{\"user\":_vm.userInfo}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}