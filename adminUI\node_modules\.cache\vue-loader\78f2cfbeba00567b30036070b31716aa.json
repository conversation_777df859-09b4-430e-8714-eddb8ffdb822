{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue?vue&type=template&id=ca7660d8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-row :gutter=\"30\">\n      <el-col v-bind=\"grid\" class=\"left mb15 ml40\">\n        <div>\n          <img class=\"top\" src=\"@/assets/imgs/mobilehead.png\">\n          <img class=\"bottom\" src=\"@/assets/imgs/mobilefoot.png\">\n          <div\n            style=\"background: #F4F5F9; min-height: 438px; position: absolute;\n       top: 63px; width: 320px; \"\n          />\n          <div class=\"textbot\">\n            <div v-for=\"(item,indx) in list\" :key=\"indx\" class=\"li\" :class=\"{active:item === formValidate}\">\n              <div>\n                <div class=\"add\" @click=\"add(item,indx)\">\n                  <i class=\"el-icon-plus\" />\n                  <div class=\"arrow\" />\n                </div>\n                <div class=\"tianjia\">\n                  <div\n                    v-for=\"(j,index) in item.sub_button\"\n                    :key=\"index\"\n                    class=\"addadd menuBox\"\n                    :class=\"{active:j === formValidate}\"\n                    @click=\"gettem(j,index,indx)\"\n                  >\n                    <el-tooltip class=\"item\" effect=\"dark\" :content=\"j.name\" placement=\"top-start\">\n                      <el-button>{{ j.name || '二级菜单' }}</el-button>\n                    </el-tooltip>\n                  </div>\n                </div>\n              </div>\n              <div class=\"text menuBox\" @click=\"gettem(item,indx,null)\">\n                <el-tooltip class=\"item\" effect=\"dark\" :content=\"item.name\" placement=\"top-start\">\n                  <el-button>{{ item.name || '一级菜单' }}</el-button>\n                </el-tooltip>\n              </div>\n            </div>\n            <div v-show=\"list.length < 3\" class=\"li\">\n              <div class=\"text\" @click=\"addtext\"><i class=\"el-icon-plus\" /></div>\n            </div>\n          </div>\n        </div>\n      </el-col>\n      <el-col :xl=\"11\" :lg=\"12\" :md=\"22\" :sm=\"22\" :xs=\"22\">\n        <div v-if=\"checkedMenuId !== null\">\n          <div class=\"dividerTitle acea-row row-between row-bottom\">\n            <span class=\"title\">菜单信息</span>\n            <el-button slot=\"extra\" size=\"small\" type=\"danger\" @click=\"deltMenus\" v-hasPermi=\"['admin:wechat:menu:public:delete']\">删除</el-button>\n            <el-divider />\n          </div>\n          <el-col :span=\"24\" class=\"userAlert\">\n            <div class=\"box-card right\">\n              <el-alert\n                class=\"mb15\"\n                title=\"已添加子菜单，仅可设置菜单名称\"\n                type=\"success\"\n                show-icon\n              />\n              <el-form ref=\"formValidate\" :model=\"formValidate\" :rules=\"ruleValidate\" label-width=\"100px\" class=\"mt20\">\n                <el-form-item label=\"菜单名称\" prop=\"name\">\n                  <el-input v-model=\"formValidate.name\" placeholder=\"请填写菜单名称\" class=\"spwidth\" />\n                </el-form-item>\n                <el-form-item label=\"规则状态\" prop=\"type\">\n                  <el-select v-model=\"formValidate.type\" placeholder=\"请选择规则状态\" class=\"spwidth\">\n                    <el-option value=\"click\" label=\"关键字\">关键字</el-option>\n                    <el-option value=\"view\" label=\"跳转网页\">跳转网页</el-option>\n                    <el-option value=\"miniprogram\" label=\"小程序\">小程序</el-option>\n                  </el-select>\n                </el-form-item>\n                <div v-if=\"formValidate.type === 'click'\">\n                  <el-form-item label=\"关键字\" prop=\"key\">\n                    <el-input v-model=\"formValidate.key\" placeholder=\"请填写关键字\" class=\"spwidth\" />\n                  </el-form-item>\n                </div>\n                <div v-if=\"formValidate.type === 'miniprogram'\">\n                  <el-form-item label=\"appid\" prop=\"appid\">\n                    <el-input v-model=\"formValidate.appid\" placeholder=\"请填写appid\" class=\"spwidth\" />\n                  </el-form-item>\n                  <el-form-item label=\"备用网页\" prop=\"url\">\n                    <el-input v-model=\"formValidate.url\" placeholder=\"请填写备用网页\" class=\"spwidth\" />\n                  </el-form-item>\n                  <el-form-item label=\"小程序路径\" prop=\"pagepath\">\n                    <el-input v-model=\"formValidate.pagepath\" placeholder=\"请填写小程序路径\" class=\"spwidth\" />\n                  </el-form-item>\n                </div>\n                <div v-if=\"formValidate.type === 'view'\">\n                  <el-form-item label=\"跳转地址\" prop=\"url\">\n                    <el-input v-model=\"formValidate.url\" placeholder=\"请填写跳转地址\" class=\"spwidth\" />\n                  </el-form-item>\n                </div>\n              </el-form>\n            </div>\n          </el-col>\n        </div>\n        <el-col v-if=\"isTrue\" :span=\"24\">\n          <el-button size=\"mini\" type=\"primary\" \n          style=\"display: block;margin: 10px auto;\"\n           @click=\"submenus('formValidate')\"\n           v-hasPermi=\"['admin:wechat:menu:public:create']\">保存并发布</el-button>\n        </el-col>\n      </el-col>\n    </el-row>\n  </el-card>\n</div>\n", null]}