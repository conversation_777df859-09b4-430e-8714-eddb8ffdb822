{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\config\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { configApi, configUpdateApi, productCheckApi } from '@/api/distribution';\nimport * as selfUtil from '@/utils/ZBKJIutil.js';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport { Debounce } from '@/utils/validate';\nexport default {\n  name: 'Index',\n  data: function data() {\n    return {\n      promoterForm: {},\n      loading: true,\n      rules: {\n        brokerageFuncStatus: [{\n          required: true,\n          message: '请选择是否启用分销',\n          trigger: 'change'\n        }],\n        storeBrokerageRatio: [{\n          required: true,\n          message: '请输入一级返佣比例',\n          trigger: 'blur'\n        }],\n        storeBrokerageTwo: [{\n          required: true,\n          message: '请输入二级返佣比例',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getDetal();\n  },\n  methods: {\n    checkPermi: checkPermi,\n    channelInputLimit: function channelInputLimit(e) {\n      var key = e.key;\n      // 不允许输入'e'和'.'\n      if (key === 'e' || key === '.') {\n        e.returnValue = false;\n        return false;\n      }\n      return true;\n    },\n    getDetal: function getDetal() {\n      var _this = this;\n      this.loading = true;\n      configApi().then(function (res) {\n        _this.loading = false;\n        _this.promoterForm = res;\n        _this.promoterForm.storeBrokerageIsBubble = res.storeBrokerageIsBubble.toString();\n        _this.promoterForm.brokerageFuncStatus = res.brokerageFuncStatus.toString();\n        _this.promoterForm.brokerageBindind = res.brokerageBindind.toString();\n      }).catch(function (res) {\n        _this.$message.error(res.message);\n      });\n    },\n    submitForm: Debounce(function (formName) {\n      var _this2 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          if (selfUtil.Add(_this2.promoterForm.storeBrokerageRatio, _this2.promoterForm.storeBrokerageTwo) > 100) return _this2.$message.warning('返佣比例相加不能超过100%');\n          _this2.loading = true;\n          configUpdateApi(_this2.promoterForm).then(function (res) {\n            _this2.loading = false;\n            _this2.$message.success('提交成功');\n            // this.$modalSure('提交成功，是否自动下架商户低于此佣金比例的商品').then(() => {\n            //   productCheckApi().then(({ message }) => {\n            //     this.$message.success(message)\n            //   }).catch(({ message }) => {\n            //     this.$message.error(message)\n            //   })\n            // })\n          }).catch(function (err) {\n            _this2.loading = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    })\n  }\n};", null]}