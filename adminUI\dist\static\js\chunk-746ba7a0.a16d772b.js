(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-746ba7a0","chunk-1e152eec"],{2638:function(t,e,a){"use strict";function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e,a=1;a<arguments.length;a++)for(var i in e=arguments[a],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},i.apply(this,arguments)}var n=["attrs","props","domProps"],s=["class","style","directives"],r=["on","nativeOn"],o=function(t){return t.reduce((function(t,e){for(var a in e)if(t[a])if(-1!==n.indexOf(a))t[a]=i({},t[a],e[a]);else if(-1!==s.indexOf(a)){var o=t[a]instanceof Array?t[a]:[t[a]],d=e[a]instanceof Array?e[a]:[e[a]];t[a]=[].concat(o,d)}else if(-1!==r.indexOf(a))for(var c in e[a])if(t[a][c]){var u=t[a][c]instanceof Array?t[a][c]:[t[a][c]],m=e[a][c]instanceof Array?e[a][c]:[e[a][c]];t[a][c]=[].concat(u,m)}else t[a][c]=e[a][c];else if("hook"===a)for(var f in e[a])t[a][f]=t[a][f]?l(t[a][f],e[a][f]):e[a][f];else t[a]=e[a];else t[a]=e[a];return t}),{})},l=function(t,e){return function(){t&&t.apply(this,arguments),e&&e.apply(this,arguments)}};t.exports=o},5317:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div")},n=[],s=a("2877"),r={},o=Object(s["a"])(r,i,n,!1,null,null,null);e["a"]=o.exports},"92c6":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return r})),a.d(e,"a",(function(){return o})),a.d(e,"f",(function(){return l})),a.d(e,"g",(function(){return d})),a.d(e,"j",(function(){return c})),a.d(e,"h",(function(){return u})),a.d(e,"e",(function(){return m})),a.d(e,"i",(function(){return f}));var i=a("b775");function n(t){var e={id:t.id};return Object(i["a"])({url:"/admin/system/form/temp/info",method:"GET",params:e})}function s(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(i["a"])({url:"/admin/system/form/temp/list",method:"GET",params:e})}function r(t){var e={content:t.content,info:t.info,name:t.name};return Object(i["a"])({url:"/admin/system/form/temp/save",method:"POST",data:e})}function o(t){var e={id:t.id},a={content:t.content,info:t.info,name:t.name};return Object(i["a"])({url:"/admin/system/form/temp/update",method:"POST",params:e,data:a})}function l(t){var e={sendType:t.sendType};return Object(i["a"])({url:"/admin/system/notification/list",method:"GET",params:e})}function d(t){return Object(i["a"])({url:"/admin/system/notification/routine/switch/".concat(t),method:"post"})}function c(t){return Object(i["a"])({url:"/admin/system/notification/wechat/switch/".concat(t),method:"post"})}function u(t){return Object(i["a"])({url:"/admin/system/notification/sms/switch/".concat(t),method:"post"})}function m(t){var e={detailType:t.type,id:t.id};return Object(i["a"])({url:"/admin/system/notification/detail",method:"get",params:e})}function f(t){var e={detailType:t.type,id:t.id,status:t.status,tempId:t.tempId};return Object(i["a"])({url:"/admin/system/notification/update",method:"post",data:e})}},d1ad:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"数据搜索"}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入ID，KEY，组合数据名称，简介",size:"small",clearable:""},model:{value:t.listPram.keywords,callback:function(e){t.$set(t.listPram,"keywords",e)},expression:"listPram.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1)],1)],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:save"],expression:"['admin:system:group:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerOpenEdit({},0)}}},[t._v("添加数据组")])],1),t._v(" "),a("el-table",{staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:t.dataList.list,size:"mini","highlight-current-row":"","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{label:"数据组名称",prop:"name","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"简介",prop:"info","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",fixed:"right","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:data:list"],expression:"['admin:system:group:data:list']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return t.handleDataList(e.row)}}},[t._v("数据列表")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:info","admin:system:group:update"],expression:"['admin:system:group:info','admin:system:group:update']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return t.handlerOpenEdit(e.row,1)}}},[t._v("编辑")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:delete"],expression:"['admin:system:group:delete']"}],attrs:{size:"small",type:"text"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{title:0===t.editDialogConfig.isCreate?"创建数据组":"编辑数据组",visible:t.editDialogConfig.visible},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?a("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHideDialog}}):t._e()],1),t._v(" "),a("el-dialog",{attrs:{title:"组合数据列表",visible:t.comDataListConfig.visible,fullscreen:""},on:{"update:visible":function(e){return t.$set(t.comDataListConfig,"visible",e)}}},[t.comDataListConfig.visible?a("cm-data-list",{attrs:{"form-data":t.comDataListConfig.formData}}):t._e()],1)],1)},n=[],s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"components-container"},[a("el-form",{ref:"editPram",attrs:{model:t.editPram,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"数据组名称",prop:"name",rules:[{required:!0,message:"填写数据组名称",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"数据组名称",clearable:""},model:{value:t.editPram.name,callback:function(e){t.$set(t.editPram,"name",e)},expression:"editPram.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"数据简介",prop:"info",rules:[{required:!0,message:"填写数据简介",trigger:["blur","change"]}]}},[a("el-input",{attrs:{placeholder:"数据简介",clearable:""},model:{value:t.editPram.info,callback:function(e){t.$set(t.editPram,"info",e)},expression:"editPram.info"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"表单数据ID",prop:"formId",rules:[{required:!0,message:"请选择表单数据",trigger:["change"]}]}},[a("span",[t._v(t._s(t.editPram.formId))]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.selectFormDialogConfig.visible=!0}}},[t._v("选择模板数据")])],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"btn-width100",attrs:{type:"primary"},on:{click:function(e){return t.handlerSubmit("editPram")}}},[t._v("确定")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"选择表单模板",visible:t.selectFormDialogConfig.visible,"append-to-body":""},on:{"update:visible":function(e){return t.$set(t.selectFormDialogConfig,"visible",e)}}},[a("form-config-list",{attrs:{"select-model":""},on:{selectedRowData:t.handlerSelectedRowData}})],1)],1)},r=[],o=a("d1da"),l=a("e7ac"),d=a("61f7"),c={components:{formConfigList:o["default"]},props:{isCreate:{type:Number,default:0},editData:{type:Object,default:{}}},data:function(){return{editPram:{formId:null,info:null,name:null,id:null},selectedFormConfigData:{},selectFormDialogConfig:{visible:!1}}},mounted:function(){this.handlerInitEditData()},methods:{handlerInitEditData:function(){if(1===this.isCreate){var t=this.editData,e=t.id,a=t.name,i=t.info,n=t.formId;t.createTime,t.updateTime;this.editPram.id=e,this.editPram.name=a,this.editPram.info=i,this.editPram.formId=n}},handlerSelectedRowData:function(t){this.selectedFormConfigData=t,this.editPram.formId=this.selectedFormConfigData.id,this.selectFormDialogConfig.visible=!1},handlerSubmit:Object(d["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&(0===e.isCreate?e.handlerSave(e.editPram):e.handlerEdit(e.editPram))}))})),handlerSave:function(t){var e=this;l["e"](t).then((function(t){e.$message.success("添加组合数据成功"),e.$emit("hideDialog")}))},handlerEdit:function(t){var e=this;l["c"](t).then((function(t){e.$message.success("编辑组合数据成功"),e.$emit("hideDialog")}))}}},u=c,m=a("2877"),f=Object(m["a"])(u,s,r,!1,null,"8804e14e",null),h=f.exports,p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"components-container"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"状态",clearable:""},on:{change:t.handlerSearch},model:{value:t.listPram.status,callback:function(e){t.$set(t.listPram,"status",e)},expression:"listPram.status"}},t._l(t.constants.roleListStatus,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:data:save"],expression:"['admin:system:group:data:save']"}],attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handlerOpenEditData({},0)}}},[t._v("添加数据")]),t._v(" "),a("el-dialog",{attrs:{title:0===t.editDataConfig.isCreate?"添加数据":"编辑数据",visible:t.editDataConfig.visible,"append-to-body":"","destroy-on-close":"",width:"700px"},on:{"update:visible":function(e){return t.$set(t.editDataConfig,"visible",e)}}},[t.editDataConfig.visible?a("edit",{attrs:{"form-data":t.formData,"edit-data":t.editDataConfig.editData,"is-create":t.editDataConfig.isCreate},on:{hideDialog:t.handlerHideDia}}):t._e()],1),t._v(" "),a("el-table",{staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:t.dataList.list,"header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{label:"编号",prop:"id"}}),t._v(" "),t._l(t.formConf.fields,(function(e,i){return a("el-table-column",{key:i,attrs:{label:e.__config__.label,prop:e.__vModel__},scopedSlots:t._u([{key:"default",fn:function(i){return[["img","image","pic"].indexOf(e.__vModel__)>-1?a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:i.row[e.__vModel__],"preview-src-list":[i.row[e.__vModel__]]}})],1):a("span",[t._v(t._s(i.row[e.__vModel__]))])]}}],null,!0)})})),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterShowOrHide")(e.row.status)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:data:update","admin:system:group:data:info"],expression:"['admin:system:group:data:update','admin:system:group:data:info']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handlerOpenEditData(e.row,1)}}},[t._v("编辑")]),t._v(" "),99!==t.formMark?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:group:data:delete"],expression:"['admin:system:group:data:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handlerDelete(e.row)}}},[t._v("删除")]):t._e()]}}])})],2),t._v(" "),a("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)},g=[],v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"selfForm",attrs:{model:t.selfForm,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"排序",prop:"sort",rules:[{required:!0,message:"排序不能为空",trigger:["blur","change"]}]}},[a("el-input-number",{model:{value:t.selfForm.sort,callback:function(e){t.$set(t.selfForm,"sort",e)},expression:"selfForm.sort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态",prop:"status",rules:[{required:!0,message:"正确操作状态",trigger:["change"]}]}},[a("el-switch",{model:{value:t.selfForm.status,callback:function(e){t.$set(t.selfForm,"status",e)},expression:"selfForm.status"}})],1)],1),t._v(" "),t.formConf.fields.length>0?a("parser",{attrs:{"is-edit":1===t.isCreate,"form-conf":t.formConf,"form-edit-data":t.editData},on:{submit:t.handlerSubmit}}):t._e()],1)},b=[],D=a("3fbe"),C=a("b775");function y(t){var e={id:t.id};return Object(C["a"])({url:"/admin/system/group/data/delete",method:"GET",params:e})}function _(t){var e={gid:t.gid,keywords:t.keywords,status:t.status,page:t.page,limit:t.limit};return Object(C["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}function P(t){return Object(C["a"])({url:"/admin/system/group/data/save",method:"POST",data:t})}function w(t,e){return Object(C["a"])({url:"/admin/system/group/data/update",method:"POST",data:t,params:{id:e}})}var O=a("92c6"),k={components:{parser:D["a"]},props:{formData:{type:Object,required:!0},isCreate:{type:Number,default:0},editData:{type:Object}},data:function(){return{formConf:{fields:[]},selfForm:{sort:0,status:0}}},mounted:function(){this.handlerGetFormConfig(),this.handlerInitEditData()},methods:{handlerInitEditData:function(){var t=this.editData,e=t.sort,a=t.status;this.selfForm.sort=e,this.selfForm.status=a},handlerGetFormConfig:function(){var t=this,e={id:this.formData.formId};O["b"](e).then((function(e){t.formConf=JSON.parse(e.content)}))},handlerSubmit:Object(d["a"])((function(t){0===this.isCreate?this.handlerSave(t):this.handlerEdit(t)})),handlerSave:function(t){var e=this,a=this.buildFormPram(t);P(a).then((function(t){e.$message.success("添加数据成功"),e.$emit("hideDialog")}))},handlerEdit:function(t){var e=this,a=this.buildFormPram(t);w(a,this.editData.id).then((function(t){e.$message.success("编辑数据成功"),e.$emit("hideDialog")}))},buildFormPram:function(t){var e={gid:this.formData.id,form:{fields:[],id:this.formData.formId,sort:this.selfForm.sort,status:this.selfForm.status}},a=[];return Object.keys(t).forEach((function(e){a.push({name:e,title:e,value:t[e]})})),e.form.fields=a,e}}},x=k,S=Object(m["a"])(x,v,b,!1,null,"696cde34",null),L=S.exports,j={components:{edit:L},props:{formData:{type:Object,required:!0}},data:function(){return{constants:this.$constants,listPram:{gid:null,keywords:null,status:null,page:1,pageSize:this.$constants.page.limit[0]},editDataConfig:{visible:!1,isCreate:0,editData:{}},formConf:{fields:[]},dataList:{list:[],total:0},formMark:0}},mounted:function(){this.handlerGetFormConfig(),this.listPram.gid=this.formData.id,this.handlerGetListData(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetListData(this.listPram)},handlerGetListData:function(t){var e=this;_(t).then((function(t){var a=[];t.list.forEach((function(t){t.value=JSON.parse(t.value);var e=t.value.fields,i={};e.map((function(t){i[t.name]=t.value})),i.id=t.id,i.sort=t.sort,i.status=t.status,a.push(i)})),e.dataList.list=a,e.dataList.total=t.total}))},handlerGetFormConfig:function(){var t=this,e={id:this.formData.formId};O["b"](e).then((function(e){t.formMark=parseInt(e.id),t.formConf=JSON.parse(e.content)}))},handlerOpenEditData:function(t,e){this.editDataConfig.editData=t,this.editDataConfig.isCreate=e,this.editDataConfig.visible=!0},handlerHideDia:function(){this.handlerGetListData(this.listPram),this.editDataConfig.visible=!1},handlerDelete:function(t){var e=this;this.$confirm("确实删除当前数据","提示").then((function(){y(t).then((function(t){e.$message.success("删除数据成功"),e.handlerHideDia()}))}))},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetListData(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetListData(this.listPram)}}},$=j,E=Object(m["a"])($,p,g,!1,null,"36c5d61a",null),G=E.exports,F={components:{edit:h,cmDataList:G},data:function(){return{constants:this.$constants,dataList:{list:[],total:0},listPram:{keywords:null,page:1,pageSize:this.$constants.page.limit[0]},editDialogConfig:{visible:!1,isCreate:0,editData:{}},comDataListConfig:{visible:!1,formData:{}}}},mounted:function(){this.handlerGetList(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetList(this.listPram)},handlerOpenEdit:function(t,e){this.editDialogConfig.editData=0===e?{}:t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerGetList:function(t){var e=this;l["d"](t).then((function(t){e.dataList=t}))},handleDataList:function(t){if(t.formId<=0)return this.$message.error("请先关联表单");this.comDataListConfig.formData=t,this.comDataListConfig.visible=!0},handleDelete:function(t){var e=this;this.$confirm("确定删除当前数据","提示").then((function(){l["b"](t).then((function(t){e.$message.success("删除数据成功"),setTimeout((function(){e.handlerGetList(e.listPram)}),800)}))}))},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetList(this.listPram)},handlerHideDialog:function(){var t=this;setTimeout((function(){t.editDialogConfig.visible=!1,t.handlerGetList(t.listPram)}),800)}}},z=F,T=Object(m["a"])(z,i,n,!1,null,"286ce210",null);e["default"]=T.exports},d1da:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"关键字"}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入id，名称，描述",clearable:"",size:"small"},model:{value:t.listPram.keywords,callback:function(e){t.$set(t.listPram,"keywords",e)},expression:"listPram.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1),t._v(" "),t.selectModel?a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:!t.selectedConfigData.id},on:{click:t.handlerConfimSelect}},[t._v("确定选择")])],1):t._e()],1)],1),t._v(" "),t.selectModel?t._e():a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:form:save"],expression:"['admin:system:form:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerEditData({},0)}}},[t._v("创建表单")])],1),t._v(" "),a("el-table",{staticClass:"table",attrs:{data:t.dataList.list,"highlight-current-row":t.selectModel,size:"mini","header-cell-style":{fontWeight:"bold"}},on:{"current-change":t.handleCurrentRowChange}},[a("el-table-column",{attrs:{label:"ID",prop:"id",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"info","min-width":"220"}}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"200"}}),t._v(" "),t.selectModel?t._e():a("el-table-column",{attrs:{label:"操作","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:form:info"],expression:"['admin:system:form:info']"}],attrs:{type:"text",size:"small"},on:{click:function(a){return t.handlerEditData(e.row,1)}}},[t._v("编辑")])]}}],null,!1,109836554)})],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),a("el-dialog",{attrs:{visible:t.editDialogConfig.visible,fullscreen:"",title:0===t.editDialogConfig.isCreate?"创建表单":"编辑表单","destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?a("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHide}}):t._e()],1)],1)},n=[],s=a("92c6"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("config-list",{attrs:{"edit-data":t.editData,"is-create":t.isCreate},on:{getFormConfigDataResult:t.handlerGetFormConfigData}})],1)},o=[],l=a("5abd"),d={components:{configList:l["a"]},props:{editData:{type:Object,default:{}},isCreate:{type:Number,default:0}},data:function(){return{}},methods:{handlerGetFormConfigData:function(t){t.id?this.handlerEdit(t):this.handlerSave(t)},handlerSave:function(t){var e=this;s["d"](t).then((function(t){e.$message.success("创建表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))},handlerEdit:function(t){var e=this;s["a"](t).then((function(t){e.$message.success("编辑表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))}}},c=d,u=a("2877"),m=Object(u["a"])(c,r,o,!1,null,"1b0ee2a8",null),f=m.exports,h={components:{edit:f},props:{selectModel:{type:Boolean,default:!1}},data:function(){return{constants:this.$constants,listPram:{keywords:null,page:1,limit:this.$constants.page.limit[0]},editDialogConfig:{visible:!1,editData:{},isCreate:0},dataList:{list:[],total:0},selectedConfigData:{}}},mounted:function(){this.handlerGetList(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetList(this.listPram)},handlerGetList:function(t){var e=this;s["c"](t).then((function(t){e.dataList=t}))},handlerEditData:function(t,e){this.editDialogConfig.editData=0===e?{}:t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerHide:function(){this.editDialogConfig.editData={},this.editDialogConfig.isCreate=0,this.editDialogConfig.visible=!1,this.handlerGetList(this.listPram)},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetList(this.listPram)},handleCurrentRowChange:function(t){this.selectedConfigData=t},handlerConfimSelect:function(){this.$emit("selectedRowData",this.selectedConfigData)}}},p=h,g=Object(u["a"])(p,i,n,!1,null,"cf311f6a",null);e["default"]=g.exports},e7ac:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"d",(function(){return s})),a.d(e,"e",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return l}));var i=a("b775");function n(t){var e={id:t.id};return Object(i["a"])({url:"/admin/system/group/delete",method:"GET",params:e})}function s(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(i["a"])({url:"/admin/system/group/list",method:"GET",params:e})}function r(t){var e={formId:t.formId,info:t.info,name:t.name};return Object(i["a"])({url:"/admin/system/group/save",method:"POST",params:e})}function o(t){var e={formId:t.formId,info:t.info,name:t.name,id:t.id};return Object(i["a"])({url:"/admin/system/group/update",method:"POST",params:e})}function l(t){var e={gid:t.gid};return Object(i["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}},fb9d:function(t,e,a){var i={"./el-button.js":"64b1","./el-checkbox-group.js":"cc1a","./el-input.js":"373c","./el-radio-group.js":"afca","./el-select.js":"a938","./el-upload.js":"73ee"};function n(t){var e=s(t);return a(e)}function s(t){var e=i[t];if(!(e+1)){var a=new Error("Cannot find module '"+t+"'");throw a.code="MODULE_NOT_FOUND",a}return e}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="fb9d"}}]);