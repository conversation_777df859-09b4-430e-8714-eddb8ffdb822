package com.genco.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.vo.OrderInfoDetailVo;
import com.genco.common.vo.StoreOrderInfoOldVo;
import com.genco.common.vo.StoreOrderInfoVo;
import com.genco.service.dao.StoreOrderInfoDao;
import com.genco.service.service.StoreOrderInfoService;
import com.genco.service.service.StoreProductReplyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * StoreOrderInfoServiceImpl 接口实现
 */
@Service
public class StoreOrderInfoServiceImpl extends ServiceImpl<StoreOrderInfoDao, StoreOrderInfo>
        implements StoreOrderInfoService {

    @Resource
    private StoreOrderInfoDao dao;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    /**
     * 根据id集合查询数据，返回 map
     *
     * @param orderList List<Integer> id集合
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public HashMap<Integer, List<StoreOrderInfoOldVo>> getMapInId(List<Integer> orderList) {
        HashMap<Integer, List<StoreOrderInfoOldVo>> map = new HashMap<>();
        if (orderList.size() < 1) {
            return map;
        }
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(StoreOrderInfo::getOrderId, orderList);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return map;
        }
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            StoreOrderInfoOldVo StoreOrderInfoVo = new StoreOrderInfoOldVo();
            try {
                //解析商品详情JSON
                BeanUtils.copyProperties(storeOrderInfo, StoreOrderInfoVo, "info");
                StoreOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            } catch (Exception e) {
                //TODO
            }
            if (map.containsKey(storeOrderInfo.getOrderId())) {
                map.get(storeOrderInfo.getOrderId()).add(StoreOrderInfoVo);
            } else {
                List<StoreOrderInfoOldVo> storeOrderInfoVoList = new ArrayList<>();
                storeOrderInfoVoList.add(StoreOrderInfoVo);
                map.put(storeOrderInfo.getOrderId(), storeOrderInfoVoList);
            }
        }
        return map;
    }

    /**
     * 根据id集合查询数据，返回 map
     *
     * @param orderId Integer id
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfoOldVo> getOrderListByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return null;
        }

        List<StoreOrderInfoOldVo> storeOrderInfoVoList = new ArrayList<>();
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            //解析商品详情JSON
            StoreOrderInfoOldVo storeOrderInfoVo = new StoreOrderInfoOldVo();
            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfoVo, "info");
            storeOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            storeOrderInfoVo.getInfo().setIsReply(
                    storeProductReplyService.isReply(storeOrderInfoVo.getUnique(), storeOrderInfoVo.getOrderId()) ? 1 : 0
            );
            storeOrderInfoVoList.add(storeOrderInfoVo);
        }
        return storeOrderInfoVoList;
    }

    /**
     * 根据id集合查询数据，返回 map
     *
     * @param orderId 订单id
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfoVo> getVoListByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return null;
        }

        List<StoreOrderInfoVo> storeOrderInfoVoList = new ArrayList<>();
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            //解析商品详情JSON
            StoreOrderInfoVo storeOrderInfoVo = new StoreOrderInfoVo();
            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfoVo, "info");
            storeOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            storeOrderInfoVoList.add(storeOrderInfoVo);
        }
        return storeOrderInfoVoList;
    }

    /**
     * 获取订单详情-订单编号
     *
     * @param orderNo 订单编号
     * @return List
     */
    @Override
    public List<StoreOrderInfo> getListByOrderNo(String orderNo) {
        LambdaQueryWrapper<StoreOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrderInfo::getOrderNo, orderNo);
        return dao.selectList(lqw);
    }

    /**
     * 根据时间、商品id获取销售件数
     *
     * @param date  时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return Integer
     */
    @Override
    public Integer getSalesNumByDateAndProductId(String date, Integer proId) {
        return dao.getSalesNumByDateAndProductId(date, proId);
    }

    /**
     * 根据时间、商品id获取销售额
     *
     * @param date  时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return BigDecimal
     */
    @Override
    public BigDecimal getSalesByDateAndProductId(String date, Integer proId) {
        return dao.getSalesByDateAndProductId(date, proId);
    }

    @Override
    public BigDecimal sumEstimatedCommission(List<Integer> ids) {
        LambdaQueryWrapper<StoreOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.in(StoreOrderInfo::getOrderId, ids);
        List<StoreOrderInfo> orderList = dao.selectList(lqw);
        return orderList.stream().map(StoreOrderInfo::getEstimatedCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal sumActualCommission(List<Integer> ids) {
        LambdaQueryWrapper<StoreOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.in(StoreOrderInfo::getOrderId, ids);
        List<StoreOrderInfo> orderList = dao.selectList(lqw);
        return orderList.stream().map(StoreOrderInfo::getActualCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 新增订单详情
     *
     * @param storeOrderInfos 订单详情集合
     * @return 订单新增结果
     */
    @Override
    public boolean saveOrderInfos(List<StoreOrderInfo> storeOrderInfos) {
        return saveBatch(storeOrderInfos);
    }

    /**
     * 通过订单编号和规格号查询
     *
     * @param uni     规格号
     * @param orderId 订单编号
     * @return StoreOrderInfo
     */
    @Override
    public StoreOrderInfo getByUniAndOrderId(String uni, Integer orderId) {
        //带 StoreOrderInfo 类的多条件查询
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        lambdaQueryWrapper.eq(StoreOrderInfo::getUnique, uni);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 根据商品名称查询订单ID列表
     *
     * @param productTitle 商品名称
     * @return List<Integer> 订单ID列表
     */
    @Override
    public List<Integer> getOrderIdsByProductName(String productTitle) {
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(StoreOrderInfo::getOrderId);
        lambdaQueryWrapper.like(StoreOrderInfo::getProductName, productTitle);
        List<StoreOrderInfo> orderInfoList = dao.selectList(lambdaQueryWrapper);
        return orderInfoList.stream()
                .map(StoreOrderInfo::getOrderId)
                .distinct()
                .collect(Collectors.toList());
    }
}

