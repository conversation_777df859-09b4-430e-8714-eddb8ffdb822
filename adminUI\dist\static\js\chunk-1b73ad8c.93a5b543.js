(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1b73ad8c"],{"2f2c":function(e,t,i){"use strict";i.d(t,"b",(function(){return u})),i.d(t,"c",(function(){return m})),i.d(t,"r",(function(){return d})),i.d(t,"d",(function(){return f})),i.d(t,"a",(function(){return p})),i.d(t,"g",(function(){return g})),i.d(t,"h",(function(){return h})),i.d(t,"j",(function(){return b})),i.d(t,"i",(function(){return v})),i.d(t,"e",(function(){return y})),i.d(t,"o",(function(){return w})),i.d(t,"q",(function(){return V})),i.d(t,"l",(function(){return _})),i.d(t,"m",(function(){return T})),i.d(t,"n",(function(){return x})),i.d(t,"p",(function(){return k})),i.d(t,"k",(function(){return O})),i.d(t,"f",(function(){return S}));var r=i("b775");function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function n(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function o(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?n(Object(i),!0).forEach((function(t){s(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function s(e,t,i){return(t=l(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function l(e){var t=c(e,"string");return"symbol"==a(t)?t:t+""}function c(e,t){if("object"!=a(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function u(e){return Object(r["a"])({url:"/admin/system/city/list",method:"get",params:o({},e)})}function m(){return Object(r["a"])({url:"/admin/system/city/list/tree",method:"get"})}function d(e){return Object(r["a"])({url:"/admin/system/city/update/status",method:"post",params:o({},e)})}function f(e){return Object(r["a"])({url:"/admin/system/city/update",method:"post",params:o({},e)})}function p(e){return Object(r["a"])({url:"/admin/system/city/info",method:"get",params:o({},e)})}function g(e){return Object(r["a"])({url:"/admin/express/list",method:"get",params:o({},e)})}function h(){return Object(r["a"])({url:"/admin/express/sync/express",method:"post"})}function b(e){return Object(r["a"])({url:"/admin/express/update/show",method:"post",data:e})}function v(e){return Object(r["a"])({url:"/admin/express/update",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/admin/express/delete",method:"GET",params:o({},e)})}function w(e){return Object(r["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:o({},e)})}function V(e){return Object(r["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:o({},e)})}function _(e){return Object(r["a"])({url:"/admin/express/shipping/free/list",method:"get",params:o({},e)})}function T(e){return Object(r["a"])({url:"admin/express/shipping/region/list",method:"get",params:o({},e)})}function x(e){return Object(r["a"])({url:"admin/express/shipping/templates/save",method:"post",data:e})}function k(e,t){return Object(r["a"])({url:"admin/express/shipping/templates/update",method:"post",data:e,params:o({},t)})}function O(e){return Object(r["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:e})}function S(e){return Object(r["a"])({url:"admin/express/info",method:"get",params:o({},e)})}},4509:function(e,t,i){},4825:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-steps",{attrs:{active:e.currentTab,"align-center":"","finish-status":"success"}},[i("el-step",{attrs:{title:"选择秒杀商品"}}),e._v(" "),i("el-step",{attrs:{title:"填写基础信息"}}),e._v(" "),i("el-step",{attrs:{title:"修改商品详情"}})],1)],1),e._v(" "),i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:e.ruleValidate,model:e.formValidate,"label-width":"150px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("div",{directives:[{name:"show",rawName:"v-show",value:0===e.currentTab,expression:"currentTab === 0"}]},[i("el-form-item",{attrs:{label:"选择商品：",prop:"image"}},[i("div",{staticClass:"upLoadPicBox",on:{click:e.changeGood}},[e.formValidate.image?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.formValidate.image}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:1===e.currentTab,expression:"currentTab === 1"}]},[i("el-row",{attrs:{gutter:24}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品主图：",prop:"image"}},[i("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("1")}}},[e.formValidate.image?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.formValidate.image}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品轮播图：",prop:"imagess"}},[i("div",{staticClass:"acea-row"},[e._l(e.formValidate.imagess,(function(t,r){return i("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(i){return e.handleDragStart(i,t)},dragover:function(i){return i.preventDefault(),e.handleDragOver(i,t)},dragenter:function(i){return e.handleDragEnter(i,t)},dragend:function(i){return e.handleDragEnd(i,t)}}},[i("img",{attrs:{src:t}}),e._v(" "),i("i",{staticClass:"el-icon-error btndel",on:{click:function(t){return e.handleRemove(r)}}})])})),e._v(" "),e.formValidate.imagess.length<10?i("div",{staticClass:"upLoadPicBox",on:{click:function(t){return e.modalPicTap("2")}}},[i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])]):e._e()],2)])],1),e._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"商品标题：",prop:"title"}},[i("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称"},model:{value:e.formValidate.title,callback:function(t){e.$set(e.formValidate,"title",t)},expression:"formValidate.title"}})],1)],1),e._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"秒杀活动简介："}},[i("el-input",{attrs:{type:"textarea",maxlength:"250",rows:3,placeholder:"请输入商品简介"},model:{value:e.formValidate.info,callback:function(t){e.$set(e.formValidate,"info",t)},expression:"formValidate.info"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid2,!1),[i("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入单位"},model:{value:e.formValidate.unitName,callback:function(t){e.$set(e.formValidate,"unitName",t)},expression:"formValidate.unitName"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid2,!1),[i("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[i("div",{staticClass:"acea-row"},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:e.formValidate.tempId,callback:function(t){e.$set(e.formValidate,"tempId",t)},expression:"formValidate.tempId"}},e._l(e.shippingList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)])],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid2,!1),[i("el-form-item",{attrs:{label:"当天参与活动次数：",prop:"num"}},[i("el-input-number",{staticClass:"selWidth",attrs:{step:1,"step-strictly":"",min:1,placeholder:"请输入活动次数"},model:{value:e.formValidate.num,callback:function(t){e.$set(e.formValidate,"num",t)},expression:"formValidate.num"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid2,!1),[i("el-form-item",{attrs:{label:"活动日期：",prop:"timeVal"}},[i("el-date-picker",{staticClass:"selWidth",attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","picker-options":e.pickerOptions,placeholder:"请选择活动日期"},on:{change:e.onchangeTime},model:{value:e.formValidate.timeVal,callback:function(t){e.$set(e.formValidate,"timeVal",t)},expression:"formValidate.timeVal"}})],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid2,!1),[i("el-form-item",{attrs:{label:"活动时间：",prop:"timeId"}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择"},model:{value:e.formValidate.timeId,callback:function(t){e.$set(e.formValidate,"timeId",t)},expression:"formValidate.timeId"}},e._l(e.seckillTime,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name+" | "+e.time,value:e.id}})})),1)],1)],1),e._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"活动状态：",required:""}},[i("el-radio-group",{model:{value:e.formValidate.status,callback:function(t){e.$set(e.formValidate,"status",t)},expression:"formValidate.status"}},[i("el-radio",{staticClass:"radio",attrs:{label:0}},[e._v("关闭")]),e._v(" "),i("el-radio",{attrs:{label:1}},[e._v("开启")])],1)],1)],1),e._v(" "),i("el-col",{attrs:{span:24}},[i("el-form-item",{staticClass:"labeltop",attrs:{label:"商品属性：",required:""}},[i("el-table",{ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.ManyAttrValue,"tooltip-effect":"dark"},on:{"selection-change":e.handleSelectionChange}},[e.formValidate.specType?i("el-table-column",{key:"1",attrs:{type:"selection",width:"55"}}):e._e(),e._v(" "),e.manyTabDate&&e.formValidate.specType?e._l(e.manyTabDate,(function(t,r){return i("el-table-column",{key:r,attrs:{align:"center",label:e.manyTabTit[r].title,"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",{staticClass:"priceBox",domProps:{textContent:e._s(t.row[r])}})]}}],null,!0)})})):e._e(),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"upLoadPicBox",on:{click:function(i){return e.modalPicTap("1","duo",t.$index)}}},[t.row.image?i("div",{staticClass:"pictrue tabPic"},[i("img",{attrs:{src:t.row.image}})]):i("div",{staticClass:"upLoad tabPic"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}])}),e._v(" "),e._l(e.attrValue,(function(t,r){return i("el-table-column",{key:r,attrs:{label:e.formThead[r].title,align:"center","min-width":"145"},scopedSlots:e._u([{key:"default",fn:function(t){return["秒杀价"===e.formThead[r].title?i("el-input-number",{staticClass:"priceBox",attrs:{size:"small",min:0,precision:2,step:.1},model:{value:t.row[r],callback:function(i){e.$set(t.row,r,i)},expression:"scope.row[iii]"}}):"限量"===e.formThead[r].title?i("el-input-number",{staticClass:"priceBox",attrs:{size:"small",type:"number",min:1,max:t.row.stock,step:1,"step-strictly":""},model:{value:t.row[r],callback:function(i){e.$set(t.row,r,i)},expression:"scope.row[iii]"}}):i("span",{staticClass:"priceBox",domProps:{textContent:e._s(t.row[r])}})]}}],null,!0)})}))],2)],1)],1)],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:2===e.currentTab,expression:"currentTab === 2"}]},[i("el-form-item",{attrs:{label:"商品详情："}},[i("Tinymce",{model:{value:e.formValidate.content,callback:function(t){e.$set(e.formValidate,"content",t)},expression:"formValidate.content"}})],1)],1),e._v(" "),i("el-form-item",{staticStyle:{"margin-top":"30px"}},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.$route.params.id&&e.currentTab>0||e.$route.params.id&&2===e.currentTab,expression:"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:e.handleSubmitUp}},[e._v("上一步")]),e._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:0==e.currentTab,expression:"currentTab == 0"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSubmitNest1("formValidate")}}},[e._v("下一步")]),e._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:1==e.currentTab,expression:"currentTab == 1"}],staticClass:"submission",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.handleSubmitNest2("formValidate")}}},[e._v("下一步")]),e._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:2===e.currentTab,expression:"currentTab===2"},{name:"hasPermi",rawName:"v-hasPermi",value:["admin:seckill:update"],expression:"['admin:seckill:update']"}],staticClass:"submission",attrs:{loading:e.loading,type:"primary",size:"small"},on:{click:function(t){return e.handleSubmit("formValidate")}}},[e._v("提交")])],1)],1)],1),e._v(" "),i("CreatTemplates",{ref:"addTemplates",on:{getList:e.getShippingList}})],1)},a=[],n=i("8256"),o=i("73f5"),s=i("2f2c"),l=i("02df"),c=i("b7be"),u=i("ff3a"),m=i("61f7");function d(e){return h(e)||g(e)||p(e)||f()}function f(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"==typeof e)return b(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?b(e,t):void 0}}function g(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function h(e){if(Array.isArray(e))return b(e)}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=Array(t);i<t;i++)r[i]=e[i];return r}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,i="function"==typeof Symbol?Symbol:{},r=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function n(i,r,a,n){var l=r&&r.prototype instanceof s?r:s,c=Object.create(l.prototype);return y(c,"_invoke",function(i,r,a){var n,s,l,c=0,u=a||[],m=!1,d={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(t,i){return n=t,s=0,l=e,d.n=i,o}};function f(i,r){for(s=i,l=r,t=0;!m&&c&&!a&&t<u.length;t++){var a,n=u[t],f=d.p,p=n[2];i>3?(a=p===r)&&(l=n[(s=n[4])?5:(s=3,3)],n[4]=n[5]=e):n[0]<=f&&((a=i<2&&f<n[1])?(s=0,d.v=r,d.n=n[1]):f<p&&(a=i<3||n[0]>r||r>p)&&(n[4]=i,n[5]=r,d.n=p,s=0))}if(a||i>1)return o;throw m=!0,r}return function(a,u,p){if(c>1)throw TypeError("Generator is already running");for(m&&1===u&&f(u,p),s=u,l=p;(t=s<2?e:l)||!m;){n||(s?s<3?(s>1&&(d.n=-1),f(s,l)):d.n=l:d.v=l);try{if(c=2,n){if(s||(a="next"),t=n[a]){if(!(t=t.call(n,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,s<2&&(s=0)}else 1===s&&(t=n.return)&&t.call(n),s<2&&(l=TypeError("The iterator does not provide a '"+a+"' method"),s=1);n=e}else if((t=(m=d.n<0)?l:i.call(r,d))!==o)break}catch(t){n=e,s=1,l=t}finally{c=1}}return{value:t,done:m}}}(i,a,n),!0),c}var o={};function s(){}function l(){}function c(){}t=Object.getPrototypeOf;var u=[][r]?t(t([][r]())):(y(t={},r,(function(){return this})),t),m=c.prototype=s.prototype=Object.create(u);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,y(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return l.prototype=c,y(m,"constructor",c),y(c,"constructor",l),l.displayName="GeneratorFunction",y(c,a,"GeneratorFunction"),y(m),y(m,a,"Generator"),y(m,r,(function(){return this})),y(m,"toString",(function(){return"[object Generator]"})),(v=function(){return{w:n,m:d}})()}function y(e,t,i,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}y=function(e,t,i,r){function n(t,i){y(e,t,(function(e){return this._invoke(t,i,e)}))}t?a?a(e,t,{value:i,enumerable:!r,configurable:!r,writable:!r}):e[t]=i:(n("next",0),n("throw",1),n("return",2))},y(e,t,i,r)}function w(e,t,i,r,a,n,o){try{var s=e[n](o),l=s.value}catch(e){return void i(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function V(e){return function(){var t=this,i=arguments;return new Promise((function(r,a){var n=e.apply(t,i);function o(e){w(n,r,a,o,s,"next",e)}function s(e){w(n,r,a,o,s,"throw",e)}o(void 0)}))}}var _={image:"",images:"",imagess:[],title:"",info:"",num:1,unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,quota:1,barCode:"",weight:0,volume:0}],attr:[],selectRule:"",content:"",specType:!1,id:0,timeId:1,startTime:"",stopTime:"",timeVal:[],status:0},T={price:{title:"秒杀价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},quota:{title:"限量"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},x={name:"creatSeckill",components:{CreatTemplates:u["a"],Tinymce:n["a"]},data:function(){return{pickerOptions:{disabledDate:function(e){return e.getTime()<(new Date).setTime((new Date).getTime()-864e5)}},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},grid2:{xl:8,lg:10,md:12,sm:24,xs:24},currentTab:0,formThead:Object.assign({},T),formValidate:Object.assign({},_),loading:!1,fullscreenLoading:!1,merCateList:[],shippingList:[],seckillTime:[],ruleValidate:{productId:[{required:!0,message:"请选择商品",trigger:"change"}],title:[{required:!0,message:"请输入商品标题",trigger:"blur"}],attrValue:[{required:!0,message:"请选择商品属相",trigger:"change",type:"array",min:"1"}],num:[{required:!0,message:"请输入活动次数",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],info:[{required:!0,message:"请输入秒杀商品简介",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],timeId:[{required:!0,message:"请选择活动时间",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],imagess:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}],timeVal:[{required:!0,message:"请选择活动日期",trigger:"change",type:"array"}]},manyTabDate:{},manyTabTit:{},attrInfo:{},tempRoute:{},multipleSelection:[],productId:0,OneattrValue:[Object.assign({},_.attrValue[0])],ManyAttrValue:[Object.assign({},_.attrValue[0])]}},computed:{attrValue:function(){var e=Object.assign({},_.attrValue[0]);return delete e.image,e}},created:function(){this.$watch("formValidate.attr",this.watCh),this.tempRoute=Object.assign({},this.$route)},mounted:function(){var e=this;Object(l["a"])(1).then((function(t){e.seckillTime=t.list})),this.formValidate.imagess=[],this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo(),this.currentTab=1),this.getShippingList(),this.getCategorySelect()},methods:{watCh:function(e){var t={},i={};this.formValidate.attr.forEach((function(e,r){t[e.attrName]={title:e.attrName},i[e.attrName]=""})),this.manyTabTit=t,this.manyTabDate=i,this.formThead=Object.assign({},this.formThead,t)},handleRemove:function(e){this.formValidate.imagess.splice(e,1)},handleSelectionChange:function(e){this.multipleSelection=e},modalPicTap:function(e,t,i){var r=this;this.$modalUpload((function(a){if("1"!==e||t||(r.formValidate.image=a[0].sattDir,r.ManyAttrValue[0].image=a[0].sattDir),"2"===e&&!t){if(a.length>10)return this.$message.warning("最多选择10张图片！");if(a.length+r.formValidate.imagess.length>10)return this.$message.warning("最多选择10张图片！");a.map((function(e){r.formValidate.imagess.push(e.sattDir)}))}"1"===e&&"duo"===t&&(r.ManyAttrValue[i].image=a[0].sattDir)}),e,"content")},onchangeTime:function(e){this.formValidate.timeVal=e,this.formValidate.startTime=e?e[0]:"",this.formValidate.stopTime=e?e[1]:""},changeGood:function(){var e=this;this.$modalGoodList((function(t){e.formValidate.image=t.image,e.productId=t.id}))},handleSubmitNest1:function(){if(!this.formValidate.image)return this.$message.warning("请选择商品！");this.currentTab++,this.$route.params.id||this.getProdect(this.productId)},getCategorySelect:function(){var e=this;Object(o["d"])({status:-1,type:1}).then((function(t){e.merCateList=e.filerMerCateList(t)}))},filerMerCateList:function(e){return e.map((function(e){return e.child||(e.disabled=!0),e.label=e.name,e}))},getShippingList:function(){var e=this;Object(s["o"])(this.tempData).then((function(t){e.shippingList=t.list}))},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},getInfo:function(){this.$route.params.id?this.getSekllProdect(this.$route.params.id):this.getProdect(this.productId)},getProdect:function(e){var t=this;this.fullscreenLoading=!0,Object(o["l"])(e).then(function(){var e=V(v().m((function e(i){var r;return v().w((function(e){while(1)switch(e.n){case 0:r=i,t.formValidate={image:t.$selfUtil.setDomain(r.image),imagess:JSON.parse(r.sliderImage),title:r.storeName,info:r.storeInfo,quota:"",unitName:r.unitName,sort:r.sort,isShow:r.isShow,tempId:r.tempId,attr:r.attr,attrValue:r.attrValue,selectRule:r.selectRule,content:r.content,specType:r.specType,productId:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti,timeId:t.$route.params.id?Number(r.timeId):t.$route.params.timeId?Number(t.$route.params.timeId):"",startTime:r.startTime||"",stopTime:r.stopTime||"",timeVal:[],status:0,num:1},r.specType?(t.$nextTick((function(){r.attrValue.forEach((function(e){for(var i in e.quota=e.stock,e.attrValue=JSON.parse(e.attrValue),e.attrValue)e[i]=e.attrValue[i];e.image=t.$selfUtil.setDomain(e.image),t.$refs.multipleTable.toggleRowSelection(e,!0)}))})),t.ManyAttrValue=r.attrValue,t.multipleSelection=r.attrValue):(r.attrValue.forEach((function(e){e.quota=e.stock})),t.ManyAttrValue=r.attrValue),t.fullscreenLoading=!1;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1}))},getSekllProdect:function(e){var t=this;this.fullscreenLoading=!0,Object(c["I"])({id:e}).then(function(){var e=V(v().m((function e(i){var r;return v().w((function(e){while(1)switch(e.n){case 0:r=i,t.formValidate={image:t.$selfUtil.setDomain(r.image),imagess:JSON.parse(r.sliderImage),title:r.storeName,info:r.storeInfo,quota:r.quota,unitName:r.unitName,sort:r.sort,isShow:r.isShow,tempId:r.tempId,attr:r.attr,attrValue:r.attrValue,selectRule:r.selectRule,content:r.content,specType:r.specType,productId:r.productId,giveIntegral:r.giveIntegral,ficti:r.ficti,timeId:Number(r.timeId),startTime:r.startTimeStr||"",stopTime:r.stopTimeStr||"",status:r.status,num:r.num,timeVal:r.startTimeStr&&r.stopTimeStr?[r.startTimeStr,r.stopTimeStr]:[],id:r.id},r.specType?(t.ManyAttrValue=r.attrValue,t.$nextTick((function(){t.ManyAttrValue.forEach((function(e,i){for(var r in e.attrValue=JSON.parse(e.attrValue),e.attrValue)e[r]=e.attrValue[r];e.image=t.$selfUtil.setDomain(e.image),e.id&&(t.$set(e,"price",e.price),t.$set(e,"quota",e.quota),t.$nextTick((function(){t.$refs.multipleTable.toggleRowSelection(e,!0)})))}))}))):t.ManyAttrValue=r.attrValue,t.fullscreenLoading=!1;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1}))},handleSubmitNest2:function(e){var t=this;this.$refs[e].validate((function(e){return!!e&&(t.formValidate.specType&&0===t.multipleSelection.length?t.$message.warning("请填选择至少一个商品属性！"):void t.currentTab++)}))},handleSubmit:Object(m["a"])((function(e){var t=this;this.formValidate.specType?this.formValidate.attrValue=this.multipleSelection:this.formValidate.attrValue=this.ManyAttrValue,this.formValidate.images=JSON.stringify(this.formValidate.imagess),this.formValidate.attrValue.forEach((function(e){e.attrValue=JSON.stringify(e.attrValue)})),this.$refs[e].validate((function(i){i?(t.fullscreenLoading=!0,t.loading=!0,t.$route.params.id?Object(c["M"])({id:t.$route.params.id},t.formValidate).then(V(v().m((function i(){return v().w((function(i){while(1)switch(i.n){case 0:t.fullscreenLoading=!1,t.$message.success("编辑成功"),t.$router.push({path:"/marketing/seckill/list"}),t.$refs[e].resetFields(),t.formValidate.images=[],t.loading=!1;case 1:return i.a(2)}}),i)})))).catch((function(){t.fullscreenLoading=!1,t.loading=!1})):Object(c["K"])(t.formValidate).then(function(){var i=V(v().m((function i(r){return v().w((function(i){while(1)switch(i.n){case 0:t.fullscreenLoading=!1,t.$message.success("新增成功"),t.$router.push({path:"/marketing/seckill/list"}),t.$refs[e].resetFields(),t.formValidate.images=[],t.loading=!1;case 1:return i.a(2)}}),i)})));return function(e){return i.apply(this,arguments)}}()).catch((function(){t.fullscreenLoading=!1,t.loading=!1}))):t.formValidate.storeName&&t.formValidate.unitName&&t.formValidate.store_info&&t.formValidate.image&&t.formValidate.images||t.$message.warning("请填写完整商品信息！")}))})),handleSubmitUp:function(){this.currentTab--<0&&(this.currentTab=0)},setTagsViewTitle:function(){var e="编辑秒杀商品",t=Object.assign({},this.tempRoute,{title:"".concat(e,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",t)},handleDragStart:function(e,t){this.dragging=t},handleDragEnd:function(e,t){this.dragging=null},handleDragOver:function(e){e.dataTransfer.dropEffect="move"},handleDragEnter:function(e,t){if(e.dataTransfer.effectAllowed="move",t!==this.dragging){var i=d(this.formValidate.imagess),r=i.indexOf(this.dragging),a=i.indexOf(t);i.splice.apply(i,[a,0].concat(d(i.splice(r,1)))),this.formValidate.imagess=i}}}},k=x,O=(i("5fb2"),i("2877")),S=Object(O["a"])(k,r,a,!1,null,"55ba8746",null);t["default"]=S.exports},"5fb2":function(e,t,i){"use strict";i("4509")},cf0d:function(e,t,i){},e16d:function(e,t,i){"use strict";i("cf0d")},ff3a:function(e,t,i){"use strict";var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.dialogVisible?i("el-dialog",{attrs:{title:"运费模板",visible:e.dialogVisible,width:"1000px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogVisible?i("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"label-width":"120px",size:"mini",rules:e.rules}},[i("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[i("el-radio-group",{on:{change:function(t){return e.changeRadio(e.ruleForm.type)}},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,"type",t)},expression:"ruleForm.type"}},[i("el-radio",{attrs:{label:1}},[e._v("按件数")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("按重量")]),e._v(" "),i("el-radio",{attrs:{label:3}},[e._v("按体积")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"配送区域及运费",prop:"region"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:e.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"可配送区域","min-width":"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.$index?i("span",[e._v("默认全国")]):i("el-cascader",{staticStyle:{width:"98%"},attrs:{options:e.cityList,props:e.props,"collapse-tags":"",clearable:"",filterable:""},on:{change:e.changeRegion},model:{value:t.row.city_ids,callback:function(i){e.$set(t.row,"city_ids",i)},expression:"scope.row.city_ids"}})]}}],null,!1,41555841)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"130px",align:"center",label:e.columns.title,prop:"first"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.first,prop:"region."+t.$index+".first"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.first,callback:function(i){e.$set(t.row,"first",i)},expression:"scope.row.first"}})],1)]}}],null,!1,2918704294)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.firstPrice,prop:"region."+t.$index+".firstPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.firstPrice,callback:function(i){e.$set(t.row,"firstPrice",i)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3560784729)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:e.columns.title2,prop:"renewal"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.renewal,prop:"region."+t.$index+".renewal"}},[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:t.row.renewal,callback:function(i){e.$set(t.row,"renewal",i)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3001982106)}),e._v(" "),i("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form-item",{attrs:{rules:e.rules.renewalPrice,prop:"region."+t.$index+".renewalPrice"}},[i("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:t.row.renewalPrice,callback:function(i){e.$set(t.row,"renewalPrice",i)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,1318028453)}),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.$index>0?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.confirmEdit(e.ruleForm.region,t.$index)}}},[e._v("\n              删除\n            ")]):e._e()]}}],null,!1,3477974826)})],1)],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addRegion(e.ruleForm.region)}}},[e._v("\n        添加配送区域\n      ")])],1),e._v(" "),i("el-form-item",{attrs:{label:"指定包邮",prop:"appoint"}},[i("el-radio-group",{model:{value:e.ruleForm.appoint,callback:function(t){e.$set(e.ruleForm,"appoint",t)},expression:"ruleForm.appoint"}},[i("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),i("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),!0===e.ruleForm.appoint?i("el-form-item",{attrs:{prop:"free"}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[i("el-table-column",{attrs:{align:"center",label:"选择地区","min-width":"220"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-cascader",{staticStyle:{width:"95%"},attrs:{options:e.cityList,props:e.props,"collapse-tags":"",clearable:""},model:{value:r.city_ids,callback:function(t){e.$set(r,"city_ids",t)},expression:"row.city_ids"}})]}}],null,!1,3891925036)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"180px",align:"center",label:e.columns.title3},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===e.ruleForm.type,min:1===e.ruleForm.type?1:.1},model:{value:r.number,callback:function(t){e.$set(r,"number",t)},expression:"row.number"}})]}}],null,!1,2163935474)}),e._v(" "),i("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[i("el-input-number",{attrs:{"controls-position":"right"},model:{value:r.price,callback:function(t){e.$set(r,"price",t)},expression:"row.price"}})]}}],null,!1,187737026)}),e._v(" "),i("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return e.confirmEdit(e.ruleForm.free,t.$index)}}},[e._v("\n              删除\n            ")])]}}],null,!1,4029474057)})],1)],1):e._e(),e._v(" "),!0===e.ruleForm.appoint?i("el-form-item",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(t){return e.addFree(e.ruleForm.free)}}},[e._v("\n        添加指定包邮区域\n      ")])],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:e.ruleForm.sort,callback:function(t){e.$set(e.ruleForm,"sort",t)},expression:"ruleForm.sort"}})],1)],1):e._e(),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){return e.onClose("ruleForm")}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.onsubmit("ruleForm")}}},[e._v("确 定")])],1)],1):e._e()},a=[],n=i("2f2c"),o=i("5c96"),s={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]},l="重量（kg）",c="体积（m³）",u=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(l),title2:"续件".concat(l),title3:"包邮".concat(l)},{title:"首件".concat(c),title2:"续件".concat(c),title3:"包邮".concat(c)}],m={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个地区",trigger:"change"}],appoint:[{required:!0,message:"请选择是否指定包邮",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择活动区域",trigger:"change"}],city_id3:[{type:"array",required:!0,message:"请至少选择一个地区",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_id",props:{children:"child",label:"name",value:"cityId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},s),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,type:0}},mounted:function(){var e=this;setTimeout((function(){var t=JSON.parse(sessionStorage.getItem("cityList"));e.cityList=t}),1e3)},methods:{changType:function(e){this.type=e},onClose:function(e){this.dialogVisible=!1,this.$refs[e].resetFields()},confirmEdit:function(e,t){e.splice(t,1)},popoverHide:function(){},handleClose:function(){this.dialogVisible=!1,this.ruleForm={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]}},changeRegion:function(e){console.log(e)},changeRadio:function(e){this.columns=Object.assign({},u[e-1])},addRegion:function(e){e.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}))},addFree:function(e){e.push(Object.assign({},{city_id:[],number:1,price:1,city_ids:[]}))},getInfo:function(e,t){var i=this;this.tempId=e;var r=o["Loading"].service({fullscreen:!0});n["q"]({id:e}).then((function(e){i.dialogVisible=!0;var t=e;i.ruleForm=Object.assign(i.ruleForm,{name:t.name,type:t.type,appoint:t.appoint,sort:t.sort}),i.columns=Object.assign({},u[i.ruleForm.type-1]),i.$nextTick((function(){r.close()})),i.shippingRegion(),t.appoint&&i.shippingFree()})).catch((function(e){i.$message.error(e.message),i.$nextTick((function(){r.close()}))}))},shippingRegion:function(){var e=this;n["m"]({tempId:this.tempId}).then((function(t){t.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title})),e.ruleForm.region=t}))},shippingFree:function(){var e=this;n["l"]({tempId:this.tempId}).then((function(t){t.forEach((function(e,t){e.title=JSON.parse(e.title),e.city_ids=e.title})),e.ruleForm.free=t}))},getCityList:function(){var e=this;n["c"]().then((function(t){sessionStorage.setItem("cityList",JSON.stringify(t));var i=JSON.parse(sessionStorage.getItem("cityList"));e.cityList=i})).catch((function(t){e.$message.error(t.message)}))},change:function(e){return e.map((function(e){var t=[];e.city_ids.map((function(e){e.splice(0,1),t.push(e[0])})),e.city_id=t})),e},changeOne:function(e){var t=[];return e.map((function(e){e.splice(0,1),t.push(e[0])})),t},onsubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.loading=!0;var i={appoint:t.ruleForm.appoint,name:t.ruleForm.name,sort:t.ruleForm.sort,type:t.ruleForm.type};t.ruleForm.region.forEach((function(e,t){e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]);for(var i=0;i<e.city_ids.length;i++)e.city_ids[i].shift();e.cityId=e.city_ids.length>0?e.city_ids.join(","):"all"})),i.shippingTemplatesRegionRequestList=t.ruleForm.region,i.shippingTemplatesRegionRequestList.forEach((function(e,t){})),t.ruleForm.appoint&&(t.ruleForm.free.forEach((function(e,t){e.title=e.city_ids.length>0?JSON.stringify(e.city_ids):JSON.stringify([[0,0]]);for(var i=0;i<e.city_ids.length;i++)e.city_ids[i].shift();e.cityId=e.city_ids.length>0?e.city_ids.join(","):"all"})),i.shippingTemplatesFreeRequestList=t.ruleForm.free,i.shippingTemplatesFreeRequestList.forEach((function(e,t){}))),0===t.type?n["n"](i).then((function(e){t.$message.success("操作成功"),t.handleClose(),t.$nextTick((function(){t.dialogVisible=!1})),setTimeout((function(){t.$emit("getList")}),600),t.loading=!1})):n["p"](i,{id:t.tempId}).then((function(e){t.$message.success("操作成功"),setTimeout((function(){t.$emit("getList"),t.handleClose()}),600),t.$nextTick((function(){t.dialogVisible=!1})),t.loading=!1}))}))},clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},d=m,f=(i("e16d"),i("2877")),p=Object(f["a"])(d,r,a,!1,null,"44a816e5",null);t["a"]=p.exports}}]);