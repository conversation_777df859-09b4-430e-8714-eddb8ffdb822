(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a242d236"],{"0612":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("span",[e._v(e._s(e.$t("user.levelUpgrade.title")))])]),e._v(" "),r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm}},[r("el-form-item",{attrs:{label:e.$t("user.levelUpgrade.orderNo")}},[r("el-input",{attrs:{placeholder:e.$t("user.levelUpgrade.enterOrderNo"),clearable:""},model:{value:e.searchForm.orderNo,callback:function(t){e.$set(e.searchForm,"orderNo",t)},expression:"searchForm.orderNo"}})],1),e._v(" "),r("el-form-item",{attrs:{label:e.$t("user.levelUpgrade.orderStatus")}},[r("el-select",{attrs:{placeholder:e.$t("user.levelUpgrade.selectStatus"),clearable:""},model:{value:e.searchForm.orderStatus,callback:function(t){e.$set(e.searchForm,"orderStatus",t)},expression:"searchForm.orderStatus"}},[r("el-option",{attrs:{label:e.$t("user.levelUpgrade.pending"),value:0}}),e._v(" "),r("el-option",{attrs:{label:e.$t("user.levelUpgrade.paid"),value:1}}),e._v(" "),r("el-option",{attrs:{label:e.$t("user.levelUpgrade.cancelled"),value:2}}),e._v(" "),r("el-option",{attrs:{label:e.$t("user.levelUpgrade.refunded"),value:3}})],1)],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:e.getList}},[e._v(e._s(e.$t("user.center.query")))]),e._v(" "),r("el-button",{on:{click:e.resetSearch}},[e._v(e._s(e.$t("user.center.reset")))])],1)],1),e._v(" "),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,size:"mini"}},[r("el-table-column",{attrs:{prop:"orderNo",label:e.$t("user.levelUpgrade.orderNo"),"min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{prop:"uid",label:e.$t("user.levelUpgrade.userId"),"min-width":"80"}}),e._v(" "),r("el-table-column",{attrs:{label:e.$t("user.levelUpgrade.upgradeInfo"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(e._s(e.getLevelName(t.row.fromLevelId))+" → "+e._s(e.getLevelName(t.row.toLevelId)))])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"upgradePrice",label:e.$t("user.levelUpgrade.upgradeFee"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v("Rp "+e._s(t.row.upgradePrice))])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"paymentMethod",label:e.$t("user.levelUpgrade.paymentMethod"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.getPaymentMethodName(t.row.paymentMethod)))])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"orderStatus",label:e.$t("user.levelUpgrade.orderStatus"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:e.getStatusColor(t.row.orderStatus)}},[e._v("\n            "+e._s(e.getStatusName(t.row.orderStatus))+"\n          ")])]}}])}),e._v(" "),r("el-table-column",{attrs:{prop:"createTime",label:e.$t("user.levelUpgrade.createTime"),"min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{prop:"payTime",label:e.$t("user.levelUpgrade.payTime"),"min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{label:e.$t("user.levelUpgrade.operation"),"min-width":"120",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.orderStatus?r("el-button",{staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(r){return e.cancelOrder(t.row.orderNo)}}},[e._v("\n            "+e._s(e.$t("user.levelUpgrade.cancelOrder"))+"\n          ")]):e._e(),e._v(" "),r("el-button",{attrs:{type:"text",size:"small"},on:{click:function(r){return e.viewDetail(t.row)}}},[e._v(e._s(e.$t("user.levelUpgrade.viewDetail")))])]}}])})],1),e._v(" "),r("div",{staticClass:"block"},[r("el-pagination",{attrs:{"current-page":e.searchForm.page,"page-sizes":[10,20,50,100],"page-size":e.searchForm.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),r("el-dialog",{attrs:{title:e.$t("user.levelUpgrade.orderDetail"),visible:e.detailVisible,width:"600px"},on:{"update:visible":function(t){e.detailVisible=t}}},[e.currentOrder?r("div",[r("el-descriptions",{attrs:{column:2,border:""}},[r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.orderNo")}},[e._v(e._s(e.currentOrder.orderNo))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.userId")}},[e._v(e._s(e.currentOrder.uid))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.fromLevel")}},[e._v(e._s(e.getLevelName(e.currentOrder.fromLevelId)))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.toLevel")}},[e._v(e._s(e.getLevelName(e.currentOrder.toLevelId)))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.upgradeFee")}},[e._v("Rp "+e._s(e.currentOrder.upgradePrice))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.paymentMethod")}},[e._v(e._s(e.getPaymentMethodName(e.currentOrder.paymentMethod)))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.orderStatus")}},[r("el-tag",{attrs:{type:e.getStatusColor(e.currentOrder.orderStatus)}},[e._v("\n            "+e._s(e.getStatusName(e.currentOrder.orderStatus))+"\n          ")])],1),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.createTime")}},[e._v(e._s(e.currentOrder.createTime))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.payTime")}},[e._v(e._s(e.currentOrder.payTime||e.$t("user.levelUpgrade.unpaid")))]),e._v(" "),r("el-descriptions-item",{attrs:{label:e.$t("user.levelUpgrade.remark")}},[e._v(e._s(e.currentOrder.remark||e.$t("user.levelUpgrade.noRemark")))])],1)],1):e._e()])],1)},l=[],s=r("c24f"),n={name:"UserLevelUpgrade",data:function(){return{searchForm:{orderNo:"",orderStatus:"",page:1,limit:20},tableData:[],total:0,listLoading:!1,detailVisible:!1,currentOrder:null}},mounted:function(){var e=this;this.$nextTick((function(){e.getList()}))},methods:{getList:function(){var e=this;this.listLoading=!0,setTimeout((function(){e.tableData=[{orderNo:"UG202501030001",uid:1001,fromLevelId:1,toLevelId:2,upgradePrice:1e5,paymentMethod:"balance",orderStatus:0,createTime:"2025-01-03 10:00:00",payTime:null,remark:"测试订单"}],e.total=1,e.listLoading=!1}),500)},resetSearch:function(){this.searchForm={orderNo:"",orderStatus:"",page:1,limit:20},this.getList()},handleSizeChange:function(e){this.searchForm.limit=e,this.searchForm.page=1,this.getList()},handleCurrentChange:function(e){this.searchForm.page=e,this.getList()},cancelOrder:function(e){var t=this;this.$confirm(this.$t("user.levelUpgrade.confirmCancel"),this.$t("common.confirm"),{confirmButtonText:this.$t("common.confirm"),cancelButtonText:this.$t("common.cancel"),type:"warning"}).then((function(){Object(s["a"])(e).then((function(){t.$message.success(t.$t("user.levelUpgrade.cancelSuccess")),t.getList()})).catch((function(e){console.error("取消订单失败:",e),t.$message.error(t.$t("user.levelUpgrade.cancelFailed"))}))})).catch((function(){}))},viewDetail:function(e){this.currentOrder=e,this.detailVisible=!0},getLevelName:function(e){return this.$t("user.levelUpgrade.levelNames.".concat(e))||this.$t("user.levelUpgrade.unknownLevel")},getPaymentMethodName:function(e){var t={balance:this.$t("user.levelUpgrade.balancePayment")};return t[e]||e},getStatusName:function(e){var t={0:this.$t("user.levelUpgrade.pending"),1:this.$t("user.levelUpgrade.paid"),2:this.$t("user.levelUpgrade.cancelled"),3:this.$t("user.levelUpgrade.refunded")};return t[e]||this.$t("user.levelUpgrade.unknownStatus")},getStatusColor:function(e){var t={0:"warning",1:"success",2:"info",3:"danger"};return t[e]||"info"}}},i=n,o=(r("7c79"),r("2877")),d=Object(o["a"])(i,a,l,!1,null,"1cbd0e4c",null);t["default"]=d.exports},"7c79":function(e,t,r){"use strict";r("b6da")},b6da:function(e,t,r){}}]);