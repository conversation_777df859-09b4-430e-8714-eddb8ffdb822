{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: ['originResource'],\n  data() {\n    return {\n      resources: null\n    }\n  },\n  computed: {},\n  watch: {},\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.resources = this.originResource.length ? JSON.parse(JSON.stringify(this.originResource)) : ['']\n    },\n    onClose() {\n    },\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      const results = this.resources.filter(item => !!item) || []\n      this.$emit('save', results)\n      this.close()\n      if (results.length) {\n        this.resources = results\n      }\n    },\n    deleteOne(index) {\n      this.resources.splice(index, 1)\n    },\n    addOne(url) {\n      if (this.resources.indexOf(url) > -1) {\n        this.$message('资源已存在')\n      } else {\n        this.resources.push(url)\n      }\n    }\n  }\n}\n\n", null]}