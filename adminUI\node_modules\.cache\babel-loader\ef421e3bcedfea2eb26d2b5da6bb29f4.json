{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-echarts\\components\\ECharts.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-echarts\\components\\ECharts.vue", "mtime": 1754138273363}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport echarts from 'echarts/lib/echarts';\nimport debounce from 'lodash/debounce';\nimport { addListener, removeListener } from 'resize-detector';\n\n// enumerating ECharts events for now\nvar EVENTS = ['legendselectchanged', 'legendselected', 'legendunselected', 'legendscroll', 'datazoom', 'datarangeselected', 'timelinechanged', 'timelineplaychanged', 'restore', 'dataviewchanged', 'magictypechanged', 'geoselectchanged', 'geoselected', 'geounselected', 'pieselectchanged', 'pieselected', 'pieunselected', 'mapselectchanged', 'mapselected', 'mapunselected', 'axisareaselected', 'focusnodeadjacency', 'unfocusnodeadjacency', 'brush', 'brushselected', 'rendered', 'finished', 'click', 'dblclick', 'mouseover', 'mouseout', 'mousemove', 'mousedown', 'mouseup', 'globalout', 'contextmenu'];\nvar ZR_EVENTS = ['click', 'mousedown', 'mouseup', 'mousewheel', 'dblclick', 'contextmenu'];\nvar INIT_TRIGGERS = ['theme', 'initOptions', 'autoresize'];\nvar REWATCH_TRIGGERS = ['manualUpdate', 'watchShallow'];\nexport default {\n  props: {\n    options: Object,\n    theme: [String, Object],\n    initOptions: Object,\n    group: String,\n    autoresize: Boolean,\n    watchShallow: Boolean,\n    manualUpdate: Boolean\n  },\n  data: function data() {\n    return {\n      lastArea: 0\n    };\n  },\n  watch: {\n    group: function group(_group) {\n      this.chart.group = _group;\n    }\n  },\n  methods: {\n    // provide an explicit merge option method\n    mergeOptions: function mergeOptions(options, notMerge, lazyUpdate) {\n      if (this.manualUpdate) {\n        this.manualOptions = options;\n      }\n      if (!this.chart) {\n        this.init(options);\n      } else {\n        this.delegateMethod('setOption', options, notMerge, lazyUpdate);\n      }\n    },\n    // just delegates ECharts methods to Vue component\n    // use explicit params to reduce transpiled size for now\n    appendData: function appendData(params) {\n      this.delegateMethod('appendData', params);\n    },\n    resize: function resize(options) {\n      this.delegateMethod('resize', options);\n    },\n    dispatchAction: function dispatchAction(payload) {\n      this.delegateMethod('dispatchAction', payload);\n    },\n    convertToPixel: function convertToPixel(finder, value) {\n      return this.delegateMethod('convertToPixel', finder, value);\n    },\n    convertFromPixel: function convertFromPixel(finder, value) {\n      return this.delegateMethod('convertFromPixel', finder, value);\n    },\n    containPixel: function containPixel(finder, value) {\n      return this.delegateMethod('containPixel', finder, value);\n    },\n    showLoading: function showLoading(type, options) {\n      this.delegateMethod('showLoading', type, options);\n    },\n    hideLoading: function hideLoading() {\n      this.delegateMethod('hideLoading');\n    },\n    getDataURL: function getDataURL(options) {\n      return this.delegateMethod('getDataURL', options);\n    },\n    getConnectedDataURL: function getConnectedDataURL(options) {\n      return this.delegateMethod('getConnectedDataURL', options);\n    },\n    clear: function clear() {\n      this.delegateMethod('clear');\n    },\n    dispose: function dispose() {\n      this.delegateMethod('dispose');\n    },\n    delegateMethod: function delegateMethod(name) {\n      var _this$chart;\n      if (!this.chart) {\n        this.init();\n      }\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      return (_this$chart = this.chart)[name].apply(_this$chart, args);\n    },\n    delegateGet: function delegateGet(methodName) {\n      if (!this.chart) {\n        this.init();\n      }\n      return this.chart[methodName]();\n    },\n    getArea: function getArea() {\n      return this.$el.offsetWidth * this.$el.offsetHeight;\n    },\n    init: function init(options) {\n      var _this = this;\n      if (this.chart) {\n        return;\n      }\n      var chart = echarts.init(this.$el, this.theme, this.initOptions);\n      if (this.group) {\n        chart.group = this.group;\n      }\n      chart.setOption(options || this.manualOptions || this.options || {}, true);\n\n      // expose ECharts events as custom events\n      EVENTS.forEach(function (event) {\n        chart.on(event, function (params) {\n          _this.$emit(event, params);\n        });\n      });\n      ZR_EVENTS.forEach(function (event) {\n        chart.getZr().on(event, function (params) {\n          _this.$emit(\"zr:\".concat(event), params);\n        });\n      });\n      if (this.autoresize) {\n        this.lastArea = this.getArea();\n        this.__resizeHandler = debounce(function () {\n          if (_this.lastArea === 0) {\n            // emulate initial render for initially hidden charts\n            _this.mergeOptions({}, true);\n            _this.resize();\n            _this.mergeOptions(_this.options || _this.manualOptions || {}, true);\n          } else {\n            _this.resize();\n          }\n          _this.lastArea = _this.getArea();\n        }, 100, {\n          leading: true\n        });\n        addListener(this.$el, this.__resizeHandler);\n      }\n      Object.defineProperties(this, {\n        // Only recalculated when accessed from JavaScript.\n        // Won't update DOM on value change because getters\n        // don't depend on reactive values\n        width: {\n          configurable: true,\n          get: function get() {\n            return _this.delegateGet('getWidth');\n          }\n        },\n        height: {\n          configurable: true,\n          get: function get() {\n            return _this.delegateGet('getHeight');\n          }\n        },\n        isDisposed: {\n          configurable: true,\n          get: function get() {\n            return !!_this.delegateGet('isDisposed');\n          }\n        },\n        computedOptions: {\n          configurable: true,\n          get: function get() {\n            return _this.delegateGet('getOption');\n          }\n        }\n      });\n      this.chart = chart;\n    },\n    initOptionsWatcher: function initOptionsWatcher() {\n      var _this2 = this;\n      if (this.__unwatchOptions) {\n        this.__unwatchOptions();\n        this.__unwatchOptions = null;\n      }\n      if (!this.manualUpdate) {\n        this.__unwatchOptions = this.$watch('options', function (val, oldVal) {\n          if (!_this2.chart && val) {\n            _this2.init();\n          } else {\n            // mutating `options` will lead to merging\n            // replacing it with new reference will lead to not merging\n            // eg.\n            // `this.options = Object.assign({}, this.options, { ... })`\n            // will trigger `this.chart.setOption(val, true)\n            // `this.options.title.text = 'Trends'`\n            // will trigger `this.chart.setOption(val, false)`\n            _this2.chart.setOption(val, val !== oldVal);\n          }\n        }, {\n          deep: !this.watchShallow\n        });\n      }\n    },\n    destroy: function destroy() {\n      if (this.autoresize) {\n        removeListener(this.$el, this.__resizeHandler);\n      }\n      this.dispose();\n      this.chart = null;\n    },\n    refresh: function refresh() {\n      if (this.chart) {\n        this.destroy();\n        this.init();\n      }\n    }\n  },\n  created: function created() {\n    var _this3 = this;\n    this.initOptionsWatcher();\n    INIT_TRIGGERS.forEach(function (prop) {\n      _this3.$watch(prop, function () {\n        _this3.refresh();\n      }, {\n        deep: true\n      });\n    });\n    REWATCH_TRIGGERS.forEach(function (prop) {\n      _this3.$watch(prop, function () {\n        _this3.initOptionsWatcher();\n        _this3.refresh();\n      });\n    });\n  },\n  mounted: function mounted() {\n    // auto init if `options` is already provided\n    if (this.options) {\n      this.init();\n    }\n  },\n  activated: function activated() {\n    if (this.autoresize) {\n      this.chart && this.chart.resize();\n    }\n  },\n  destroyed: function destroyed() {\n    if (this.chart) {\n      this.destroy();\n    }\n  },\n  connect: function connect(group) {\n    if (typeof group !== 'string') {\n      group = group.map(function (chart) {\n        return chart.chart;\n      });\n    }\n    echarts.connect(group);\n  },\n  disconnect: function disconnect(group) {\n    echarts.disConnect(group);\n  },\n  registerMap: function registerMap(mapName, geoJSON, specialAreas) {\n    echarts.registerMap(mapName, geoJSON, specialAreas);\n  },\n  registerTheme: function registerTheme(name, theme) {\n    echarts.registerTheme(name, theme);\n  },\n  graphic: echarts.graphic\n};", null]}