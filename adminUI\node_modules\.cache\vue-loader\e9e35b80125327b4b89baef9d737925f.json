{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue?vue&type=template&id=1cbd0e4c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue", "mtime": 1754269254569}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <span>{{ $t('user.levelUpgrade.title') }}</span>\n    </div>\n\n    <!-- 搜索条件 -->\n    <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\n      <el-form-item :label=\"$t('user.levelUpgrade.orderNo')\">\n        <el-input v-model=\"searchForm.orderNo\" :placeholder=\"$t('user.levelUpgrade.enterOrderNo')\" clearable></el-input>\n      </el-form-item>\n      <el-form-item :label=\"$t('user.levelUpgrade.orderStatus')\">\n        <el-select v-model=\"searchForm.orderStatus\" :placeholder=\"$t('user.levelUpgrade.selectStatus')\" clearable>\n          <el-option :label=\"$t('user.levelUpgrade.pending')\" :value=\"0\"></el-option>\n          <el-option :label=\"$t('user.levelUpgrade.paid')\" :value=\"1\"></el-option>\n          <el-option :label=\"$t('user.levelUpgrade.cancelled')\" :value=\"2\"></el-option>\n          <el-option :label=\"$t('user.levelUpgrade.refunded')\" :value=\"3\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"getList\">{{ $t('user.center.query') }}</el-button>\n        <el-button @click=\"resetSearch\">{{ $t('user.center.reset') }}</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 订单列表 -->\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData\"\n      style=\"width: 100%\"\n      size=\"mini\"\n    >\n      <el-table-column\n        prop=\"orderNo\"\n        :label=\"$t('user.levelUpgrade.orderNo')\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        prop=\"uid\"\n        :label=\"$t('user.levelUpgrade.userId')\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        :label=\"$t('user.levelUpgrade.upgradeInfo')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <div>{{ getLevelName(scope.row.fromLevelId) }} → {{ getLevelName(scope.row.toLevelId) }}</div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"upgradePrice\"\n        :label=\"$t('user.levelUpgrade.upgradeFee')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>Rp {{ scope.row.upgradePrice }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"paymentMethod\"\n        :label=\"$t('user.levelUpgrade.paymentMethod')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ getPaymentMethodName(scope.row.paymentMethod) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"orderStatus\"\n        :label=\"$t('user.levelUpgrade.orderStatus')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusColor(scope.row.orderStatus)\">\n            {{ getStatusName(scope.row.orderStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        :label=\"$t('user.levelUpgrade.createTime')\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        prop=\"payTime\"\n        :label=\"$t('user.levelUpgrade.payTime')\"\n        min-width=\"150\"\n      />\n      <el-table-column :label=\"$t('user.levelUpgrade.operation')\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button\n            v-if=\"scope.row.orderStatus === 0\"\n            type=\"text\"\n            size=\"small\"\n            @click=\"cancelOrder(scope.row.orderNo)\"\n            class=\"mr10\"\n          >\n            {{ $t('user.levelUpgrade.cancelOrder') }}\n          </el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"viewDetail(scope.row)\">{{ $t('user.levelUpgrade.viewDetail') }}</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"block\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"searchForm.page\"\n        :page-sizes=\"[10, 20, 50, 100]\"\n        :page-size=\"searchForm.limit\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"total\">\n      </el-pagination>\n    </div>\n  </el-card>\n\n  <!-- 订单详情弹窗 -->\n  <el-dialog :title=\"$t('user.levelUpgrade.orderDetail')\" :visible.sync=\"detailVisible\" width=\"600px\">\n    <div v-if=\"currentOrder\">\n      <el-descriptions :column=\"2\" border>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.orderNo')\">{{ currentOrder.orderNo }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.userId')\">{{ currentOrder.uid }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.fromLevel')\">{{ getLevelName(currentOrder.fromLevelId) }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.toLevel')\">{{ getLevelName(currentOrder.toLevelId) }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.upgradeFee')\">Rp {{ currentOrder.upgradePrice }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.paymentMethod')\">{{ getPaymentMethodName(currentOrder.paymentMethod) }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.orderStatus')\">\n          <el-tag :type=\"getStatusColor(currentOrder.orderStatus)\">\n            {{ getStatusName(currentOrder.orderStatus) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.createTime')\">{{ currentOrder.createTime }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.payTime')\">{{ currentOrder.payTime || $t('user.levelUpgrade.unpaid') }}</el-descriptions-item>\n        <el-descriptions-item :label=\"$t('user.levelUpgrade.remark')\">{{ currentOrder.remark || $t('user.levelUpgrade.noRemark') }}</el-descriptions-item>\n      </el-descriptions>\n    </div>\n  </el-dialog>\n</div>\n", null]}