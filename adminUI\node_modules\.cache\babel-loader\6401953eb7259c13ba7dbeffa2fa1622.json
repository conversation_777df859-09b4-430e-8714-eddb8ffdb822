{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue", "mtime": 1754397765195}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import { productLstApi, productDeleteApi, categoryApi, putOnShellApi, offShellApi, productHeadersApi, productExportApi, restoreApi, productExcelApi } from '@/api/store'\nimport { brandLstApi, addBrand, updateBrandInfo, batchUpdateBrandInfo, industryLstApi } from \"@/api/brand\";\nimport { getToken } from \"@/utils/auth\";\n// import taoBao from './taoBao'\n// import UploadIndex from '@/components/uploadPicture/index.vue'\n\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: \"BrandProductList\",\n  // components: { UploadIndex },\n  data: function data() {\n    return {\n      pictureType: \"maintain\",\n      // 品牌状态常量\n      BRAND_STATUS: {\n        DELETED: \"-1\",\n        // 已删除\n        OFFLINE: \"0\",\n        // 待上架\n        ONLINE: \"1\",\n        // 已上架\n        SUSPENDED: \"2\" // 已下架\n      },\n      /*\r\n       * 品牌状态使用规范：\r\n       * - 新增品牌：默认 \"1\"(已上架)，可选待上架\r\n       * - 审核通过：从 \"0\" → \"1\"(已上架)\r\n       * - 临时下架：从 \"1\" → \"2\"(已下架)\r\n       * - 重新上架：从 \"2\" → \"1\"(已上架)\r\n       * - 逻辑删除：任何状态 → \"-1\"(已删除)\r\n       */\n      statusOptions: [{\n        value: \"-1\",\n        label: \"brand.pleaseSelect\"\n      }, {\n        value: \"0\",\n        // BRAND_STATUS.OFFLINE\n        label: \"brand.isOutline\"\n      }, {\n        value: \"1\",\n        // BRAND_STATUS.ONLINE\n        label: \"brand.isOnline\"\n      }, {\n        value: \"2\",\n        // BRAND_STATUS.SUSPENDED\n        label: \"brand.isOuted\"\n      }],\n      locationOptions: [{\n        value: \"all\",\n        label: \"common.pleaseSelect\"\n      }, {\n        value: \"yes\",\n        label: \"common.yes\"\n      }, {\n        value: \"no\",\n        label: \"common.no\"\n      }],\n      industryOptions: [{\n        value: \"1\",\n        label: \"2\"\n      }],\n      platformOptions: [{\n        value: \"tiktok\",\n        label: \"brand.platformTiktok\"\n      }, {\n        value: \"shopee\",\n        label: \"brand.platformShopee\"\n      }],\n      typeOptions: [{\n        value: \"1\",\n        label: \"common.yes\"\n      }, {\n        value: \"0\",\n        label: \"common.no\"\n      }],\n      editStatusOptions: [{\n        value: \"0\",\n        // BRAND_STATUS.OFFLINE - 待上架\n        label: \"brand.isOutline\"\n      }, {\n        value: \"1\",\n        // BRAND_STATUS.ONLINE - 已上架\n        label: \"brand.isOnline\"\n      }, {\n        value: \"2\",\n        // BRAND_STATUS.SUSPENDED - 已下架\n        label: \"brand.isOuted\"\n      }],\n      categoryOptions: [],\n      loading: false,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      form: {\n        page: 1,\n        limit: 20,\n        name: \"\",\n        type: \"-1\"\n      },\n      dform: {\n        id: \"\",\n        name: \"\",\n        image: \"\",\n        logoUrl: \"\",\n        industry: \"\",\n        platform: \"\",\n        status: \"\",\n        contactPhone: \"\",\n        contactPerson: \"\"\n      },\n      brandDialogVisible: false,\n      isEditMode: false,\n      // 标识是否为编辑模式\n      multipleSelection: [],\n      industryListOptions: []\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.getList();\n    industryLstApi(74).then(function (res) {\n      res.list.forEach(function (item) {\n        var values = item.value;\n        var fields = JSON.parse(values);\n        var one = {};\n        fields.fields.forEach(function (v) {\n          console.log(v);\n          if (v.name == \"code\") {\n            one[\"value\"] = v.value;\n          } else if (v.name == \"name\") {\n            one[\"label\"] = v.value;\n          }\n        });\n        _this.industryListOptions.push(one);\n      });\n    }).catch(function (res) {});\n  },\n  methods: {\n    checkPermi: checkPermi,\n    onSearch: function onSearch() {\n      this.form.page = 1;\n      this.getList();\n    },\n    onReset: function onReset() {\n      this.form.name = \"\";\n      this.form.type = \"-1\";\n    },\n    getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n      brandLstApi(this.form).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n        _this2.$message.error(_this2.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.form.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.form.limit = val;\n      this.getList();\n    },\n    // 处理表格选择变化\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.multipleSelection = selection;\n    },\n    handleDelete: function handleDelete(rowId, idx) {\n      var _this3 = this;\n      var _this = this;\n      var rows = [];\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: this.$t(\"brand.confirm\"),\n        cancelButtonText: this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        // 状态流转：任何状态 → \"-1\"(已删除)\n        var item = {\n          id: rowId,\n          status: _this3.BRAND_STATUS.DELETED\n        };\n        rows.push(item);\n        batchUpdateBrandInfo(rows).then(function (res) {\n          _this.getList();\n        }).catch(function (res) {});\n      });\n    },\n    onAdd: function onAdd() {\n      this.isEditMode = false;\n      this.dform = {\n        id: \"\",\n        name: \"\",\n        image: \"\",\n        logoUrl: \"\",\n        industry: \"\",\n        platform: \"\",\n        status: this.BRAND_STATUS.ONLINE,\n        // 新增品牌默认为已上架状态\n        contactPhone: \"\",\n        contactPerson: \"\"\n      };\n      this.brandDialogVisible = true;\n    },\n    handleCloseBrandDialog: function handleCloseBrandDialog() {\n      this.brandDialogVisible = false;\n      this.isEditMode = false;\n    },\n    editBrand: function editBrand(row, idx) {\n      this.isEditMode = true;\n      this.dform = _objectSpread({}, row); // 使用展开运算符避免直接引用\n      this.brandDialogVisible = true;\n    },\n    onSubBrand: function onSubBrand() {\n      var _this4 = this;\n      if (this.isEditMode) {\n        // 编辑模式，调用更新接口\n        updateBrandInfo(this.dform).then(function (res) {\n          _this4.brandDialogVisible = false;\n          _this4.isEditMode = false;\n          _this4.$message.success(_this4.$t(\"common.operationSuccess\"));\n          _this4.getList(); // 刷新列表\n        }).catch(function (res) {\n          _this4.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      } else {\n        // 新增模式，调用新增接口\n        addBrand(this.dform).then(function (res) {\n          _this4.brandDialogVisible = false;\n          _this4.$message.success(_this4.$t(\"common.operationSuccess\"));\n          _this4.getList(); // 刷新列表\n        }).catch(function (res) {\n          _this4.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      }\n    },\n    handleUpdate: function handleUpdate(row, idx) {\n      var _this5 = this;\n      var _this = this;\n      var rows = [];\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: _this.$t(\"brand.confirm\"),\n        cancelButtonText: _this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        var item = {\n          id: row.id\n        };\n        // 根据状态使用规范进行状态流转\n        if (row.status == _this5.BRAND_STATUS.ONLINE) {\n          // 已上架 → 已下架\n          item.status = _this5.BRAND_STATUS.SUSPENDED;\n        } else if (row.status == _this5.BRAND_STATUS.SUSPENDED) {\n          // 已下架 → 已上架\n          item.status = _this5.BRAND_STATUS.ONLINE;\n        } else if (row.status == _this5.BRAND_STATUS.OFFLINE) {\n          // 待上架 → 已上架\n          item.status = _this5.BRAND_STATUS.ONLINE;\n        }\n        rows.push(item);\n        batchUpdateBrandInfo(rows).then(function (res) {\n          _this.getList();\n        }).catch(function (res) {});\n      });\n    },\n    batchHandle: function batchHandle(type) {\n      var _this6 = this;\n      var _this = this;\n      var rows = [];\n      if (this.multipleSelection.length <= 0) {\n        this.$alert(this.$t(\"brand.selectTip\"), this.$t('brand.prompt'), {\n          confirmButtonText: _this.$t(\"brand.confirm\"),\n          type: 'warning',\n          showConfirmButton: false\n        });\n      } else {\n        this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n          confirmButtonText: this.$t(\"brand.confirm\"),\n          cancelButtonText: this.$t(\"brand.cancel\"),\n          type: \"warning\",\n          showClose: false\n        }).then(function () {\n          _this.multipleSelection.forEach(function (row) {\n            var item = {\n              id: row.id\n            };\n            // 根据状态使用规范设置目标状态\n            if (type == \"online\") {\n              // 状态流转：待上架/已下架 → 已上架\n              item[\"status\"] = _this6.BRAND_STATUS.ONLINE;\n            } else if (type == \"outline\") {\n              // 状态流转：已上架 → 已下架\n              item[\"status\"] = _this6.BRAND_STATUS.SUSPENDED;\n            } else if (type == \"delete\") {\n              // 状态流转：任何状态 → 已删除\n              item[\"status\"] = _this6.BRAND_STATUS.DELETED;\n            }\n            rows.push(item);\n          });\n          if (rows.length > 0) {\n            _this6.listLoading = true;\n            batchUpdateBrandInfo(rows).then(function (res) {\n              _this.getList();\n            }).catch(function (res) {});\n          }\n        });\n      }\n    },\n    isHighCashbackChange: function isHighCashbackChange(rowId, val) {\n      var _this = this;\n      var rows = [];\n      var item = {\n        id: rowId\n      };\n      if (val) {\n        item[\"isHighCashback\"] = true;\n      } else {\n        item[\"isHighCashback\"] = false;\n      }\n      rows.push(item);\n      batchUpdateBrandInfo(rows).then(function (res) {\n        _this.getList();\n      }).catch(function (res) {});\n    },\n    isHotChange: function isHotChange(rowId, val) {\n      var _this = this;\n      var rows = [];\n      var item = {\n        id: rowId\n      };\n      if (val) {\n        item[\"isHot\"] = true;\n      } else {\n        item[\"isHot\"] = false;\n      }\n      rows.push(item);\n      batchUpdateBrandInfo(rows).then(function (res) {\n        _this.getList();\n      }).catch(function (res) {});\n    }\n  }\n};", null]}