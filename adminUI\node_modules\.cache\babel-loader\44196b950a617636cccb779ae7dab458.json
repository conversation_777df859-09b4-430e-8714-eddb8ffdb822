{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport PriceChange from \"../components/PriceChange\";\nimport Loading from \"../components/Loading\";\nimport { orderRefuseApi, orderListApi, statisticsDataApi, orderMarkApi, editPriceApi, orderRefundApi } from '@/api/order';\nimport { required, num } from \"@/utils/validate\";\nimport { validatorDefaultCatch } from \"@/libs/dialog\";\nimport { isWriteOff } from \"@/utils\";\nexport default {\n  name: \"AdminOrderList\",\n  components: {\n    PriceChange: PriceChange,\n    Loading: Loading\n  },\n  props: {},\n  data: function data() {\n    return {\n      isWriteOff: isWriteOff(),\n      current: \"\",\n      change: false,\n      types: 0,\n      where: {\n        page: 1,\n        limit: 10,\n        status: 'unPaid'\n      },\n      list: [],\n      loaded: false,\n      loading: false,\n      orderInfo: {},\n      status: null\n    };\n  },\n  watch: {\n    \"$route.params.types\": function $routeParamsTypes(newVal) {\n      var that = this;\n      if (newVal != undefined) {\n        that.where.status = newVal;\n        that.init();\n      }\n    },\n    types: function types() {\n      this.getIndex();\n    }\n  },\n  created: function created() {\n    import('@/assets/js/media_750');\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.where.status = this.$route.params.types;\n    this.current = \"\";\n    this.getIndex();\n    this.$scroll(this.$refs.container, function () {\n      !_this.loading && _this.getIndex();\n    });\n  },\n  methods: {\n    more: function more(index) {\n      if (this.current === index) this.current = \"\";else this.current = index;\n    },\n    modify: function modify(item, status) {\n      this.change = true;\n      this.orderInfo = item;\n      this.status = status;\n    },\n    changeclose: function changeclose(msg) {\n      this.change = msg;\n      this.init();\n    },\n    // 拒绝退款\n    getRefuse: function getRefuse(id, reason) {\n      var _this2 = this;\n      orderRefuseApi({\n        orderNo: id,\n        reason: reason\n      }).then(function () {\n        _this2.change = false;\n        _this2.$dialog.success(\"已拒绝退款\");\n        _this2.init();\n      }).catch(function (error) {\n        _this2.$dialog.error(error.message);\n      });\n    },\n    savePrice: function () {\n      var _savePrice = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(opt) {\n        var that, data, price, refundPrice, refundStatus, remark, _t, _t2, _t3;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              that = this, data = {}, price = opt.price, refundPrice = opt.refundPrice, refundStatus = that.orderInfo.refundStatus, remark = opt.remark;\n              if (!(that.status == 0 && refundStatus === 0)) {\n                _context.n = 5;\n                break;\n              }\n              _context.p = 1;\n              _context.n = 2;\n              return this.$validator({\n                price: [required(required.message(\"金额\"))]\n              }).validate({\n                price: price\n              });\n            case 2:\n              _context.n = 4;\n              break;\n            case 3:\n              _context.p = 3;\n              _t = _context.v;\n              return _context.a(2, validatorDefaultCatch(_t));\n            case 4:\n              data.price = price;\n              data.orderNo = opt.orderId;\n              editPriceApi(data).then(function () {\n                that.change = false;\n                that.$dialog.success(\"改价成功\");\n                that.init();\n              }).catch(function (error) {\n                that.$dialog.error(error.message);\n              });\n              _context.n = 14;\n              break;\n            case 5:\n              if (!(that.status == 0 && refundStatus === 1)) {\n                _context.n = 10;\n                break;\n              }\n              _context.p = 6;\n              _context.n = 7;\n              return this.$validator({\n                refundPrice: [required(required.message(\"金额\")), num(num.message(\"金额\"))]\n              }).validate({\n                refundPrice: refundPrice\n              });\n            case 7:\n              _context.n = 9;\n              break;\n            case 8:\n              _context.p = 8;\n              _t2 = _context.v;\n              return _context.a(2, validatorDefaultCatch(_t2));\n            case 9:\n              data.amount = refundPrice;\n              data.type = opt.type;\n              data.orderNo = opt.orderId;\n              orderRefundApi(data).then(function (res) {\n                that.change = false;\n                that.$dialog.success('退款成功');\n                that.init();\n              }, function (err) {\n                that.change = false;\n                that.$dialog.error(err.message);\n              });\n              _context.n = 14;\n              break;\n            case 10:\n              _context.p = 10;\n              _context.n = 11;\n              return this.$validator({\n                remark: [required(required.message(\"备注\"))]\n              }).validate({\n                remark: remark\n              });\n            case 11:\n              _context.n = 13;\n              break;\n            case 12:\n              _context.p = 12;\n              _t3 = _context.v;\n              return _context.a(2, validatorDefaultCatch(_t3));\n            case 13:\n              data.mark = remark;\n              data.orderNo = opt.orderId;\n              orderMarkApi(data).then(function (res) {\n                that.change = false;\n                that.$dialog.success('提交成功');\n                that.init();\n              }, function (err) {\n                that.change = false;\n                that.$dialog.error(err.message);\n              });\n            case 14:\n              return _context.a(2);\n          }\n        }, _callee, this, [[10, 12], [6, 8], [1, 3]]);\n      }));\n      function savePrice(_x) {\n        return _savePrice.apply(this, arguments);\n      }\n      return savePrice;\n    }(),\n    init: function init() {\n      this.list = [];\n      this.where.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.getIndex();\n      this.current = \"\";\n    },\n    getIndex: function getIndex() {\n      var _this3 = this;\n      if (this.loading || this.loaded) return;\n      this.loading = true;\n      orderListApi(this.where).then(function (res) {\n        _this3.loading = false;\n        _this3.loaded = res.list.length < _this3.where.limit;\n        _this3.list.push.apply(_this3.list, res.list);\n        _this3.where.page = _this3.where.page + 1;\n      }, function (err) {\n        _this3.$dialog.error(err.message);\n      });\n    },\n    changeStatus: function changeStatus(val) {\n      if (this.where.status != val) {\n        this.where.status = val;\n        this.init();\n      }\n    },\n    toDetail: function toDetail(item) {\n      this.$router.push({\n        path: \"/javaMobile/orderDetail/\" + item.orderId\n      });\n    },\n    offlinePay: function offlinePay(item) {\n      // setOfflinePay({ order_id: item.order_id }).then(\n      //   res => {\n      //     this.$dialog.success(res.message);\n      //     this.init();\n      //   },\n      //   error => {\n      //     this.$dialog.error(error.message);\n      //   }\n      // );\n    }\n  }\n};", null]}