{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue?vue&type=template&id=a9ba4704&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.iShidden === false),expression:\"iShidden === false\"}]},[_c('div',{staticClass:\"WriteOff\"},[_c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":_vm.orderInfo.storeOrderInfoVos[0].info.image}})]),_vm._v(\" \"),_c('div',{staticClass:\"num acea-row row-center-wrapper\"},[_vm._v(\"\\n      \"+_vm._s(_vm.orderInfo.orderId)+\"\\n      \"),_c('div',{staticClass:\"views\",on:{\"click\":function($event){return _vm.toDetail(_vm.orderInfo)}}},[_vm._v(\"\\n        查看\"),_c('span',{staticClass:\"iconfont icon-jiantou views-jian\"})])]),_vm._v(\" \"),_c('div',{staticClass:\"tip\"},[_vm._v(\"确定要核销此订单吗？\")]),_vm._v(\" \"),_c('div',{staticClass:\"sure\",on:{\"click\":_vm.confirm}},[_vm._v(\"确定核销\")]),_vm._v(\" \"),_c('div',{staticClass:\"sure cancel\",on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")])]),_vm._v(\" \"),_c('div',{staticClass:\"maskModel\",on:{\"touchmove\":function($event){$event.preventDefault();}}})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}