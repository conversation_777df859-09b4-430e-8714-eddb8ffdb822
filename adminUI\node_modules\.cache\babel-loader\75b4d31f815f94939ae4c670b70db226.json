{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\utils\\loadMonaco.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\utils\\loadMonaco.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["import { loadScriptQueue } from './loadScript';\nimport ELEMENT from 'element-ui';\n\n// monaco-editor单例\nvar monacoEidtor;\n\n/**\n * 动态加载monaco-editor cdn资源\n * @param {Function} cb 回调，必填\n */\nexport default function loadMonaco(cb) {\n  if (monacoEidtor) {\n    cb(monacoEidtor);\n    return;\n  }\n  var vs = 'https://cdn.bootcss.com/monaco-editor/0.18.0/min/vs';\n\n  // 使用element ui实现加载提示\n  var loading = ELEMENT.Loading.service({\n    fullscreen: true,\n    lock: true,\n    text: '编辑器资源初始化中...',\n    spinner: 'el-icon-loading',\n    background: 'rgba(255, 255, 255, 0.5)'\n  });\n  !window.require && (window.require = {});\n  !window.require.paths && (window.require.paths = {});\n  window.require.paths.vs = vs;\n  loadScriptQueue([\"\".concat(vs, \"/loader.js\"), \"\".concat(vs, \"/editor/editor.main.nls.js\"), \"\".concat(vs, \"/editor/editor.main.js\")], function () {\n    loading.close();\n    // eslint-disable-next-line no-undef\n    monacoEidtor = monaco;\n    cb(monacoEidtor);\n  });\n}", null]}