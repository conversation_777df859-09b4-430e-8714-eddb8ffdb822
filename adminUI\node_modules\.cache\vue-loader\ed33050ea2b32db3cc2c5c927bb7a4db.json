{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue?vue&type=template&id=1dbd976e&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue", "mtime": 1754397765195}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.search')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('brand.brandNameInput'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.form.name),callback:function ($$v) {_vm.$set(_vm.form, \"name\", $$v)},expression:\"form.name\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.status')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('brand.pleaseSelect')},model:{value:(_vm.form.type),callback:function ($$v) {_vm.$set(_vm.form, \"type\", $$v)},expression:\"form.type\"}},_vm._l((_vm.statusOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t(item.label),\"value\":item.value}})}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.onSearch}},[_vm._v(_vm._s(_vm.$t(\"brand.query\")))]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":_vm.onReset}},[_vm._v(_vm._s(_vm.$t(\"brand.reset\")))]),_vm._v(\" \"),_c('div',{staticClass:\"acea-row padtop-10\"},[_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"success\"},on:{\"click\":_vm.onAdd}},[_vm._v(_vm._s(_vm.$t(\"brand.addBrand\")))]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"disabled\":_vm.multipleSelection.length === 0},on:{\"click\":function($event){return _vm.batchHandle('online')}}},[_vm._v(_vm._s(_vm.$t(\"brand.batchOnline\")))]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"disabled\":_vm.multipleSelection.length === 0},on:{\"click\":function($event){return _vm.batchHandle('outline')}}},[_vm._v(_vm._s(_vm.$t(\"brand.batchOffline\")))]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"disabled\":_vm.multipleSelection.length === 0},on:{\"click\":function($event){return _vm.batchHandle('delete')}}},[_vm._v(_vm._s(_vm.$t(\"brand.batchDelete\")))])],1)],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData.data,\"size\":\"mini\",\"highlight-current-row\":true,\"header-cell-style\":{ fontWeight: 'bold' }},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.brandLogo'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.logoUrl,\"preview-src-list\":[scope.row.logoUrl]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.brandName'),\"min-width\":\"80\",\"prop\":\"name\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.industry'),\"min-width\":\"160\",\"show-overflow-tooltip\":true,\"prop\":\"industry\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.platform'),\"min-width\":\"90\",\"align\":\"center\",\"prop\":\"platform\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.productCount'),\"min-width\":\"100\",\"align\":\"center\",\"prop\":\"productCount\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.createTime'),\"min-width\":\"120\",\"align\":\"center\",\"prop\":\"gmtCreate\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.creator'),\"min-width\":\"120\",\"align\":\"center\",\"prop\":\"creator\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.isHot'),\"min-width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.isHotChange(scope.row.id, scope.row.isHot)}},model:{value:(scope.row.isHot),callback:function ($$v) {_vm.$set(scope.row, \"isHot\", $$v)},expression:\"scope.row.isHot\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('brand.isHighCashback'),\"min-width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.isHighCashbackChange(scope.row.id, scope.row.isHighCashback)}},model:{value:(scope.row.isHighCashback),callback:function ($$v) {_vm.$set(scope.row, \"isHighCashback\", $$v)},expression:\"scope.row.isHighCashback\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.action'),\"min-width\":\"150\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('router-link',{attrs:{\"to\":{ path:'/brand/product/list',query: {brand:scope.row.code} }}},[_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"text\"}},[_vm._v(_vm._s(_vm.$t(\"brand.productList\")))])],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editBrand(scope.row, scope.$index)}}},[_vm._v(_vm._s(_vm.$t(\"brand.edit\")))]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row.id, scope.$index)}}},[_vm._v(_vm._s(_vm.$t(\"brand.delete\")))])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"block\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 40, 60, 80],\"page-size\":_vm.form.limit,\"current-page\":_vm.form.page,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.tableData.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.pageChange}})],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":_vm.isEditMode ? _vm.$t('brand.editDialogTitle') : _vm.$t('brand.addDialogTitle'),\"visible\":_vm.brandDialogVisible,\"width\":\"500px\",\"before-close\":_vm.handleCloseBrandDialog},on:{\"update:visible\":function($event){_vm.brandDialogVisible=$event}}},[_c('el-form',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"dform\",staticClass:\"mt20\",attrs:{\"model\":_vm.dform,\"label-width\":\"120px\"},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.brandName')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('brand.brandNameInput'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.name),callback:function ($$v) {_vm.$set(_vm.dform, \"name\", $$v)},expression:\"dform.name\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.brandLogo')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('brand.brandLogoInput'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.logoUrl),callback:function ($$v) {_vm.$set(_vm.dform, \"logoUrl\", $$v)},expression:\"dform.logoUrl\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.industry')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('brand.pleaseSelect')},model:{value:(_vm.dform.industry),callback:function ($$v) {_vm.$set(_vm.dform, \"industry\", $$v)},expression:\"dform.industry\"}},_vm._l((_vm.industryListOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.platform')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('brand.pleaseSelect')},model:{value:(_vm.dform.platform),callback:function ($$v) {_vm.$set(_vm.dform, \"platform\", $$v)},expression:\"dform.platform\"}},_vm._l((_vm.platformOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t(item.label),\"value\":item.value}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.contactPerson')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('brand.contactPerson'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.contactPerson),callback:function ($$v) {_vm.$set(_vm.dform, \"contactPerson\", $$v)},expression:\"dform.contactPerson\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.contactPhone')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('brand.contactPhone'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.contactPhone),callback:function ($$v) {_vm.$set(_vm.dform, \"contactPhone\", $$v)},expression:\"dform.contactPhone\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.status')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('brand.pleaseSelect')},model:{value:(_vm.dform.status),callback:function ($$v) {_vm.$set(_vm.dform, \"status\", $$v)},expression:\"dform.status\"}},_vm._l((_vm.editStatusOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t(item.label),\"value\":item.value}})}),1)],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.onSubBrand}},[_vm._v(_vm._s(_vm.isEditMode ? _vm.$t(\"brand.update\") : _vm.$t(\"brand.confirm\")))]),_vm._v(\" \"),_c('el-button',{on:{\"click\":_vm.handleCloseBrandDialog}},[_vm._v(_vm._s(_vm.$t(\"brand.cancel\")))])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}