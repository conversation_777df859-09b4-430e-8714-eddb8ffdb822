{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue?vue&type=template&id=4c776bef&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div>\n  <el-drawer v-bind=\"$attrs\" append-to-body v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n    <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n      <span class=\"bar-btn\" @click=\"refresh\">\n        <i class=\"el-icon-refresh\" />\n        刷新\n      </span>\n      <span ref=\"copyBtn\" class=\"bar-btn copy-json-btn\">\n        <i class=\"el-icon-document-copy\" />\n        复制JSON\n      </span>\n      <span class=\"bar-btn\" @click=\"exportJsonFile\">\n        <i class=\"el-icon-download\" />\n        导出JSON文件\n      </span>\n      <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n        <i class=\"el-icon-circle-close\" />\n        关闭\n      </span>\n    </div>\n    <div id=\"editorJson\" class=\"json-editor\" />\n  </el-drawer>\n</div>\n", null]}