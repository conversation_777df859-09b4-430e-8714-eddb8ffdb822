{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue?vue&type=template&id=004b6a8e&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\">\n          <el-form-item label=\"时间选择：\" class=\"width100\"> \n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <el-form-item label=\"提现状态：\">\n            <el-radio-group v-model=\"tableFrom.status\" type=\"button\" size=\"small\" @change=\"getList(1)\" clearable>\n              <el-radio-button label=\"\">全部</el-radio-button>\n              <el-radio-button label=\"0\">审核中</el-radio-button>\n              <el-radio-button label=\"1\">已提现</el-radio-button>\n              <el-radio-button label=\"-1\">已拒绝</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"提现方式：\">\n            <el-radio-group v-model=\"tableFrom.extractType\" type=\"button\" size=\"small\" @change=\"getList(1)\" clearable>\n              <el-radio-button label=\"\">全部</el-radio-button>\n              <el-radio-button label=\"bank\">银行卡</el-radio-button>\n              <el-radio-button label=\"alipay\">支付宝</el-radio-button>\n              <el-radio-button label=\"weixin\">微信</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"关键字：\" class=\"width100\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"微信号/姓名/支付宝账号/银行卡号/失败原因\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n  </el-card>\n  <div class=\"mt20\">\n    <cards-data :cardLists=\"cardLists\" v-if=\"checkPermi(['admin:finance:apply:balance'])\"></cards-data>\n  </div>\n  <el-card class=\"box-card\">\n     <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"60\"\n      />\n      <el-table-column\n        label=\"用户信息\"\n        min-width=\"180\"\n      >\n        <template slot-scope=\"scope\">\n          <p>用户昵称：{{scope.row.nickName}}</p>\n          <p>用户id：{{scope.row.uid}}</p>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"extractPrice\"\n        label=\"提现金额\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        label=\"提现方式\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.extractType | extractTypeFilter }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"账号\"\n        min-width=\"200\"\n      >\n        <template slot-scope=\"scope\">\n          <div v-if=\"scope.row.extractType=== 'bank'\">\n            <p>姓名：{{scope.row.realName }}</p>\n            <p>卡号：{{scope.row.bankCode }}</p>\n            <p>开户行：{{scope.row.bankName }}</p>\n          </div>\n          <span v-else-if=\"scope.row.extractType=== 'alipay'\">\n             <p>姓名：{{scope.row.realName }}</p>\n             <p>支付宝号：{{scope.row.alipayCode }}</p>\n             <div class=\"acea-row\">\n               收款码：\n               <div class=\"demo-image__preview\" v-if=\"scope.row.qrcodeUrl\">\n                  <el-image\n                   :src=\"scope.row.qrcodeUrl\"\n                   :preview-src-list=\"[scope.row.qrcodeUrl]\"\n                 />\n               </div>\n               <div v-else>无</div>\n             </div>\n          </span>\n          <span v-else-if=\"scope.row.extractType=== 'weixin'\">\n             <p>姓名：{{scope.row.realName }}</p>\n             <p>微信号：{{scope.row.wechat }}</p>\n             <div class=\"acea-row\">\n               收款码：\n               <div class=\"demo-image__preview\" v-if=\"scope.row.qrcodeUrl\">\n               <el-image\n               :src=\"scope.row.qrcodeUrl\"\n               :preview-src-list=\"[scope.row.qrcodeUrl]\"\n               />\n               </div>\n               <div v-else>无</div>\n             </div>\n          </span>\n          <span v-else>已退款</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"审核状态\"\n        min-width=\"200\"\n      >\n        <template slot-scope=\"scope\">\n          <span class=\"spBlock\">{{ scope.row.status | extractStatusFilter }}</span>\n          <span v-if=\"scope.row.status === -1\">拒绝原因：{{scope.row.failMsg}}</span>\n          <template v-if=\"scope.row.status === 0\">\n            <el-button type=\"danger\" icon=\"el-icon-close\" size=\"mini\" @click=\"onExamine(scope.row.id)\" v-hasPermi=\"['admin:finance:apply:apply']\">未通过</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-check\" size=\"mini\" @click=\"ok(scope.row.id)\" v-hasPermi=\"['admin:finance:apply:apply']\">通过</el-button>\n          </template>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"备注\"\n        min-width=\"200\"\n      >\n        <template slot-scope=\"scope\">\n          <span class=\"spBlock\">{{ scope.row.mark | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        label=\"创建时间\"\n        min-width=\"150\"\n      />\n      <el-table-column label=\"操作\" min-width=\"80\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button v-if=\"scope.row.status !== 1\" type=\"text\" size=\"small\" @click=\"handleEdit(scope.row)\" v-hasPermi=\"['admin:finance:apply:update']\">编辑</el-button>\n          <span v-else>无</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n   </el-card>\n\n  <!--编辑-->\n  <el-dialog\n    title=\"编辑\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n    <!--微信-->\n    <zb-parser\n      v-if=\"dialogVisible && (tableFrom.extractType==='weixin' || extractType==='weixin')\"\n      :form-id=\"124\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n    <!--支付宝-->\n    <zb-parser\n      v-if=\"dialogVisible && (tableFrom.extractType==='alipay' || extractType==='alipay')\"\n      :form-id=\"126\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n    <!--银行卡-->\n    <zb-parser\n      v-if=\"dialogVisible && (tableFrom.extractType==='bank' || extractType==='bank')\"\n      :form-id=\"125\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n  </el-dialog>\n</div>\n", null]}