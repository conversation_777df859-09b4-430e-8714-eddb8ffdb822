{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-37280400\"],{\"14cf\":function(t,e,a){\"use strict\";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"divBox relative\"},[a(\"el-card\",{staticClass:\"box-card\"},[a(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[a(\"div\",{staticClass:\"container mt-1\"},[a(\"el-form\",{attrs:{inline:\"\",size:\"small\"}},[a(\"el-form-item\",{attrs:{label:t.$t(\"product.search\")}},[a(\"el-input\",{staticClass:\"selWidth\",attrs:{placeholder:t.$t(\"product.enterProductName\"),size:\"small\",clearable:\"\"},model:{value:t.form.keywords,callback:function(e){t.$set(t.form,\"keywords\",e)},expression:\"form.keywords\"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.status\")}},[a(\"el-select\",{attrs:{placeholder:t.$t(\"product.pleaseSelect\")},model:{value:t.form.isShow,callback:function(e){t.$set(t.form,\"isShow\",e)},expression:\"form.isShow\"}},t._l(t.statusOptions,(function(e){return a(\"el-option\",{key:e.value,attrs:{label:t.$t(\"product.\"+e.label),value:e.value}})})),1)],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"brand.brandName\")}},[a(\"el-select\",{attrs:{placeholder:t.$t(\"product.pleaseSelect\"),clearable:\"\"},model:{value:t.form.brand,callback:function(e){t.$set(t.form,\"brand\",e)},expression:\"form.brand\"}},t._l(t.brandOptions,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),t._v(\" \"),a(\"el-button\",{staticClass:\"mr10\",attrs:{size:\"small\",type:\"primary\"},on:{click:t.onSearch}},[t._v(\"\\n         \"+t._s(t.$t(\"product.query\"))+\"\\n       \")]),t._v(\" \"),a(\"el-button\",{staticClass:\"mr10\",attrs:{size:\"small\",type:\"\"},on:{click:t.onReset}},[t._v(\"\\n         \"+t._s(t.$t(\"product.reset\"))+\"\\n       \")]),t._v(\" \"),a(\"div\",{staticClass:\"acea-row padtop-10\"},[a(\"el-button\",{attrs:{size:\"small\",type:\"success\"},on:{click:t.onAdd}},[t._v(\"\\n           \"+t._s(t.$t(\"product.addProduct\"))+\"\\n         \")]),t._v(\" \"),a(\"el-button\",{attrs:{size:\"small\"},on:{click:function(e){return t.batchHandle(\"online\")}}},[t._v(\"\\n           \"+t._s(t.$t(\"product.batchOnline\"))+\"\\n         \")]),t._v(\" \"),a(\"el-button\",{attrs:{size:\"small\"},on:{click:function(e){return t.batchHandle(\"outline\")}}},[t._v(\"\\n           \"+t._s(t.$t(\"product.batchOffline\"))+\"\\n         \")]),t._v(\" \"),a(\"el-button\",{attrs:{size:\"small\"},on:{click:function(e){return t.batchHandle(\"delete\")}}},[t._v(\"\\n           \"+t._s(t.$t(\"product.batchDelete\"))+\"\\n         \")])],1)],1),t._v(\" \"),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.listLoading,expression:\"listLoading\"}],staticStyle:{width:\"100%\"},attrs:{data:t.tableData.data,size:\"mini\",\"highlight-current-row\":!0,\"header-cell-style\":{fontWeight:\"bold\"}},on:{\"selection-change\":t.handleSelection}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.productImage\"),\"min-width\":\"80\"},scopedSlots:t._u([{key:\"default\",fn:function(t){return[a(\"div\",{staticClass:\"demo-image__preview\"},[a(\"el-image\",{staticStyle:{width:\"36px\",height:\"36px\"},attrs:{src:t.row.image||\"\",\"preview-src-list\":[t.row.image||\"\"]}})],1)]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.productName\"),\"min-width\":\"160\",\"show-overflow-tooltip\":!0,prop:\"storeName\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.productPrice\"),\"min-width\":\"90\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(\"\\n           \"+t._s(t.formatAmount(e.row.price))+\"\\n       \")]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.cashbackRate\"),\"min-width\":\"100\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(t._s(t.formatRate(e.row.cashBackRate)))]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.isHot\"),\"min-width\":\"80\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-switch\",{attrs:{\"active-value\":!0,\"inactive-value\":!1},on:{change:function(a){return t.isShowChange(e.row,e.row.isHot,\"isHot\")}},model:{value:e.row.isHot,callback:function(a){t.$set(e.row,\"isHot\",a)},expression:\"scope.row.isHot\"}})]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.isBenefit\"),\"min-width\":\"80\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-switch\",{attrs:{\"active-value\":!0,\"inactive-value\":!1},on:{change:function(a){return t.isShowChange(e.row,e.row.isBenefit,\"isBenefit\")}},model:{value:e.row.isBenefit,callback:function(a){t.$set(e.row,\"isBenefit\",a)},expression:\"scope.row.isBenefit\"}})]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.isTikTok\"),\"min-width\":\"80\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-switch\",{attrs:{\"active-value\":!0,\"inactive-value\":!1},on:{change:function(a){return t.isShowChange(e.row,e.row.isBest,\"isBest\")}},model:{value:e.row.isBest,callback:function(a){t.$set(e.row,\"isBest\",a)},expression:\"scope.row.isBest\"}})]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.addTime\"),\"min-width\":\"120\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(t._s(t.formatTime(e.row.addTime)))]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.online\"),\"min-width\":\"80\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-switch\",{attrs:{\"active-value\":!0,\"inactive-value\":!1},on:{change:function(a){return t.handleUpdate(e.row)}},model:{value:e.row.isShow,callback:function(a){t.$set(e.row,\"isShow\",a)},expression:\"scope.row.isShow\"}})]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:t.$t(\"product.action\"),\"min-width\":\"100\",fixed:\"right\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{staticClass:\"mr10\",attrs:{type:\"text\",size:\"small\"},on:{click:function(a){return t.editProduct(e.row,e.$index)}}},[t._v(\"\\n             \"+t._s(t.$t(\"product.edit\"))+\"\\n           \")]),t._v(\" \"),a(\"el-button\",{attrs:{type:\"text\",size:\"small\"},on:{click:function(a){return t.handleDelete(e.row,e.$index)}}},[t._v(\"\\n             \"+t._s(t.$t(\"product.delete\"))+\"\\n           \")])]}}])})],1),t._v(\" \"),a(\"div\",{staticClass:\"block\"},[a(\"el-pagination\",{attrs:{\"page-sizes\":[20,40,60,80],\"page-size\":t.form.limit,\"current-page\":t.form.page,layout:\"total, sizes, prev, pager, next, jumper\",total:t.tableData.total},on:{\"size-change\":t.handleSizeChange,\"current-change\":t.pageChange}})],1)],1),t._v(\" \"),a(\"el-dialog\",{attrs:{title:t.isEditMode?t.$t(\"product.editDialogTitle\"):t.$t(\"product.addDialogTitle\"),visible:t.productDialogVisible,width:\"540px\",\"before-close\":t.handleCloseProductDialog},on:{\"update:visible\":function(e){t.productDialogVisible=e}}},[a(\"el-form\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],ref:\"dform\",staticClass:\"mt24\",attrs:{model:t.dform,\"label-width\":\"160px\"},nativeOn:{submit:function(t){t.preventDefault()}}},[a(\"el-form-item\",{attrs:{label:t.$t(\"product.enterProductLink\")}},[a(\"el-input\",{staticClass:\"selWidth width200\",attrs:{placeholder:\"please input link\",size:\"small\",clearable:\"\"},model:{value:t.url,callback:function(e){t.url=e},expression:\"url\"}}),t._v(\" \"),a(\"el-button\",{attrs:{size:\"small\"},on:{click:t.fetchProduct}},[t._v(\"\\n           \"+t._s(t.$t(\"product.fetchProductInfo\"))+\"\\n         \")])],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"brand.brandName\")}},[a(\"div\",{staticStyle:{display:\"flex\",\"align-items\":\"center\",gap:\"8px\"}},[a(\"el-select\",{staticStyle:{flex:\"1\"},attrs:{placeholder:t.$t(\"brand.pleaseSelect\"),loading:t.brandLoading,filterable:\"\",clearable:\"\"},model:{value:t.brandName,callback:function(e){t.brandName=e},expression:\"brandName\"}},t._l(t.brandOptions,(function(t){return a(\"el-option\",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(\" \"),a(\"el-button\",{attrs:{size:\"small\",icon:\"el-icon-refresh\",loading:t.brandLoading,title:t.$t(\"brand.refreshBrands\")||\"刷新品牌数据\"},on:{click:t.refreshBrands}})],1)]),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.productName\")}},[a(\"el-input\",{staticClass:\"selWidth readonly-input\",attrs:{readonly:\"\",placeholder:t.$t(\"product.enterProductName\"),size:\"small\",clearable:\"\"},model:{value:t.dform.storeName,callback:function(e){t.$set(t.dform,\"storeName\",e)},expression:\"dform.storeName\"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.productImage\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"div\",{staticClass:\"demo-image__preview\"},[a(\"el-image\",{staticStyle:{width:\"36px\",height:\"36px\"},attrs:{src:t.dform.image,\"preview-src-list\":[t.dform.image]}})],1)]}}])}),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.productPrice\")}},[a(\"el-input\",{staticClass:\"selWidth readonly-input\",attrs:{readonly:\"\",placeholder:t.$t(\"product.enterProductPrice\"),size:\"small\",clearable:\"\"},model:{value:t.dform.price,callback:function(e){t.$set(t.dform,\"price\",e)},expression:\"dform.price\"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.cashbackRate\")}},[a(\"el-input\",{staticClass:\"selWidth readonly-input\",attrs:{readonly:\"\",placeholder:t.$t(\"product.enterCashbackRate\"),size:\"small\",clearable:\"\"},model:{value:t.dform.forMatCashBackRate,callback:function(e){t.$set(t.dform,\"forMatCashBackRate\",e)},expression:\"dform.forMatCashBackRate\"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:t.$t(\"product.isOnline\")}},[a(\"el-select\",{attrs:{placeholder:t.$t(\"product.pleaseSelect\")},model:{value:t.status,callback:function(e){t.status=e},expression:\"status\"}},t._l(t.typeOptions,(function(e){return a(\"el-option\",{key:e.value,attrs:{label:t.$t(\"product.\"+e.label),value:e.value}})})),1)],1)],1),t._v(\" \"),a(\"span\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:t.onSubProduct}},[t._v(t._s(t.$t(\"product.confirm\")))]),t._v(\" \"),a(\"el-button\",{on:{click:t.handleCloseProductDialog}},[t._v(t._s(t.$t(\"product.cancel\")))])],1)],1)],1)},r=[],o=a(\"9add\"),i=(a(\"5f87\"),a(\"e350\"));function l(t){return l=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t},l(t)}function s(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?s(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return(e=d(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function d(t){var e=f(t,\"string\");return\"symbol\"==l(e)?e:e+\"\"}function f(t,e){if(\"object\"!=l(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var n=a.call(t,e||\"default\");if(\"object\"!=l(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===e?String:Number)(t)}var m={name:\"BrandProductList\",data:function(){return{statusOptions:[{value:null,label:\"all\"},{value:!0,label:\"online\"},{value:!1,label:\"offline\"}],typeOptions:[{value:\"1\",label:this.$t(\"yes\")},{value:\"0\",label:this.$t(\"no\")}],loading:!1,listLoading:!1,tableData:{data:[],total:0},form:{page:1,limit:20,keywords:\"\",isShow:null,total:0,isIndex:!1,brand:\"\"},url:\"\",brandName:\"\",dform:{},status:\"\",productDialogVisible:!1,isEditMode:!1,multipleSelection:[],brandOptions:[],brandLoading:!1}},mounted:function(){var t=this.$route.query.brand||\"\";this.form.brand=t,this.getList(),this.getBrands()},watch:{\"$route.query.brand\":function(t){this.form.brand=t,this.getList()}},methods:{checkPermi:i[\"a\"],formatAmount:function(t){void 0==t&&(t=0);var e=(t/1e3).toFixed(3);return e},getBrands:function(){var t=this;this.brandLoading=!0,Object(o[\"f\"])({page:1,limit:1e4,type:\"-1\",name:\"\"}).then((function(e){if(e.list){var a=[];for(var n in e.list){var r=e.list[n];a.push({label:r[\"name\"],value:r[\"code\"]})}t.brandOptions=a,console.log(\"成功获取 \".concat(t.brandOptions.length,\" 个品牌数据\"))}t.brandLoading=!1})).catch((function(e){t.brandLoading=!1,t.$message.error(t.$t(\"common.fetchDataFailed\")||\"获取品牌数据失败\")}))},refreshBrands:function(){this.getBrands(),this.$message.success(this.$t(\"brand.refreshingBrands\")||\"正在刷新品牌数据...\")},getList:function(){var t=this,e=this;this.listLoading=!0;var a=c({},this.form);null===a.isShow&&delete a.isShow,Object(o[\"i\"])(a).then((function(t){e.listLoading=!1,e.tableData.data=t.list,e.tableData.total=t.total})).catch((function(e){t.listLoading=!1,t.$message.error(t.$t(\"common.fetchDataFailed\"))}))},onSearch:function(){this.form.page=1,this.getList()},onReset:function(){this.form.keywords=\"\",this.form.isShow=null,this.form.brand=\"\",this.form.page=1,this.getList()},onAdd:function(){this.getBrands(),this.isEditMode=!1,this.productDialogVisible=!0,this.dform={},this.brandName=\"\",this.status=\"\",this.url=\"\"},handleSelection:function(t){this.multipleSelection=t},batchHandle:function(t){var e=this,a=[];if(this.multipleSelection.forEach((function(t){a.push(t.id)})),a.length>0){this.listLoading=!0;var n={ids:a};\"online\"===t?Object(o[\"c\"])(n).then((function(t){e.getList()})):\"outline\"===t?Object(o[\"d\"])(n).then((function(t){e.getList()})):\"delete\"===t&&(n.type=\"recycle\",Object(o[\"b\"])(n).then((function(t){e.getList()})))}},handleSizeChange:function(t){this.form.limit=t,this.getList()},pageChange:function(t){this.form.page=t,this.getList()},handleUpdate:function(t){var e=this,a=this,n={ids:[t.id]};Boolean(t.isShow)?Object(o[\"c\"])(n).then((function(t){})).catch((function(t){a.$message.error(e.$t(\"common.operationFailed\"))})):Object(o[\"d\"])(n).then((function(t){})).catch((function(t){a.$message.error(e.$t(\"common.operationFailed\"))}))},formatTime:function(t){var e=new Date(1e3*t),a=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,\"0\"),r=String(e.getDate()).padStart(2,\"0\"),o=String(e.getHours()).padStart(2,\"0\"),i=String(e.getMinutes()).padStart(2,\"0\"),l=String(e.getSeconds()).padStart(2,\"0\");return\"\".concat(a,\"-\").concat(n,\"-\").concat(r,\" \").concat(o,\":\").concat(i,\":\").concat(l)},editProduct:function(t){this.getBrands(),this.isEditMode=!0,this.productDialogVisible=!0,this.dform=t,this.status=t.isShow?\"1\":\"0\",this.brandName=t.brand,this.dform.forMatCashBackRate=this.formatRate(this.dform.cashBackRate)},isShowChange:function(t,e,a){var n=this,r={id:t.id};r[a]=!!e,Object(o[\"k\"])(r).then((function(t){})).catch((function(t){n.$message.error(n.$t(\"common.operationFailed\"))}))},handleDelete:function(t){var e=this;this.$confirm(this.$t(\"brand.confirmOperation\"),this.$t(\"brand.prompt\"),{confirmButtonText:this.$t(\"brand.confirm\"),cancelButtonText:this.$t(\"brand.cancel\"),type:\"warning\",showClose:!1}).then((function(){var a={ids:[t.id],type:\"recycle\"};Object(o[\"b\"])(a).then((function(t){e.getList()}))}))},handleCloseProductDialog:function(){this.productDialogVisible=!1},onSubProduct:function(){var t=this,e=this;this.dform&&this.dform.id&&Object(o[\"k\"])({id:this.dform.id,isShow:\"1\"==this.status,brand:this.brandName}).then((function(t){e.getList(),e.productDialogVisible=!1})).catch((function(a){e.productDialogVisible=!1,t.$message.error(t.$t(\"common.operationFailed\"))}))},fetchProduct:function(){var t=this,e=this;Object(o[\"g\"])(6,this.url).then((function(t){e.dform=t,e.dform.cashBackRate=e.formatRate(e.dform.cashBackRate)})).catch((function(e){t.$message.error(t.$t(\"product.fetchProductFailed\"))}))},formatRate:function(t){return parseInt(1e4*t)/100+\"%\"}}},h=m,p=(a(\"532b\"),a(\"e45a\"),a(\"2877\")),b=Object(p[\"a\"])(h,n,r,!1,null,\"1907ea3f\",null);e[\"default\"]=b.exports},\"427a\":function(t,e,a){},\"532b\":function(t,e,a){\"use strict\";a(\"427a\")},\"9add\":function(t,e,a){\"use strict\";a.d(e,\"f\",(function(){return r})),a.d(e,\"j\",(function(){return o})),a.d(e,\"a\",(function(){return i})),a.d(e,\"e\",(function(){return l})),a.d(e,\"h\",(function(){return s})),a.d(e,\"i\",(function(){return c})),a.d(e,\"g\",(function(){return u})),a.d(e,\"c\",(function(){return d})),a.d(e,\"d\",(function(){return f})),a.d(e,\"b\",(function(){return m})),a.d(e,\"k\",(function(){return h}));var n=a(\"b775\");function r(t){return Object(n[\"a\"])({url:\"/admin/brand/list\",method:\"GET\",params:t})}function o(t){return Object(n[\"a\"])({url:\"/admin/brand/update\",method:\"POST\",data:t})}function i(t){return Object(n[\"a\"])({url:\"/admin/brand/add\",method:\"POST\",data:t})}function l(t){return Object(n[\"a\"])({url:\"/admin/brand/batchUpdate\",method:\"POST\",data:t})}function s(t){return Object(n[\"a\"])({url:\"/admin/system/group/data/list?gid=\"+t,method:\"GET\"})}function c(t){return Object(n[\"a\"])({url:\"/admin/store/product/list\",method:\"GET\",params:t})}function u(t,e){return Object(n[\"a\"])({url:\"/admin/store/product/importProduct?form=\"+t+\"&url=\"+e,method:\"POST\"})}function d(t){return Object(n[\"a\"])({url:\"/admin/store/product/batch/putOnShell\",method:\"POST\",data:t})}function f(t){return Object(n[\"a\"])({url:\"/admin/store/product/batch/offShell\",method:\"POST\",data:t})}function m(t){return Object(n[\"a\"])({url:\"/admin/store/product/batch/delete\",method:\"POST\",data:t})}function h(t){return Object(n[\"a\"])({url:\"/admin/store/product/update\",method:\"POST\",data:t})}},cc39:function(t,e,a){},e45a:function(t,e,a){\"use strict\";a(\"cc39\")}}]);", "extractedComments": []}