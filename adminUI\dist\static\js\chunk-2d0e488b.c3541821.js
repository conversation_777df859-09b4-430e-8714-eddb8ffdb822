(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e488b"],{9141:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"divBox"},[n("el-card",{staticClass:"box-card"},[n("upload-index",{attrs:{pictureType:e.pictureType}})],1)],1)},c=[],i=n("b5b8"),r={name:"index",data:function(){return{pictureType:"maintain"}},components:{UploadIndex:i["default"]}},s=r,u=n("2877"),d=Object(u["a"])(s,a,c,!1,null,"2e219c05",null);t["default"]=d.exports}}]);