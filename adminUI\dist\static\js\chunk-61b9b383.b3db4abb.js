(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-61b9b383"],{"06c3":function(e,t,i){},"0bad":function(e,t,i){},"21d2":function(e,t,i){"use strict";i.d(t,"a",(function(){return r})),i.d(t,"b",(function(){return l})),i.d(t,"c",(function(){return n})),i.d(t,"e",(function(){return o})),i.d(t,"f",(function(){return s})),i.d(t,"d",(function(){return u}));var a=i("b775");function r(){return Object(a["a"])({url:"/admin/store/retail/spread/manage/get",method:"get"})}function l(e){return Object(a["a"])({url:"/admin/store/retail/spread/manage/set",method:"post",data:e})}function n(e){return Object(a["a"])({url:"/admin/store/retail/list",method:"get",params:e})}function o(e,t){return Object(a["a"])({url:"/admin/store/retail/spread/userlist",method:"post",params:e,data:t})}function s(e,t){return Object(a["a"])({url:"/admin/store/retail/spread/orderlist",method:"post",params:e,data:t})}function u(e){return Object(a["a"])({url:"/admin/store/retail/spread/clean/".concat(e),method:"get"})}},"2f2c":function(e,t,i){"use strict";i.d(t,"b",(function(){return c})),i.d(t,"c",(function(){return d})),i.d(t,"r",(function(){return m})),i.d(t,"d",(function(){return p})),i.d(t,"a",(function(){return f})),i.d(t,"g",(function(){return h})),i.d(t,"h",(function(){return b})),i.d(t,"j",(function(){return v})),i.d(t,"i",(function(){return g})),i.d(t,"e",(function(){return y})),i.d(t,"o",(function(){return _})),i.d(t,"q",(function(){return w})),i.d(t,"l",(function(){return k})),i.d(t,"m",(function(){return F})),i.d(t,"n",(function(){return x})),i.d(t,"p",(function(){return C})),i.d(t,"k",(function(){return O})),i.d(t,"f",(function(){return P}));var a=i("b775");function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,a)}return i}function n(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?l(Object(i),!0).forEach((function(t){o(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function o(e,t,i){return(t=s(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function s(e){var t=u(e,"string");return"symbol"==r(t)?t:t+""}function u(e,t){if("object"!=r(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function c(e){return Object(a["a"])({url:"/admin/system/city/list",method:"get",params:n({},e)})}function d(){return Object(a["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(e){return Object(a["a"])({url:"/admin/system/city/update/status",method:"post",params:n({},e)})}function p(e){return Object(a["a"])({url:"/admin/system/city/update",method:"post",params:n({},e)})}function f(e){return Object(a["a"])({url:"/admin/system/city/info",method:"get",params:n({},e)})}function h(e){return Object(a["a"])({url:"/admin/express/list",method:"get",params:n({},e)})}function b(){return Object(a["a"])({url:"/admin/express/sync/express",method:"post"})}function v(e){return Object(a["a"])({url:"/admin/express/update/show",method:"post",data:e})}function g(e){return Object(a["a"])({url:"/admin/express/update",method:"post",data:e})}function y(e){return Object(a["a"])({url:"/admin/express/delete",method:"GET",params:n({},e)})}function _(e){return Object(a["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:n({},e)})}function w(e){return Object(a["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:n({},e)})}function k(e){return Object(a["a"])({url:"/admin/express/shipping/free/list",method:"get",params:n({},e)})}function F(e){return Object(a["a"])({url:"admin/express/shipping/region/list",method:"get",params:n({},e)})}function x(e){return Object(a["a"])({url:"admin/express/shipping/templates/save",method:"post",data:e})}function C(e,t){return Object(a["a"])({url:"admin/express/shipping/templates/update",method:"post",data:e,params:n({},t)})}function O(e){return Object(a["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:e})}function P(e){return Object(a["a"])({url:"admin/express/info",method:"get",params:n({},e)})}},"7e9a":function(e,t,i){"use strict";i("06c3")},b2bf:function(e,t,i){},b34e:function(e,t,i){"use strict";i("0bad")},b9c2:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox relative"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-tabs",{on:{"tab-click":function(t){return e.getList(1)}},model:{value:e.loginType,callback:function(t){e.loginType=t},expression:"loginType"}},e._l(e.headeNum,(function(e,t){return i("el-tab-pane",{key:t,attrs:{label:e.name,name:e.type.toString()}})})),1),e._v(" "),i("div",{staticClass:"container"},[i("el-form",{ref:"userFrom",attrs:{inline:"",size:"small",model:e.userFrom,"label-position":e.labelPosition,"label-width":"100px"}},[i("el-row",[i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"用户搜索："}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入姓名或手机号",clearable:""},model:{value:e.userFrom.keywords,callback:function(t){e.$set(e.userFrom,"keywords",t)},expression:"userFrom.keywords"}})],1)],1)],1),e._v(" "),e.collapse?[i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:e.$t("user.center.userLevel")+"："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:e.$t("common.pleaseSelect"),clearable:"",filterable:"",multiple:""},model:{value:e.levelData,callback:function(t){e.levelData=t},expression:"levelData"}},e._l(e.levelList,(function(t,a){return i("el-option",{key:a,attrs:{value:t.id,label:t.name}},[i("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),e._v(" "),i("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("\n                          "+e._s(e.getUpgradeTypeText(t.upgradeType))+"\n                          "),1===t.upgradeType?i("span",[e._v(" - Rp "+e._s(t.upgradePrice))]):e._e()])])})),1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"用户分组："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.groupData,callback:function(t){e.groupData=t},expression:"groupData"}},e._l(e.groupList,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.groupName}})})),1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"用户标签："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.labelData,callback:function(t){e.labelData=t},expression:"labelData"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1)],1)],1),e._v(" "),i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"国家："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},on:{"on-change":e.changeCountry},model:{value:e.userFrom.country,callback:function(t){e.$set(e.userFrom,"country",t)},expression:"userFrom.country"}},[i("el-option",{attrs:{value:"",label:"全部"}}),e._v(" "),i("el-option",{attrs:{value:"CN",label:"中国"}}),e._v(" "),i("el-option",{attrs:{value:"OTHER",label:"国外"}})],1)],1)],1),e._v(" "),"CN"===e.userFrom.country?i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"省份："}},[i("el-cascader",{staticClass:"selWidth",attrs:{options:e.addresData,props:e.propsCity,filterable:"",clearable:""},on:{change:e.handleChange},model:{value:e.address,callback:function(t){e.address=t},expression:"address"}})],1)],1):e._e(),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"消费情况："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},model:{value:e.userFrom.payCount,callback:function(t){e.$set(e.userFrom,"payCount",t)},expression:"userFrom.payCount"}},[i("el-option",{attrs:{value:"",label:"全部"}}),e._v(" "),i("el-option",{attrs:{value:"0",label:"0"}}),e._v(" "),i("el-option",{attrs:{value:"1",label:"1+"}}),e._v(" "),i("el-option",{attrs:{value:"2",label:"2+"}}),e._v(" "),i("el-option",{attrs:{value:"3",label:"3+"}}),e._v(" "),i("el-option",{attrs:{value:"4",label:"4+"}}),e._v(" "),i("el-option",{attrs:{value:"5",label:"5+"}})],1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{staticClass:"timeBox",attrs:{label:"时间选择："}},[i("el-date-picker",{staticClass:"selWidth",attrs:{align:"right","unlink-panels":"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间","picker-options":e.pickerOptions},on:{change:e.onchangeTime},model:{value:e.timeVal,callback:function(t){e.timeVal=t},expression:"timeVal"}})],1)],1)],1),e._v(" "),i("el-col",{attrs:{xs:24,sm:24,md:24,lg:18,xl:18}},[i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"访问情况："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},model:{value:e.userFrom.accessType,callback:function(t){e.$set(e.userFrom,"accessType",t)},expression:"userFrom.accessType"}},[i("el-option",{attrs:{value:0,label:"全部"}}),e._v(" "),i("el-option",{attrs:{value:1,label:"首次访问"}}),e._v(" "),i("el-option",{attrs:{value:2,label:"时间段访问过"}}),e._v(" "),i("el-option",{attrs:{value:3,label:"时间段未访问"}})],1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"性别："}},[i("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:e.userFrom.sex,callback:function(t){e.$set(e.userFrom,"sex",t)},expression:"userFrom.sex"}},[i("el-radio-button",{attrs:{label:""}},[i("span",[e._v("全部")])]),e._v(" "),i("el-radio-button",{attrs:{label:"0"}},[i("span",[e._v("未知")])]),e._v(" "),i("el-radio-button",{attrs:{label:"1"}},[i("span",[e._v("男")])]),e._v(" "),i("el-radio-button",{attrs:{label:"2"}},[i("span",[e._v("女")])]),e._v(" "),i("el-radio-button",{attrs:{label:"3"}},[i("span",[e._v("保密")])])],1)],1)],1),e._v(" "),i("el-col",e._b({},"el-col",e.grid,!1),[i("el-form-item",{attrs:{label:"身份："}},[i("el-radio-group",{staticClass:"selWidth",attrs:{type:"button"},model:{value:e.userFrom.isPromoter,callback:function(t){e.$set(e.userFrom,"isPromoter",t)},expression:"userFrom.isPromoter"}},[i("el-radio-button",{attrs:{label:""}},[i("span",[e._v("全部")])]),e._v(" "),i("el-radio-button",{attrs:{label:"1"}},[i("span",[e._v("推广员")])]),e._v(" "),i("el-radio-button",{attrs:{label:"0"}},[i("span",[e._v("普通用户")])])],1)],1)],1)],1)]:e._e(),e._v(" "),i("el-col",{staticClass:"text-right userFrom",attrs:{xs:24,sm:24,md:24,lg:6,xl:6}},[i("el-form-item",[i("el-button",{staticClass:"mr15",attrs:{type:"primary",icon:"ios-search",label:"default",size:"small"},on:{click:e.userSearchs}},[e._v("搜索")]),e._v(" "),i("el-button",{staticClass:"ResetSearch mr10",attrs:{size:"small"},on:{click:function(t){return e.reset("userFrom")}}},[e._v("重置")]),e._v(" "),i("a",{staticClass:"ivu-ml-8",on:{click:function(t){e.collapse=!e.collapse}}},[e.collapse?[e._v("\n                    收起 "),i("i",{staticClass:"el-icon-arrow-up"})]:[e._v("\n                    展开 "),i("i",{staticClass:"el-icon-arrow-down"})]],2)],1)],1)],2)],1)],1),e._v(" "),i("div",{staticClass:"btn_bt"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:coupon:user:receive"],expression:"['admin:coupon:user:receive']"}],staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:e.onSend}},[e._v("发送优惠券")]),e._v(" "),i("el-button",{staticClass:"mr10",attrs:{size:"small"},on:{click:function(t){return e.setBatch("group")}}},[e._v("批量设置分组")]),e._v(" "),i("el-button",{staticClass:"mr10",attrs:{size:"small"},on:{click:function(t){return e.setBatch("label")}}},[e._v("批量设置标签")])],1)],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.tableData.data,size:"mini","highlight-current-row":""},on:{"selection-change":e.onSelectTab}},[i("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form",{staticClass:"demo-table-expand",attrs:{"label-position":"left",inline:""}},[i("el-form-item",{attrs:{label:"身份："}},[i("span",[e._v(e._s(e._f("filterIsPromoter")(t.row.isPromoter)))])]),e._v(" "),i("el-form-item",{attrs:{label:"首次访问："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.createTime)))])]),e._v(" "),i("el-form-item",{attrs:{label:"近次访问："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.lastLoginTime)))])]),e._v(" "),i("el-form-item",{attrs:{label:"手机号："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]),e._v(" "),i("el-form-item",{attrs:{label:"标签："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.tagName)))])]),e._v(" "),i("el-form-item",{attrs:{label:"地址："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.addres)))])]),e._v(" "),i("el-form-item",{staticStyle:{width:"100%",display:"flex","margin-right":"10px"},attrs:{label:"备注："}},[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.mark)))])])],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),i("el-table-column",{attrs:{prop:"uid",label:"ID","min-width":"80"}}),e._v(" "),i("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"demo-image__preview"},[i("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"姓名","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.nickname))+" | "+e._s(e._f("sexFilter")(t.row.sex)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("user.center.userLevel"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("filterEmpty")(e._f("levelFilter")(t.row.level))))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"groupName",label:"分组","min-width":"100"}}),e._v(" "),i("el-table-column",{attrs:{prop:"spreadNickname",label:"推荐人","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{label:"手机号","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"nowMoney",label:"余额","min-width":"100"}}),e._v(" "),i("el-table-column",{attrs:{prop:"integral",label:"积分","min-width":"100"}}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"130",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:user:infobycondition"],expression:"['admin:user:infobycondition']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return e.editUser(t.row.uid)}}},[e._v("编辑")]),e._v(" "),i("el-dropdown",{attrs:{trigger:"click"}},[i("span",{staticClass:"el-dropdown-link"},[e._v("\n              更多"),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e._v(" "),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e.checkPermi(["admin:user:topdetail"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.onDetails(t.row.uid)}}},[e._v("账户详情")]):e._e(),e._v(" "),e.checkPermi(["admin:user:operate:founds"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.editPoint(t.row.uid)}}},[e._v("积分余额")]):e._e(),e._v(" "),e.checkPermi(["admin:user:group"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setBatch("group",t.row)}}},[e._v("设置分组")]):e._e(),e._v(" "),e.checkPermi(["admin:user:tag"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setBatch("label",t.row)}}},[e._v("设置标签")]):e._e(),e._v(" "),e.checkPermi(["admin:user:update:phone"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setPhone(t.row)}}},[e._v("修改手机号")]):e._e(),e._v(" "),e.checkPermi(["admin:user:update:level"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.onLevel(t.row.uid,t.row.level)}}},[e._v(e._s(e.$t("user.center.userLevel")))]):e._e(),e._v(" "),e.checkPermi(["admin:user:update:spread"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.setExtension(t.row)}}},[e._v("修改上级推广人")]):e._e(),e._v(" "),t.row.spreadUid&&t.row.spreadUid>0&&e.checkPermi(["admin:retail:spread:clean"])?i("el-dropdown-item",{nativeOn:{click:function(i){return e.clearSpread(t.row)}}},[e._v("清除上级推广人")]):e._e()],1)],1)]}}])})],1),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[15,30,45,60],"page-size":e.userFrom.limit,"current-page":e.userFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"修改推广人",visible:e.extensionVisible,width:"500px","before-close":e.handleCloseExtension},on:{"update:visible":function(t){e.extensionVisible=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"formExtension",staticClass:"formExtension mt20",attrs:{model:e.formExtension,rules:e.ruleInline,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",{attrs:{label:"用户头像：",prop:"image"}},[i("div",{staticClass:"upLoadPicBox",on:{click:e.modalPicTap}},[e.formExtension.image?i("div",{staticClass:"pictrue"},[i("img",{attrs:{src:e.formExtension.image}})]):i("div",{staticClass:"upLoad"},[i("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onSubExtension("formExtension")}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"用户列表",visible:e.userVisible,width:"900px"},on:{"update:visible":function(t){e.userVisible=t}}},[e.userVisible?i("user-list",{on:{getTemplateRow:e.getTemplateRow}}):e._e(),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.userVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.userVisible=!1}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"设置",visible:e.dialogVisible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"dynamicValidateForm",staticClass:"demo-dynamic",attrs:{model:e.dynamicValidateForm,"label-width":"100px"}},["group"===e.batchName?i("el-form-item",{key:"1",attrs:{prop:"groupId",label:"用户分组",rules:[{required:!0,message:"请选择用户分组",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择分组",filterable:""},model:{value:e.dynamicValidateForm.groupId,callback:function(t){e.$set(e.dynamicValidateForm,"groupId",t)},expression:"dynamicValidateForm.groupId"}},e._l(e.groupList,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.groupName}})})),1)],1):i("el-form-item",{attrs:{prop:"groupId",label:"用户标签",rules:[{required:!0,message:"请选择用户标签",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择标签",filterable:""},model:{value:e.dynamicValidateForm.groupId,callback:function(t){e.$set(e.dynamicValidateForm,"groupId",t)},expression:"dynamicValidateForm.groupId"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("dynamicValidateForm")}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"编辑",visible:e.visible,width:"600px"},on:{"update:visible":function(t){e.visible=t}}},[e.visible?i("edit-from",{attrs:{uid:e.uid},on:{resetForm:e.resetForm}}):e._e()],1),e._v(" "),i("el-dialog",{attrs:{title:"积分余额",visible:e.VisiblePoint,width:"500px","close-on-click-modal":!1,"before-close":e.handlePointClose},on:{"update:visible":function(t){e.VisiblePoint=t}}},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingPoint,expression:"loadingPoint"}],ref:"PointValidateForm",staticClass:"demo-dynamic",attrs:{model:e.PointValidateForm,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"修改余额",required:""}},[i("el-radio-group",{model:{value:e.PointValidateForm.moneyType,callback:function(t){e.$set(e.PointValidateForm,"moneyType",t)},expression:"PointValidateForm.moneyType"}},[i("el-radio",{attrs:{label:1}},[e._v("增加")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("减少")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"余额",required:""}},[i("el-input-number",{attrs:{type:"text",precision:2,step:.1,min:0,max:999999},model:{value:e.PointValidateForm.moneyValue,callback:function(t){e.$set(e.PointValidateForm,"moneyValue",t)},expression:"PointValidateForm.moneyValue"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"修改积分",required:""}},[i("el-radio-group",{model:{value:e.PointValidateForm.integralType,callback:function(t){e.$set(e.PointValidateForm,"integralType",t)},expression:"PointValidateForm.integralType"}},[i("el-radio",{attrs:{label:1}},[e._v("增加")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("减少")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"积分",required:""}},[i("el-input-number",{attrs:{type:"text","step-strictly":"",min:0,max:999999},model:{value:e.PointValidateForm.integralValue,callback:function(t){e.$set(e.PointValidateForm,"integralValue",t)},expression:"PointValidateForm.integralValue"}})],1)],1),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.handlePointClose}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",loading:e.loadingBtn},on:{click:function(t){return e.submitPointForm("PointValidateForm")}}},[e._v("确 定")])],1)],1),e._v(" "),e.uid?i("el-dialog",{attrs:{title:"用户详情",visible:e.Visible,width:"1100px","before-close":e.Close},on:{"update:visible":function(t){e.Visible=t}}},[e.Visible?i("user-details",{ref:"userDetails",attrs:{uid:e.uid}}):e._e()],1):e._e(),e._v(" "),i("el-dialog",{attrs:{title:"设置",visible:e.levelVisible,width:"600px","before-close":e.Close},on:{"update:visible":function(t){e.levelVisible=t}}},[i("level-edit",{attrs:{levelInfo:e.levelInfo,levelList:e.levelList}})],1)],1)},r=[],l=i("c24f"),n=i("21d2"),o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"用户编号："}},[i("el-input",{staticClass:"selWidth",attrs:{disabled:""},model:{value:e.ruleForm.id,callback:function(t){e.$set(e.ruleForm,"id",t)},expression:"ruleForm.id"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户地址："}},[i("el-input",{staticClass:"selWidth",model:{value:e.ruleForm.addres,callback:function(t){e.$set(e.ruleForm,"addres",t)},expression:"ruleForm.addres"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户备注："}},[i("el-input",{staticClass:"selWidth",attrs:{type:"textarea"},model:{value:e.ruleForm.mark,callback:function(t){e.$set(e.ruleForm,"mark",t)},expression:"ruleForm.mark"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户分组："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.ruleForm.groupId,callback:function(t){e.$set(e.ruleForm,"groupId",t)},expression:"ruleForm.groupId"}},e._l(e.groupList,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.groupName}})})),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"用户标签："}},[i("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.labelData,callback:function(t){e.labelData=t},expression:"labelData"}},e._l(e.labelLists,(function(e,t){return i("el-option",{key:t,attrs:{value:e.id,label:e.name}})})),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"推广员"}},[i("el-radio-group",{model:{value:e.ruleForm.isPromoter,callback:function(t){e.$set(e.ruleForm,"isPromoter",t)},expression:"ruleForm.isPromoter"}},[i("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),i("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-radio-group",{model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[i("el-radio",{attrs:{label:!0}},[e._v("开启")]),e._v(" "),i("el-radio",{attrs:{label:!1}},[e._v("关闭")])],1)],1),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:user:update"],expression:"['admin:user:update']"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("提交")]),e._v(" "),i("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("取消")])],1)],1)},s=[],u=i("61f7");function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function l(i,a,r,l){var s=a&&a.prototype instanceof o?a:o,u=Object.create(s.prototype);return d(u,"_invoke",function(i,a,r){var l,o,s,u=0,c=r||[],d=!1,m={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,i){return l=t,o=0,s=e,m.n=i,n}};function p(i,a){for(o=i,s=a,t=0;!d&&u&&!r&&t<c.length;t++){var r,l=c[t],p=m.p,f=l[2];i>3?(r=f===a)&&(s=l[(o=l[4])?5:(o=3,3)],l[4]=l[5]=e):l[0]<=p&&((r=i<2&&p<l[1])?(o=0,m.v=a,m.n=l[1]):p<f&&(r=i<3||l[0]>a||a>f)&&(l[4]=i,l[5]=a,m.n=f,o=0))}if(r||i>1)return n;throw d=!0,a}return function(r,c,f){if(u>1)throw TypeError("Generator is already running");for(d&&1===c&&p(c,f),o=c,s=f;(t=o<2?e:s)||!d;){l||(o?o<3?(o>1&&(m.n=-1),p(o,s)):m.n=s:m.v=s);try{if(u=2,l){if(o||(r="next"),t=l[r]){if(!(t=t.call(l,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,o<2&&(o=0)}else 1===o&&(t=l.return)&&t.call(l),o<2&&(s=TypeError("The iterator does not provide a '"+r+"' method"),o=1);l=e}else if((t=(d=m.n<0)?s:i.call(a,m))!==n)break}catch(t){l=e,o=1,s=t}finally{u=1}}return{value:t,done:d}}}(i,r,l),!0),u}var n={};function o(){}function s(){}function u(){}t=Object.getPrototypeOf;var m=[][a]?t(t([][a]())):(d(t={},a,(function(){return this})),t),p=u.prototype=o.prototype=Object.create(m);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,d(e,r,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=u,d(p,"constructor",u),d(u,"constructor",s),s.displayName="GeneratorFunction",d(u,r,"GeneratorFunction"),d(p),d(p,r,"Generator"),d(p,a,(function(){return this})),d(p,"toString",(function(){return"[object Generator]"})),(c=function(){return{w:l,m:f}})()}function d(e,t,i,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}d=function(e,t,i,a){function l(t,i){d(e,t,(function(e){return this._invoke(t,i,e)}))}t?r?r(e,t,{value:i,enumerable:!a,configurable:!a,writable:!a}):e[t]=i:(l("next",0),l("throw",1),l("return",2))},d(e,t,i,a)}function m(e,t,i,a,r,l,n){try{var o=e[l](n),s=o.value}catch(e){return void i(e)}o.done?t(s):Promise.resolve(s).then(a,r)}function p(e){return function(){var t=this,i=arguments;return new Promise((function(a,r){var l=e.apply(t,i);function n(e){m(l,a,r,n,o,"next",e)}function o(e){m(l,a,r,n,o,"throw",e)}n(void 0)}))}}var f={id:null,mark:"",addres:"",groupId:"",level:"",isPromoter:!1,status:!1},h={name:"UserEdit",props:{uid:{type:Number,default:null}},data:function(){return{ruleForm:Object.assign({},f),groupData:[],labelData:[],labelLists:[],levelList:[],groupList:[],rules:{}}},mounted:function(){this.uid&&this.userInfo(),this.groupLists(),this.levelLists(),this.getTagList()},methods:{userInfo:function(){var e=this;Object(l["B"])({id:this.uid}).then(function(){var t=p(c().m((function t(i){return c().w((function(t){while(1)switch(t.n){case 0:e.ruleForm={id:i.uid,mark:i.mark,status:i.status,addres:i.addres,groupId:Number(i.groupId)||"",level:i.level||"",isPromoter:i.isPromoter,tagId:i.tagId||""},e.labelData=i.tagId?i.tagId.split(",").map(Number):[];case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},groupLists:function(){var e=this;Object(l["g"])({page:1,limit:9999}).then(function(){var t=p(c().m((function t(i){return c().w((function(t){while(1)switch(t.n){case 0:e.groupList=i.list;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},getTagList:function(){var e=this;Object(l["u"])({page:1,limit:9999}).then((function(t){e.labelLists=t.list}))},levelLists:function(){var e=this;Object(l["n"])().then(function(){var t=p(c().m((function t(i){return c().w((function(t){while(1)switch(t.n){case 0:e.levelList=i.list;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},submitForm:Object(u["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.ruleForm.tagId=t.labelData.join(","),Object(l["E"])({id:t.ruleForm.id},t.ruleForm).then(function(){var e=p(c().m((function e(i){return c().w((function(e){while(1)switch(e.n){case 0:t.$message.success("编辑成功"),t.$parent.$parent.visible=!1,t.$parent.$parent.getList();case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())}))})),resetForm:function(e){this.$refs[e].resetFields(),this.$emit("resetForm")}}},b=h,v=(i("f076"),i("2877")),g=Object(v["a"])(b,o,s,!1,null,"48a67e34",null),y=g.exports,_=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.psInfo?i("div",{staticClass:"acea-row row-middle border_bottom pb-24"},[i("div",{staticClass:"avatar mr20"},[i("img",{attrs:{src:e.psInfo.user.avatar}})]),e._v(" "),i("div",{staticClass:"dashboard-workplace-header-tip"},[i("p",{staticClass:"dashboard-workplace-header-tip-title",domProps:{textContent:e._s(e.psInfo.user.nickname||"-")}}),e._v(" "),i("div",{staticClass:"dashboard-workplace-header-tip-desc"},[i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[e._v(e._s(e.$t("userDetails.balance"))+": "+e._s(e.psInfo.balance))]),e._v(" "),i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[e._v(e._s(e.$t("userDetails.allOrderCount"))+": "+e._s(e.psInfo.allOrderCount))]),e._v(" "),i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp pb-1"},[e._v(e._s(e.$t("userDetails.allConsumeCount"))+": "+e._s(e.psInfo.allConsumeCount))]),e._v(" "),i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[e._v(e._s(e.$t("userDetails.integralCount"))+": "+e._s(e.psInfo.integralCount))]),e._v(" "),i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[e._v(e._s(e.$t("userDetails.mothOrderCount"))+": "+e._s(e.psInfo.mothOrderCount))]),e._v(" "),i("span",{staticClass:"dashboard-workplace-header-tip-desc-sp"},[e._v(e._s(e.$t("userDetails.mothConsumeCount"))+": "+e._s(e.psInfo.mothConsumeCount))])])])]):e._e(),e._v(" "),i("el-row",{staticClass:"ivu-mt mt20",attrs:{align:"middle",gutter:10}},[i("el-col",{attrs:{span:4}},[i("el-menu",{staticClass:"el-menu-vertical-demo",attrs:{"default-active":"0"},on:{select:e.changeType}},e._l(e.list,(function(t,a){return i("el-menu-item",{key:a,attrs:{name:t.val,index:t.val}},[i("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(t.label))])])})),1)],1),e._v(" "),i("el-col",{attrs:{span:20}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"tabNumWidth",attrs:{data:e.tableData.data,"max-height":"400"}},e._l(e.columns,(function(e,t){return i("el-table-column",{key:t,attrs:{prop:e.key,label:e.title,width:"item.minWidth","show-overflow-tooltip":!0}})})),1),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[6,12,18,24],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1)],1)],1)},w=[],k=i("b7be"),F={name:"UserDetails",props:{uid:{type:Number,default:null}},data:function(){return{loading:!1,columns:[],Visible:!1,list:[{val:"0",label:"userDetails.consumeRecord"},{val:"1",label:"userDetails.integralDetail"},{val:"2",label:"userDetails.signInRecord"},{val:"3",label:"userDetails.coupons"},{val:"4",label:"userDetails.balanceChange"},{val:"5",label:"userDetails.friendRelation"}],tableData:{data:[],total:0},tableFrom:{page:1,limit:6,type:"0",userId:""},psInfo:null}},mounted:function(){this.uid&&(this.getHeader(),this.getInfo())},methods:{changeType:function(e){this.tableFrom.type=e,"1"===e?this.integral():this.getInfo()},integral:function(){var e=this;this.loading=!0,Object(k["z"])({limit:this.tableFrom.limit,page:this.tableFrom.page},{uid:this.uid}).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.columns=[{title:"来源/用途",key:"title",minWidth:120},{title:"积分变化",key:"integral",minWidth:120},{title:"变化后积分",key:"balance",minWidth:120},{title:"日期",key:"updateTime",minWidth:120},{title:"备注",key:"mark",minWidth:120}],e.loading=!1})).catch((function(t){e.loading=!1}))},getInfo:function(){var e=this;this.tableFrom.userId=this.uid,this.loading=!0,Object(l["k"])(this.tableFrom).then((function(t){switch(e.tableData.data=t.list,e.tableData.total=t.total,e.tableFrom.type){case"0":e.columns=[{title:"订单ID",key:"orderId",minWidth:250},{title:"收货人",key:"realName",minWidth:90},{title:"商品数量",key:"totalNum",minWidth:80},{title:"商品总价",key:"totalPrice",minWidth:90},{title:"实付金额",key:"payPrice",minWidth:90},{title:"交易完成时间",key:"payTime",minWidth:160}];break;case"2":e.columns=[{title:"动作",key:"title",minWidth:120},{title:"获得积分",key:"number",minWidth:120},{title:"签到时间",key:"createTime",minWidth:120},{title:"备注",key:"title",minWidth:120}];break;case"3":e.columns=[{title:"优惠券名称",key:"name",minWidth:120},{title:"面值",key:"money",minWidth:120},{title:"有效期",key:"endTime",minWidth:120},{title:"最低消费额",key:"minPrice",minWidth:120},{title:"兑换时间",key:"updateTime",minWidth:120}];break;case"4":e.columns=[{title:"变动金额",key:"number",minWidth:120},{title:"变动后",key:"balance",minWidth:120},{title:"类型",key:"title",minWidth:120},{title:"创建时间",key:"add_time",minWidth:120},{title:"备注",key:"mark",minWidth:120}];break;default:e.columns=[{title:"ID",key:"uid",minWidth:120},{title:"昵称",key:"nickname",minWidth:120},{title:"等级",key:"level",minWidth:120},{title:"加入时间",key:"createTime",minWidth:120}]}e.loading=!1})).catch((function(){e.loading=!1}))},pageChange:function(e){this.tableFrom.page=e,"1"===this.tableFrom.type?this.integral():this.getInfo()},handleSizeChange:function(e){this.tableFrom.limit=e,"1"===this.tableFrom.type?this.integral():this.getInfo()},getHeader:function(){var e=this;Object(l["y"])({userId:this.uid}).then((function(t){e.psInfo=t}))}}},x=F,C=(i("7e9a"),Object(v["a"])(x,_,w,!1,null,"3685c7c7",null)),O=C.exports,P=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,"label-width":"100px"}},[i("el-form-item",[i("el-alert",{attrs:{title:e.$t("user.levelUpgrade.changeWarning"),type:"warning"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("user.center.userLevel"),"label-width":"100px"}},[i("el-select",{attrs:{clearable:"",placeholder:e.$t("common.pleaseSelect")},on:{change:e.currentSel},model:{value:e.ruleForm.levelId,callback:function(t){e.$set(e.ruleForm,"levelId",t)},expression:"ruleForm.levelId"}},e._l(e.levelList,(function(t){return i("el-option",{key:t.grade,attrs:{label:t.name,value:t.id}},[i("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),e._v(" "),i("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("\n          "+e._s(e.getUpgradeTypeText(t.upgradeType))+"\n          "),1===t.upgradeType?i("span",[e._v(" - Rp "+e._s(t.upgradePrice))]):e._e()])])})),1)],1),e._v(" "),""!=e.grade&&e.grade<e.levelInfo.gradeLevel?i("el-form-item",{attrs:{label:e.$t("user.levelUpgrade.deductExperience"),"label-width":"100px"}},[i("el-switch",{model:{value:e.ruleForm.isSub,callback:function(t){e.$set(e.ruleForm,"isSub",t)},expression:"ruleForm.isSub"}})],1):e._e(),e._v(" "),i("el-form-item",[i("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v(e._s(e.$t("common.cancel")))]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v(e._s(e.$t("common.confirm")))])],1)],1)},S=[],j={props:{levelInfo:{type:Object,default:{}},levelList:{type:Array,default:[]}},data:function(){return{grade:"",levelStatus:!1,ruleForm:{isSub:!1,levelId:"",uid:this.levelInfo.uid}}},created:function(){this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):""},watch:{levelInfo:function(e){this.ruleForm.uid=e.uid||0,this.ruleForm.levelId=this.levelInfo.level?Number(this.levelInfo.level):e.levelId}},methods:{submitForm:Object(u["a"])((function(e){var t=this;this.$refs[e].validate((function(i){if(!i)return!1;Object(l["C"])(t.ruleForm).then((function(i){t.$message.success("编辑成功"),t.$parent.$parent.getList(),t.$parent.$parent.levelVisible=!1,t.$refs[e].resetFields(),t.grade=""}))}))})),currentSel:function(){var e=this;this.levelList.forEach((function(t){t.id==e.ruleForm.levelId&&(e.grade=t.grade)}))},resetForm:function(e){var t=this;this.$nextTick((function(){t.$refs[e].resetFields(),t.grade=""})),this.$parent.$parent.levelVisible=!1},getUpgradeTypeText:function(e){return this.$t("user.grade.upgradeTypes.".concat(e))||this.$t("common.unknown")}}},I=j,$=Object(v["a"])(I,P,S,!1,null,null,null),V=$.exports,D=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("el-form",{attrs:{inline:""}},[i("el-form-item",[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户名称"},model:{value:e.tableFrom.keywords,callback:function(t){e.$set(e.tableFrom,"keywords",t)},expression:"tableFrom.keywords"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1)],1)],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData.data,width:"800px",size:"small"}},[i("el-table-column",{attrs:{label:"",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-radio",{attrs:{label:t.row.uid},nativeOn:{change:function(i){return e.getTemplateRow(t.$index,t.row)}},model:{value:e.templateRadio,callback:function(t){e.templateRadio=t},expression:"templateRadio"}},[e._v(" ")])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"uid",label:"ID","min-width":"60"}}),e._v(" "),i("el-table-column",{attrs:{prop:"nickname",label:"微信用户名称","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("div",{staticClass:"demo-image__preview"},[i("el-image",{staticClass:"tabImage",attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"性别","min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("saxFilter")(t.row.sex)))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"地区","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.addres))])]}}])})],1),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":e.tableFrom.limit,"current-page":e.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.pageChange}})],1)],1)],1)},L=[],T={name:"UserList",filters:{saxFilter:function(e){var t={0:"未知",1:"男",2:"女"};return t[e]},statusFilter:function(e){var t={wechat:"微信用户",routine:"小程序用户"};return t[e]}},data:function(){return{templateRadio:0,loading:!1,tableData:{data:[],total:0},tableFrom:{page:1,limit:10,keywords:""}}},mounted:function(){this.getList()},methods:{getTemplateRow:function(e,t){this.$emit("getTemplateRow",t)},getList:function(){var e=this;this.loading=!0,Object(l["D"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},search:function(){var e=this;this.loading=!0,Object(l["D"])({keywords:this.tableFrom.keywords}).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},pageChange:function(e){this.tableFrom.page=e,this.getList()},handleSizeChange:function(e){this.tableFrom.limit=e,this.getList()}}},E=T,W=Object(v["a"])(E,D,L,!1,null,"44b1ead6",null),N=W.exports,z=i("2f2c"),R=(i("a78e"),i("e350"));function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function B(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function l(i,a,r,l){var s=a&&a.prototype instanceof o?a:o,u=Object.create(s.prototype);return U(u,"_invoke",function(i,a,r){var l,o,s,u=0,c=r||[],d=!1,m={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,i){return l=t,o=0,s=e,m.n=i,n}};function p(i,a){for(o=i,s=a,t=0;!d&&u&&!r&&t<c.length;t++){var r,l=c[t],p=m.p,f=l[2];i>3?(r=f===a)&&(s=l[(o=l[4])?5:(o=3,3)],l[4]=l[5]=e):l[0]<=p&&((r=i<2&&p<l[1])?(o=0,m.v=a,m.n=l[1]):p<f&&(r=i<3||l[0]>a||a>f)&&(l[4]=i,l[5]=a,m.n=f,o=0))}if(r||i>1)return n;throw d=!0,a}return function(r,c,f){if(u>1)throw TypeError("Generator is already running");for(d&&1===c&&p(c,f),o=c,s=f;(t=o<2?e:s)||!d;){l||(o?o<3?(o>1&&(m.n=-1),p(o,s)):m.n=s:m.v=s);try{if(u=2,l){if(o||(r="next"),t=l[r]){if(!(t=t.call(l,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,o<2&&(o=0)}else 1===o&&(t=l.return)&&t.call(l),o<2&&(s=TypeError("The iterator does not provide a '"+r+"' method"),o=1);l=e}else if((t=(d=m.n<0)?s:i.call(a,m))!==n)break}catch(t){l=e,o=1,s=t}finally{u=1}}return{value:t,done:d}}}(i,r,l),!0),u}var n={};function o(){}function s(){}function u(){}t=Object.getPrototypeOf;var c=[][a]?t(t([][a]())):(U(t={},a,(function(){return this})),t),d=u.prototype=o.prototype=Object.create(c);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,U(e,r,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=u,U(d,"constructor",u),U(u,"constructor",s),s.displayName="GeneratorFunction",U(u,r,"GeneratorFunction"),U(d),U(d,r,"Generator"),U(d,a,(function(){return this})),U(d,"toString",(function(){return"[object Generator]"})),(B=function(){return{w:l,m:m}})()}function U(e,t,i,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}U=function(e,t,i,a){function l(t,i){U(e,t,(function(e){return this._invoke(t,i,e)}))}t?r?r(e,t,{value:i,enumerable:!a,configurable:!a,writable:!a}):e[t]=i:(l("next",0),l("throw",1),l("return",2))},U(e,t,i,a)}function G(e,t,i,a,r,l,n){try{var o=e[l](n),s=o.value}catch(e){return void i(e)}o.done?t(s):Promise.resolve(s).then(a,r)}function q(e){return function(){var t=this,i=arguments;return new Promise((function(a,r){var l=e.apply(t,i);function n(e){G(l,a,r,n,o,"next",e)}function o(e){G(l,a,r,n,o,"throw",e)}n(void 0)}))}}function M(e,t,i){return(t=H(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function H(e){var t=J(e,"string");return"symbol"==A(t)?t:t+""}function J(e,t){if("object"!=A(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!=A(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var K={name:"UserIndex",components:{editFrom:y,userDetails:O,userList:N,levelEdit:V},filters:{sexFilter:function(e){var t={0:"未知",1:"男",2:"女",3:"保密"};return t[e]}},data:function(){return M({formExtension:{image:"",spreadUid:"",userId:""},ruleInline:{},extensionVisible:!1,userVisible:!1,levelInfo:"",pickerOptions:this.$timeOptions,loadingBtn:!1,PointValidateForm:{integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""},loadingPoint:!1,VisiblePoint:!1,visible:!1,userIds:"",dialogVisible:!1,levelVisible:!1,levelData:[],groupData:[],labelData:[],selData:[],labelPosition:"right",collapse:!1,props:{children:"child",label:"name",value:"name",emitPath:!1},propsCity:{children:"child",label:"name",value:"name"},headeNum:[{type:"",name:"全部用户"},{type:"wechat",name:"微信公众号用户"},{type:"routine",name:"微信小程序用户"},{type:"h5",name:"H5用户"}],listLoading:!0,tableData:{data:[],total:0},loginType:"",userFrom:{labelId:"",userType:"",sex:"",isPromoter:"",country:"",payCount:"",accessType:0,dateLimit:"",keywords:"",province:"",city:"",page:1,limit:15,level:"",groupId:""},grid:{xl:8,lg:12,md:12,sm:24,xs:24},levelList:[],labelLists:[],groupList:[],selectedData:[],timeVal:[],addresData:[],dynamicValidateForm:{groupId:[]},loading:!1,groupIdFrom:[],selectionList:[],batchName:"",uid:0,Visible:!1,keyNum:0,address:[],multipleSelectionAll:[],idKey:"uid"},"uid","")},activated:function(){this.userFrom.keywords="",this.loginType="0",this.getList(1)},mounted:function(){this.getList(),this.groupLists(),this.levelLists(),this.getTagList(),this.getCityList()},methods:{checkPermi:R["a"],setPhone:function(e){var t=this;this.$prompt("修改手机号",{confirmButtonText:"确定",cancelButtonText:"取消",inputErrorMessage:"请输入修改手机号",inputType:"text",inputValue:e.phone,inputPlaceholder:"请输入手机号",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"请填写手机号"}}).then((function(i){var a=i.value;Object(l["z"])({id:e.uid,phone:a}).then((function(){t.$message.success("编辑成功"),t.getList()}))})).catch((function(){t.$message.info("取消输入")}))},clearSpread:function(e){var t=this;this.$modalSure("解除【"+e.nickname+"】的上级推广人吗").then((function(){Object(n["d"])(e.uid).then((function(e){t.$message.success("清除成功"),t.getList()}))}))},onSubExtension:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(l["A"])(t.formExtension).then((function(e){t.$message.success("设置成功"),t.extensionVisible=!1,t.getList()}))}))},getTemplateRow:function(e){this.formExtension.image=e.avatar,this.formExtension.spreadUid=e.uid},setExtension:function(e){this.formExtension={image:"",spreadUid:"",userId:e.uid},this.extensionVisible=!0},handleCloseExtension:function(){this.extensionVisible=!1},modalPicTap:function(){this.userVisible=!0},resetForm:function(){this.visible=!1},reset:function(e){this.userFrom={labelId:"",userType:"",sex:"",isPromoter:"",country:"",payCount:"",accessType:0,dateLimit:"",keywords:"",province:"",city:"",page:1,limit:15,level:"",groupId:""},this.levelData=[],this.groupData=[],this.labelData=[],this.timeVal=[],this.getList()},getCityList:function(){var e=q(B().m((function e(){var t;return B().w((function(e){while(1)switch(e.n){case 0:return e.n=1,z["c"]();case 1:t=e.v,this.addresData=t;case 2:return e.a(2)}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),sendNews:function(){if(0===this.selectionList.length)return this.$message.warning("请先选择用户");this.$modalArticle((function(e){}),"send")},onSend:function(){if(0===this.selectionList.length)return this.$message.warning("请选择要设置的用户");var e=this;this.$modalCoupon("send",this.keyNum+=1,[],(function(t){e.formValidate.give_coupon_ids=[],e.couponData=[],t.map((function(t){e.formValidate.give_coupon_ids.push(t.coupon_id),e.couponData.push(t.title)})),e.selectionList=[]}),this.userIds,"user")},Close:function(){this.Visible=!1,this.levelVisible=!1},onDetails:function(e){this.uid=e,this.Visible=!0},onLevel:function(e,t){var i=new Object;this.levelList.forEach((function(e){e.id==t&&(i.gradeLevel=e.grade)})),i.uid=e,i.level=t,this.levelInfo=i,this.levelVisible=!0},editPoint:function(e){this.uid=e,this.VisiblePoint=!0},submitPointForm:Object(u["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.PointValidateForm.uid=t.uid,t.loadingBtn=!0,Object(l["c"])(t.PointValidateForm).then((function(e){t.$message.success("设置成功"),t.loadingBtn=!1,t.handlePointClose(),t.getList()})).catch((function(){t.loadingBtn=!1}))}))})),handlePointClose:function(){this.VisiblePoint=!1,this.PointValidateForm={integralType:2,integralValue:0,moneyType:2,moneyValue:0,uid:""}},editUser:function(e){this.uid=e,this.visible=!0},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.loading=!0,"group"===t.batchName?Object(l["h"])({groupId:t.dynamicValidateForm.groupId,id:t.userIds}).then((function(e){t.$message.success("设置成功"),t.loading=!1,t.handleClose(),t.getList()})).catch((function(){t.loading=!1})):Object(l["v"])({tagId:t.dynamicValidateForm.groupId.join(","),id:t.userIds}).then((function(e){t.$message.success("设置成功"),t.loading=!1,t.handleClose(),t.getList()})).catch((function(){t.loading=!1}))}))},setBatch:function(e,t){if(this.batchName=e,t?(this.userIds=t.uid,"group"===this.batchName?this.dynamicValidateForm.groupId=t.groupId?Number(t.groupId):"":this.dynamicValidateForm.groupId=t.tagId?t.tagId.split(",").map(Number):[]):this.dynamicValidateForm.groupId="",0===this.multipleSelectionAll.length&&!t)return this.$message.warning("请选择要设置的用户");this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.$refs["dynamicValidateForm"].resetFields()},onSelectTab:function(e){var t=this;this.selectionList=e,setTimeout((function(){t.changePageCoreRecordData();var e=[];t.multipleSelectionAll.length&&(t.multipleSelectionAll.map((function(t){e.push(t.uid)})),t.userIds=e.join(","))}),50)},userSearchs:function(){this.userFrom.page=1,this.getList()},changeCountry:function(){"OTHER"!==this.userFrom.country&&this.userFrom.country||(this.selectedData=[],this.userFrom.province="",this.userFrom.city="",this.address=[])},handleChange:function(e){this.userFrom.province=e[0],this.userFrom.city=e[1]},onchangeTime:function(e){this.timeVal=e,this.userFrom.dateLimit=e?this.timeVal.join(","):""},groupLists:function(){var e=this;Object(l["g"])({page:1,limit:9999}).then(function(){var t=q(B().m((function t(i){return B().w((function(t){while(1)switch(t.n){case 0:e.groupList=i.list;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},getTagList:function(){var e=this;Object(l["u"])({page:1,limit:9999}).then((function(t){e.labelLists=t.list}))},levelLists:function(){var e=this;Object(l["n"])().then(function(){var t=q(B().m((function t(i){return B().w((function(t){while(1)switch(t.n){case 0:e.levelList=i,localStorage.setItem("levelKey",JSON.stringify(i));case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},getList:function(e){var t=this;this.listLoading=!0,this.userFrom.page=e||this.userFrom.page,this.userFrom.userType=this.loginType,0==this.loginType&&(this.userFrom.userType=""),this.userFrom.level=this.levelData.join(","),this.userFrom.groupId=this.groupData.join(","),this.userFrom.labelId=this.labelData.join(","),Object(l["D"])(this.userFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.$nextTick((function(){this.setSelectRow()})),t.listLoading=!1})).catch((function(){t.listLoading=!1})),this.checkedCities=this.$cache.local.has("user_stroge")?this.$cache.local.getJSON("user_stroge"):this.checkedCities},setSelectRow:function(){if(this.multipleSelectionAll&&!(this.multipleSelectionAll.length<=0)){var e=this.idKey,t=[];this.multipleSelectionAll.forEach((function(i){t.push(i[e])})),this.$refs.table.clearSelection();for(var i=0;i<this.tableData.data.length;i++)t.indexOf(this.tableData.data[i][e])>=0&&this.$refs.table.toggleRowSelection(this.tableData.data[i],!0)}},changePageCoreRecordData:function(){var e=this.idKey,t=this;if(this.multipleSelectionAll.length<=0)this.multipleSelectionAll=this.selectionList;else{var i=[];this.multipleSelectionAll.forEach((function(t){i.push(t[e])}));var a=[];this.selectionList.forEach((function(r){a.push(r[e]),i.indexOf(r[e])<0&&t.multipleSelectionAll.push(r)}));var r=[];this.tableData.data.forEach((function(t){a.indexOf(t[e])<0&&r.push(t[e])})),r.forEach((function(a){if(i.indexOf(a)>=0)for(var r=0;r<t.multipleSelectionAll.length;r++)if(t.multipleSelectionAll[r][e]==a){t.multipleSelectionAll.splice(r,1);break}}))}},pageChange:function(e){this.changePageCoreRecordData(),this.userFrom.page=e,this.getList()},handleSizeChange:function(e){this.changePageCoreRecordData(),this.userFrom.limit=e,this.getList()},handleDelete:function(e,t){var i=this;this.$modalSure().then((function(){productDeleteApi(e).then((function(){i.$message.success("删除成功"),i.getList()}))}))},onchangeIsShow:function(e){var t=this;e.isShow?putOnShellApi(e.id).then((function(){t.$message.success("上架成功"),t.getList()})).catch((function(){e.isShow=!e.isShow})):offShellApi(e.id).then((function(){t.$message.success("下架成功"),t.getList()})).catch((function(){e.isShow=!e.isShow}))},getUpgradeTypeText:function(e){return this.$t("user.grade.upgradeTypes.".concat(e))||this.$t("common.unknown")}}},Q=K,X=(i("b34e"),Object(v["a"])(Q,a,r,!1,null,"3807d465",null));t["default"]=X.exports},f076:function(e,t,i){"use strict";i("b2bf")}}]);