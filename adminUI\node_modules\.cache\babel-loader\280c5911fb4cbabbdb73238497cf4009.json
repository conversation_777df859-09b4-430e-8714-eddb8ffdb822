{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: \"WriteOff\",\n  props: {\n    iShidden: {\n      type: Boolean,\n      default: true\n    },\n    orderInfo: {\n      type: Object,\n      default: null\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  created: function created() {\n    import('@/assets/js/media_750');\n  },\n  methods: {\n    toDetail: function toDetail(item) {\n      this.$router.push({\n        path: \"/javaMobile/orderDetail/\" + item.orderId + \"/looks\"\n      });\n    },\n    cancel: function cancel() {\n      this.$emit(\"cancel\", true);\n    },\n    confirm: function confirm() {\n      this.$emit(\"confirm\", true);\n    }\n  }\n};", null]}