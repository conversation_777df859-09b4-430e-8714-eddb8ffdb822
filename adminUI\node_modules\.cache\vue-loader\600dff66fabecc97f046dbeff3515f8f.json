{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\search.vue?vue&type=template&id=7cb9dcfa&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\search.vue", "mtime": 1754388065807}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.searchFrom),callback:function ($$v) {_vm.searchFrom=$$v},expression:\"searchFrom\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('order.search.orderNo') + '：'}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('order.search.orderNo')},model:{value:(_vm.searchFrom.orderNo),callback:function ($$v) {_vm.$set(_vm.searchFrom, \"orderNo\", $$v)},expression:\"searchFrom.orderNo\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('order.search.productTitle') + '：'}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('order.search.productTitle')},model:{value:(_vm.searchFrom.productTitle),callback:function ($$v) {_vm.$set(_vm.searchFrom, \"productTitle\", $$v)},expression:\"searchFrom.productTitle\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('order.search.status') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all')},model:{value:(_vm.searchFrom.status),callback:function ($$v) {_vm.$set(_vm.searchFrom, \"status\", $$v)},expression:\"searchFrom.status\"}},_vm._l((_vm.statusList),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t('order.search.' + item.label),\"value\":item.value}})}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.getList(1)}}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"common.query\"))+\"\\n    \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":_vm.resetForm}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"common.reset\"))+\"\\n    \")])],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:financialCenter:request:upload']),expression:\"['admin:financialCenter:request:upload']\"}],attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t(\"order.search.exportExcel\"))+\"\\n      \")])],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"label\":_vm.$t('common.serialNumber'),\"type\":\"index\",\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.image'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.avatar,\"preview-src-list\":[scope.row.avatar]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.orderId'),\"min-width\":\"80\",\"prop\":\"id\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.nickname'),\"min-width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.realName)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.orderNo'),\"min-width\":\"80\",\"prop\":\"orderId\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.productName'),\"width\":\"120\",\"prop\":\"productName\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.payCount'),\"min-width\":\"80\",\"prop\":\"payCount\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.payCount)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.actualCommission'),\"width\":\"120\",\"prop\":\"price\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.payPrice'),\"width\":\"120\",\"prop\":\"totalPrice\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.commissionRate'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatRate(scope.row.commissionRate)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.estimatedCommission'),\"width\":\"140\",\"prop\":\"estimatedCommission\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.userCashBackRate'),\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatRate(scope.row.userCashBackRate)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.creatTime'),\"width\":\"120\",\"prop\":\"createTime\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.contentId'),\"min-width\":\"80\",\"prop\":\"contentId\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('order.search.statusLabel'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.$t((\"order.search.\" + (scope.row.statusCode)))))]}}])})],1),_vm._v(\" \"),_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.searchFrom.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.searchFrom.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.searchFrom.total},on:{\"size-change\":_vm.sizeChange,\"current-change\":_vm.pageChange}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}