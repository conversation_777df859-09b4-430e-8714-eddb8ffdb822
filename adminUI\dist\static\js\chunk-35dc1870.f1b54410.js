(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-35dc1870"],{"3e08":function(e,t,a){},"61c3":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"divBox relative"},[a("el-card",{staticClass:"box-card"},[a("el-tabs",{staticClass:"mb20",on:{"tab-click":function(t){return e.getList(e.tableFromType,1)}},model:{value:e.tableFromType,callback:function(t){e.tableFromType=t},expression:"tableFromType"}},[a("el-tab-pane",{attrs:{label:e.$t("financial.detail.purchaseDetail"),name:"purchase"}}),e._v(" "),a("el-tab-pane",{attrs:{label:e.$t("financial.detail.tradeDetail"),name:"trade"}})],1),e._v(" "),a("div",{staticClass:"container mt-1"},["purchase"===e.tableFromType?a("el-form",{attrs:{inline:"",size:"small"},model:{value:e.purchaseFrom,callback:function(t){e.purchaseFrom=t},expression:"purchaseFrom"}},[a("el-form-item",{attrs:{label:e.$t("financial.detail.rechargeType")+"："}},[a("el-select",{attrs:{placeholder:e.$t("common.all"),clearable:""},model:{value:e.purchaseFrom.rechargeType,callback:function(t){e.$set(e.purchaseFrom,"rechargeType",t)},expression:"purchaseFrom.rechargeType"}},e._l(e.rechargeTypeList,(function(t,l){return a("el-option",{key:l,attrs:{label:e.$t("financial.detail."+t.label),value:t.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("financial.detail.transactionTime")+"："}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{placeholder:e.$t("common.startDate"),clearable:"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","start-placeholder":e.$t("common.startDate"),"end-placeholder":e.$t("common.endDate")},model:{value:e.timeList,callback:function(t){e.timeList=t},expression:"timeList"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("financial.detail.paymentMethod")+"："}},[a("el-select",{attrs:{placeholder:e.$t("common.all"),clearable:""},model:{value:e.purchaseFrom.payChannel,callback:function(t){e.$set(e.purchaseFrom,"payChannel",t)},expression:"purchaseFrom.payChannel"}},[a("el-option",{attrs:{label:e.$t("financial.detail.bankTransfer"),value:"xendit"}}),e._v(" "),a("el-option",{attrs:{label:e.$t("financial.detail.electronicWallet"),value:"haipay"}})],1)],1),e._v(" "),"haipay"==e.purchaseFrom.payChannel?a("el-form-item",{attrs:{label:e.$t("financial.detail.electronicWallet")+"："}},[a("el-select",{attrs:{placeholder:e.$t("common.all"),clearable:""},model:{value:e.purchaseFrom.walletCode,callback:function(t){e.$set(e.purchaseFrom,"walletCode",t)},expression:"purchaseFrom.walletCode"}},e._l(e.walletList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e._v(" "),"xendit"==e.purchaseFrom.payChannel?a("el-form-item",{attrs:{label:e.$t("financial.detail.bankName")+"："}},[a("el-select",{attrs:{clearable:"",placeholder:e.$t("common.all")},model:{value:e.purchaseFrom.bankName,callback:function(t){e.$set(e.purchaseFrom,"bankName",t)},expression:"purchaseFrom.bankName"}},e._l(e.bankList,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:e}})})),1)],1):e._e()],1):a("el-form",{attrs:{inline:"",size:"small"},model:{value:e.tradeFrom,callback:function(t){e.tradeFrom=t},expression:"tradeFrom"}},[a("el-form-item",{attrs:{label:e.$t("financial.detail.tradeNo")+"："}},[a("el-input",{attrs:{size:"small",placeholder:e.$t("financial.detail.tradeNo")},model:{value:e.tradeFrom.linkId,callback:function(t){e.$set(e.tradeFrom,"linkId",t)},expression:"tradeFrom.linkId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("financial.detail.transactionTime")+"："}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end","start-placeholder":e.$t("common.startDate"),"end-placeholder":e.$t("common.endDate"),clearable:""},model:{value:e.timeList,callback:function(t){e.timeList=t},expression:"timeList"}})],1),e._v(" "),a("el-form-item",{attrs:{label:e.$t("financial.detail.tradeType")+"："}},[a("el-select",{attrs:{placeholder:e.$t("common.all"),clearable:""},model:{value:e.tradeFrom.type,callback:function(t){e.$set(e.tradeFrom,"type",t)},expression:"tradeFrom.type"}},[a("el-option",{attrs:{label:e.$t("financial.detail.agentFee"),value:"1"}}),e._v(" "),a("el-option",{attrs:{label:e.$t("financial.detail.partnerFee"),value:"2"}})],1)],1)],1)],1),e._v(" "),a("el-button",{staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.getList(e.tableFromType)}}},[e._v(e._s(e.$t("common.query")))]),e._v(" "),a("el-button",{staticClass:"mr10",attrs:{size:"small",type:""},on:{click:function(t){return e.handleReset()}}},[e._v(e._s(e.$t("common.reset")))])],1),e._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"12px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:financialCenter:detail:upload"],expression:"['admin:financialCenter:detail:upload']"}],attrs:{type:"primary",size:"small"},on:{click:e.handleUpload}},[e._v(e._s(e.$t("financial.detail.exportExcel")))])],1),e._v(" "),"purchase"===e.tableFromType?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.purchaseTableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"index",label:e.$t("common.serialNumber"),width:"110"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.paymentTime"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.payTime)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.paymentNo"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.outTradeNo)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.rechargeType"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.paymentAccount"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.tradeAmount"),"min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.actualPaymentAmount"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.paymentMethod"),"min-width":"100",prop:"payChannel"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.payChannel)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.electronicWallet"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.institutionNumber"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.bankName"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.paymentAccount"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.mobile"),"min-width":"100",prop:"phone"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.phone)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.payee"),"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.payeeAccount"),"min-width":"80"}})],1):e._e(),e._v(" "),"purchase"===e.tableFromType?a("el-pagination",{staticClass:"mt20",attrs:{"current-page":e.purchaseFrom.page,"page-sizes":[20,40,60,100],"page-size":e.purchaseFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.purchaseFrom.total},on:{"size-change":function(t){return e.sizeChange(t,"purchase")},"current-change":function(t){return e.pageChange(t,"purchase")}}}):e._e(),e._v(" "),"trade"===e.tableFromType?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tradeTableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{type:"index",label:e.$t("common.serialNumber"),width:"110"}}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.tradeNo"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.linkId)))]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.tradeType"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.title)))]}}],null,!1,3648579569)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.tradeAmount"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.number)))]}}],null,!1,2226103986)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.transactionTime"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.createTime)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.userNickname"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.nickName)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.tikTokAccount"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.tiktokAccount)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.whatsApp"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.whatsAppAccount)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.channel"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.channel)))]}}],null,!1,**********)}),e._v(" "),a("el-table-column",{attrs:{label:e.$t("financial.detail.orderNo"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("filterEmpty")(t.row.linkId)))]}}],null,!1,*********)})],1):e._e(),e._v(" "),"trade"===e.tableFromType?a("el-pagination",{staticClass:"mt20",attrs:{"current-page":e.tradeFrom.page,"page-sizes":[20,40,60,100],"page-size":e.tradeFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.tradeFrom.total},on:{"size-change":function(t){return e.sizeChange(t,"trade")},"current-change":function(t){return e.pageChange(t,"trade")}}}):e._e()],1)],1)},n=[],i=a("e7de"),r={name:"FinancialDetail",data:function(){return{loading:!1,purchaseTableData:[],tradeTableData:[],timeList:[],purchaseFrom:{uid:"",keyword:"",rechargeType:"",dateLimit:[],payChannel:"",walletCode:"",bankName:"",page:1,limit:20,total:0},tradeFrom:{keyword:"",dateLimit:[],linkId:"",type:"",page:1,limit:20,total:0},tableFromType:"purchase",rechargeTypeList:[{label:"agentFee",value:"agent"},{label:"partnerFee",value:"partner"}],walletList:[{label:"ShopeePay",value:"ShopeePay"},{label:"DANA",value:"DANA"},{label:"OVO",value:"OVO"},{label:"Gopay",value:"Gopay"}],bankList:[]}},created:function(){},mounted:function(){this.getList(this.tableFromType),this.getBankList()},methods:{getBankList:function(){var e=this;Object(i["g"])().then((function(t){e.bankList=t})).catch((function(){}))},getList:function(e,t){var a=this;this.loading=!0,"purchase"===e?(this.purchaseFrom.page=t||this.purchaseFrom.page,this.purchaseFrom.dateLimit=this.timeList.length?this.timeList.join(","):"",Object(i["o"])(this.purchaseFrom).then((function(e){a.purchaseTableData=e.list,a.purchaseFrom.total=e.total,a.loading=!1}))):(this.tradeFrom.page=t||this.tradeFrom.page,this.tradeFrom.dateLimit=this.timeList.length?this.timeList.join(","):"",Object(i["j"])(this.tradeFrom).then((function(e){a.tradeTableData=e.list,a.tradeFrom.total=e.total,a.loading=!1})))},pageChange:function(e,t){"purchase"===t?this.purchaseFrom.page=e:this.tradeFrom.page=e,this.getList(t)},sizeChange:function(e,t){"purchase"===t?this.purchaseFrom.limit=e:this.tradeFrom.limit=e,this.getList(t)},handleReset:function(){this.purchaseFrom={uid:"",keyword:"",rechargeType:"",dateLimit:[],payChannel:"",walletCode:"",bankName:"",page:1,limit:20,total:0},this.tradeFrom={keyword:"",dateLimit:[],linkId:"",type:"",page:1,limit:20,total:0}},handleUpload:function(){}}},o=r,c=(a("9eb6"),a("2877")),s=Object(c["a"])(o,l,n,!1,null,"44a9d692",null);t["default"]=s.exports},"9eb6":function(e,t,a){"use strict";a("3e08")},e7de:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"n",(function(){return c})),a.d(t,"e",(function(){return s})),a.d(t,"m",(function(){return u})),a.d(t,"l",(function(){return m})),a.d(t,"k",(function(){return d})),a.d(t,"f",(function(){return p})),a.d(t,"g",(function(){return f})),a.d(t,"h",(function(){return h})),a.d(t,"i",(function(){return b})),a.d(t,"p",(function(){return y})),a.d(t,"o",(function(){return _})),a.d(t,"j",(function(){return v}));var l=a("b775");function n(e){return Object(l["a"])({url:"/admin/finance/apply/list",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/admin/finance/apply/balance",method:"post",params:e})}function r(e){return Object(l["a"])({url:"/admin/finance/apply/update",method:"post",params:e})}function o(e,t){return Object(l["a"])({url:"/admin/finance/apply/apply",method:"post",params:e,data:t})}function c(e){return Object(l["a"])({url:"/admin/user/topUpLog/list",method:"get",params:e})}function s(){return Object(l["a"])({url:"/admin/user/topUpLog/balance",method:"post"})}function u(e){return Object(l["a"])({url:"/admin/user/topUpLog/delete",method:"get",params:e})}function m(e){return Object(l["a"])({url:"/admin/user/topUpLog/refund",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/admin/finance/founds/monitor/list",method:"get",params:e})}function p(e){return Object(l["a"])({url:"/admin/finance/founds/monitor/brokerage/record",method:"get",params:e})}function f(){return Object(l["a"])({url:"/admin/finance/apply/extract/bank",method:"get"})}function h(e){return Object(l["a"])({url:"/admin/finance/apply/apply",method:"post",params:e})}function b(e){return Object(l["a"])({url:"/admin/finance/apply/deal",method:"post",params:e})}function y(e,t){return Object(l["a"])({url:"/admin/upload/image",method:"POST",params:t,data:e})}function _(e){return Object(l["a"])({url:"/admin/user/topUpLog/list",method:"get",params:e})}function v(e){return Object(l["a"])({url:"/admin/finance/funds/monitor/list",method:"get",params:e})}}}]);