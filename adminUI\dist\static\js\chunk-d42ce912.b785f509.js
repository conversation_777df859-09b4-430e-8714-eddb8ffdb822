(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d42ce912"],{"3bf7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,i){return a("el-radio-button",{key:i,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(" "),a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"砍价状态："}},[a("el-select",{staticClass:"filter-item selWidth mr20",attrs:{placeholder:"请选择",clearable:""},on:{change:function(e){return t.getList(1)}},model:{value:t.tableFrom.status,callback:function(e){t.$set(t.tableFrom,"status",e)},expression:"tableFrom.status"}},[a("el-option",{attrs:{label:"进行中",value:1}}),t._v(" "),a("el-option",{attrs:{label:"未完成",value:2}}),t._v(" "),a("el-option",{attrs:{label:"已成功",value:3}})],1)],1)],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"发起用户",prop:"nickname","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"开启时间",prop:"addTime","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价商品",prop:"title","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{trigger:"hover",placement:"right","open-delay":800}},[a("div",{staticClass:"text_overflow",attrs:{slot:"reference"},slot:"reference"},[t._v(t._s(e.row.title))]),t._v(" "),a("div",{staticClass:"pup_card"},[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"最低价",prop:"bargainPriceMin","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"当前价",prop:"nowPrice","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"总砍价次数",prop:"peopleNum","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"剩余砍价次数","min-width":"100",prop:"num"}}),t._v(" "),a("el-table-column",{attrs:{prop:"dataTime",label:"结束时间","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价状态","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t._f("bargainColorFilter")(e.row.status)}},[t._v(t._s(t._f("bargainStatusFilter")(e.row.status)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(a){return t.handleLook(e.row.id)}}},[t._v("查看详情")])]}}])})],1),t._v(" "),a("div",{staticClass:"block mb20"},[a("el-pagination",{attrs:{"page-sizes":[10,20,30,40],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"查看详情",visible:t.dialogVisible,width:"650px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoadingPink,expression:"listLoadingPink"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.tableDataPink.data,size:"mini"}},[a("el-table-column",{attrs:{prop:"uid",label:"用户id","min-width":"50"}}),t._v(" "),a("el-table-column",{attrs:{label:"用户头像","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("div",{staticClass:"demo-image__preview"},[a("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.avatar,"preview-src-list":[t.row.avatar]}})],1)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"用户名称",prop:"nickname","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价金额",prop:"price","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"砍价时间",prop:"addTime","min-width":"180"}})],1)],1)],1)},l=[],s=a("b7be"),n={name:"index",data:function(){return{listLoadingPink:!1,dialogVisible:!1,tableDataPink:{data:[]},tableData:{data:[],total:0},listLoading:!1,tableFrom:{dateLimit:"",status:"",page:1,limit:20},fromList:this.$constants.fromList,timeVal:[]}},mounted:function(){this.getList()},methods:{handleClose:function(){this.dialogVisible=!1},handleLook:function(t){this.dialogVisible=!0,this.getPink(t)},getPink:function(t){var e=this;this.listLoadingPink=!0,Object(s["e"])(t).then((function(t){e.tableDataPink.data=t,e.listLoadingPink=!1})).catch((function(){e.listLoadingPink=!1}))},selectChange:function(t){this.tableFrom.dateLimit=t,this.tableFrom.page=1,this.timeVal=[],this.getList()},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList()},getList:function(t){var e=this;this.listLoading=!0,this.tableFrom.page=t||this.tableFrom.page,Object(s["d"])(this.tableFrom).then((function(t){e.tableData.data=t.list,e.tableData.total=t.total,e.listLoading=!1})).catch((function(){e.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},o=n,r=(a("f2c1"),a("2877")),c=Object(r["a"])(o,i,l,!1,null,"2308c02f",null);e["default"]=c.exports},b82d:function(t,e,a){},f2c1:function(t,e,a){"use strict";a("b82d")}}]);