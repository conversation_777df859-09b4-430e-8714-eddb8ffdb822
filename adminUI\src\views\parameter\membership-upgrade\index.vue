<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="$t('common.serialNumber')"  width="110"> </el-table-column>
        <el-table-column :label="$t('parameter.membershipFee.feeTemplateId')" min-width="50" prop="id">
        </el-table-column>
        <el-table-column :label="$t('parameter.membershipFee.agentFee')" min-width="100" prop="agent_fee">
        </el-table-column>
        <el-table-column :label="$t('parameter.membershipFee.partnerFee')" min-width="100" prop="partner_fee">
        </el-table-column>
        <el-table-column fixed="right" :label="$t('parameter.membershipFee.operation')" min-width="80">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">{{ $t('parameter.membershipFee.edit') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="dialogTitle"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="artFrom"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item :label="$t('parameter.membershipFee.feeTemplateId')" prop="id">
            <el-input v-model="artFrom.id" size="small" disabled />
          </el-form-item>
          <el-form-item :label="$t('parameter.membershipFee.agentFee') + '：'" prop="agent_fee">
            <el-input
              v-model="artFrom.agent_fee"
              size="small"
              :placeholder="$t('parameter.membershipFee.placeholder.agentFee')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('parameter.membershipFee.partnerFee') + '：'" prop="partner_fee">
            <el-input
              v-model="artFrom.partner_fee"
              size="small"
              :placeholder="$t('parameter.membershipFee.placeholder.partnerFee')"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="handleCancle">{{ $t('common.cancel') }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>
<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "MembershipUpgrade",
  data() {
    return {
      searchFrom: {
        formId: 104
      },
      loading: false,
      tableData: [],
      dialogTitle: this.$t("parameter.membershipFee.addTitle"),
      dialogFormVisible: false,
      artFrom: {
        formId: 104,
        agent_fee: "",
        partner_fee: ""
      },
     rules: {
  agent_fee: [
    {
      required: true,
      message: this.$t("parameter.membershipFee.placeholder.agentFee"),
      trigger: "blur"
    }
  ],
  partner_fee: [
    {
      required: true,
      message: this.$t("parameter.membershipFee.placeholder.partnerFee"),
      trigger: "blur"
    }
  ]
}
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList() {
      this.loading = true;

      configInfo(this.searchFrom)
        .then(res => {
          if (res) {
            this.tableData = [res];
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
   handleEdit(row) {
  this.artFrom.id = row.id;
  this.artFrom.agent_fee = row.agent_fee;
  this.artFrom.partner_fee = row.partner_fee;
  this.dialogTitle = this.$t("parameter.membershipFee.editTitle");
  this.dialogFormVisible = true;
},
    handleCancle() {
      (this.artFrom = {
        formId: 104,
        id: "",
        agent_fee: "",
        partner_fee: ""
      }),
        (this.dialogFormVisible = false);
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        const param = {
          id: this.artFrom.id,
          sort: 1,
          status: true,
          fields: [
            {
              name: "agent_fee",
              value: this.artFrom.agent_fee,
              title: "agent_fee"
            },
            {
              name: "partner_fee",
              value: this.artFrom.partner_fee,
              title: "partner_fee"
            }
          ]
        };
        configSaveForm(param).then(res => {
          this.$message.success(this.$t("common.operationSuccess"));

          this.handleCancle();
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>
