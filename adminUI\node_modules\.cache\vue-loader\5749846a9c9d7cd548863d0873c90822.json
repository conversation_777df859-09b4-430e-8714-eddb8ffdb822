{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue", "mtime": 1754269254569}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { levelUpgradeOrderListApi, cancelLevelUpgradeOrderApi } from '@/api/user'\r\n\r\nexport default {\r\n  name: 'UserLevelUpgrade',\r\n  data() {\r\n    return {\r\n      searchForm: {\r\n        orderNo: '',\r\n        orderStatus: '',\r\n        page: 1,\r\n        limit: 20\r\n      },\r\n      tableData: [],\r\n      total: 0,\r\n      listLoading: false,\r\n      detailVisible: false,\r\n      currentOrder: null\r\n    }\r\n  },\r\n  mounted() {\r\n    // 延迟执行，确保组件完全挂载\r\n    this.$nextTick(() => {\r\n      this.getList()\r\n    })\r\n  },\r\n  methods: {\r\n    // 获取订单列表\r\n    getList() {\r\n      this.listLoading = true\r\n\r\n      // 暂时使用模拟数据进行测试\r\n      setTimeout(() => {\r\n        this.tableData = [\r\n          {\r\n            orderNo: 'UG202501030001',\r\n            uid: 1001,\r\n            fromLevelId: 1,\r\n            toLevelId: 2,\r\n            upgradePrice: 100000,\r\n            paymentMethod: 'balance',\r\n            orderStatus: 0,\r\n            createTime: '2025-01-03 10:00:00',\r\n            payTime: null,\r\n            remark: '测试订单'\r\n          }\r\n        ]\r\n        this.total = 1\r\n        this.listLoading = false\r\n      }, 500)\r\n\r\n      // 注释掉真实API调用，避免权限问题\r\n      /*\r\n      const params = {\r\n        page: this.searchForm.page,\r\n        limit: this.searchForm.limit\r\n      }\r\n      if (this.searchForm.orderNo) {\r\n        params.orderNo = this.searchForm.orderNo\r\n      }\r\n      if (this.searchForm.orderStatus !== '') {\r\n        params.orderStatus = this.searchForm.orderStatus\r\n      }\r\n\r\n      levelUpgradeOrderListApi(params).then(res => {\r\n        // 确保数据结构正确\r\n        if (res && res.data) {\r\n          this.tableData = res.data.list || []\r\n          this.total = res.data.total || 0\r\n        } else {\r\n          this.tableData = []\r\n          this.total = 0\r\n        }\r\n        this.listLoading = false\r\n      }).catch(error => {\r\n        console.error('获取订单列表失败:', error)\r\n        this.tableData = []\r\n        this.total = 0\r\n        this.listLoading = false\r\n        this.$message.error(this.$t('user.levelUpgrade.getListFailed'))\r\n      })\r\n      */\r\n    },\r\n    \r\n    // 重置搜索\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        orderNo: '',\r\n        orderStatus: '',\r\n        page: 1,\r\n        limit: 20\r\n      }\r\n      this.getList()\r\n    },\r\n    \r\n    // 分页大小改变\r\n    handleSizeChange(val) {\r\n      this.searchForm.limit = val\r\n      this.searchForm.page = 1\r\n      this.getList()\r\n    },\r\n    \r\n    // 当前页改变\r\n    handleCurrentChange(val) {\r\n      this.searchForm.page = val\r\n      this.getList()\r\n    },\r\n    \r\n    // 取消订单\r\n    cancelOrder(orderNo) {\r\n      this.$confirm(this.$t('user.levelUpgrade.confirmCancel'), this.$t('common.confirm'), {\r\n        confirmButtonText: this.$t('common.confirm'),\r\n        cancelButtonText: this.$t('common.cancel'),\r\n        type: 'warning'\r\n      }).then(() => {\r\n        cancelLevelUpgradeOrderApi(orderNo).then(() => {\r\n          this.$message.success(this.$t('user.levelUpgrade.cancelSuccess'))\r\n          this.getList()\r\n        }).catch(error => {\r\n          console.error('取消订单失败:', error)\r\n          this.$message.error(this.$t('user.levelUpgrade.cancelFailed'))\r\n        })\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n    \r\n    // 查看详情\r\n    viewDetail(order) {\r\n      this.currentOrder = order\r\n      this.detailVisible = true\r\n    },\r\n    \r\n    // 获取等级名称\r\n    getLevelName(levelId) {\r\n      return this.$t(`user.levelUpgrade.levelNames.${levelId}`) || this.$t('user.levelUpgrade.unknownLevel')\r\n    },\r\n\r\n    // 获取支付方式名称\r\n    getPaymentMethodName(method) {\r\n      const methodMap = {\r\n        'balance': this.$t('user.levelUpgrade.balancePayment')\r\n      }\r\n      return methodMap[method] || method\r\n    },\r\n\r\n    // 获取状态名称\r\n    getStatusName(status) {\r\n      const statusMap = {\r\n        0: this.$t('user.levelUpgrade.pending'),\r\n        1: this.$t('user.levelUpgrade.paid'),\r\n        2: this.$t('user.levelUpgrade.cancelled'),\r\n        3: this.$t('user.levelUpgrade.refunded')\r\n      }\r\n      return statusMap[status] || this.$t('user.levelUpgrade.unknownStatus')\r\n    },\r\n    \r\n    // 获取状态颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        0: 'warning',\r\n        1: 'success',\r\n        2: 'info', \r\n        3: 'danger'\r\n      }\r\n      return colorMap[status] || 'info'\r\n    }\r\n  }\r\n}\r\n", null]}