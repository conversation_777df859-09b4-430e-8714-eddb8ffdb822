{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\search.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\search.vue", "mtime": 1754388065807}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { orderListApi } from \"@/api/order\";\nexport default {\n  name: \"OrderSearch\",\n  data: function data() {\n    return {\n      loading: false,\n      searchFrom: {\n        orderNo: \"\",\n        productTitle: \"\",\n        type: \"2\",\n        dateLimit: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tableData: [],\n      statusList: [{\n        value: \"\",\n        label: \"all\"\n      }, {\n        value: \"unPaid\",\n        label: \"unPaid\"\n      }, {\n        value: \"notShipped\",\n        label: \"notShipped\"\n      }, {\n        value: \"spike\",\n        label: \"spike\"\n      }, {\n        value: \"bargain\",\n        label: \"bargain\"\n      }, {\n        value: \"complete\",\n        label: \"complete\"\n      }, {\n        value: \"toBeWrittenOff\",\n        label: \"toBeWrittenOff\"\n      }, {\n        value: \"refunding\",\n        label: \"refunding\"\n      }, {\n        value: \"refunded\",\n        label: \"refunded\"\n      }, {\n        value: \"deleted\",\n        label: \"deleted\"\n      }]\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      var _this = this;\n      this.loading = true;\n      this.searchFrom.page = num ? num : this.searchFrom.page;\n      orderListApi(this.searchFrom).then(function (res) {\n        _this2.tableData = res.list || [];\n        _this2.tableData.forEach(function (item) {\n          item.payCount = item.productList ? item.productList.length : 0;\n          // item.statusLabel = this.statusList.filter(\n          //   i => i.value == item.status\n          // )[0].label;\n\n          // 安全检查：确保productList存在且不为空\n          if (item.productList && item.productList.length > 0) {\n            item.productName = item.productList[0].productName;\n            item.actualCommission = item.productList[0].actualCommission;\n            item.commissionRate = item.productList[0].commissionRate;\n            item.contentId = item.productList[0].contentId;\n            item.estimatedCommission = item.productList[0].estimatedCommission;\n            item.price = _this.formatAmount(item.productList[0].price);\n            item.image = item.productList[0].image;\n          } else {\n            // 设置默认值\n            item.productName = '';\n            item.actualCommission = 0;\n            item.commissionRate = 0;\n            item.contentId = '';\n            item.estimatedCommission = 0;\n            item.price = _this.formatAmount(0);\n            item.image = '';\n          }\n          item.totalPrice = _this.formatAmount(item.totalPrice);\n        });\n        _this2.searchFrom.total = res.total || 0;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n        _this2.tableData = [];\n        _this2.searchFrom.total = 0;\n      });\n    },\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.searchFrom.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.searchFrom.limit = index;\n      this.getList();\n    },\n    resetForm: function resetForm() {\n      this.searchFrom = {\n        orderNo: \"\",\n        productTitle: \"\",\n        type: \"2\",\n        dateLimit: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.getList();\n    },\n    formatRate: function formatRate(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      return parseInt(s * 10000) / 100 + \"%\";\n    }\n  }\n};", null]}