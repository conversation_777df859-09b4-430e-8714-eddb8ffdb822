{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754382949112}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./list.vue?vue&type=template&id=1907ea3f&scoped=true\"\nimport script from \"./list.vue?vue&type=script&lang=js\"\nexport * from \"./list.vue?vue&type=script&lang=js\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=1907ea3f&prod&scoped=true&lang=scss\"\nimport style1 from \"./list.vue?vue&type=style&index=1&id=1907ea3f&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1907ea3f\",\n  null\n  \n)\n\nexport default component.exports"]}