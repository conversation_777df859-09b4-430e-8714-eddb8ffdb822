{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue?vue&type=template&id=2f47f1f8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <el-tabs\n      v-model=\"searchForm.extractType\"\n      @tab-click=\"onChangeType\"\n      class=\"mb20\"\n    >\n      <el-tab-pane\n        :label=\"$t('financial.history.walletWithdrawal')\"\n        name=\"wallet\"\n      ></el-tab-pane>\n      <el-tab-pane\n        :label=\"$t('financial.history.bankWithdrawal')\"\n        name=\"bank\"\n      ></el-tab-pane>\n    </el-tabs>\n    <div class=\"container mt-1\">\n      <el-form v-model=\"searchForm\" inline size=\"small\">\n        <el-form-item :label=\"$t('financial.history.applicant') + '：'\">\n          <el-input\n            v-model=\"searchForm.keywords\"\n            size=\"small\"\n            :placeholder=\"$t('common.enter')\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.history.applicationTime') + '：'\">\n          <el-date-picker\n            v-model=\"timeList\"\n            value-format=\"yyyy-MM-dd\"\n            format=\"yyyy-MM-dd\"\n            size=\"small\"\n            type=\"daterange\"\n            placement=\"bottom-end\"\n            :start-placeholder=\"$t('common.startDate')\"\n            :end-placeholder=\"$t('common.endDate')\"\n            style=\"width: 250px;\"\n          />\n        </el-form-item>\n        <el-form-item\n          :label=\"$t('financial.history.electronicWallet') + '：'\"\n          v-if=\"searchForm.extractType == 'wallet'\"\n        >\n          <el-select\n            v-model=\"searchForm.walletCode\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"item in walletList\"\n              :key=\"item.value\"\n              :label=\"$t('operations.withdrawal.' + item.label)\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          :label=\"$t('financial.history.bankName') + '：'\"\n          v-if=\"searchForm.extractType == 'bank'\"\n        >\n          <el-select\n            v-model=\"searchForm.bankName\"\n            clearable\n            :placeholder=\"$t('common.all')\"\n          >\n            <el-option\n              v-for=\"(item, index) in bankList\"\n              :key=\"index\"\n              :label=\"item\"\n              :value=\"item\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('financial.history.status') + '：'\">\n          <el-select\n            v-model=\"searchForm.status\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"(item, index) in statusList\"\n              :key=\"index\"\n              :label=\"$t('operations.withdrawal.' + item.label)\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\">{{\n      $t(\"common.query\")\n    }}</el-button>\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm\">{{\n      $t(\"common.reset\")\n    }}</el-button>\n  </el-card>\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        @click=\"handleUpload\"\n        v-hasPermi=\"['admin:financialCenter:request:upload']\"\n        >{{ $t(\"financial.history.exportExcel\") }}</el-button\n      >\n    </div>\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        type=\"index\"\n        :label=\"$t('common.serialNumber')\"\n        width=\"110\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.applicationId')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.uid | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.applicantName')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.realName | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.withdrawalAmount')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.extractPrice | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.serviceFee')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.serviceFee | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.actualAmount')\"\n        min-width=\"100\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.actualAmount | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.applicationTime')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.createTime | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        v-if=\"searchForm.extractType === 'wallet'\"\n        :label=\"$t('financial.history.electronicWallet')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.walletCode | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        v-if=\"searchForm.extractType === 'wallet'\"\n        :label=\"$t('financial.history.walletAccount')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.walletAccount | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        v-if=\"searchForm.extractType === 'bank'\"\n        :label=\"$t('financial.history.bankName')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.bankName | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        v-if=\"searchForm.extractType === 'bank'\"\n        :label=\"$t('financial.history.bankCardNumber')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column :label=\"$t('financial.history.name')\" min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.nickName | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.phoneNumber')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.phone | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.transferTime')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.transferTime | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.transferResult')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.transferResult | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column :label=\"$t('financial.history.remark')\" min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.mark | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.attachment')\"\n        min-width=\"80\"\n        v-if=\"searchForm.extractType === 'wallet'\"\n        ><template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.voucherImage\"\n              :preview-src-list=\"[scope.row.voucherImage]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.history.operator')\"\n        min-width=\"80\"\n        ><template slot-scope=\"scope\">{{\n          scope.row.operator | filterEmpty\n        }}</template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"e => sizeChange\"\n      @current-change=\"e => pageChange\"\n      :current-page=\"searchForm.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"searchForm.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"searchForm.total\"\n    >\n    </el-pagination>\n  </el-card>\n</div>\n", null]}