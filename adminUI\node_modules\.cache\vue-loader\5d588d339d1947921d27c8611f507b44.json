{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue?vue&type=template&id=b6513f38&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <el-tabs\n      v-model=\"searchForm.extractType\"\n      @tab-click=\"onChangeType\"\n      class=\"mb20\"\n    >\n      <el-tab-pane\n        :label=\"$t('financial.request.walletWithdrawal')\"\n        name=\"wallet\"\n      ></el-tab-pane>\n      <el-tab-pane\n        :label=\"$t('financial.request.bankWithdrawal')\"\n        name=\"bank\"\n      ></el-tab-pane>\n    </el-tabs>\n    <div class=\"container mt-1\">\n      <el-form v-model=\"searchForm\" inline size=\"small\">\n        <el-form-item :label=\"$t('financial.request.applicant') + '：'\">\n          <el-input\n            v-model=\"searchForm.keywords\"\n            size=\"small\"\n            :placeholder=\"$t('common.enter')\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.request.applicationTime') + '：'\">\n          <el-date-picker\n            v-model=\"timeList\"\n            value-format=\"yyyy-MM-dd\"\n            format=\"yyyy-MM-dd\"\n            size=\"small\"\n            type=\"daterange\"\n            placement=\"bottom-end\"\n            :start-placeholder=\"$t('common.startDate')\"\n            :end-placeholder=\"$t('common.endDate')\"\n            style=\"width: 250px;\"\n          />\n        </el-form-item>\n        <el-form-item\n          :label=\"$t('financial.request.electronicWallet') + '：'\"\n          v-if=\"searchForm.extractType == 'wallet'\"\n        >\n          <el-select\n            v-model=\"searchForm.walletCode\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"item in walletList\"\n              :key=\"item.value\"\n              :label=\"$t('operations.withdrawal.' + item.label)\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          :label=\"$t('financial.request.bankName') + '：'\"\n          v-if=\"searchForm.extractType == 'bank'\"\n        >\n          <el-select\n            v-model=\"searchForm.bankName\"\n            :placeholder=\"$t('common.all')\"\n          >\n            <el-option\n              v-for=\"(item, index) in bankList\"\n              :key=\"index\"\n              :label=\"item\"\n              :value=\"item\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"getList(1)\">{{\n      $t(\"common.query\")\n    }}</el-button>\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm\">{{\n      $t(\"common.reset\")\n    }}</el-button>\n  </el-card>\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button type=\"primary\" size=\"small\" @click=\"handleUpload\">{{\n        $t(\"financial.request.exportExcel\")\n      }}</el-button>\n    </div>\n    <div>\n      <el-table\n        v-loading=\"loading\"\n        :data=\"tableData\"\n        size=\"small\"\n        :header-cell-style=\"{ fontWeight: 'bold' }\"\n      >\n        <el-table-column\n          type=\"index\"\n          :label=\"$t('common.serialNumber')\"\n          min-width=\"110\"\n        >\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.applicationId')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.id | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.applicantName')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.realName | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.withdrawalAmount')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.extractPrice | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.serviceFee')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.serviceFee | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.actualAmount')\"\n          min-width=\"100\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.actualAmount | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.applicationTime')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.createTime | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"searchForm.extractType === 'wallet'\"\n          :label=\"$t('financial.request.electronicWallet')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.walletCode | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"searchForm.extractType == 'wallet'\"\n          :label=\"$t('financial.request.walletAccount')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.walletAccount | filterEmpty }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"searchForm.extractType == 'bank'\"\n          :label=\"$t('financial.request.bankName')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.bankName | filterEmpty }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          v-if=\"searchForm.extractType == 'bank'\"\n          :label=\"$t('financial.request.bankCardNumber')\"\n          min-width=\"80\"\n        >\n        </el-table-column>\n        <el-table-column :label=\"$t('financial.request.name')\" min-width=\"80\">\n          <template slot-scope=\"scope\">{{\n            scope.row.nickName | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.phoneNumber')\"\n          min-width=\"80\"\n        >\n          <template slot-scope=\"scope\">{{\n            scope.row.phone | filterEmpty\n          }}</template>\n        </el-table-column>\n        <el-table-column\n          :label=\"$t('financial.request.action')\"\n          min-width=\"80\"\n          fixed=\"right\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"small\"\n              type=\"text\"\n              @click=\"handleFinish(scope.row)\"\n              >{{ $t(\"financial.request.transferComplete\") }}</el-button\n            >\n          </template>\n        </el-table-column>\n      </el-table>\n      <el-pagination\n        class=\"mt20\"\n        @size-change=\"e => sizeChange\"\n        @current-change=\"e => pageChange\"\n        :current-page=\"searchForm.page\"\n        :page-sizes=\"[20, 40, 60, 100]\"\n        :page-size=\"searchForm.limit\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"searchForm.total\"\n      >\n      </el-pagination>\n    </div>\n\n    <el-dialog\n      append-to-body\n      :visible.sync=\"dialogFormVisible\"\n      :title=\"$t('financial.request.transferComplete')\"\n      width=\"680px\"\n      @close=\"handleCancle\"\n    >\n      <el-form\n        ref=\"elForm\"\n        inline\n        :model=\"artFrom\"\n        :rules=\"rules\"\n        label-width=\"200px\"\n      >\n        <el-form-item\n          :label=\"$t('financial.request.attachment') + '：'\"\n          prop=\"voucherImage\"\n        >\n          <el-upload\n            class=\"avatar-uploader\"\n            action\n            :show-file-list=\"false\"\n            :http-request=\"handleUploadForm\"\n            :on-change=\"imgSaveToUrl\"\n            :before-upload=\"beforeAvatarUpload\"\n            :headers=\"myHeaders\"\n            multiple\n          >\n            <i class=\"el-icon-plus\" />\n          </el-upload>\n          <el-image\n            v-if=\"artFrom.voucherImage\"\n            style=\"width: 36px; height: 36px;margin-top: 8px;\"\n            :src=\"artFrom.voucherImage\"\n            :preview-src-list=\"[artFrom.voucherImage]\"\n          />\n        </el-form-item>\n        <el-form-item\n          :label=\"$t('financial.request.remark') + '：'\"\n          prop=\"remark\"\n        >\n          <el-input\n            v-model=\"artFrom.remark\"\n            size=\"small\"\n            :placeholder=\"$t('financial.request.remark')\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button type=\"primary\" @click=\"handelConfirm\">\n          {{ $t(\"common.confirm\") }}\n        </el-button>\n        <el-button @click=\"handleCancle\">\n          {{ $t(\"common.cancel\") }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </el-card>\n</div>\n", null]}