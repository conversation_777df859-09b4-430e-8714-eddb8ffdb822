{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderDetail.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n0\nimport { orderDetailApi, getLogisticsInfoApi } from '@/api/order'\nexport default {\n  name: 'OrderDetail',\n  props: {\n    orderId: {\n      type: String,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      reverse: true,\n      dialogVisible: false,\n      orderDatalist: null,\n      loading: false,\n      modal2: false,\n      result: []\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    openLogistics () {\n      this.getOrderData()\n      this.modal2 = true;\n    },\n    // 获取订单物流信息\n    getOrderData () {\n      getLogisticsInfoApi({orderNo:this.orderId}).then(async res => {\n        this.result = res.list;\n      })\n    },\n    getDetail(id) {\n      this.loading = true\n      orderDetailApi({orderNo: id}).then(res => {\n        this.orderDatalist = res\n        this.loading = false\n      }).catch(() => {\n        this.orderDatalist = null\n        this.loading = false\n      })\n    }\n  }\n}\n", null]}