{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {orderStatisticsApi, statisticsDataApi } from '@/api/order';\nimport statisticsData from \"../components/statisticsData\";\nimport Loading from \"../components/Loading\";\nexport default {\n  name: \"OrderIndex\",\n  components: {\n    Loading,\n    statisticsData\n  },\n  props: {},\n  data: function() {\n    return {\n      census: [],\n      list: [],\n      where: {\n        page: 1,\n        limit: 10\n      },\n      loaded: false,\n      loading: false\n    };\n  },\n  created() {\n    import('@/assets/js/media_750')\n  },\n  mounted: function() {\n    this.getIndex();\n    this.getList();\n    this.$scroll(this.$refs.container, () => {\n      !this.loading && this.getList();\n    });\n  },\n  methods: {\n    getIndex() {\n      orderStatisticsApi().then(\n        res => {\n          this.census = res\n        },\n        err => {\n          this.$dialog.message(err.message);\n        }\n      );\n    },\n    getList() {\n      if (this.loading || this.loaded) return;\n      this.loading = true;\n      statisticsDataApi(this.where).then(\n        res => {\n          this.loading = false;\n          this.loaded = res.length < this.where.limit;\n          this.list.push.apply(this.list, res);\n          this.where.page = this.where.page + 1;\n        },\n        error => {\n          this.$dialog.message(error.message);\n        },\n        300\n      );\n    }\n  }\n};\n", null]}