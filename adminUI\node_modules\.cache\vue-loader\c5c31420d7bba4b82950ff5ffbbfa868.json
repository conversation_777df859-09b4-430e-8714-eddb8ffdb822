{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { parse } from '@babel/parser'\nimport ClipboardJS from 'clipboard'\nimport { saveAs } from 'file-saver'\nimport {\n  makeUpHtml, vueTemplate, vueScript, cssStyle\n} from '@/components/FormGenerator/components/generator/html'\nimport { makeUpJs } from '@/components/FormGenerator/components/generator/js'\nimport { makeUpCss } from '@/components/FormGenerator/components/generator/css'\nimport { exportDefault, beautifierConf, titleCase } from '../utils/index'\nimport ResourceDialog from './ResourceDialog'\nimport loadMonaco from '../utils/loadMonaco'\nimport loadBeautifier from '../utils/loadBeautifier'\n\nconst editorObj = {\n  html: null,\n  js: null,\n  css: null\n}\nconst mode = {\n  html: 'html',\n  js: 'javascript',\n  css: 'css'\n}\nlet beautifier\nlet monaco\n\nexport default {\n  components: { ResourceDialog },\n  props: ['formData', 'generateConf'],\n  data() {\n    return {\n      activeTab: 'html',\n      htmlCode: '',\n      jsCode: '',\n      cssCode: '',\n      codeFrame: '',\n      isIframeLoaded: false,\n      isInitcode: false, // 保证open后两个异步只执行一次runcode\n      isRefreshCode: false, // 每次打开都需要重新刷新代码\n      resourceVisible: false,\n      scripts: [],\n      links: [],\n      monaco: null\n    }\n  },\n  computed: {\n    resources() {\n      return this.scripts.concat(this.links)\n    }\n  },\n  watch: {},\n  created() {\n  },\n  mounted() {\n    window.addEventListener('keydown', this.preventDefaultSave)\n    const clipboard = new ClipboardJS('.copy-btn', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave)\n  },\n  methods: {\n    preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault()\n      }\n    },\n    onOpen() {\n      const { type } = this.generateConf\n      this.htmlCode = makeUpHtml(this.formData, type)\n      this.jsCode = makeUpJs(this.formData, type)\n      this.cssCode = makeUpCss(this.formData)\n\n      loadBeautifier(btf => {\n        beautifier = btf\n        this.htmlCode = beautifier.html(this.htmlCode, beautifierConf.html)\n        this.jsCode = beautifier.js(this.jsCode, beautifierConf.js)\n        this.cssCode = beautifier.css(this.cssCode, beautifierConf.html)\n\n        loadMonaco(val => {\n          monaco = val\n          this.setEditorValue('editorHtml', 'html', this.htmlCode)\n          this.setEditorValue('editorJs', 'js', this.jsCode)\n          this.setEditorValue('editorCss', 'css', this.cssCode)\n          if (!this.isInitcode) {\n            this.isRefreshCode = true\n            this.isIframeLoaded && (this.isInitcode = true) && this.runCode()\n          }\n        })\n      })\n    },\n    onClose() {\n      this.isInitcode = false\n      this.isRefreshCode = false\n      this.isIframeLoaded = false\n    },\n    iframeLoad() {\n      if (!this.isInitcode) {\n        this.isIframeLoaded = true\n        this.isRefreshCode && (this.isInitcode = true) && this.runCode()\n      }\n    },\n    setEditorValue(id, type, codeStr) {\n      if (editorObj[type]) {\n        editorObj[type].setValue(codeStr)\n      } else {\n        editorObj[type] = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: mode[type],\n          automaticLayout: true\n        })\n      }\n      // ctrl + s 刷新\n      editorObj[type].onKeyDown(e => {\n        if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n          this.runCode()\n        }\n      })\n    },\n    runCode() {\n      const jsCodeStr = editorObj.js.getValue()\n      try {\n        const ast = parse(jsCodeStr, { sourceType: 'module' })\n        const astBody = ast.program.body\n        if (astBody.length > 1) {\n          this.$confirm(\n            'js格式不能识别，仅支持修改export default的对象内容',\n            '提示',\n            {\n              type: 'warning'\n            }\n          )\n          return\n        }\n        if (astBody[0].type === 'ExportDefaultDeclaration') {\n          const postData = {\n            type: 'refreshFrame',\n            data: {\n              generateConf: this.generateConf,\n              html: editorObj.html.getValue(),\n              js: jsCodeStr.replace(exportDefault, ''),\n              css: editorObj.css.getValue(),\n              scripts: this.scripts,\n              links: this.links\n            }\n          }\n\n          this.$refs.previewPage.contentWindow.postMessage(\n            postData,\n            location.origin\n          )\n        } else {\n          this.$message.error('请使用export default')\n        }\n      } catch (err) {\n        this.$message.error(`js错误：${err}`)\n      }\n    },\n    generateCode() {\n      const html = vueTemplate(editorObj.html.getValue())\n      const script = vueScript(editorObj.js.getValue())\n      const css = cssStyle(editorObj.css.getValue())\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    exportFile() {\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: `${+new Date()}.vue`,\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(({ value }) => {\n        if (!value) value = `${+new Date()}.vue`\n        const codeStr = this.generateCode()\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n        saveAs(blob, value)\n      })\n    },\n    showResource() {\n      this.resourceVisible = true\n    },\n    setResource(arr) {\n      const scripts = []; const\n        links = []\n      if (Array.isArray(arr)) {\n        arr.forEach(item => {\n          if (item.endsWith('.css')) {\n            links.push(item)\n          } else {\n            scripts.push(item)\n          }\n        })\n        this.scripts = scripts\n        this.links = links\n      } else {\n        this.scripts = []\n        this.links = []\n      }\n    }\n  }\n}\n", null]}