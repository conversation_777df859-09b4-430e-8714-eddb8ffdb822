import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MemberLevelStatusScreen extends StatefulWidget {
  final dynamic memberLevel;

  const MemberLevelStatusScreen({
    Key? key,
    required this.memberLevel,
  }) : super(key: key);

  @override
  State<MemberLevelStatusScreen> createState() => _MemberLevelStatusScreenState();
}

class _MemberLevelStatusScreenState extends State<MemberLevelStatusScreen> {
  int currentLevelIndex = 2; // 当前是金牌代理（索引2）

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F3F2),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '等级状态',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      extendBodyBehindAppBar: true,
      body: SingleChildScrollView(
        child: Column(
          children: [
            buildTopSection(),
            buildBenefitsSection(),
          ],
        ),
      ),
    );
  }

  Widget buildTopSection() {
    return Container(
      width: double.infinity,
      height: 360, // 进一步减小高度，因为移除了进度区域
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/member_level/bg_main.png'),
          fit: BoxFit.fitWidth, // 填充整个容器，可能会拉伸
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            SizedBox(height: 10), // 减小间距
            buildMemberCard(),
            SizedBox(height: 30), // 增加间距
            buildLevelIndicators(),
          ],
        ),
      ),
    );
  }

  Widget buildMemberCard() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      height: 140, // 减小高度
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 卡片背景
          Container(
            width: double.infinity,
            height: 140,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getLevelColor(currentLevelIndex).withValues(alpha: 0.8),
                  _getLevelColor(currentLevelIndex),
                ],
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                image: DecorationImage(
                  image: AssetImage(_getCardBackground(currentLevelIndex)),
                  fit: BoxFit.fill, // 填充整个卡片
                ),
              ),
            ),
          ),

          // 右侧装饰图案
          Positioned(
            top: 10, // 向下移动
            right: 5, // 向左移动
            child: Container(
              width: 120, // 放大
              height: 120,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(_getCardDecoration(currentLevelIndex)),
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),

          // 卡片内容
          Positioned(
            left: 16,
            top: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 等级名称
                Text(
                  _getLevelName(currentLevelIndex),
                  style: TextStyle(
                    fontSize: 24, // 减小字体
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                    color: _getLevelColor(currentLevelIndex),
                    fontFamily: 'Roboto',
                  ),
                ),
                SizedBox(height: 8),
                // 有效期
                Text(
                  '有效期: 1年',
                  style: TextStyle(
                    fontSize: 12, // 减小字体
                    color: _getTextColor(currentLevelIndex),
                    fontFamily: 'PingFang SC',
                  ),
                ),
                SizedBox(height: 4),
                // 升级时间
                Text(
                  '升级时间: 2025年6月11日',
                  style: TextStyle(
                    fontSize: 12, // 减小字体
                    color: _getTextColor(currentLevelIndex),
                    fontFamily: 'PingFang SC',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget buildLevelIndicators() {
    List<String> levels = ['普通', '银牌', '金牌', '钻石', '王者'];
    List<String> iconPaths = [
      'assets/images/member_level/level_silver.png', // 普通用银牌图标
      'assets/images/member_level/level_silver.png',
      'assets/images/member_level/level_gold.png',
      'assets/images/member_level/level_diamond.png',
      'assets/images/member_level/level_partner.png', // 王者用合作伙伴图标
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: levels.asMap().entries.map((entry) {
          int index = entry.key;
          String level = entry.value;
          bool isActive = index == currentLevelIndex; // 使用状态变量

          return GestureDetector(
            onTap: () {
              setState(() {
                currentLevelIndex = index; // 添加点击切换功能
              });
            },
            child: Column(
              children: [
                // 等级图标
                Container(
                  width: isActive ? 40 : 24, // 减小尺寸
                  height: isActive ? 40 : 24,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: isActive ? [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.3),
                        blurRadius: 6,
                        spreadRadius: 1,
                      ),
                    ] : null,
                  ),
                  child: Image.asset(
                    iconPaths[index],
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 6),
                // 等级文字
                Text(
                  level,
                  style: TextStyle(
                    fontSize: isActive ? 12 : 10, // 减小字体
                    color: Colors.white,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    fontFamily: 'PingFang SC',
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.6),
                        blurRadius: 3,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget buildBenefitsSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/member_level/benefits_bg.png'),
          fit: BoxFit.fill, // 填充整个权益区域
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: 40),
          buildBenefitsTitle(),
          SizedBox(height: 30),
          buildBenefitsContent(),
          SizedBox(height: 40),
          buildBottomIndicator(),
          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget buildBenefitsTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/member_level/star_left.png',
          width: 20, // 减小尺寸
          height: 20,
        ),
        SizedBox(width: 8),
        Text(
          '专属权益',
          style: TextStyle(
            fontSize: 20, // 减小字体
            fontWeight: FontWeight.w600,
            color: Color(0xFF3C3A36),
            fontFamily: 'PingFang SC',
          ),
        ),
        SizedBox(width: 8),
        Image.asset(
          'assets/images/member_level/star_right.png',
          width: 20, // 减小尺寸
          height: 20,
        ),
      ],
    );
  }

  Widget buildBenefitsContent() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          buildBenefitItem(
            iconPath: 'assets/images/member_level/icon_reward.png',
            title: '直接邀请奖励',
            description: '每邀请 1 位代理，奖励 35,000 印尼盾，邀请3人直接回本！',
          ),
          SizedBox(height: 20),
          buildBenefitItem(
            iconPath: 'assets/images/member_level/icon_commission.png',
            title: '团队购物佣金',
            description: '间接邀请（二级）：每人可累计 15.000 印尼盾；间接邀请（三级）：每人可累计 10.000 印尼盾。',
          ),
          SizedBox(height: 20),
          buildBenefitItem(
            iconPath: 'assets/images/member_level/icon_cashback.png',
            title: '额外现金返还',
            description: '每发展 10 位黄金代理：奖励 300.000 印尼盾',
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget buildBenefitItem({
    required String iconPath,
    required String title,
    required String description,
    bool isLast = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: Color(0xFFF5F5F5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 图标
          Container(
            width: 50, // 减小尺寸
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF362C2C), Color(0xFF67605B)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Image.asset(
                iconPath,
                width: 24, // 减小图标
                height: 24,
              ),
            ),
          ),
          SizedBox(width: 12),
          // 文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16, // 减小字体
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF3C3A36),
                    fontFamily: 'PingFang SC',
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12, // 减小字体
                    color: Color(0xFF98938F),
                    fontFamily: 'PingFang SC',
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBottomIndicator() {
    return Container(
      width: 134,
      height: 5,
      decoration: BoxDecoration(
        color: Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(3),
      ),
    );
  }

  String _getLevelName(int index) {
    List<String> names = ['Member Biasa', 'Agen Perak', 'Agen Emas', 'Agen Berlian', 'Agen Raja'];
    return names[index];
  }

  Color _getLevelColor(int index) {
    List<Color> colors = [
      Color(0xFF666666), // 普通灰色
      Color(0xFF8C8C8C), // 银色
      Color(0xFFB76928), // 金色
      Color(0xFF4A90E2), // 钻石蓝
      Color(0xFF9B59B6), // 王者紫色
    ];
    return colors[index];
  }

  String _getCardBackground(int index) {
    List<String> backgrounds = [
      'assets/images/member_level/card_bg.png', // 普通用原背景
      'assets/images/member_level/card_bg_silver.png',
      'assets/images/member_level/card_bg_gold.png',
      'assets/images/member_level/card_bg_diamond.png',
      'assets/images/member_level/card_bg_king.png',
    ];
    return backgrounds[index];
  }

  String _getCardDecoration(int index) {
    // 暂时都使用同一个装饰图案，确保稳定显示
    return 'assets/images/member_level/card_decoration.png';
  }

  Color _getTextColor(int index) {
    List<Color> textColors = [
      Color(0xFF333333), // 普通深灰色
      Color(0xFF4A4A4A), // 银牌深灰色
      Color(0xFF6F2F25), // 金牌深棕色
      Color(0xFF2C3E50), // 钻石深蓝色
      Color(0xFF4A148C), // 王者深紫色
    ];
    return textColors[index];
  }
}