{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  applyListApi,\n  extractBankApi,\n  financeApplyDealApi,\n  uploadImage\n} from \"@/api/financial\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"WithdrawalRequest\",\n  components: {},\n  data() {\n    return {\n      loading: false,\n      tableData: [],\n      realName: \"\",\n      pid: \"\",\n      myHeaders: { \"X-Token\": getToken() },\n      isMore: \"\",\n      modelName: \"\",\n      searchForm: {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: \"wallet\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      timeList: [],\n      dialogFormVisible: false,\n      artFrom: {\n        id: null,\n        voucherImage: \"\",\n        remark: \"\"\n      },\n      walletList: [\n        { label: \"ShopeePay\", value: \"ShopeePay\" },\n        { label: \"DANA\", value: \"DANA\" },\n        { label: \"OVO\", value: \"OVO\" },\n        { label: \"Gopay\", value: \"Gopay\" }\n      ],\n      bankList: [],\n\n      rules: {\n        voucherImage: [\n          {\n            required: true,\n            message: \"请选择\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    };\n  },\n  created() {},\n  mounted() {\n    this.getList();\n    this.getBankList();\n  },\n  methods: {\n    // 获取银行列表\n    getBankList() {\n      extractBankApi()\n        .then(res => {\n          this.bankList = res;\n        })\n        .catch(() => {});\n    },\n    // 列表\n    getList(num) {\n      this.loading = true;\n      this.searchForm.page = num ? num : this.searchForm.page;\n      this.searchForm.dateLimit = this.timeList.length\n        ? this.timeList.join(\",\")\n        : \"\";\n      applyListApi(this.searchForm)\n        .then(res => {\n          this.tableData = res.list;\n          this.searchForm.total = res.total;\n          this.loading = false;\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    },\n    resetForm() {\n      this.searchForm = {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: this.searchForm.extractType,\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.timeList = [];\n      this.getList();\n    },\n    //切换页数\n    pageChange(index) {\n      this.searchForm.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange(index) {\n      this.searchForm.limit = index;\n      this.getList();\n    },\n    handleUpload() {},\n    onChangeType(tab) {\n      this.getList();\n    },\n    handelConfirm() {\n      this.$refs.elForm.validate(async valid => {\n        if (!valid) return;\n        financeApplyDealApi(this.artFrom).then(res => {\n          this.$message.success(\"操作成功\");\n          this.handleCancle();\n          getList(1);\n        });\n      });\n    },\n    handleFinish(row) {\n      this.dialogFormVisible = true;\n      this.artFrom.id = row.id;\n      this.pid = row.id;\n      this.realName = row.realName;\n    },\n    handleCancle() {\n      this.dialogFormVisible = false;\n      this.artFrom = {\n        id: null,\n        voucherImage: \"\",\n        remark: \"\"\n      };\n    },\n\n    // 选取图片后自动回调，里面可以获取到文件\n    imgSaveToUrl(event) {\n      // 也可以用file\n      this.localFile = event.raw; // 或者 this.localFile=file.raw\n\n      // 转换操作可以不放到这个函数里面，\n      // 因为这个函数会被多次触发，上传时触发，上传成功也触发\n      let reader = new FileReader();\n      reader.readAsDataURL(this.localFile); // 这里也可以直接写参数event.raw\n\n      // 转换成功后的操作，reader.result即为转换后的DataURL ，\n      // 它不需要自己定义，你可以console.integralLog(reader.result)看一下\n      reader.onload = () => {\n        // console.integralLog(reader.result)\n      };\n\n      /* 另外一种本地预览方法 */\n      let URL = window.URL || window.webkitURL;\n      this.localImg = URL.createObjectURL(event.raw);\n      // 转换后的地址为 blob:http://xxx/7bf54338-74bb-47b9-9a7f-7a7093c716b5\n    },\n\n    beforeAvatarUpload(rawFile) {\n      if (rawFile.type === \"image/jpeg\" || rawFile.type === \"image/png\") {\n        return true;\n      } else {\n        this.$message.error(\"Avatar picture must be JPG format!\");\n        return false;\n      }\n    },\n    // 上传\n    handleUploadForm(param) {\n      const formData = new FormData();\n      const data = {\n        model: this.realName,\n        pid: this.pid\n      };\n      formData.append(\"multipart\", param.file);\n      let loading = this.$loading({\n        lock: true,\n        text: \"上传中，请稍候...\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\"\n      });\n      uploadImage(formData, data)\n        .then(res => {\n          loading.close();\n          this.$message.success(\"上传成功\");\n          this.artFrom.voucherImage = res.url;\n        })\n        .catch(res => {\n          loading.close();\n        });\n    }\n  }\n};\n", null]}