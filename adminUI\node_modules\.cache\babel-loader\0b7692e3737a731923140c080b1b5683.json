{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { marketingListApi, couponUserApi, marketingSendApi } from '@/api/marketing';\nexport default {\n  name: 'CouponList',\n  props: {\n    handle: {\n      type: String,\n      default: ''\n    },\n    couponData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    keyNum: {\n      type: Number,\n      default: 0\n    },\n    userIds: {\n      type: String,\n      default: ''\n    },\n    userType: {\n      type: String,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 10,\n        keywords: '',\n        type: ''\n      },\n      multipleSelection: [],\n      multipleSelectionAll: [],\n      idKey: 'id',\n      nextPageFlag: false,\n      attr: []\n    };\n  },\n  watch: {\n    keyNum: {\n      deep: true,\n      handler: function handler(val) {\n        this.getList();\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.tableFrom.page = 1;\n    this.getList();\n    if (!this.couponData) return;\n    this.couponData.forEach(function (row) {\n      _this.$refs.table.toggleRowSelection(row);\n    });\n  },\n  methods: {\n    close: function close() {\n      this.multipleSelection = [];\n    },\n    handleSelectionChange: function handleSelectionChange(val) {\n      var _this2 = this;\n      this.multipleSelection = val;\n      setTimeout(function () {\n        _this2.changePageCoreRecordData();\n      }, 50);\n    },\n    // 设置选中的方法\n    setSelectRow: function setSelectRow() {\n      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {\n        return;\n      }\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      this.$refs.table.clearSelection();\n      for (var i = 0; i < this.tableData.data.length; i++) {\n        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {\n          // 设置选中，记住table组件需要使用ref=\"table\"\n          this.$refs.table.toggleRowSelection(this.tableData.data[i], true);\n        }\n      }\n    },\n    // 记忆选择核心方法\n    changePageCoreRecordData: function changePageCoreRecordData() {\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var that = this;\n      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\n      if (this.multipleSelectionAll.length <= 0) {\n        this.multipleSelectionAll = this.multipleSelection;\n        return;\n      }\n      // 总选择里面的key集合\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      var selectIds = [];\n      // 获取当前页选中的id\n      this.multipleSelection.forEach(function (row) {\n        selectIds.push(row[idKey]);\n        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\n        if (selectAllIds.indexOf(row[idKey]) < 0) {\n          that.multipleSelectionAll.push(row);\n        }\n      });\n      var noSelectIds = [];\n      // 得到当前页没有选中的id\n      this.tableData.data.forEach(function (row) {\n        if (selectIds.indexOf(row[idKey]) < 0) {\n          noSelectIds.push(row[idKey]);\n        }\n      });\n      noSelectIds.forEach(function (id) {\n        if (selectAllIds.indexOf(id) >= 0) {\n          for (var i = 0; i < that.multipleSelectionAll.length; i++) {\n            if (that.multipleSelectionAll[i][idKey] == id) {\n              // 如果总选择中有未被选中的，那么就删除这条\n              that.multipleSelectionAll.splice(i, 1);\n              break;\n            }\n          }\n        }\n      });\n    },\n    ok: function ok() {\n      if (this.multipleSelection.length > 0) {\n        this.$emit('getCouponId', this.multipleSelectionAll);\n        this.close();\n      } else {\n        this.$message.warning('请先选择优惠劵');\n      }\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this3 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      this.userType ? this.tableFrom.type = 1 : this.tableFrom.type = 3;\n      marketingSendApi(this.tableFrom).then(function (res) {\n        _this3.tableData.data = res.list;\n        _this3.tableData.total = res.total;\n        _this3.listLoading = false;\n        _this3.$nextTick(function () {\n          this.setSelectRow(); // 调用跨页选中方法\n        });\n      }).catch(function (res) {\n        _this3.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.changePageCoreRecordData();\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.changePageCoreRecordData();\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 发送\n    sendGrant: function sendGrant(id) {\n      var _this4 = this;\n      this.$modalSure('发送优惠劵吗').then(function () {\n        couponUserApi({\n          couponId: id,\n          uid: _this4.userIds\n        }).then(function () {\n          _this4.$message.success('发送成功');\n          _this4.getList();\n        });\n      });\n    }\n  }\n};", null]}