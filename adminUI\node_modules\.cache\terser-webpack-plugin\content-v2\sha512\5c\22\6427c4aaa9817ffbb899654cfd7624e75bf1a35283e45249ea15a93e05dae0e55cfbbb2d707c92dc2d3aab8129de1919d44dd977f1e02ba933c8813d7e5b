{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-01f57fc8\"],{2059:function(t,e,a){\"use strict\";a(\"6a8e\")},\"6a8e\":function(t,e,a){},b3d7:function(t,e,a){},bf06:function(t,e,a){\"use strict\";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"div\",{staticClass:\"divBox\"},[a(\"el-card\",{staticClass:\"box-card\"},[a(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[a(\"div\",{staticClass:\"container\"},[a(\"el-form\",{attrs:{inline:!0}},[a(\"el-form-item\",{staticClass:\"width100\",attrs:{label:\"时间选择：\"}},[a(\"el-radio-group\",{staticClass:\"mr20\",attrs:{type:\"button\",size:\"small\"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,\"dateLimit\",e)},expression:\"tableFrom.dateLimit\"}},t._l(t.fromList.fromTxt,(function(e,i){return a(\"el-radio-button\",{key:i,attrs:{label:e.val}},[t._v(t._s(e.text))])})),1),t._v(\" \"),a(\"el-date-picker\",{staticStyle:{width:\"220px\"},attrs:{\"value-format\":\"yyyy-MM-dd\",format:\"yyyy-MM-dd\",size:\"small\",type:\"daterange\",placement:\"bottom-end\",placeholder:\"自定义时间\"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:\"timeVal\"}})],1),t._v(\" \"),a(\"el-form-item\",{staticClass:\"mr10\",attrs:{label:\"评价状态：\"}},[a(\"el-select\",{staticClass:\"selWidth\",attrs:{placeholder:\"请选择评价状态\",size:\"small\",clearable:\"\"},on:{change:t.seachList},model:{value:t.tableFrom.isReply,callback:function(e){t.$set(t.tableFrom,\"isReply\",e)},expression:\"tableFrom.isReply\"}},[a(\"el-option\",{attrs:{label:\"已回复\",value:\"1\"}}),t._v(\" \"),a(\"el-option\",{attrs:{label:\"未回复\",value:\"0\"}})],1)],1),t._v(\" \"),a(\"el-form-item\",{staticClass:\"mr10\",attrs:{label:\"商品搜索：\"}},[a(\"el-input\",{staticClass:\"selWidth\",attrs:{placeholder:\"请输入商品名称\",size:\"small\",clearable:\"\"},model:{value:t.tableFrom.productSearch,callback:function(e){t.$set(t.tableFrom,\"productSearch\",e)},expression:\"tableFrom.productSearch\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-search\",size:\"small\"},on:{click:t.seachList},slot:\"append\"})],1)],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:\"用户名称：\"}},[a(\"el-input\",{staticClass:\"selWidth\",attrs:{placeholder:\"请输入用户名称\",size:\"small\",clearable:\"\"},model:{value:t.tableFrom.nickname,callback:function(e){t.$set(t.tableFrom,\"nickname\",e)},expression:\"tableFrom.nickname\"}},[a(\"el-button\",{attrs:{slot:\"append\",icon:\"el-icon-search\",size:\"small\"},on:{click:t.seachList},slot:\"append\"})],1)],1)],1)],1),t._v(\" \"),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"admin:product:reply:save\"],expression:\"['admin:product:reply:save']\"}],attrs:{size:\"small\",type:\"primary\"},on:{click:t.add}},[t._v(\"添加虚拟评论\")])],1),t._v(\" \"),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.listLoading,expression:\"listLoading\"}],staticClass:\"table\",staticStyle:{width:\"100%\"},attrs:{data:t.tableData.data,size:\"mini\"}},[a(\"el-table-column\",{attrs:{prop:\"id\",label:\"ID\",width:\"50\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:\"商品信息\",\"min-width\":\"400\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[e.row.storeProduct?a(\"div\",{staticClass:\"demo-image__preview acea-row row-middle\"},[a(\"el-image\",{staticClass:\"mr10\",staticStyle:{width:\"30px\",height:\"30px\"},attrs:{src:e.row.storeProduct.image,\"preview-src-list\":[e.row.storeProduct.image]}}),t._v(\" \"),a(\"div\",{staticClass:\"info\"},[t._v(t._s(e.row.storeProduct.storeName))])],1):t._e()]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{prop:\"nickname\",label:\"用户名称\",\"min-width\":\"100\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{prop:\"productScore\",label:\"商品评分\",\"min-width\":\"90\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{prop:\"serviceScore\",label:\"服务评分\",\"min-width\":\"90\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:\"评价内容\",\"min-width\":\"210\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"div\",{staticClass:\"mb5 content_font\"},[t._v(t._s(e.row.comment))]),t._v(\" \"),e.row.pics.length&&e.row.pics[0]?[a(\"div\",{staticClass:\"demo-image__preview\"},t._l(e.row.pics,(function(t,e){return a(\"el-image\",{key:e,staticClass:\"mr5\",attrs:{src:t,\"preview-src-list\":[t]}})})),1)]:t._e()]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{prop:\"merchantReplyContent\",label:\"回复内容\",\"min-width\":\"250\"}}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:\"评价时间\",\"min-width\":\"120\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"span\",[t._v(\" \"+t._s(e.row.createTime))])]}}])}),t._v(\" \"),a(\"el-table-column\",{attrs:{label:\"操作\",\"min-width\":\"120\",fixed:\"right\",align:\"center\"},scopedSlots:t._u([{key:\"default\",fn:function(e){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"admin:product:reply:comment\"],expression:\"['admin:product:reply:comment']\"}],staticClass:\"mr10\",attrs:{type:\"text\",size:\"small\"},on:{click:function(a){return t.reply(e.row.id)}}},[t._v(\"回复\")]),t._v(\" \"),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"admin:product:reply:delete\"],expression:\"['admin:product:reply:delete']\"}],attrs:{type:\"text\",size:\"small\"},on:{click:function(a){return t.handleDelete(e.row.id,e.$index)}}},[t._v(\"删除\")])]}}])})],1),t._v(\" \"),a(\"div\",{staticClass:\"block\"},[a(\"el-pagination\",{attrs:{\"page-sizes\":[20,40,60,80],\"page-size\":t.tableFrom.limit,\"current-page\":t.tableFrom.page,layout:\"total, sizes, prev, pager, next, jumper\",total:t.tableData.total},on:{\"size-change\":t.handleSizeChange,\"current-change\":t.pageChange}})],1),t._v(\" \"),a(\"el-dialog\",{attrs:{title:\"提示\",visible:t.dialogVisible,width:\"700px\",\"z-index\":\"4\",\"before-close\":t.handleClose},on:{\"update:visible\":function(e){t.dialogVisible=e}}},[a(\"creat-comment\",{key:t.timer,on:{getList:t.seachList}})],1)],1)],1)},r=[],n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a(\"el-form\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],ref:\"formValidate\",staticClass:\"demo-formValidate\",attrs:{model:t.formValidate,rules:t.rules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"商品：\",prop:\"productId\"}},[a(\"div\",{staticClass:\"upLoadPicBox\",on:{click:t.changeGood}},[t.formValidate.productId?a(\"div\",{staticClass:\"pictrue\"},[a(\"img\",{attrs:{src:t.image}})]):a(\"div\",{staticClass:\"upLoad\"},[a(\"i\",{staticClass:\"el-icon-camera cameraIconfont\"})])])]),t._v(\" \"),a(\"el-form-item\",{attrs:{label:\"用户名称：\",prop:\"nickname\"}},[a(\"el-input\",{attrs:{type:\"text\"},model:{value:t.formValidate.nickname,callback:function(e){t.$set(t.formValidate,\"nickname\",e)},expression:\"formValidate.nickname \"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:\"评价文字：\",prop:\"comment\"}},[a(\"el-input\",{attrs:{type:\"textarea\"},model:{value:t.formValidate.comment,callback:function(e){t.$set(t.formValidate,\"comment\",e)},expression:\"formValidate.comment \"}})],1),t._v(\" \"),a(\"el-form-item\",{staticClass:\"productScore\",attrs:{label:\"商品分数：\",prop:\"productScore\"}},[a(\"el-rate\",{model:{value:t.formValidate.productScore,callback:function(e){t.$set(t.formValidate,\"productScore\",e)},expression:\"formValidate.productScore\"}})],1),t._v(\" \"),a(\"el-form-item\",{staticClass:\"productScore\",attrs:{label:\"服务分数：\",prop:\"serviceScore\"}},[a(\"el-rate\",{model:{value:t.formValidate.serviceScore,callback:function(e){t.$set(t.formValidate,\"serviceScore\",e)},expression:\"formValidate.serviceScore\"}})],1),t._v(\" \"),a(\"el-form-item\",{attrs:{label:\"用户头像：\",prop:\"avatar\"}},[a(\"div\",{staticClass:\"upLoadPicBox\",on:{click:function(e){return t.modalPicTap(\"1\")}}},[t.formValidate.avatar?a(\"div\",{staticClass:\"pictrue\"},[a(\"img\",{attrs:{src:t.formValidate.avatar}})]):a(\"div\",{staticClass:\"upLoad\"},[a(\"i\",{staticClass:\"el-icon-camera cameraIconfont\"})])])]),t._v(\" \"),a(\"el-form-item\",{attrs:{label:\"评价图片：\"}},[a(\"div\",{staticClass:\"acea-row\"},[t._l(t.pics,(function(e,i){return a(\"div\",{key:i,staticClass:\"pictrue\",attrs:{draggable:\"false\"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a(\"img\",{attrs:{src:e}}),t._v(\" \"),a(\"i\",{staticClass:\"el-icon-error btndel\",on:{click:function(e){return t.handleRemove(i)}}})])})),t._v(\" \"),t.pics<10?a(\"div\",{staticClass:\"upLoadPicBox\",on:{click:function(e){return t.modalPicTap(\"2\")}}},[a(\"div\",{staticClass:\"upLoad\"},[a(\"i\",{staticClass:\"el-icon-camera cameraIconfont\"})])]):t._e()],2)]),t._v(\" \"),a(\"el-form-item\",[a(\"el-button\",{attrs:{size:\"mini\",type:\"primary\",loading:t.loadingbtn},on:{click:function(e){return t.submitForm(\"formValidate\")}}},[t._v(\"提交\")]),t._v(\" \"),a(\"el-button\",{attrs:{size:\"mini\"},on:{click:function(e){return t.resetForm(\"formValidate\")}}},[t._v(\"重置\")])],1)],1)},s=[],l=a(\"73f5\"),o=a(\"61f7\");function c(t){return p(t)||u(t)||m(t)||d()}function d(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function m(t,e){if(t){if(\"string\"==typeof t)return f(t,e);var a={}.toString.call(t).slice(8,-1);return\"Object\"===a&&t.constructor&&(a=t.constructor.name),\"Map\"===a||\"Set\"===a?Array.from(t):\"Arguments\"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?f(t,e):void 0}}function u(t){if(\"undefined\"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t[\"@@iterator\"])return Array.from(t)}function p(t){if(Array.isArray(t))return f(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=Array(e);a<e;a++)i[a]=t[a];return i}var h={avatar:\"\",comment:\"\",nickname:\"\",pics:\"\",productId:\"\",productScore:null,serviceScore:null,sku:\"\"},g={name:\"creatComment\",props:{num:{type:Number,required:0}},data:function(){var t=function(t,e,a){if(!e)return a(new Error(\"商品分数不能为空\"));a()},e=function(t,e,a){if(!e)return a(new Error(\"服务分数不能为空\"));a()};return{loadingbtn:!1,loading:!1,pics:[],image:\"\",formValidate:Object.assign({},h),rules:{avatar:[{required:!0,message:\"请选择用户头像\",trigger:\"change\"}],productId:[{required:!0,message:\"请选择商品\",trigger:\"change\"}],comment:[{required:!0,message:\"请填写评价内容\",trigger:\"blur\"}],nickname:[{required:!0,message:\"请填写用户名称\",trigger:\"blur\"}],pics:[{required:!0,message:\"请选择评价图片\",trigger:\"change\"}],productScore:[{required:!0,validator:t,trigger:\"blur\"}],serviceScore:[{required:!0,validator:e,trigger:\"change\"}]}}},watch:{num:{handler:function(t){this.resetForm(\"formValidate\")},deep:!0}},methods:{changeGood:function(){var t=this;this.$modalGoodList((function(e){t.image=e.image,t.formValidate.productId=e.id,t.formValidate.sku=e.attrValue[0].suk}))},modalPicTap:function(t){var e=this;e.$modalUpload((function(a){\"1\"===t?e.formValidate.avatar=a[0].sattDir:a.map((function(t){e.pics.push(t.sattDir)}))}),t,\"store\")},handleRemove:function(t){this.pics.splice(t,1)},submitForm:Object(o[\"a\"])((function(t){var e=this;this.formValidate.pics=this.pics.length>0?JSON.stringify(this.pics):\"\",this.$refs[t].validate((function(t){if(!t)return!1;e.loadingbtn=!0,Object(l[\"s\"])(e.formValidate).then((function(){e.$message.success(\"新增成功\"),setTimeout((function(){e.$emit(\"getList\")}),600),e.loadingbtn=!1})).catch((function(){e.loadingbtn=!1}))}))})),resetForm:function(t){this.$refs[t].resetFields(),this.pics=[],this.formValidate.pics=\"\"},info:function(){var t=this;this.loading=!0,Object(l[\"u\"])(this.formValidate).then((function(){t.formValidate=res,t.loading=!1})).catch((function(){t.loading=!1}))},handleDragStart:function(t,e){this.dragging=e},handleDragEnd:function(t,e){this.dragging=null},handleDragOver:function(t){t.dataTransfer.dropEffect=\"move\"},handleDragEnter:function(t,e){if(t.dataTransfer.effectAllowed=\"move\",e!==this.dragging){var a=c(this.pics),i=a.indexOf(this.dragging),r=a.indexOf(e);a.splice.apply(a,[r,0].concat(c(a.splice(i,1)))),this.pics=a}}}},v=g,b=(a(\"e38d\"),a(\"2877\")),y=Object(b[\"a\"])(v,n,s,!1,null,\"1b6d8398\",null),_=y.exports,C=a(\"ed08\"),w=a(\"c24f\"),k={name:\"StoreComment\",filters:{formatDate:function(t){if(0!==t){var e=new Date(1e3*t);return Object(C[\"c\"])(e,\"yyyy-MM-dd hh:mm\")}}},components:{creatComment:_},data:function(){return{merCateList:[],props:{children:\"child\",label:\"name\",value:\"id\",emitPath:!1},fromList:this.$constants.fromList,tableData:{data:[],total:0},listLoading:!0,tableFrom:{page:1,limit:20,isReply:\"\",dateLimit:\"\",nickname:\"\",productSearch:\"\",isDel:!1},timeVal:[],loading:!1,uids:[],options:[],dialogVisible:!1,timer:\"\"}},watch:{$route:function(t,e){this.getList(),this.getCategorySelect()}},mounted:function(){this.getList(),this.getCategorySelect()},methods:{remoteMethod:function(t){var e=this;\"\"!==t?(this.loading=!0,setTimeout((function(){e.loading=!1,Object(w[\"D\"])({keywords:t,page:1,limit:10}).then((function(t){e.options=t.list}))}),200)):this.options=[]},seachList:function(){this.dialogVisible=!1,this.tableFrom.page=1,this.getList()},reply:function(t){var e=this;this.$prompt(\"回复内容\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",inputErrorMessage:\"请输入回复内容\",inputType:\"textarea\",inputPlaceholder:\"请输入回复内容\",inputValidator:function(t){if(!t)return\"输入不能为空\"}}).then((function(a){var i=a.value;Object(l[\"r\"])({ids:t,merchantReplyContent:i}).then((function(t){e.$message({type:\"success\",message:\"回复成功\"}),e.getList()}))})).catch((function(){e.$message({type:\"info\",message:\"取消输入\"})}))},selectChange:function(t){this.timeVal=[],this.tableFrom.page=1,this.getList()},getCategorySelect:function(){var t=this;Object(l[\"d\"])({status:-1,type:1}).then((function(e){t.merCateList=e})).catch((function(e){t.$message.error(e.message)}))},add:function(){this.dialogVisible=!0,this.timer=(new Date).getTime()},handleClose:function(){this.dialogVisible=!1},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(\",\"):\"\",this.tableFrom.page=1,this.getList()},handleDelete:function(t,e){var a=this;this.$modalSure().then((function(){Object(l[\"t\"])(t).then((function(){a.$message.success(\"删除成功\"),a.tableData.data.splice(e,1)}))}))},getList:function(){var t=this;this.listLoading=!0,this.tableFrom.uid=this.uids.join(\",\"),Object(l[\"v\"])(this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(){t.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},V=k,x=(a(\"2059\"),Object(b[\"a\"])(V,i,r,!1,null,\"7546f3b1\",null));e[\"default\"]=x.exports},e38d:function(t,e,a){\"use strict\";a(\"b3d7\")}}]);", "extractedComments": []}