-- H2数据库初始化脚本
-- 用于测试环境的表结构创建

-- 多平台订单分页拉取任务表
CREATE TABLE IF NOT EXISTS es_order_pull_task (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  platform VARCHAR(32) NOT NULL DEFAULT 'tiktok',
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NOT NULL,
  batch_no VARCHAR(64) NOT NULL,
  page_no INT NOT NULL DEFAULT 1,
  next_page_token VARCHAR(255),
  status TINYINT NOT NULL DEFAULT 0,
  retry_count INT NOT NULL DEFAULT 0,
  last_pull_time TIMESTAMP,
  remark VARCHAR(255),
  create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 多平台订单分页任务生成进度表
CREATE TABLE IF NOT EXISTS es_order_pull_progress (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  platform VARCHAR(32) NOT NULL DEFAULT 'tiktok',
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NOT NULL,
  batch_no VARCHAR(64) NOT NULL,
  last_page_no INT NOT NULL DEFAULT 0,
  last_page_token VARCHAR(255),
  status TINYINT NOT NULL DEFAULT 0,
  remark VARCHAR(255),
  update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uniq_platform_time_page_batch ON es_order_pull_task (platform, start_time, end_time, batch_no, page_no);
CREATE INDEX IF NOT EXISTS idx_platform_status_batch ON es_order_pull_task (platform, status, batch_no);
CREATE INDEX IF NOT EXISTS idx_platform_time_batch ON es_order_pull_task (platform, start_time, end_time, batch_no);

CREATE UNIQUE INDEX IF NOT EXISTS uniq_platform_time_range_batch ON es_order_pull_progress (platform, start_time, end_time, batch_no);
CREATE INDEX IF NOT EXISTS idx_platform_status_batch_progress ON es_order_pull_progress (platform, status, batch_no);
CREATE INDEX IF NOT EXISTS idx_platform_time_batch_progress ON es_order_pull_progress (platform, start_time, end_time, batch_no); 