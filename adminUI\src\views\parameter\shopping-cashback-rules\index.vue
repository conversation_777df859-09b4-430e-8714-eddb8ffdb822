<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column 
          :label="$t('parameter.rewardRules.rewardTemplateId')" 
          min-width="120" 
          prop="id" 
        />
        <el-table-column 
          :label="$t('parameter.rewardRules.rewardTemplateName')" 
          min-width="200" 
          prop="name" 
        />
        <el-table-column
          :label="$t('parameter.shoppingCashbackRules.directCashbackRate')"
          min-width="220"
          prop="value1"
        />
        <el-table-column
          :label="$t('parameter.shoppingCashbackRules.secondLevelCashbackRate')"
          min-width="200"
          prop="value2"
        />
        <el-table-column
          :label="$t('parameter.shoppingCashbackRules.thirdLevelCashbackRate')"
          min-width="200"
          prop="value3"
        />
        <el-table-column
          fixed="right"
          :label="$t('parameter.withdrawalFee.operation')"
          width="80"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              >{{ $t("parameter.withdrawalFee.edit") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="dialogTitle"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="form"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('parameter.rewardRules.rewardTemplateId')"
            prop="id"
          >
            <el-input v-model="form.id" size="small" disabled />
          </el-form-item>
          <el-form-item
            :label="$t('platformCashbackRate.platformCashbackRate') + '(%)：'"
            prop="platform_cash_back_rate"
          >
            <el-input
              v-model="form.platform_cash_back_rate"
              size="small"
              :placeholder="$t('parameter.withdrawalFee.placeholder.couponId')"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="handleCancle">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "ShoppingCashbackRules",
  data() {
    return {
      loading: false,
      tableData: [],
      dialogTitle: this.$t("platformCashbackRate.addTitle"),
      dialogFormVisible: false,
      form: {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: ""
      },
      rules: {
        value1: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],

        value2: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],

        value3: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList(num) {
      this.loading = true;
      configInfo({ formId: 113 })
        .then(res => {
          if (res) {
            this.tableData.push({
              id: "113",
              name: this.$t("parameter.shoppingCashbackRules.normalUserRule"),
              value1: res.normal_cash_back_rate,
              value2: res.normal_cash_back_rate_2l,
              value3: res.normal_cash_back_rate_3l
            });

            configInfo({ formId: 114 })
              .then(ress => {
                if (ress) {
                  this.tableData.push({
                    id: "114",
                    name: this.$t("parameter.shoppingCashbackRules.agentTeamRule"),
                    value1: ress.agent_cash_back_rate,
                    value2: ress.agent_cash_back_rate_2l,
                    value3: ress.agent_cash_back_rate_3l
                  });
                  configInfo({ formId: 115 })
                    .then(ress => {
                      if (ress) {
                        this.tableData.push({
                          id: "115",
                          name: this.$t("parameter.shoppingCashbackRules.partnerTeamRule"),
                          value1: ress.partner_cash_back_rate,
                          value2: ress.partner_cash_back_rate_2l,
                          value3: ress.partner_cash_back_rate_3l
                        });
                        this.loading = false;
                      }
                    })
                    .catch(() => {
                      this.loading = false;
                    });
                }
              })
              .catch(() => {
                this.loading = false;
              });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleEdit(row) {
      this.dialogTitle = this.$t("platformCashbackRate.editTitle");
      this.dialogFormVisible = true;
      this.form.id = row.id;
      this.form.name = row.name;
      this.form.value1 = row.value1;
      this.form.value2 = row.value2;
      this.form.value3 = row.value3;
    },
    handleCancle() {
      (this.form = {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: ""
      }),
        (this.dialogFormVisible = false);
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        let param = {};
        if (this.form.id === "113") {
          param = {
            id: this.form.id,
            sort: 1,
            status: true,
            fields: [
              {
                name: "normal_cash_back_rate",
                value: this.form.value1,
                title: "normal_cash_back_rate"
              },
              {
                name: "normal_cash_back_rate_2l",
                value: this.form.value2,
                title: "normal_cash_back_rate_2l"
              },
              {
                name: "normal_cash_back_rate_3l",
                value: this.form.value3,
                title: "normal_cash_back_rate_3l"
              }
            ]
          };
        } else if (this.form.id === "114") {
          param = {
            id: this.form.id,
            sort: 1,
            status: true,
            fields: [
              {
                name: "agent_cash_back_rate",
                value: this.form.value1,
                title: "agent_cash_back_rate"
              },
              {
                name: "agent_cash_back_rate_2l",
                value: this.form.value2,
                title: "agent_cash_back_rate_2l"
              },
              {
                name: "agent_cash_back_rate_3l",
                value: this.form.value3,
                title: "agent_cash_back_rate_3l"
              }
            ]
          };
        } else {
          param = {
            id: this.form.id,
            sort: 1,
            status: true,
            fields: [
              {
                name: "partner_cash_back_rate",
                value: this.form.value1,
                title: "partner_cash_back_rate"
              },
              {
                name: "partner_cash_back_rate_2l",
                value: this.form.value2,
                title: "partner_cash_back_rate_2l"
              },
              {
                name: "partner_cash_back_rate_3l",
                value: this.form.value3,
                title: "partner_cash_back_rate_3l"
              }
            ]
          };
        }
        configSaveForm(param).then(res => {
          this.$message.success(this.$t("common.operationSuccess"));

          this.handleCancle();
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>