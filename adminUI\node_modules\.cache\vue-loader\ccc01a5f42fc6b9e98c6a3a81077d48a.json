{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue?vue&type=template&id=64cb5309&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1754275430524}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<el-dialog\n  v-if=\"dialogVisible\"\n  :title=\"$t('user.grade.form.dialogTitle')\"\n  :visible.sync=\"dialogVisible\"\n  width=\"500px\"\n  :before-close=\"handleClose\" >\n  <el-form :model=\"user\" :rules=\"rules\" ref=\"user\" label-width=\"100px\" class=\"demo-ruleForm\" v-loading=\"loading\">\n    <el-form-item :label=\"$t('user.grade.form.levelNameLabel')\" prop=\"name\">\n      <el-input v-model=\"user.name\" :placeholder=\"$t('user.grade.form.levelNamePlaceholder')\"></el-input>\n    </el-form-item>\n    <el-form-item :label=\"$t('user.grade.form.gradeLabel')\" prop=\"grade\">\n      <el-input  v-model.number=\"user.grade\" :placeholder=\"$t('user.grade.form.gradePlaceholder')\"></el-input>\n    </el-form-item>\n    <el-form-item :label=\"$t('user.grade.form.discountLabel')\" prop=\"discount\">\n      <el-input-number  :min=\"0\" :max=\"100\" step-strictly  v-model=\"user.discount\" :placeholder=\"$t('user.grade.form.discountPlaceholder')\"></el-input-number>\n    </el-form-item>\n    <el-form-item :label=\"$t('user.grade.form.experienceLabel')\" prop=\"experience\">\n      <el-input-number  v-model.number=\"user.experience\" :placeholder=\"$t('user.grade.form.experiencePlaceholder')\" :min=\"0\" step-strictly></el-input-number>\n    </el-form-item>\n    <el-form-item :label=\"$t('user.grade.form.iconLabel')\" prop=\"icon\">\n      <div class=\"upLoadPicBox\" @click=\"modalPicTap('1', 'icon')\">\n        <div v-if=\"user.icon\" class=\"pictrue\"><img :src=\"user.icon\"></div>\n        <div v-else-if=\"formValidate.icon\" class=\"pictrue\"><img :src=\"formValidate.icon\"></div>\n        <div v-else class=\"upLoad\">\n          <i class=\"el-icon-camera cameraIconfont\" />\n        </div>\n      </div>\n    </el-form-item>\n  </el-form>\n  <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"resetForm('user')\">{{ $t('user.grade.form.cancel') }}</el-button>\n    <el-button type=\"primary\" @click=\"submitForm('formValidate')\" v-hasPermi=\"['admin:system:user:level:update','admin:system:user:level:save']\">{{ $t('user.grade.form.confirm') }}</el-button>\n  </span>\n</el-dialog>\n", null]}