{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue", "mtime": 1754269254569}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { levelUpgradeOrderListApi, cancelLevelUpgradeOrderApi } from '@/api/user';\nexport default {\n  name: 'UserLevelUpgrade',\n  data: function data() {\n    return {\n      searchForm: {\n        orderNo: '',\n        orderStatus: '',\n        page: 1,\n        limit: 20\n      },\n      tableData: [],\n      total: 0,\n      listLoading: false,\n      detailVisible: false,\n      currentOrder: null\n    };\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 延迟执行，确保组件完全挂载\n    this.$nextTick(function () {\n      _this.getList();\n    });\n  },\n  methods: {\n    // 获取订单列表\n    getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n\n      // 暂时使用模拟数据进行测试\n      setTimeout(function () {\n        _this2.tableData = [{\n          orderNo: 'UG202501030001',\n          uid: 1001,\n          fromLevelId: 1,\n          toLevelId: 2,\n          upgradePrice: 100000,\n          paymentMethod: 'balance',\n          orderStatus: 0,\n          createTime: '2025-01-03 10:00:00',\n          payTime: null,\n          remark: '测试订单'\n        }];\n        _this2.total = 1;\n        _this2.listLoading = false;\n      }, 500);\n\n      // 注释掉真实API调用，避免权限问题\n      /*\r\n      const params = {\r\n        page: this.searchForm.page,\r\n        limit: this.searchForm.limit\r\n      }\r\n      if (this.searchForm.orderNo) {\r\n        params.orderNo = this.searchForm.orderNo\r\n      }\r\n      if (this.searchForm.orderStatus !== '') {\r\n        params.orderStatus = this.searchForm.orderStatus\r\n      }\r\n        levelUpgradeOrderListApi(params).then(res => {\r\n        // 确保数据结构正确\r\n        if (res && res.data) {\r\n          this.tableData = res.data.list || []\r\n          this.total = res.data.total || 0\r\n        } else {\r\n          this.tableData = []\r\n          this.total = 0\r\n        }\r\n        this.listLoading = false\r\n      }).catch(error => {\r\n        console.error('获取订单列表失败:', error)\r\n        this.tableData = []\r\n        this.total = 0\r\n        this.listLoading = false\r\n        this.$message.error(this.$t('user.levelUpgrade.getListFailed'))\r\n      })\r\n      */\n    },\n    // 重置搜索\n    resetSearch: function resetSearch() {\n      this.searchForm = {\n        orderNo: '',\n        orderStatus: '',\n        page: 1,\n        limit: 20\n      };\n      this.getList();\n    },\n    // 分页大小改变\n    handleSizeChange: function handleSizeChange(val) {\n      this.searchForm.limit = val;\n      this.searchForm.page = 1;\n      this.getList();\n    },\n    // 当前页改变\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.searchForm.page = val;\n      this.getList();\n    },\n    // 取消订单\n    cancelOrder: function cancelOrder(orderNo) {\n      var _this3 = this;\n      this.$confirm(this.$t('user.levelUpgrade.confirmCancel'), this.$t('common.confirm'), {\n        confirmButtonText: this.$t('common.confirm'),\n        cancelButtonText: this.$t('common.cancel'),\n        type: 'warning'\n      }).then(function () {\n        cancelLevelUpgradeOrderApi(orderNo).then(function () {\n          _this3.$message.success(_this3.$t('user.levelUpgrade.cancelSuccess'));\n          _this3.getList();\n        }).catch(function (error) {\n          console.error('取消订单失败:', error);\n          _this3.$message.error(_this3.$t('user.levelUpgrade.cancelFailed'));\n        });\n      }).catch(function () {\n        // 用户取消操作\n      });\n    },\n    // 查看详情\n    viewDetail: function viewDetail(order) {\n      this.currentOrder = order;\n      this.detailVisible = true;\n    },\n    // 获取等级名称\n    getLevelName: function getLevelName(levelId) {\n      return this.$t(\"user.levelUpgrade.levelNames.\".concat(levelId)) || this.$t('user.levelUpgrade.unknownLevel');\n    },\n    // 获取支付方式名称\n    getPaymentMethodName: function getPaymentMethodName(method) {\n      var methodMap = {\n        'balance': this.$t('user.levelUpgrade.balancePayment')\n      };\n      return methodMap[method] || method;\n    },\n    // 获取状态名称\n    getStatusName: function getStatusName(status) {\n      var statusMap = {\n        0: this.$t('user.levelUpgrade.pending'),\n        1: this.$t('user.levelUpgrade.paid'),\n        2: this.$t('user.levelUpgrade.cancelled'),\n        3: this.$t('user.levelUpgrade.refunded')\n      };\n      return statusMap[status] || this.$t('user.levelUpgrade.unknownStatus');\n    },\n    // 获取状态颜色\n    getStatusColor: function getStatusColor(status) {\n      var colorMap = {\n        0: 'warning',\n        1: 'success',\n        2: 'info',\n        3: 'danger'\n      };\n      return colorMap[status] || 'info';\n    }\n  }\n};", null]}