(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1c63144c"],{"04f8":function(t,e,i){},"1fb2":function(t,e,i){"use strict";i("04f8")},6606:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUAAAAAyCAIAAACib5WDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjE1NEJCMUE0NzZGNDExRTVBOTBBQTZFOEFEMjc4NTkzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjE1NEJCMUE1NzZGNDExRTVBOTBBQTZFOEFEMjc4NTkzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MTU0QkIxQTI3NkY0MTFFNUE5MEFBNkU4QUQyNzg1OTMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MTU0QkIxQTM3NkY0MTFFNUE5MEFBNkU4QUQyNzg1OTMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4yWLBJAAABuklEQVR42uzcu0ocURzA4XWxMIWiQhJwtVhxMW0wEkWj+AwWgm9gJfgggpVPoElEUwUCKRNFJaQWsygWXvAKXlBZGw8KIiIJmWFnGPg+pjiryMIffpxzRLemUqnkUlUul0ulUg74f3kjAAEDAgYEDAIGBAwIGBAwCBgQMCBgEHAMlZub8BglJK825s/vHxzOfl4Ii9GR4devXhooZGYHPjo+mfk0f3l5FZ6wCC8NFDKzA+fz+aHB/scvDRQyE3BzU2N4DBEyeYQGBAxU5wi9sbm1+ut3W2shznucnp296Sx1tBeNGxINeG39z+jIcPy3+Tj3RcCQ9BG6ob7+fjE5NR2eaOugtdBi1pD0Dvzg6vo68hpIOeAXdXWR10CV1Pz9c6F/LC4P9PfGf5ufSysf+nqe/ZbPhYZq3YGfiHD7BdI/Qrv9QuYDdvsFd2B3YEjjDgxk+Aidu/sd1T9vueEUPTE+ZrhgBwai7sA7u3tPvhJtaz0/vzBrSDrg7ndvv377/vAX0dFs7+y+7+4ya0g64I72ov8iAndgQMCAgEHAgIABAYOAAQEDAgYEDAIGBAwIGBAwCBhIy60AAwBiy5esmSYLKgAAAABJRU5ErkJggg=="},f0da:function(t,e,i){t.exports=i.p+"static/img/mobilehead.1c931282.png"},f6e6:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("el-row",{attrs:{gutter:30}},[a("el-col",t._b({staticClass:"left mb15 ml40"},"el-col",t.grid,!1),[a("div",[a("img",{staticClass:"top",attrs:{src:i("f0da")}}),t._v(" "),a("img",{staticClass:"bottom",attrs:{src:i("6606")}}),t._v(" "),a("div",{staticStyle:{background:"#F4F5F9","min-height":"438px",position:"absolute",top:"63px",width:"320px"}}),t._v(" "),a("div",{staticClass:"textbot"},[t._l(t.list,(function(e,i){return a("div",{key:i,staticClass:"li",class:{active:e===t.formValidate}},[a("div",[a("div",{staticClass:"add",on:{click:function(a){return t.add(e,i)}}},[a("i",{staticClass:"el-icon-plus"}),t._v(" "),a("div",{staticClass:"arrow"})]),t._v(" "),a("div",{staticClass:"tianjia"},t._l(e.sub_button,(function(e,r){return a("div",{key:r,staticClass:"addadd menuBox",class:{active:e===t.formValidate},on:{click:function(a){return t.gettem(e,r,i)}}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.name,placement:"top-start"}},[a("el-button",[t._v(t._s(e.name||"二级菜单"))])],1)],1)})),0)]),t._v(" "),a("div",{staticClass:"text menuBox",on:{click:function(a){return t.gettem(e,i,null)}}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.name,placement:"top-start"}},[a("el-button",[t._v(t._s(e.name||"一级菜单"))])],1)],1)])})),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.list.length<3,expression:"list.length < 3"}],staticClass:"li"},[a("div",{staticClass:"text",on:{click:t.addtext}},[a("i",{staticClass:"el-icon-plus"})])])],2)])]),t._v(" "),a("el-col",{attrs:{xl:11,lg:12,md:22,sm:22,xs:22}},[null!==t.checkedMenuId?a("div",[a("div",{staticClass:"dividerTitle acea-row row-between row-bottom"},[a("span",{staticClass:"title"},[t._v("菜单信息")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:wechat:menu:public:delete"],expression:"['admin:wechat:menu:public:delete']"}],attrs:{slot:"extra",size:"small",type:"danger"},on:{click:t.deltMenus},slot:"extra"},[t._v("删除")]),t._v(" "),a("el-divider")],1),t._v(" "),a("el-col",{staticClass:"userAlert",attrs:{span:24}},[a("div",{staticClass:"box-card right"},[a("el-alert",{staticClass:"mb15",attrs:{title:"已添加子菜单，仅可设置菜单名称",type:"success","show-icon":""}}),t._v(" "),a("el-form",{ref:"formValidate",staticClass:"mt20",attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"菜单名称",prop:"name"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写菜单名称"},model:{value:t.formValidate.name,callback:function(e){t.$set(t.formValidate,"name",e)},expression:"formValidate.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"规则状态",prop:"type"}},[a("el-select",{staticClass:"spwidth",attrs:{placeholder:"请选择规则状态"},model:{value:t.formValidate.type,callback:function(e){t.$set(t.formValidate,"type",e)},expression:"formValidate.type"}},[a("el-option",{attrs:{value:"click",label:"关键字"}},[t._v("关键字")]),t._v(" "),a("el-option",{attrs:{value:"view",label:"跳转网页"}},[t._v("跳转网页")]),t._v(" "),a("el-option",{attrs:{value:"miniprogram",label:"小程序"}},[t._v("小程序")])],1)],1),t._v(" "),"click"===t.formValidate.type?a("div",[a("el-form-item",{attrs:{label:"关键字",prop:"key"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写关键字"},model:{value:t.formValidate.key,callback:function(e){t.$set(t.formValidate,"key",e)},expression:"formValidate.key"}})],1)],1):t._e(),t._v(" "),"miniprogram"===t.formValidate.type?a("div",[a("el-form-item",{attrs:{label:"appid",prop:"appid"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写appid"},model:{value:t.formValidate.appid,callback:function(e){t.$set(t.formValidate,"appid",e)},expression:"formValidate.appid"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"备用网页",prop:"url"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写备用网页"},model:{value:t.formValidate.url,callback:function(e){t.$set(t.formValidate,"url",e)},expression:"formValidate.url"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"小程序路径",prop:"pagepath"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写小程序路径"},model:{value:t.formValidate.pagepath,callback:function(e){t.$set(t.formValidate,"pagepath",e)},expression:"formValidate.pagepath"}})],1)],1):t._e(),t._v(" "),"view"===t.formValidate.type?a("div",[a("el-form-item",{attrs:{label:"跳转地址",prop:"url"}},[a("el-input",{staticClass:"spwidth",attrs:{placeholder:"请填写跳转地址"},model:{value:t.formValidate.url,callback:function(e){t.$set(t.formValidate,"url",e)},expression:"formValidate.url"}})],1)],1):t._e()],1)],1)])],1):t._e(),t._v(" "),t.isTrue?a("el-col",{attrs:{span:24}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:wechat:menu:public:create"],expression:"['admin:wechat:menu:public:create']"}],staticStyle:{display:"block",margin:"10px auto"},attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submenus("formValidate")}}},[t._v("保存并发布")])],1):t._e()],1)],1)],1)],1)},r=[],n=i("ffd2"),s=i("61f7");function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",r=i.toStringTag||"@@toStringTag";function n(i,a,r,n){var l=a&&a.prototype instanceof c?a:c,u=Object.create(l.prototype);return o(u,"_invoke",function(i,a,r){var n,l,o,c=0,u=r||[],d=!1,p={p:0,n:0,v:t,a:m,f:m.bind(t,4),d:function(e,i){return n=e,l=0,o=t,p.n=i,s}};function m(i,a){for(l=i,o=a,e=0;!d&&c&&!r&&e<u.length;e++){var r,n=u[e],m=p.p,h=n[2];i>3?(r=h===a)&&(o=n[(l=n[4])?5:(l=3,3)],n[4]=n[5]=t):n[0]<=m&&((r=i<2&&m<n[1])?(l=0,p.v=a,p.n=n[1]):m<h&&(r=i<3||n[0]>a||a>h)&&(n[4]=i,n[5]=a,p.n=h,l=0))}if(r||i>1)return s;throw d=!0,a}return function(r,u,h){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&m(u,h),l=u,o=h;(e=l<2?t:o)||!d;){n||(l?l<3?(l>1&&(p.n=-1),m(l,o)):p.n=o:p.v=o);try{if(c=2,n){if(l||(r="next"),e=n[r]){if(!(e=e.call(n,o)))throw TypeError("iterator result is not an object");if(!e.done)return e;o=e.value,l<2&&(l=0)}else 1===l&&(e=n.return)&&e.call(n),l<2&&(o=TypeError("The iterator does not provide a '"+r+"' method"),l=1);n=t}else if((e=(d=p.n<0)?o:i.call(a,p))!==s)break}catch(e){n=t,l=1,o=e}finally{c=1}}return{value:e,done:d}}}(i,r,n),!0),u}var s={};function c(){}function u(){}function d(){}e=Object.getPrototypeOf;var p=[][a]?e(e([][a]())):(o(e={},a,(function(){return this})),e),m=d.prototype=c.prototype=Object.create(p);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,o(t,r,"GeneratorFunction")),t.prototype=Object.create(m),t}return u.prototype=d,o(m,"constructor",d),o(d,"constructor",u),u.displayName="GeneratorFunction",o(d,r,"GeneratorFunction"),o(m),o(m,r,"Generator"),o(m,a,(function(){return this})),o(m,"toString",(function(){return"[object Generator]"})),(l=function(){return{w:n,m:h}})()}function o(t,e,i,a){var r=Object.defineProperty;try{r({},"",{})}catch(t){r=0}o=function(t,e,i,a){function n(e,i){o(t,e,(function(t){return this._invoke(e,i,t)}))}e?r?r(t,e,{value:i,enumerable:!a,configurable:!a,writable:!a}):t[e]=i:(n("next",0),n("throw",1),n("return",2))},o(t,e,i,a)}function c(t,e,i,a,r,n,s){try{var l=t[n](s),o=l.value}catch(t){return void i(t)}l.done?e(o):Promise.resolve(o).then(a,r)}function u(t){return function(){var e=this,i=arguments;return new Promise((function(a,r){var n=t.apply(e,i);function s(t){c(n,a,r,s,l,"next",t)}function l(t){c(n,a,r,s,l,"throw",t)}s(void 0)}))}}var d={name:"WechatMenus",data:function(){return{grid:{xl:8,lg:8,md:8,sm:8,xs:24},grid2:{xl:16,lg:16,md:16,sm:16,xs:24},modal2:!1,formValidate:{name:"",type:"click",appid:"",url:"",key:"",pagepath:"",id:0},ruleValidate:{name:[{required:!0,message:"请填写菜单名称",trigger:"blur"}],key:[{required:!0,message:"请填写关键字",trigger:"blur"}],appid:[{required:!0,message:"请填写appid",trigger:"blur"}],pagepath:[{required:!0,message:"请填写小程序路径",trigger:"blur"}],url:[{required:!0,message:"请填写跳转地址",trigger:"blur"}],type:[{required:!0,message:"请选择规则状态",trigger:"change"}]},parentMenuId:null,list:[],checkedMenuId:null,isTrue:!1}},mounted:function(){if(this.getMenus(),!this.list.length)return this.formValidate;this.formValidate=this.list[this.activeClass]},methods:{defaultMenusData:function(){return{type:"click",name:"",sub_button:[]}},defaultChildData:function(){return{type:"click",name:""}},getMenus:function(){var t=this;Object(n["m"])().then(function(){var e=u(l().m((function e(i){var a;return l().w((function(e){while(1)switch(e.n){case 0:a=i.menu,t.list=a.button;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},submenus:Object(s["a"])((function(t){var e=this;this.isTrue&&!this.checkedMenuId&&0!==this.checkedMenuId?this.putData():this.$refs[t].validate((function(t){if(t)e.putData();else if(!e.check())return!1}))})),putData:function(){var t=this,e={button:this.list};Object(n["l"])(e).then(function(){var e=u(l().m((function e(i){return l().w((function(e){while(1)switch(e.n){case 0:t.$message.success("提交成功"),t.checkedMenuId=null,t.formValidate={},t.isTrue=!1;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},gettem:function(t,e,i){this.checkedMenuId=e,this.formValidate=t,this.parentMenuId=i,this.isTrue=!0},add:function(t,e){if(!this.check())return!1;if(t.sub_button.length<5){var i=this.defaultChildData(),a=t.sub_button.length;t.sub_button.push(i),this.formValidate=i,this.checkedMenuId=a,this.parentMenuId=e,this.isTrue=!0}},addtext:function(){if(!this.check())return!1;var t=this.defaultMenusData(),e=this.list.length;this.list.push(t),this.formValidate=t,this.checkedMenuId=e,this.parentMenuId=null,this.isTrue=!0},check:function(){var t=/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;return null===this.checkedMenuId||(!this.isTrue||(this.formValidate.name?"click"!==this.formValidate.type||this.formValidate.key?"view"!==this.formValidate.type||t.test(this.formValidate.url)?!!("miniprogram"!==this.formValidate.type||this.formValidate.appid&&this.formValidate.pagepath&&this.formValidate.url)||(this.$message.warning("请填写完整小程序配置!"),!1):(this.$message.warning("请输入正确的跳转地址!"),!1):(this.$message.warning("请输入关键字!"),!1):(this.$message.warning("请输入按钮名称!"),!1)))},deltMenus:function(){var t=this;this.isTrue?this.$modalSure().then((function(){t.del()})):this.$message.warning("请选择菜单!")},del:function(){null===this.parentMenuId?this.list.splice(this.checkedMenuId,1):this.list[this.parentMenuId].sub_button.splice(this.checkedMenuId,1),this.parentMenuId=null,this.formValidate={name:"",type:"click",appid:"",url:"",key:"",pagepath:"",id:0},this.isTrue=!1,this.modal2=!1,this.checkedMenuId=null,this.$refs["formValidate"].resetFields(),this.submenus("formValidate")}}},p=d,m=(i("1fb2"),i("2877")),h=Object(m["a"])(p,a,r,!1,null,"ca7660d8",null);e["default"]=h.exports}}]);