{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue?vue&type=style&index=0&id=ca7660d8&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.menuBox{\n  ::v-deep.el-button{\n    border: none;\n    background: bottom;\n    padding: 0 !important;\n    overflow:hidden;\n    text-overflow:ellipsis;\n    white-space:nowrap;\n    width: 100%;\n  }\n}\n*{\n  -moz-user-select: none; /*火狐*/\n  -webkit-user-select: none; /*webkit浏览器*/\n  -ms-user-select: none; /*IE10*/\n  -khtml-user-select: none; /*早期浏览器*/\n  user-select: none;\n}\n.title{\n  margin-bottom: -19px !important;\n  padding-bottom: 17px !important;\n}\n.left {\n  min-width: 390px;\n  min-height: 550px;\n  position: relative;\n  padding-left: 40px;\n}\n\n.top {\n  position: absolute;\n  top: 0px;\n}\n\n.bottom {\n  position: absolute;\n  bottom: 0px;\n}\n\n.textbot {\n  position: absolute;\n  bottom: 0px;\n  left: 59px;\n  width: 100%;\n}\n.active {\n  border: 1px solid #44B549 !important;\n  color: #44B549 !important;\n}\n.li {\n  float: left;\n  width: 93px;\n  line-height: 48px;\n  border: 1px solid #E7E7EB;\n  background: #FAFAFA;\n  text-align: center;\n  cursor: pointer;\n  color: #999;\n  position: relative;\n}\n.text{\n  height: 50px;\n  overflow:hidden;\n  text-overflow:ellipsis;\n  white-space:nowrap;\n}\n.text:hover {\n  color: #000;\n}\n\n.add {\n  position: absolute;\n  bottom: 65px;\n  width: 100%;\n  line-height: 48px;\n  border: 1px solid #E7E7EB;\n  background: #FAFAFA;\n}\n.arrow {\n  position: absolute;\n  bottom: -16px;\n  left: 36px;\n  /* 圆角的位置需要细心调试哦 */\n  width: 0;\n  height: 0;\n  font-size: 0;\n  border: solid 8px;\n  border-color:#fff #F4F5F9 #F4F5F9 #F4F5F9;\n}\n.tianjia {\n  position: absolute;\n  bottom: 115px;\n  width: 100%;\n  line-height: 48px;\n  background: #FAFAFA;\n}\n.addadd {\n  width: 100%;\n  line-height: 48px;\n  border: 1px solid #E7E7EB;\n  background: #FAFAFA;\n  height: 48px;\n  padding: 0 5px;\n  box-sizing: border-box;\n}\n.right {\n  background: #fff;\n  min-height: 400px;\n}\n.spwidth{\n  width: 100%;\n}\n", null]}