(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d231578"],{efab:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{attrs:{size:"small","label-width":"120px"}},[a("el-form-item",{staticClass:"width100",attrs:{label:"时间选择："}},[a("el-radio-group",{staticClass:"mr20",attrs:{type:"button",size:"small"},on:{change:function(e){return t.selectChange(t.tableFrom.dateLimit)}},model:{value:t.tableFrom.dateLimit,callback:function(e){t.$set(t.tableFrom,"dateLimit",e)},expression:"tableFrom.dateLimit"}},t._l(t.fromList.fromTxt,(function(e,l){return a("el-radio-button",{key:l,attrs:{label:e.val}},[t._v(t._s(e.text)+"\n              ")])})),1),t._v(" "),a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",size:"small",type:"daterange",placement:"bottom-end",placeholder:"自定义时间"},on:{change:t.onchangeTime},model:{value:t.timeVal,callback:function(e){t.timeVal=e},expression:"timeVal"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"用户微信昵称："}},[a("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入用户昵称",size:"small"},model:{value:t.tableFrom.keywords,callback:function(e){t.$set(t.tableFrom,"keywords",e)},expression:"tableFrom.keywords"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:function(e){return t.getList(1)}},slot:"append"})],1)],1)],1)],1)]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table",attrs:{data:t.tableData.data,size:"small","highlight-current-row":"","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"60"}}),t._v(" "),a("el-table-column",{attrs:{prop:"title",label:"标题","min-width":"130"}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",prop:"balance",label:"积分余量","min-width":"120","sort-method":function(t,e){return t.balance-e.balance}}}),t._v(" "),a("el-table-column",{attrs:{sortable:"",label:"明细数字","min-width":"120",prop:"integral","sort-method":function(t,e){return t.integral-e.integral}}}),t._v(" "),a("el-table-column",{attrs:{label:"备注","min-width":"120",prop:"mark"}}),t._v(" "),a("el-table-column",{attrs:{label:"用户昵称","min-width":"120",prop:"nickName"}}),t._v(" "),a("el-table-column",{attrs:{prop:"updateTime",label:"\t添加时间","min-width":"150"}})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.tableFrom.limit,"current-page":t.tableFrom.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1)],1)},i=[],s=a("b7be"),o=a("0f56"),n={components:{cardsData:o["a"]},data:function(){return{loading:!1,options:[],fromList:this.$constants.fromList,listLoading:!1,tableData:{data:[],total:0},tableFrom:{page:1,limit:20,dateLimit:"",keywords:""},userIdList:[],userList:[],timeVal:[],values:[]}},mounted:function(){this.getList()},methods:{seachList:function(){this.tableFrom.page=1,this.getList()},selectChange:function(t){this.tableFrom.dateLimit=t,this.tableFrom.page=1,this.timeVal=[],this.getList()},onchangeTime:function(t){this.timeVal=t,this.tableFrom.dateLimit=t?this.timeVal.join(","):"",this.tableFrom.page=1,this.getList()},getList:function(){var t=this;this.listLoading=!0,Object(s["z"])({limit:this.tableFrom.limit,page:this.tableFrom.page},this.tableFrom).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},pageChange:function(t){this.tableFrom.page=t,this.getList()},handleSizeChange:function(t){this.tableFrom.limit=t,this.getList()}}},r=n,m=a("2877"),c=Object(m["a"])(r,l,i,!1,null,"596e394e",null);e["default"]=c.exports}}]);