package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推广佣金明细
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserCommissionResponse对象", description = "推广佣金明细")
public class UserCommissionResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预计总收入")
    private BigDecimal estimatedTotalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "预计今日总收入")
    private BigDecimal estimatedTodayAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "待入账金额")
    private BigDecimal pendingAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已入账金额")
    private BigDecimal receivedAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "累计提现金额")
    private BigDecimal withdrawableAmount = BigDecimal.ZERO;
}
