{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\detail\\index.vue?vue&type=template&id=44a9d692&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\detail\\index.vue", "mtime": 1754382949115}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('el-tabs',{staticClass:\"mb20\",on:{\"tab-click\":function($event){return _vm.getList(_vm.tableFromType, 1)}},model:{value:(_vm.tableFromType),callback:function ($$v) {_vm.tableFromType=$$v},expression:\"tableFromType\"}},[_c('el-tab-pane',{attrs:{\"label\":_vm.$t('financial.detail.purchaseDetail'),\"name\":\"purchase\"}}),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":_vm.$t('financial.detail.tradeDetail'),\"name\":\"trade\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"container mt-1\"},[(_vm.tableFromType === 'purchase')?_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.purchaseFrom),callback:function ($$v) {_vm.purchaseFrom=$$v},expression:\"purchaseFrom\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.rechargeType') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all'),\"clearable\":\"\"},model:{value:(_vm.purchaseFrom.rechargeType),callback:function ($$v) {_vm.$set(_vm.purchaseFrom, \"rechargeType\", $$v)},expression:\"purchaseFrom.rechargeType\"}},_vm._l((_vm.rechargeTypeList),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":_vm.$t('financial.detail.' + item.label),\"value\":item.value}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.transactionTime') + '：'}},[_c('el-date-picker',{staticStyle:{\"width\":\"250px\"},attrs:{\"placeholder\":_vm.$t('common.startDate'),\"clearable\":\"\",\"value-format\":\"yyyy-MM-dd\",\"format\":\"yyyy-MM-dd\",\"size\":\"small\",\"type\":\"daterange\",\"placement\":\"bottom-end\",\"start-placeholder\":_vm.$t('common.startDate'),\"end-placeholder\":_vm.$t('common.endDate')},model:{value:(_vm.timeList),callback:function ($$v) {_vm.timeList=$$v},expression:\"timeList\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.paymentMethod') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all'),\"clearable\":\"\"},model:{value:(_vm.purchaseFrom.payChannel),callback:function ($$v) {_vm.$set(_vm.purchaseFrom, \"payChannel\", $$v)},expression:\"purchaseFrom.payChannel\"}},[_c('el-option',{attrs:{\"label\":_vm.$t('financial.detail.bankTransfer'),\"value\":\"xendit\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('financial.detail.electronicWallet'),\"value\":\"haipay\"}})],1)],1),_vm._v(\" \"),(_vm.purchaseFrom.payChannel == 'haipay')?_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.electronicWallet') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all'),\"clearable\":\"\"},model:{value:(_vm.purchaseFrom.walletCode),callback:function ($$v) {_vm.$set(_vm.purchaseFrom, \"walletCode\", $$v)},expression:\"purchaseFrom.walletCode\"}},_vm._l((_vm.walletList),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1):_vm._e(),_vm._v(\" \"),(_vm.purchaseFrom.payChannel == 'xendit')?_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.bankName') + '：'}},[_c('el-select',{attrs:{\"clearable\":\"\",\"placeholder\":_vm.$t('common.all')},model:{value:(_vm.purchaseFrom.bankName),callback:function ($$v) {_vm.$set(_vm.purchaseFrom, \"bankName\", $$v)},expression:\"purchaseFrom.bankName\"}},_vm._l((_vm.bankList),function(item,index){return _c('el-option',{key:index,attrs:{\"label\":item,\"value\":item}})}),1)],1):_vm._e()],1):_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.tradeFrom),callback:function ($$v) {_vm.tradeFrom=$$v},expression:\"tradeFrom\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.tradeNo') + '：'}},[_c('el-input',{attrs:{\"size\":\"small\",\"placeholder\":_vm.$t('financial.detail.tradeNo')},model:{value:(_vm.tradeFrom.linkId),callback:function ($$v) {_vm.$set(_vm.tradeFrom, \"linkId\", $$v)},expression:\"tradeFrom.linkId\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.transactionTime') + '：'}},[_c('el-date-picker',{staticStyle:{\"width\":\"250px\"},attrs:{\"value-format\":\"yyyy-MM-dd\",\"format\":\"yyyy-MM-dd\",\"size\":\"small\",\"type\":\"daterange\",\"placement\":\"bottom-end\",\"start-placeholder\":_vm.$t('common.startDate'),\"end-placeholder\":_vm.$t('common.endDate'),\"clearable\":\"\"},model:{value:(_vm.timeList),callback:function ($$v) {_vm.timeList=$$v},expression:\"timeList\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('financial.detail.tradeType') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all'),\"clearable\":\"\"},model:{value:(_vm.tradeFrom.type),callback:function ($$v) {_vm.$set(_vm.tradeFrom, \"type\", $$v)},expression:\"tradeFrom.type\"}},[_c('el-option',{attrs:{\"label\":_vm.$t('financial.detail.agentFee'),\"value\":\"1\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('financial.detail.partnerFee'),\"value\":\"2\"}})],1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.getList(_vm.tableFromType)}}},[_vm._v(_vm._s(_vm.$t(\"common.query\")))]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":function($event){return _vm.handleReset()}}},[_vm._v(_vm._s(_vm.$t(\"common.reset\")))])],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:financialCenter:detail:upload']),expression:\"['admin:financialCenter:detail:upload']\"}],attrs:{\"type\":\"primary\",\"size\":\"small\"},on:{\"click\":_vm.handleUpload}},[_vm._v(_vm._s(_vm.$t(\"financial.detail.exportExcel\")))])],1),_vm._v(\" \"),(_vm.tableFromType === 'purchase')?_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.purchaseTableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"label\":_vm.$t('common.serialNumber'),\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.paymentTime'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.payTime)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.paymentNo'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.outTradeNo)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.rechargeType'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.paymentAccount'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.tradeAmount'),\"min-width\":\"100\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.actualPaymentAmount'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.paymentMethod'),\"min-width\":\"100\",\"prop\":\"payChannel\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.payChannel)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.electronicWallet'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.institutionNumber'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.bankName'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.paymentAccount'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.mobile'),\"min-width\":\"100\",\"prop\":\"phone\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.phone)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.payee'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.payeeAccount'),\"min-width\":\"80\"}})],1):_vm._e(),_vm._v(\" \"),(_vm.tableFromType === 'purchase')?_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.purchaseFrom.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.purchaseFrom.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.purchaseFrom.total},on:{\"size-change\":function (e) { return _vm.sizeChange(e, 'purchase'); },\"current-change\":function (e) { return _vm.pageChange(e, 'purchase'); }}}):_vm._e(),_vm._v(\" \"),(_vm.tableFromType === 'trade')?_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tradeTableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"type\":\"index\",\"label\":_vm.$t('common.serialNumber'),\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.tradeNo'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.linkId)))]}}],null,false,*********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.tradeType'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.title)))]}}],null,false,3648579569)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.tradeAmount'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.number)))]}}],null,false,2226103986)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.transactionTime'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.createTime)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.userNickname'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.nickName)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.tikTokAccount'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.tiktokAccount)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.whatsApp'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.whatsAppAccount)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.channel'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.channel)))]}}],null,false,**********)}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('financial.detail.orderNo'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.linkId)))]}}],null,false,*********)})],1):_vm._e(),_vm._v(\" \"),(_vm.tableFromType === 'trade')?_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.tradeFrom.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.tradeFrom.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.tradeFrom.total},on:{\"size-change\":function (e) { return _vm.sizeChange(e, 'trade'); },\"current-change\":function (e) { return _vm.pageChange(e, 'trade'); }}}):_vm._e()],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}