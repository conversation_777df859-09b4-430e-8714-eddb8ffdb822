{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue", "mtime": 1754269254568}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userLevelUpdateApi } from '@/api/user';\nimport { Debounce } from '@/utils/validate';\nexport default {\n  props: {\n    levelInfo: {\n      type: Object,\n      default: {}\n    },\n    levelList: {\n      type: Array,\n      default: []\n    }\n  },\n  data: function data() {\n    return {\n      grade: '',\n      levelStatus: false,\n      ruleForm: {\n        isSub: false,\n        levelId: \"\",\n        uid: this.levelInfo.uid\n      }\n    };\n  },\n  created: function created() {\n    this.ruleForm.levelId = this.levelInfo.level ? Number(this.levelInfo.level) : '';\n  },\n  watch: {\n    levelInfo: function levelInfo(val) {\n      this.ruleForm.uid = val.uid || 0;\n      this.ruleForm.levelId = this.levelInfo.level ? Number(this.levelInfo.level) : val.levelId;\n    }\n  },\n  methods: {\n    submitForm: Debounce(function (formName) {\n      var _this = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          userLevelUpdateApi(_this.ruleForm).then(function (res) {\n            _this.$message.success('编辑成功');\n            _this.$parent.$parent.getList();\n            _this.$parent.$parent.levelVisible = false;\n            _this.$refs[formName].resetFields();\n            _this.grade = '';\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    currentSel: function currentSel() {\n      var _this2 = this;\n      this.levelList.forEach(function (item) {\n        if (item.id == _this2.ruleForm.levelId) {\n          _this2.grade = item.grade;\n        }\n      });\n    },\n    resetForm: function resetForm(formName) {\n      var _this3 = this;\n      this.$nextTick(function () {\n        _this3.$refs[formName].resetFields();\n        _this3.grade = '';\n      });\n      this.$parent.$parent.levelVisible = false;\n    },\n    // 获取升级方式文本\n    getUpgradeTypeText: function getUpgradeTypeText(type) {\n      return this.$t(\"user.grade.upgradeTypes.\".concat(type)) || this.$t('common.unknown');\n    }\n  }\n};", null]}