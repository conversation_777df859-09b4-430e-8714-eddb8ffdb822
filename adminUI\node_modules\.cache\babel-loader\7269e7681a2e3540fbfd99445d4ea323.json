{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\codegen\\codegenList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\codegen\\codegenList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getCodegenList } from '@/api/codegen';\nimport SettingMer from \"@/utils/settingMer\";\nexport default {\n  name: \"codegenList\",\n  data: function data() {\n    return {\n      constants: this.$constants,\n      codeListData: {\n        pram: {\n          page: 1,\n          limit: 10,\n          tableName: ''\n        },\n        data: {\n          list: [],\n          totalCount: 0\n        },\n        selectedTables: []\n      }\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {\n    handlerSearch: function handlerSearch() {\n      this.codeListData.pram.limit = 10;\n      this.codeListData.pram.page = 1;\n      this.getList(this.codeListData.pram);\n    },\n    getList: function getList(pram) {\n      var _this = this;\n      getCodegenList(pram).then(function (res) {\n        _this.codeListData.data = res;\n      });\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.codeListData.pram.limit = val;\n      this.getList(this.codeListData.pram);\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.codeListData.pram.page = val;\n      this.getList(this.codeListData.pram);\n    },\n    handleSelectionChange: function handleSelectionChange(selectedRows) {\n      var _this2 = this;\n      this.codeListData.selectedTables = [];\n      selectedRows.forEach(function (row) {\n        _this2.codeListData.selectedTables.push(row.tableName);\n      });\n    },\n    handleGenCode: function handleGenCode() {\n      window.open(\"\".concat(SettingMer.apiBaseURL, \"codegen/code?tables=\").concat(this.codeListData.selectedTables.join(\",\")));\n    }\n  }\n};", null]}