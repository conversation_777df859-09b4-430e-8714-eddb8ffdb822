{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue", "mtime": 1754269254568}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userLevelUpdateApi} from '@/api/user'\nimport {Debounce} from '@/utils/validate'\nexport default {\n  props:{\n    levelInfo:{\n      type:Object,\n      default:{},\n    },\n    levelList:{\n      type:Array,\n      default:[]\n    }\n  },\n  data() {\n    return {\n      grade:'',\n      levelStatus:false,\n      ruleForm: {\n        isSub: false,\n        levelId:\"\",\n        uid:this.levelInfo.uid\n      },\n    };\n  },\n  created(){\n    this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):''\n  },\n  watch: {\n    levelInfo(val){\n      this.ruleForm.uid = val.uid || 0;\n      this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):val.levelId;\n    },\n  },\n  methods: {\n    submitForm:Debounce(function(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          userLevelUpdateApi(this.ruleForm).then(res=>{\n            this.$message.success('编辑成功');\n            this.$parent.$parent.getList();\n            this.$parent.$parent.levelVisible = false;\n            this.$refs[formName].resetFields()\n            this.grade = '';\n          })\n        } else {\n          return false;\n        }\n      });\n    }),\n    currentSel(){\n      this.levelList.forEach(item=>{\n        if(item.id == this.ruleForm.levelId){\n          this.grade = item.grade;\n        }\n      })\n    },\n    resetForm(formName) {\n      this.$nextTick(() => {\n        this.$refs[formName].resetFields();\n        this.grade = '';\n      })\n      this.$parent.$parent.levelVisible = false\n    },\n    // 获取升级方式文本\n    getUpgradeTypeText(type) {\n      return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown')\n    }\n  },\n};\n", null]}