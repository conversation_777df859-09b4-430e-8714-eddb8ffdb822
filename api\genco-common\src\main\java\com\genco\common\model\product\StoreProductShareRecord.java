package com.genco.common.model.product;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_store_product_share_record")
@ApiModel(value = "StoreProductShareRecord对象", description = "商品链接分享记录")
public class StoreProductShareRecord {
    private Long id;
    private Long userId;
    private String tiktokUid;
    private String originUrl;
    private String shareUrl;
    private String channel;
    private String productId; // VARCHAR(32)
    private String productName;
    private BigDecimal productPrice; // DECIMAL(10,0)
    private BigDecimal productCashbackRate; // DECIMAL(8,4)
    private BigDecimal userCashbackRate; // DECIMAL(8,4)
    private String userAccount; // 用户昵称
    private Date operateTime;
    private Date createTime;
    private Date updateTime;
} 