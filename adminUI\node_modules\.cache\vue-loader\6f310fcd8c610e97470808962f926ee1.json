{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\index.vue?vue&type=template&id=2f3c81cf&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\">\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <el-form-item label=\"关键字：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入姓名、电话、UID\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"seachList\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <!-- <cards-data :cardLists=\"cardLists\"></cards-data> -->\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"uid\"\n        label=\"ID\"\n        width=\"60\"\n      />\n      <el-table-column\n        label=\"头像\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"nickname\"\n        label=\"用户信息\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        sortable\n        prop=\"spreadCount\"\n        label=\"推广用户(一级)数量\"\n        :sort-method=\"(a,b)=>{return a.spreadCount - b.spreadCount}\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        sortable\n        label=\"推广订单数量\"\n        prop=\"spreadOrderNum\"\n        :sort-method=\"(a,b)=>{return a.spreadOrderNum - b.spreadOrderNum}\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        sortable\n        label=\"推广订单金额\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.spreadOrderTotalPrice - b.spreadOrderTotalPrice}\"\n        prop=\"spreadOrderTotalPrice\"\n      />\n      <el-table-column\n        sortable\n        label=\"佣金总金额\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.totalBrokeragePrice - b.totalBrokeragePrice}\"\n        prop=\"totalBrokeragePrice\"\n      />\n      <el-table-column\n        sortable\n        label=\"已提现金额\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.extractCountPrice - b.extractCountPrice}\"\n        prop=\"extractCountPrice\"\n      />\n      <el-table-column\n        sortable\n        label=\"已提现次数\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.extractCountNum - b.extractCountNum}\"\n        prop=\"extractCountNum\"\n      />\n      <el-table-column\n        sortable\n        label=\"未提现金额\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.brokeragePrice - b.brokeragePrice}\"\n        prop=\"brokeragePrice\"\n      />\n      <el-table-column\n        sortable\n        label=\"冻结中佣金\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.freezeBrokeragePrice - b.freezeBrokeragePrice}\"\n        prop=\"freezeBrokeragePrice\"\n      />\n      <el-table-column\n        prop=\"promoterTime\"\n        label=\"成为推广员时间\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        prop=\"spreadNickname\"\n        label=\"上级推广人\"\n        min-width=\"150\"\n      />\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" class=\"mr10\" @click=\"onSpread(scope.row.uid, 'man','推广人')\" v-hasPermi=\"['admin:retail:spread:list']\">推广人</el-button>\n          <el-dropdown>\n            <span class=\"el-dropdown-link\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\" />\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item @click.native=\"onSpreadOrder(scope.row.uid, 'order','推广订单')\" v-if=\"checkPermi(['admin:retail:spread:order:list'])\">推广订单</el-dropdown-item>\n              <!--<el-dropdown-item @click.native=\"onSpreadType(scope.row.uid)\">推广方式</el-dropdown-item>-->\n              <el-dropdown-item @click.native=\"clearSpread(scope.row)\" v-if=\"scope.row.spreadNickname && scope.row.spreadNickname!=='无'\" v-hasPermi=\"['admin:retail:spread:clean']\">清除上级推广人</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n\n  <!--推广人-->\n  <el-dialog\n    :title=\"titleName+'列表'\"\n    :visible.sync=\"dialogVisible\"\n    width=\"900px\"\n    :before-close=\"handleClose\"\n  >\n    <div class=\"container\">\n      <el-form size=\"small\" label-width=\"100px\">\n        <el-form-item v-if=\"this.onName !== 'man'\" key=\"1\" label=\"时间选择：\" class=\"width100\">\n          <el-radio-group v-model=\"spreadFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChangeSpread(spreadFrom.dateLimit)\">\n            <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n          </el-radio-group>\n          <el-date-picker v-model=\"timeValSpread\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTimeSpread\" />\n        </el-form-item>\n        <el-form-item label=\"用户类型：\">\n          <el-radio-group v-model=\"spreadFrom.type\" size=\"small\" @change=\"onChanges\">\n            <el-radio-button label=\"0\">全部</el-radio-button>\n            <el-radio-button label=\"1\">一级推广人</el-radio-button>\n            <el-radio-button label=\"2\">二级推广人</el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"关键字：\" class=\"width100\">\n          <el-input v-model=\"spreadFrom.nickName\" :placeholder=\"onName === 'order'?'请输入订单号':'请输入姓名、电话、UID'\" class=\"selWidth\" size=\"small\" clearable>\n            <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"onChanges\" />\n          </el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    <el-table\n      v-if=\"onName === 'man'\"\n      key=\"men\"\n      v-loading=\"spreadLoading\"\n      :data=\"spreadData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"uid\"\n        label=\"ID\"\n        width=\"60\"\n      />\n      <el-table-column\n        label=\"头像\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"nickname\"\n        label=\"用户信息\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        prop=\"is_promoter\"\n        label=\"是否推广员\"\n        min-width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.isPromoter | filterYesOrNo }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        sortable\n        label=\"推广人数\"\n        min-width=\"120\"\n        prop=\"spreadCount\"\n      />\n      <el-table-column\n        sortable\n        label=\"订单数\"\n        min-width=\"120\"\n        prop=\"payCount\"\n      />\n      <!--<el-table-column-->\n        <!--prop=\"spreadTime\"-->\n        <!--label=\"关注时间\"-->\n        <!--min-width=\"150\"-->\n      <!--/>-->\n    </el-table>\n    <el-table\n      v-if=\"onName === 'order'\"\n      key=\"order\"\n      v-loading=\"spreadLoading\"\n      :data=\"spreadData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"orderId\"\n        label=\"订单ID\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        label=\"用户信息\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.realName}}<el-divider direction=\"vertical\"></el-divider>{{ scope.row.userPhone }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"updateTime\"\n        label=\"时间\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        sortable\n        label=\"返佣金额\"\n        min-width=\"120\"\n        prop=\"price\"\n      />\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"spreadFrom.limit\"\n        :current-page=\"spreadFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"spreadData.total\"\n        @size-change=\"handleSizeChangeSpread\"\n        @current-change=\"pageChangeSpread\"\n      />\n    </div>\n  </el-dialog>\n</div>\n", null]}