{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue?vue&type=style&index=0&id=15282485&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754138267094}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754138276757}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754138271537}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754138265892}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.pos-order-goods{padding:0 0.3rem;background-color: #fff;}\n.pos-order-goods .goods{height:1.85rem;}\n.pos-order-goods .goods~.goods{border-top:1px dashed #e5e5e5;}\n.pos-order-goods .goods .picTxt{width:5.15rem;}\n.pos-order-goods .goods .picTxt .pictrue{width:1.3rem;height:1.3rem;}\n.pos-order-goods .goods .picTxt .pictrue img{width:100%;height:100%;border-radius:0.06rem;}\n.pos-order-goods .goods .picTxt .text{width:3.65rem;height:1.3rem;}\n.pos-order-goods .goods .picTxt .text .info{font-size:0.28rem;color:#282828;}\n.pos-order-goods .goods .picTxt .text .attr{font-size:0.2rem;color:#999;height: 0.8rem;\n  line-height: 0.8rem;width: 5rem;overflow: hidden;text-overflow: ellipsis;\twhite-space: nowrap;}\n.pos-order-goods .goods .money{width:1.64rem;text-align:right;font-size:0.28rem;height: 1.3rem;}\n.pos-order-goods .goods .money .x-money{color:#282828;}\n.pos-order-goods .goods .money .num{color:#ff9600;margin:0.05rem 0;}\n.pos-order-goods .goods .money .y-money{color:#999;text-decoration:line-through;}\n.pos-order-list{background: #f5f5f5;margin-top: -50px;}\n.pos-order-list .nav{width:100%;height:0.96rem;background-color:#fff;font-size:0.3rem;color:#282828;position:fixed;top:0;left:0;z-index: 66;}\n.pos-order-list .nav .item.on{color:#2291f8;}\n.pos-order-list .list{margin-top:0.2rem;}\n.pos-order-list .list .item{background-color:#fff;width:100%;}\n.pos-order-list .list .item~.item{margin-top:0.24rem;}\n.pos-order-list .list .item .order-num{height:1.24rem;border-bottom:1px solid #eee;font-size:0.3rem;font-weight:bold;color:#282828;padding:0 0.3rem;}\n.pos-order-list .list .item .order-num .time{font-size:0.26rem;font-weight:normal;color:#999;margin-top: -0.4rem;}\n.pos-order-list .list .item .operation{padding:0.2rem 0.3rem;margin-top: 0.03rem;}\n.pos-order-list .list .item .operation .more{position:relative;}\n.pos-order-list .list .item .operation .icon-gengduo{font-size:0.5rem;color:#aaa;}\n\n.pos-order-list .list .item .operation .order .arrow{width: 0;height: 0;border-left: 0.11rem solid transparent;border-right: 0.11rem solid transparent;border-top: 0.2rem solid #e5e5e5;position:absolute;left: 0.15rem;bottom:-0.18rem;}\n.pos-order-list .list .item .operation .order .arrow:before{content:'';width: 0;height: 0;border-left: 0.07rem solid transparent;border-right: 0.07rem solid transparent;border-top: 0.2rem solid #fff;position:absolute;left:-0.07rem;bottom:0;}\n.pos-order-list .list .item .operation .order{width:2rem;background-color:#fff;border:1px solid #eee;border-radius:0.1rem;position:absolute;top:-1rem;z-index:9;}\n.pos-order-list .list .item .operation .order .items{height:0.77rem;line-height:0.77rem;text-align:center;}\n.pos-order-list .list .item .operation .order .items~.items{border-top:1px solid #f5f5f5;}\n\n.pos-order-list .list .item .operation .bnt{font-size:0.28rem;color:#5c5c5c;width:1.7rem;height:0.6rem;border-radius:0.3rem;border:1px solid #bbb;text-align:center;line-height:0.6rem;}\n.pos-order-list .list .item .operation .bnt~.bnt{margin-left:0.14rem;}\n.public-total{font-size:0.28rem;color:#282828;border-top:1px solid #eee;height:0.92rem;line-height:0.92rem;text-align:right;padding:0 0.3rem;background-color: #fff;}\n.public-total .money{color:#ff4c3c;}\n", null]}