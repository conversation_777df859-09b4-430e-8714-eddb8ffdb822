{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue?vue&type=style&index=0&id=********&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.newsBox{\n  background: #fff;\n}\n.arrbox {\n  background-color: white;\n  font-size: 12px;\n  border: 1px solid #dcdee2;\n  border-radius: 6px;\n  margin-bottom: 0px;\n  padding:0 5px;\n  text-align: left;\n  box-sizing: border-box;\n  width: 90%;\n}\n.news_sp{\n  font-size: 12px;\n  color: #000000;\n  background: #fff;\n  width: 100%;\n  height: 38px;\n  line-height: 38px;\n  padding: 0 12px;\n  box-sizing: border-box;\n  display: block;\n}\n.arrbox_ip {\n  font-size: 12px;\n  border: none;\n  box-shadow: none;\n  outline: none;\n  background-color: transparent;\n  padding: 0;\n  margin: 0;\n  width: auto !important;\n  max-width: inherit;\n  min-width: 80px;\n  vertical-align: top;\n  color: #34495e;\n  margin: 2px;\n}\n\n.left {\n  min-width: 390px;\n  min-height: 550px;\n  position: relative;\n  padding-left: 40px;\n}\n\n.top {\n  position: absolute;\n  top: 0px;\n}\n\n.bottom {\n  position: absolute;\n  bottom: 0px;\n}\n\n.centent {\n  background: #F4F5F9;\n  min-height: 545px;\n  width: 320px;\n  padding: 15px;\n  box-sizing: border-box;\n}\n\n.right {\n  background: #fff;\n  min-height: 300px;\n}\n\n.box-content {\n  position: relative;\n  max-width: 60%;\n  min-height: 40px;\n  margin-left: 15px;\n  padding: 10px;\n  box-sizing: border-box;\n  border: 1px solid #ccc;\n  word-break: break-all;\n  word-wrap: break-word;\n  line-height: 1.5;\n  border-radius: 5px;\n}\n\n.box-content_pic {\n  width: 100%;\n}\n\n.box-content_pic img {\n  width: 100%;\n  height: auto;\n}\n\n.box-content:before {\n  content: '';\n  position: absolute;\n  left: -13px;\n  top: 11px;\n  display: block;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-top: 10px solid #ccc;\n  -webkit-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n\n.box-content:after {\n  content: '';\n  content: '';\n  position: absolute;\n  left: -12px;\n  top: 11px;\n  display: block;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-top: 10px solid #f5f5f5;\n  -webkit-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n\n.time-wrapper {\n  margin-bottom: 10px;\n  text-align: center;\n  margin-top: 62px;\n}\n\n.time {\n  display: inline-block;\n  color: #f5f5f5;\n  background: rgba(0, 0, 0, .3);\n  padding: 3px 8px;\n  border-radius: 3px;\n  font-size: 12px;\n}\n\n.text-box {\n  display: flex;\n}\n\n.avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n}\n.modelBox{\n  .ivu-modal-body{\n    padding: 0 16px 16px 16px !important;\n  }\n}\n.news_pic{\n  width: 100%;\n  height: 150px;\n  overflow: hidden;\n  position: relative;\n  background-size: 100%;\n  background-position: center center;\n  border-radius: 5px 5px 0 0;\n  padding: 10px;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n.news_cent{\n  width: 100%;\n  height: auto;\n  background: #fff;\n  border-top: 1px dashed #eee;\n  display: flex;\n  padding: 10px;\n  box-sizing: border-box;\n  justify-content: space-between;\n  .news_sp1{\n    font-size: 12px;\n    color: #000000;\n    width: 71%;\n  }\n  .news_cent_img{\n    width: 81px;\n    height: 46px;\n    border-radius: 6px;\n    overflow: hidden;\n    img{\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n", null]}