{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\edit.vue?vue&type=template&id=48a67e34&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\edit.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\" class=\"demo-ruleForm\">\n    <el-form-item label=\"用户编号：\">\n      <el-input v-model=\"ruleForm.id\" disabled class=\"selWidth\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"用户地址：\">\n      <el-input v-model=\"ruleForm.addres\" class=\"selWidth\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"用户备注：\">\n      <el-input v-model=\"ruleForm.mark\" type=\"textarea\" class=\"selWidth\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"用户分组：\">\n      <el-select v-model=\"ruleForm.groupId\" placeholder=\"请选择\"  class=\"selWidth\" clearable filterable>\n        <el-option :value=\"item.id\" v-for=\"(item, index) in groupList\" :key=\"index\" :label=\"item.groupName\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item label=\"用户标签：\">\n      <el-select v-model=\"labelData\" placeholder=\"请选择\"  class=\"selWidth\" clearable filterable multiple >\n        <el-option :value=\"item.id\" v-for=\"(item, index) in labelLists\" :key=\"index\" :label=\"item.name\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item label=\"推广员\">\n      <el-radio-group v-model=\"ruleForm.isPromoter\">\n        <el-radio :label=\"true\">开启</el-radio>\n        <el-radio :label=\"false\">关闭</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item label=\"状态\">\n      <el-radio-group v-model=\"ruleForm.status\">\n        <el-radio :label=\"true\">开启</el-radio>\n        <el-radio :label=\"false\">关闭</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" @click=\"submitForm('ruleForm')\" v-hasPermi=\"['admin:user:update']\">提交</el-button>\n      <el-button @click=\"resetForm('ruleForm')\">取消</el-button>\n    </el-form-item>\n  </el-form>\n", null]}