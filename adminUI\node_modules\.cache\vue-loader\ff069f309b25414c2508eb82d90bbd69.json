{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue?vue&type=style&index=0&id=6a09cc24&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754138267094}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754138276757}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754138271537}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754138265892}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  .public-wrapper .title{font-size:0.3rem;color:#282828;padding:0 0.3rem;margin-bottom:0.2rem;}\n  .public-wrapper .title .iconfont{color:#2291f8;font-size:0.4rem;margin-right:0.13rem;vertical-align:middle;}\n  .public-wrapper{margin:0.18rem auto 0 auto;width:6.9rem;background-color:#fff;border-radius:0.1rem;padding-top:0.25rem;}\n  .public-wrapper .nav{padding:0 0.3rem;height:0.7rem;line-height:0.7rem;font-size:0.24rem;color:#999;}\n  .public-wrapper .data{width:2.1rem;text-align:left;}\n  .public-wrapper .browse{width:1.92rem;text-align:right;}\n  .public-wrapper .turnover{width:2.27rem;text-align:right;}\n  .public-wrapper .conter{padding:0 0.3rem;}\n  .public-wrapper .conter .item{border-bottom:1px solid #f7f7f7;height:0.7rem;font-size:0.24rem;}\n  .public-wrapper .conter .item .turnover{color:#d84242;}\n  .statistical-page {margin-top:-50px;}\n  .statistical-page .navs{width:100%;height:0.96rem;background-color:#fff;overflow:hidden;line-height:0.96rem;position:fixed;top:0;left:0;z-index:9;}\n  .statistical-page .navs .list{overflow-y:hidden;overflow-x:auto;white-space: nowrap;-webkit-overflow-scrolling: touch;\n    width: 100%;}\n  .statistical-page .navs .item{font-size:0.32rem;color:#282828;margin-left:0.6rem;display: inline-block;}\n  .statistical-page .navs .item.on{color:#2291f8;}\n  .statistical-page .navs .item .iconfont{font-size:0.25rem;margin-left:0.13rem;}\n  .statistical-page .wrapper{width:7.4rem;background-color:#fff;border-radius:0.1rem;margin:0.19rem auto 0 auto;padding:0.5rem 0.6rem;}\n  .statistical-page .wrapper .title{font-size:0.3rem;color:#999;text-align:center;}\n  .statistical-page .wrapper .money{font-size:0.72rem;color:#fba02a;text-align:center;margin-top:0.1rem;}\n  .statistical-page .wrapper .increase{font-size:0.28rem;color:#999;margin-top:0.2rem;}\n  .statistical-page .wrapper .increase .red{color:#ff6969;}\n  .statistical-page .wrapper .increase .green{color:#1abb1d;}\n  .statistical-page .wrapper .increase .iconfont{font-size:0.23rem;margin-left:0.15rem;}\n  .statistical-page .chart{width:6.9rem;height:4.8rem;background-color:#fff;border-radius:0.1rem;margin:0.23rem auto 0 auto;padding: 0.25rem 0.22rem 0 0.22rem;}\n  .statistical-page .chart .company{font-size:0.26rem;color:#999;}\n  .statistical-page .mc-body{padding-bottom:0;}\n  .statistical-page .mc-body tr{background-color: #edf8fe;border-top: 1px solid #fff;width:100%;}\n.echarts {\n  width: 100%;\n  height: 5.5rem;\n}\n.calendar-wrapper {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  z-index: 777;\n  transform: translate3d(0, 100%, 0);\n  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\n}\n.calendar-wrapper.on {\n  transform: translate3d(0, 0, 0);\n}\n.statistical-page .wrapper .increase {\n  font-size: 0.26rem;\n}\n.statistical-page .wrapper .increase .iconfont {\n  margin-left: 0;\n}\n", null]}