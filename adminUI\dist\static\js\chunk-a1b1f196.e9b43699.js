(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a1b1f196","chunk-1e152eec"],{5317:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div")},n=[],s=i("2877"),r={},l=Object(s["a"])(r,a,n,!1,null,null,null);e["a"]=l.exports},"92c6":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"d",(function(){return r})),i.d(e,"a",(function(){return l})),i.d(e,"f",(function(){return o})),i.d(e,"g",(function(){return d})),i.d(e,"j",(function(){return c})),i.d(e,"h",(function(){return u})),i.d(e,"e",(function(){return m})),i.d(e,"i",(function(){return f}));var a=i("b775");function n(t){var e={id:t.id};return Object(a["a"])({url:"/admin/system/form/temp/info",method:"GET",params:e})}function s(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(a["a"])({url:"/admin/system/form/temp/list",method:"GET",params:e})}function r(t){var e={content:t.content,info:t.info,name:t.name};return Object(a["a"])({url:"/admin/system/form/temp/save",method:"POST",data:e})}function l(t){var e={id:t.id},i={content:t.content,info:t.info,name:t.name};return Object(a["a"])({url:"/admin/system/form/temp/update",method:"POST",params:e,data:i})}function o(t){var e={sendType:t.sendType};return Object(a["a"])({url:"/admin/system/notification/list",method:"GET",params:e})}function d(t){return Object(a["a"])({url:"/admin/system/notification/routine/switch/".concat(t),method:"post"})}function c(t){return Object(a["a"])({url:"/admin/system/notification/wechat/switch/".concat(t),method:"post"})}function u(t){return Object(a["a"])({url:"/admin/system/notification/sms/switch/".concat(t),method:"post"})}function m(t){var e={detailType:t.type,id:t.id};return Object(a["a"])({url:"/admin/system/notification/detail",method:"get",params:e})}function f(t){var e={detailType:t.type,id:t.id,status:t.status,tempId:t.tempId};return Object(a["a"])({url:"/admin/system/notification/update",method:"post",data:e})}},d1da:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("div",{staticClass:"container"},[i("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[i("el-form-item",{attrs:{label:"关键字"}},[i("el-input",{staticClass:"selWidth",attrs:{placeholder:"请输入id，名称，描述",clearable:"",size:"small"},model:{value:t.listPram.keywords,callback:function(e){t.$set(t.listPram,"keywords",e)},expression:"listPram.keywords"}},[i("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"small"},on:{click:t.handlerSearch},slot:"append"})],1)],1),t._v(" "),t.selectModel?i("el-form-item",[i("el-button",{attrs:{type:"primary",disabled:!t.selectedConfigData.id},on:{click:t.handlerConfimSelect}},[t._v("确定选择")])],1):t._e()],1)],1),t._v(" "),t.selectModel?t._e():i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:form:save"],expression:"['admin:system:form:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerEditData({},0)}}},[t._v("创建表单")])],1),t._v(" "),i("el-table",{staticClass:"table",attrs:{data:t.dataList.list,"highlight-current-row":t.selectModel,size:"mini","header-cell-style":{fontWeight:"bold"}},on:{"current-change":t.handleCurrentRowChange}},[i("el-table-column",{attrs:{label:"ID",prop:"id",width:"80"}}),t._v(" "),i("el-table-column",{attrs:{label:"名称",prop:"name","min-width":"180"}}),t._v(" "),i("el-table-column",{attrs:{label:"描述",prop:"info","min-width":"220"}}),t._v(" "),i("el-table-column",{attrs:{label:"更新时间",prop:"updateTime","min-width":"200"}}),t._v(" "),t.selectModel?t._e():i("el-table-column",{attrs:{label:"操作","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:form:info"],expression:"['admin:system:form:info']"}],attrs:{type:"text",size:"small"},on:{click:function(i){return t.handlerEditData(e.row,1)}}},[t._v("编辑")])]}}],null,!1,109836554)})],1),t._v(" "),i("el-pagination",{attrs:{"current-page":t.listPram.page,"page-sizes":t.constants.page.limit,layout:t.constants.page.layout,total:t.dataList.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1),t._v(" "),i("el-dialog",{attrs:{visible:t.editDialogConfig.visible,fullscreen:"",title:0===t.editDialogConfig.isCreate?"创建表单":"编辑表单","destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?i("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideDialog:t.handlerHide}}):t._e()],1)],1)},n=[],s=i("92c6"),r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("config-list",{attrs:{"edit-data":t.editData,"is-create":t.isCreate},on:{getFormConfigDataResult:t.handlerGetFormConfigData}})],1)},l=[],o=i("5abd"),d={components:{configList:o["a"]},props:{editData:{type:Object,default:{}},isCreate:{type:Number,default:0}},data:function(){return{}},methods:{handlerGetFormConfigData:function(t){t.id?this.handlerEdit(t):this.handlerSave(t)},handlerSave:function(t){var e=this;s["d"](t).then((function(t){e.$message.success("创建表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))},handlerEdit:function(t){var e=this;s["a"](t).then((function(t){e.$message.success("编辑表单配置成功"),setTimeout((function(){e.$emit("hideDialog")}),800)}))}}},c=d,u=i("2877"),m=Object(u["a"])(c,r,l,!1,null,"1b0ee2a8",null),f=m.exports,h={components:{edit:f},props:{selectModel:{type:Boolean,default:!1}},data:function(){return{constants:this.$constants,listPram:{keywords:null,page:1,limit:this.$constants.page.limit[0]},editDialogConfig:{visible:!1,editData:{},isCreate:0},dataList:{list:[],total:0},selectedConfigData:{}}},mounted:function(){this.handlerGetList(this.listPram)},methods:{handlerSearch:function(){this.listPram.page=1,this.handlerGetList(this.listPram)},handlerGetList:function(t){var e=this;s["c"](t).then((function(t){e.dataList=t}))},handlerEditData:function(t,e){this.editDialogConfig.editData=0===e?{}:t,this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},handlerHide:function(){this.editDialogConfig.editData={},this.editDialogConfig.isCreate=0,this.editDialogConfig.visible=!1,this.handlerGetList(this.listPram)},handleSizeChange:function(t){this.listPram.limit=t,this.handlerGetList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handlerGetList(this.listPram)},handleCurrentRowChange:function(t){this.selectedConfigData=t},handlerConfimSelect:function(){this.$emit("selectedRowData",this.selectedConfigData)}}},p=h,g=Object(u["a"])(p,a,n,!1,null,"cf311f6a",null);e["default"]=g.exports},e395:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("div",{attrs:{slot:"header"},slot:"header"},[i("el-form",{attrs:{inline:""}},[i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerOpenAdd({id:0,name:"顶层目录"})}}},[t._v("添加分类")])],1)],1)],1),t._v(" "),i("el-table",{ref:"treeList",staticClass:"table",staticStyle:{width:"100%"},attrs:{data:t.treeList,"row-key":"id",size:"mini","highlight-current-row":"","tree-props":{children:"child",hasChildren:"hasChildren"}}},[i("el-table-column",{attrs:{prop:"name",label:"分类昵称","min-width":"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.name)+"\n        ")]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"英文名称","show-overflow-tooltip":"","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.url))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"已关联的表单","show-overflow-tooltip":"","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.extra))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"启用状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("filterYesOrNo")(e.row.status)))])]}}])}),t._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"250",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"text",size:"small",disabled:e.row.pid>0},on:{click:function(i){return t.handlerOpenAdd(e.row)}}},[t._v("添加子目录")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handleEditMenu(e.row)}}},[t._v("编辑")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handlerOpenFormConfig(e.row)}}},[t._v("配置列表")]),t._v(" "),i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(i){return t.handleDelMenu(e.row)}}},[t._v("删除")])]}}])})],1)],1),t._v(" "),i("el-dialog",{attrs:{title:0===t.editDialogConfig.isCreate?"添加分类":"编辑分类",visible:t.editDialogConfig.visible,"destroy-on-close":"","close-on-click-modal":!1},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?i("edit",{attrs:{prent:t.editDialogConfig.prent,"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.data,biztype:t.editDialogConfig.biztype,"all-tree-list":t.treeList},on:{hideEditDialog:t.hideEditDialog}}):t._e()],1),t._v(" "),i("el-dialog",{attrs:{title:"选择已配置的表单",visible:t.configFormSelectedDialog.visible},on:{"update:visible":function(e){return t.$set(t.configFormSelectedDialog,"visible",e)}}},[i("span",{staticClass:"color-red"},[t._v("注意：表单不能重复关联")]),t._v(" "),t.configFormSelectedDialog.visible?i("form-config-list",{attrs:{"select-model":""},on:{selectedRowData:t.handlerSelectedRowData}}):t._e(),t._v(" "),i("el-form",[i("el-form-item",[i("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary"},on:{click:t.handlerAddFormExtra}},[t._v("关联")])],1)],1)],1)],1)},n=[],s=i("651a"),r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"components-container"},[i("el-form",{ref:"editPram",attrs:{model:t.editPram,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"父级"}},[i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:t.allTreeList,props:t.categoryProps,disabled:""},model:{value:t.editPram.pid,callback:function(e){t.$set(t.editPram,"pid",e)},expression:"editPram.pid"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"分类名称",prop:"name",rules:[{required:!0,message:"请输入分类名称",trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:"分类名称"},model:{value:t.editPram.name,callback:function(e){t.$set(t.editPram,"name",e)},expression:"editPram.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"英文名称",prop:"url",rules:[{required:!0,message:"英文名称不能为空",trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:"URL"},model:{value:t.editPram.url,callback:function(e){t.$set(t.editPram,"url",e)},expression:"editPram.url"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{min:1,max:10},model:{value:t.editPram.sort,callback:function(e){t.$set(t.editPram,"sort",e)},expression:"editPram.sort"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:t.editPram.status,callback:function(e){t.$set(t.editPram,"status",e)},expression:"editPram.status"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerSubmit("editPram")}}},[t._v("确定")]),t._v(" "),i("el-button",{attrs:{size:"mini"},on:{click:t.close}},[t._v("取消")])],1)],1)],1)},l=[],o=i("fca7"),d=i("61f7"),c={props:{prent:{type:Object,default:0},isCreate:{type:Number,default:0},editData:{type:Object},allTreeList:{type:Array}},data:function(){return{constants:this.$constants,editPram:{extra:null,name:null,pid:null,sort:0,status:!0,type:this.$constants.categoryType[5].value,url:null,id:0},categoryProps:{value:"id",label:"name",children:"child",expandTrigger:"hover",checkStrictly:!0,emitPath:!1},parentOptions:[]}},mounted:function(){this.initEditData()},methods:{close:function(){this.$emit("hideEditDialog")},initEditData:function(){if(this.parentOptions=o["addTreeListLabelForCasCard"](this.allTreeList),1!==this.isCreate){var t=this.prent.id;this.editPram.pid=t}else{var e=this.editData,i=e.extra,a=e.name,n=e.pid,s=e.sort,r=e.status,l=e.type,d=e.url,c=e.id;this.editPram.name=a,this.editPram.pid=n,this.editPram.sort=s,this.editPram.status=r,this.editPram.type=l,this.editPram.url=d,this.editPram.id=c,this.editPram.extra=i}},handlerSubmit:Object(d["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&e.handlerSaveOrUpdate(0===e.isCreate)}))})),handlerSaveOrUpdate:function(t){var e=this;t?(this.editPram.pid=this.prent.id,s["a"](this.editPram).then((function(t){e.$emit("hideEditDialog"),e.$message.success("创建分类成功")}))):(this.editPram.pid=Array.isArray(this.editPram.pid)?this.editPram.pid[0]:this.editPram.pid,s["h"](this.editPram).then((function(t){e.$emit("hideEditDialog"),e.$message.success("更新分类成功")})))}}},u=c,m=i("2877"),f=Object(m["a"])(u,r,l,!1,null,"a7d29cda",null),h=f.exports,p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"components-container"},[i("config-list",{attrs:{"prent-data":t.prentData}})],1)},g=[],v=i("5abd"),b={components:{configList:v["a"]},props:{prentData:{type:Object,default:{}}}},y=b,C=Object(m["a"])(y,p,g,!1,null,"91d6a8da",null),D=C.exports,_=i("d1da"),P={components:{edit:h,configList:D,formConfigList:_["default"]},props:{},data:function(){return{constants:this.$constants,searchPram:{status:null,type:null},editDialogConfig:{visible:!1,isCreate:0,prent:{},data:{}},treeList:[],listPram:{pid:0,type:this.$constants.categoryType[5].value,status:null,name:null,page:this.$constants.page.page,limit:this.$constants.page.limit[1]},configFormSelectedDialog:{visible:!1,currentData:{}}}},mounted:function(){this.handlerGetTreeList()},methods:{handlerOpenFormConfig:function(t){this.configFormSelectedDialog.currentData=t,this.configFormSelectedDialog.visible=!0},handlerGetList:function(){var t=this;s["f"](this.listPram).then((function(e){t.treeList=e.list}))},handlerOpenAdd:function(t){this.editDialogConfig.isCreate=0,this.editDialogConfig.prent=t,this.editDialogConfig.data={},this.editDialogConfig.biztype=this.biztype,this.editDialogConfig.visible=!0},handleEditMenu:function(t){this.editDialogConfig.isCreate=1,this.editDialogConfig.data=t,this.editDialogConfig.prent=t,this.editDialogConfig.visible=!0},handleDelMenu:function(t){var e=this;this.$confirm("确定删除当前数据?").then((function(){s["d"](t).then((function(t){e.handlerGetTreeList(),e.$message.success("删除成功")}))}))},hideEditDialog:function(){var t=this;setTimeout((function(){t.editDialogConfig.prent={},t.editDialogConfig.type=0,t.editDialogConfig.visible=!1,t.handlerGetTreeList()}),200)},handlerGetTreeList:function(){var t=this,e={type:this.constants.categoryType[5].value,status:-1};s["g"](e).then((function(e){t.treeList=t.handleAddArrt(e)}))},handleAddArrt:function(t){var e=o["addTreeListLabel"](t);return e},handlerSelectedRowData:function(t){this.configFormSelectedDialog.currentData.extra=t.id},handlerAddFormExtra:function(){var t=this;s["h"](this.configFormSelectedDialog.currentData).then((function(e){t.$message.success("关联表单成功"),setTimeout((function(){t.configFormSelectedDialog.visible=!1,t.handlerGetTreeList()}),800)}))}}},w=P,k=Object(m["a"])(w,a,n,!1,null,"83a24f46",null);e["default"]=k.exports},fb9d:function(t,e,i){var a={"./el-button.js":"64b1","./el-checkbox-group.js":"cc1a","./el-input.js":"373c","./el-radio-group.js":"afca","./el-select.js":"a938","./el-upload.js":"73ee"};function n(t){var e=s(t);return i(e)}function s(t){var e=a[t];if(!(e+1)){var i=new Error("Cannot find module '"+t+"'");throw i.code="MODULE_NOT_FOUND",i}return e}n.keys=function(){return Object.keys(a)},n.resolve=s,t.exports=n,n.id="fb9d"}}]);