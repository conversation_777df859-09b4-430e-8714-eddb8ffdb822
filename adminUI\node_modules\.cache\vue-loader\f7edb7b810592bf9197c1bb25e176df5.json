{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\distribution\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { promoterListApi, spreadStatisticsApi, spreadListApi, spreadOrderListApi, spreadClearApi } from '@/api/distribution'\nimport cardsData from '@/components/cards/index'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'AccountsUser',\n  components: { cardsData },\n  data() {\n    return {\n      cardLists: [],\n      timeVal: [],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        dateLimit: '',\n        keywords: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      dialogVisible: false,\n      spreadData: {\n        data: [],\n        total: 0\n      },\n      spreadFrom: {\n        page: 1,\n        limit: 10,\n        dateLimit: '',\n        type: 0,\n        nickName: '',\n        uid: ''\n      },\n      timeValSpread: [],\n      spreadLoading: false,\n      uid: '',\n      onName: '',\n      titleName: ''\n    }\n  },\n  mounted() {\n    // this.spreadStatistics()\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    seachList() {\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    // 统计\n    // spreadStatistics() {\n    //   spreadStatisticsApi({ dateLimit: this.tableFrom.dateLimit, keywords: this.tableFrom.nickName}).then((res) => {\n    //     this.cardLists = [\n    //       { name: '分销人员人数', count: res.distributionNum },\n    //       { name: '发展会员人数', count: res.developNum },\n    //       { name: '推广订单总数', count: res.orderNum },\n    //       { name: '推广订单金额（元）', count: res.orderPriceCount },\n    //       { name: '提现次数', count: res.withdrawCount }\n    //     ]\n    //   })\n    // },\n    // 清除\n    clearSpread(row) {\n      this.$modalSure('解除【' + row.nickname + '】的上级推广人吗').then(() => {\n        spreadClearApi(row.uid).then((res) => {\n          this.$message.success('清除成功')\n          this.getList()\n        })\n      })\n    },\n    onSpread(uid, n, p) {\n      this.onName = n\n      this.titleName = p\n      this.uid = uid\n      this.dialogVisible = true\n      this.spreadFrom = {\n        page: 1,\n        limit: 10,\n        dateLimit: '',\n        type: 0,\n        nickName: '',\n        uid: uid\n      }\n      this.getListSpread()\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    // 选择时间\n    selectChangeSpread(tab) {\n      this.timeValSpread = []\n      this.spreadFrom.dateLimit = tab\n      this.spreadFrom.page = 1\n      this.onName === 'man' ? this.getListSpread() : this.getSpreadOrderList()\n    },\n    // 具体日期\n    onchangeTimeSpread(e) {\n      this.timeValSpread = e\n      this.tableFrom.dateLimit = e ? this.timeValSpread.join(',') : ''\n      this.spreadFrom.page = 1\n      this.onName === 'man' ? this.getListSpread() : this.getSpreadOrderList()\n    },\n    onChanges() {\n      this.spreadFrom.page = 1\n      this.onName === 'man' ? this.getListSpread() : this.getSpreadOrderList()\n    },\n    // 推广人列表\n    getListSpread() {\n      this.spreadLoading = true\n      spreadListApi({ page: this.spreadFrom.page, limit: this.spreadFrom.limit}, this.spreadFrom).then(res => {\n        this.spreadData.data = res.list\n        this.spreadData.total = res.total\n        this.spreadLoading = false\n      }).catch(() => {\n        this.spreadLoading = false\n      })\n    },\n    pageChangeSpread(page) {\n      this.spreadFrom.page = page\n      this.onName === 'man' ? this.getListSpread(this.uid) : this.getSpreadOrderList(this.uid)\n    },\n    handleSizeChangeSpread(val) {\n      this.spreadFrom.limit = val\n      this.onName === 'man' ? this.getListSpread(this.uid) : this.getSpreadOrderList(this.uid)\n    },\n    // 推广订单\n    onSpreadOrder(uid, n, p) {\n      this.uid = uid\n      this.onName = n\n      this.titleName = p\n      this.dialogVisible = true\n      this.spreadFrom = {\n        page: 1,\n        limit: 10,\n        dateLimit: '',\n        type: 0,\n        nickName: '',\n        uid: uid\n      }\n      this.getSpreadOrderList()\n    },\n    getSpreadOrderList() {\n      this.spreadLoading = true\n      spreadOrderListApi({ page: this.spreadFrom.page, limit: this.spreadFrom.limit}, this.spreadFrom).then(res => {\n        this.spreadData.data = res.list\n        this.spreadData.total = res.total\n        this.spreadLoading = false\n      }).catch(() => {\n        this.spreadLoading = false\n      })\n    },\n    selectChange(tab) {\n      this.tableFrom.dateLimit = tab\n      this.tableFrom.page = 1\n      this.timeVal = []\n      // this.spreadStatistics()\n      this.getList()\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      promoterListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    }\n  }\n}\n", null]}