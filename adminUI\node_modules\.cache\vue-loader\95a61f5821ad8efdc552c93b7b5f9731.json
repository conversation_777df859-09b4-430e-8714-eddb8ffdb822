{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue?vue&type=template&id=b1502590&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-dialog',{staticClass:\"order_box\",attrs:{\"visible\":_vm.modals,\"title\":\"发送货\",\"before-close\":_vm.handleClose,\"width\":\"600px\"},on:{\"update:visible\":function($event){_vm.modals=$event}}},[_c('el-form',{ref:\"formItem\",attrs:{\"model\":_vm.formItem,\"label-width\":\"110px\",\"rules\":_vm.rules},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',{attrs:{\"label\":\"选择类型：\"}},[_c('el-radio-group',{on:{\"change\":function($event){return _vm.changeRadioType(_vm.formItem.type)}},model:{value:(_vm.formItem.type),callback:function ($$v) {_vm.$set(_vm.formItem, \"type\", $$v)},expression:\"formItem.type\"}},[_c('el-radio',{attrs:{\"label\":\"1\"}},[_vm._v(\"发货\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"2\"}},[_vm._v(\"送货\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"3\"}},[_vm._v(\"虚拟\")])],1)],1),_vm._v(\" \"),(_vm.formItem.type==='1')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"发货类型：\",\"prop\":\"expressId\"}},[_c('el-radio-group',{on:{\"change\":function($event){return _vm.changeRadio(_vm.formItem.expressRecordType)}},model:{value:(_vm.formItem.expressRecordType),callback:function ($$v) {_vm.$set(_vm.formItem, \"expressRecordType\", $$v)},expression:\"formItem.expressRecordType\"}},[_c('el-radio',{attrs:{\"label\":\"1\"}},[_vm._v(\"手动填写\")]),_vm._v(\" \"),(_vm.checkPermi(['admin:order:sheet:info']))?_c('el-radio',{attrs:{\"label\":\"2\"}},[_vm._v(\"电子面单打印\")]):_vm._e()],1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"快递公司：\",\"prop\":\"expressCode\"}},[_c('el-select',{staticStyle:{\"width\":\"80%\"},attrs:{\"filterable\":\"\"},on:{\"change\":function($event){return _vm.onChangeExport(_vm.formItem.expressCode)}},model:{value:(_vm.formItem.expressCode),callback:function ($$v) {_vm.$set(_vm.formItem, \"expressCode\", $$v)},expression:\"formItem.expressCode\"}},_vm._l((_vm.express),function(item,i){return _c('el-option',{key:i,attrs:{\"value\":item.code,\"label\":item.name}})}),1)],1),_vm._v(\" \"),(_vm.formItem.expressRecordType === '1')?_c('el-form-item',{attrs:{\"label\":\"快递单号：\",\"prop\":\"expressNumber\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入快递单号\"},model:{value:(_vm.formItem.expressNumber),callback:function ($$v) {_vm.$set(_vm.formItem, \"expressNumber\", $$v)},expression:\"formItem.expressNumber\"}})],1):_vm._e(),_vm._v(\" \"),(_vm.formItem.expressRecordType === '2')?[_c('el-form-item',{staticClass:\"express_temp_id\",attrs:{\"label\":\"电子面单：\",\"prop\":\"expressTempId\"}},[_c('div',{staticClass:\"acea-row\"},[_c('el-select',{class:[_vm.formItem.expressTempId?'width9':'width8'],attrs:{\"placeholder\":\"请选择电子面单\"},on:{\"change\":_vm.onChangeImg},model:{value:(_vm.formItem.expressTempId),callback:function ($$v) {_vm.$set(_vm.formItem, \"expressTempId\", $$v)},expression:\"formItem.expressTempId\"}},_vm._l((_vm.exportTempList),function(item,i){return _c('el-option',{key:i,attrs:{\"value\":item.temp_id,\"label\":item.title}})}),1),_vm._v(\" \"),(_vm.formItem.expressTempId)?_c('div',{staticStyle:{\"position\":\"relative\"}},[_c('div',{staticClass:\"tempImgList ml10\"},[_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":_vm.tempImg,\"preview-src-list\":[_vm.tempImg]}})],1)])]):_vm._e()],1)]),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"寄件人姓名：\",\"prop\":\"toName\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人姓名\"},model:{value:(_vm.formItem.toName),callback:function ($$v) {_vm.$set(_vm.formItem, \"toName\", $$v)},expression:\"formItem.toName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"寄件人电话：\",\"prop\":\"toTel\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人电话\"},model:{value:(_vm.formItem.toTel),callback:function ($$v) {_vm.$set(_vm.formItem, \"toTel\", $$v)},expression:\"formItem.toTel\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"寄件人地址：\",\"prop\":\"toAddr\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入寄件人地址\"},model:{value:(_vm.formItem.toAddr),callback:function ($$v) {_vm.$set(_vm.formItem, \"toAddr\", $$v)},expression:\"formItem.toAddr\"}})],1)]:_vm._e()],2):_vm._e(),_vm._v(\" \"),(_vm.formItem.type==='2')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"送货人姓名：\",\"prop\":\"deliveryName\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入送货人姓名\"},model:{value:(_vm.formItem.deliveryName),callback:function ($$v) {_vm.$set(_vm.formItem, \"deliveryName\", $$v)},expression:\"formItem.deliveryName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"送货人电话：\",\"prop\":\"deliveryTel\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入送货人电话\"},model:{value:(_vm.formItem.deliveryTel),callback:function ($$v) {_vm.$set(_vm.formItem, \"deliveryTel\", $$v)},expression:\"formItem.deliveryTel\"}})],1)],1):_vm._e(),_vm._v(\" \"),_c('div',[_c('el-form-item',{attrs:{\"label\":\"\"}},[_c('div',{staticStyle:{\"color\":\"#CECECE\"}},[_vm._v(\"顺丰请输入单号：收件人或寄件人手机号后四位\")]),_vm._v(\" \"),_c('div',{staticStyle:{\"color\":\"#CECECE\"}},[_vm._v(\"例如：SF000000000000:3941\")])])],1)],1),_vm._v(\" \"),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.putSend('formItem')}}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('el-button',{on:{\"click\":function($event){return _vm.cancel('formItem')}}},[_vm._v(\"取消\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}