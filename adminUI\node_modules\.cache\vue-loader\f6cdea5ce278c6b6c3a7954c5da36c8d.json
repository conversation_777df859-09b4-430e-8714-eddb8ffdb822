{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { isNumberStr } from '../utils/index'\nimport { getTreeNodeId, saveTreeNodeId } from '../utils/db'\n\nconst id = getTreeNodeId()\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data() {\n    return {\n      id,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [\n          {\n            required: true,\n            message: '请输入选项名',\n            trigger: 'blur'\n          }\n        ],\n        value: [\n          {\n            required: true,\n            message: '请输入选项值',\n            trigger: 'blur'\n          }\n        ]\n      },\n      dataType: 'string',\n      dataTypeOptions: [\n        {\n          label: '字符串',\n          value: 'string'\n        },\n        {\n          label: '数字',\n          value: 'number'\n        }\n      ]\n    }\n  },\n  computed: {},\n  watch: {\n    // eslint-disable-next-line func-names\n    'formData.value': function(val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\n    },\n    id(val) {\n      saveTreeNodeId(val)\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      }\n    },\n    onClose() {},\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handelConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        if (this.dataType === 'number') {\n          this.formData.value = parseFloat(this.formData.value)\n        }\n        this.formData.id = this.id++\n        this.$emit('commit', this.formData)\n        this.close()\n      })\n    }\n  }\n}\n", null]}