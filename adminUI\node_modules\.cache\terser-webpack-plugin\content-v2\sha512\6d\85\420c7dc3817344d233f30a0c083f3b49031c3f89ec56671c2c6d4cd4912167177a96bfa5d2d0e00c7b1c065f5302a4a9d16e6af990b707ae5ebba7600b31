{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-5504f65c\"],{\"0aac\":function(t,e,r){\"use strict\";r(\"3ebb\")},\"3ebb\":function(t,e,r){},8904:function(t,e,r){\"use strict\";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r(\"div\",{staticClass:\"divBox relative\"},[r(\"el-card\",{staticClass:\"box-card\"},[r(\"div\",{staticClass:\"container mt-1\"},[r(\"el-form\",{attrs:{inline:\"\",size:\"small\"},model:{value:t.searchFrom,callback:function(e){t.searchFrom=e},expression:\"searchFrom\"}},[r(\"el-form-item\",{attrs:{label:t.$t(\"chainTransferRecord.keyword\")+\"：\"}},[r(\"el-input\",{attrs:{placeholder:t.$t(\"chainTransferRecord.enterProductName\"),clearable:\"\"},model:{value:t.searchFrom.keyword,callback:function(e){t.$set(t.searchFrom,\"keyword\",e)},expression:\"searchFrom.keyword\"}})],1),t._v(\" \"),r(\"el-form-item\",{attrs:{label:t.$t(\"chainTransferRecord.brandName\")+\"：\"}},[r(\"el-select\",{attrs:{placeholder:t.$t(\"common.all\"),clearable:\"\"},model:{value:t.searchFrom.brandCode,callback:function(e){t.$set(t.searchFrom,\"brandCode\",e)},expression:\"searchFrom.brandCode\"}},t._l(t.brandList,(function(t){return r(\"el-option\",{key:t.code,attrs:{label:t.name,value:t.code}})})),1)],1)],1)],1),t._v(\" \"),r(\"el-button\",{staticClass:\"mr10\",attrs:{size:\"small\",type:\"primary\"},on:{click:function(e){return t.getList(1)}}},[t._v(\"\\n      \"+t._s(t.$t(\"chainTransferRecord.query\"))+\"\\n    \")]),t._v(\" \"),r(\"el-button\",{staticClass:\"mr10\",attrs:{size:\"small\",type:\"\"},on:{click:t.resetForm}},[t._v(\"\\n      \"+t._s(t.$t(\"chainTransferRecord.reset\"))+\"\\n    \")])],1),t._v(\" \"),r(\"el-card\",{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[r(\"div\",{staticClass:\"clearfix\",attrs:{slot:\"header\"},slot:\"header\"},[r(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"admin:financialCenter:request:upload\"],expression:\"['admin:financialCenter:request:upload']\"}],attrs:{type:\"primary\",size:\"small\"}},[t._v(\"\\n        \"+t._s(t.$t(\"chainTransferRecord.exportExcel\"))+\"\\n      \")])],1),t._v(\" \"),r(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:t.loading,expression:\"loading\"}],attrs:{data:t.tableData,size:\"small\",\"header-cell-style\":{fontWeight:\"bold\"}}},[r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.serialNumber\"),type:\"index\",width:\"110\"}}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.nickname\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.userAccount)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.tiktokId\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.tiktokUid)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.originalLink\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.originUrl)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.rebateLink\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.shareUrl)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.operationTime\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.operateTime)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.linkSource\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.channel)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.productId\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.productId)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.productName\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(e.row.productName)))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.productPrice\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[r(\"span\",[t._v(t._s(t._f(\"filterEmpty\")(t.formatAmount(e.row.productPrice))))])]}}])}),t._v(\" \"),r(\"el-table-column\",{attrs:{label:t.$t(\"chainTransferRecord.productCashbackRate\")},scopedSlots:t._u([{key:\"default\",fn:function(e){return[t._v(t._s(t.formatRate(e.row.productCashbackRate)))]}}])})],1),t._v(\" \"),r(\"el-pagination\",{staticClass:\"mt20\",attrs:{\"current-page\":t.searchFrom.page,\"page-sizes\":[20,40,60,100],\"page-size\":t.searchFrom.limit,layout:\"total, sizes, prev, pager, next, jumper\",total:t.searchFrom.total},on:{\"size-change\":t.sizeChange,\"current-change\":t.pageChange}})],1)],1)},n=[],o=r(\"b775\");function c(t){return Object(o[\"a\"])({url:\"/admin/store/productShareRecord/list\",method:\"get\",params:t})}var s=r(\"9add\"),i={name:\"ChainTransferRecord\",data:function(){return{loading:!1,searchFrom:{keyword:\"\",brandCode:\"\",page:1,limit:20,total:0},tableData:[],brandList:[]}},created:function(){},mounted:function(){this.getList(),this.getBrandList()},methods:{formatAmount:function(t){void 0==t&&(t=0);var e=(t/1e3).toFixed(3);return e},getList:function(t){var e=this;this.loading=!0,this.searchFrom.page=t||this.searchFrom.page,c(this.searchFrom).then((function(t){e.tableData=t.list||[],e.searchFrom.total=t.total,e.loading=!1})).catch((function(){e.loading=!1}))},getBrandList:function(){var t=this,e={page:1,limit:999999,name:\"\",type:\"-1\"};Object(s[\"f\"])(e).then((function(e){t.brandList=e.list})).catch((function(e){t.$message.error(t.$t(\"common.fetchDataFailed\"))}))},pageChange:function(t){this.searchFrom.page=t,this.getList()},sizeChange:function(t){this.searchFrom.limit=t,this.getList()},resetForm:function(){this.searchFrom={keyword:\"\",brandCode:\"\",page:1,limit:20,total:0},this.getList()},formatRate:function(t){return parseInt(1e4*t)/100+\"%\"}}},l=i,u=(r(\"0aac\"),r(\"2877\")),d=Object(u[\"a\"])(l,a,n,!1,null,\"102b558c\",null);e[\"default\"]=d.exports},\"9add\":function(t,e,r){\"use strict\";r.d(e,\"f\",(function(){return n})),r.d(e,\"j\",(function(){return o})),r.d(e,\"a\",(function(){return c})),r.d(e,\"e\",(function(){return s})),r.d(e,\"h\",(function(){return i})),r.d(e,\"i\",(function(){return l})),r.d(e,\"g\",(function(){return u})),r.d(e,\"c\",(function(){return d})),r.d(e,\"d\",(function(){return f})),r.d(e,\"b\",(function(){return m})),r.d(e,\"k\",(function(){return h}));var a=r(\"b775\");function n(t){return Object(a[\"a\"])({url:\"/admin/brand/list\",method:\"GET\",params:t})}function o(t){return Object(a[\"a\"])({url:\"/admin/brand/update\",method:\"POST\",data:t})}function c(t){return Object(a[\"a\"])({url:\"/admin/brand/add\",method:\"POST\",data:t})}function s(t){return Object(a[\"a\"])({url:\"/admin/brand/batchUpdate\",method:\"POST\",data:t})}function i(t){return Object(a[\"a\"])({url:\"/admin/system/group/data/list?gid=\"+t,method:\"GET\"})}function l(t){return Object(a[\"a\"])({url:\"/admin/store/product/list\",method:\"GET\",params:t})}function u(t,e){return Object(a[\"a\"])({url:\"/admin/store/product/importProduct?form=\"+t+\"&url=\"+e,method:\"POST\"})}function d(t){return Object(a[\"a\"])({url:\"/admin/store/product/batch/putOnShell\",method:\"POST\",data:t})}function f(t){return Object(a[\"a\"])({url:\"/admin/store/product/batch/offShell\",method:\"POST\",data:t})}function m(t){return Object(a[\"a\"])({url:\"/admin/store/product/batch/delete\",method:\"POST\",data:t})}function h(t){return Object(a[\"a\"])({url:\"/admin/store/product/update\",method:\"POST\",data:t})}}}]);", "extractedComments": []}