{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\manage.vue", "mtime": 1754397765195}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\n// import { productLstApi, productDeleteApi, categoryApi, putOnShellApi, offShellApi, productHeadersApi, productExportApi, restoreApi, productExcelApi } from '@/api/store'\r\nimport {\r\n  brandLstApi,\r\n  addBrand,\r\n  updateBrandInfo,\r\n  batchUpdateBrandInfo,\r\n  industryLstApi\r\n} from \"@/api/brand\";\r\nimport { getToken } from \"@/utils/auth\";\r\n// import taoBao from './taoBao'\r\n// import UploadIndex from '@/components/uploadPicture/index.vue'\r\n\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nexport default {\r\n  name: \"BrandProductList\",\r\n  // components: { UploadIndex },\r\n  data() {\r\n    return {\r\n      pictureType: \"maintain\",\r\n      // 品牌状态常量\r\n      BRAND_STATUS: {\r\n        DELETED: \"-1\",    // 已删除\r\n        OFFLINE: \"0\",     // 待上架\r\n        ONLINE: \"1\",      // 已上架\r\n        SUSPENDED: \"2\"    // 已下架\r\n      },\r\n      /*\r\n       * 品牌状态使用规范：\r\n       * - 新增品牌：默认 \"1\"(已上架)，可选待上架\r\n       * - 审核通过：从 \"0\" → \"1\"(已上架)\r\n       * - 临时下架：从 \"1\" → \"2\"(已下架)\r\n       * - 重新上架：从 \"2\" → \"1\"(已上架)\r\n       * - 逻辑删除：任何状态 → \"-1\"(已删除)\r\n       */\r\n      statusOptions: [\r\n        {\r\n          value: \"-1\",\r\n          label: \"brand.pleaseSelect\"\r\n        },\r\n        {\r\n          value: \"0\", // BRAND_STATUS.OFFLINE\r\n          label: \"brand.isOutline\"\r\n        },\r\n        {\r\n          value: \"1\", // BRAND_STATUS.ONLINE\r\n          label: \"brand.isOnline\"\r\n        },\r\n        {\r\n          value: \"2\", // BRAND_STATUS.SUSPENDED\r\n          label: \"brand.isOuted\"\r\n        }\r\n      ],\r\n      locationOptions: [\r\n        {\r\n          value: \"all\",\r\n          label: \"common.pleaseSelect\"\r\n        },\r\n        {\r\n          value: \"yes\",\r\n          label: \"common.yes\"\r\n        },\r\n        {\r\n          value: \"no\",\r\n          label: \"common.no\"\r\n        }\r\n      ],\r\n      industryOptions: [\r\n        {\r\n          value: \"1\",\r\n          label: \"2\"\r\n        }\r\n      ],\r\n      platformOptions: [\r\n        {\r\n          value: \"tiktok\",\r\n          label: \"brand.platformTiktok\"\r\n        },\r\n        {\r\n          value: \"shopee\",\r\n          label: \"brand.platformShopee\"\r\n        }\r\n      ],\r\n      typeOptions: [\r\n        {\r\n          value: \"1\",\r\n          label: \"common.yes\"\r\n        },\r\n        {\r\n          value: \"0\",\r\n          label: \"common.no\"\r\n        }\r\n      ],\r\n      editStatusOptions: [\r\n        {\r\n          value: \"0\", // BRAND_STATUS.OFFLINE - 待上架\r\n          label: \"brand.isOutline\"\r\n        },\r\n        {\r\n          value: \"1\", // BRAND_STATUS.ONLINE - 已上架\r\n          label: \"brand.isOnline\"\r\n        },\r\n        {\r\n          value: \"2\", // BRAND_STATUS.SUSPENDED - 已下架\r\n          label: \"brand.isOuted\"\r\n        }\r\n      ],\r\n      categoryOptions: [],\r\n      loading: false,\r\n      listLoading: false,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      form: {\r\n        page: 1,\r\n        limit: 20,\r\n        name: \"\",\r\n        type: \"-1\"\r\n      },\r\n      dform: {\r\n        id: \"\",\r\n        name: \"\",\r\n        image: \"\",\r\n        logoUrl: \"\",\r\n        industry: \"\",\r\n        platform: \"\",\r\n        status: \"\",\r\n        contactPhone: \"\",\r\n        contactPerson: \"\"\r\n      },\r\n      brandDialogVisible: false,\r\n      isEditMode: false, // 标识是否为编辑模式\r\n      multipleSelection: [],\r\n      industryListOptions: []\r\n    };\r\n  },\r\n  mounted() {\r\n    let _this = this;\r\n    this.getList();\r\n    industryLstApi(74)\r\n      .then(res => {\r\n        res.list.forEach(item => {\r\n          let values = item.value;\r\n          let fields = JSON.parse(values);\r\n          let one = {};\r\n\r\n          fields.fields.forEach(v => {\r\n            console.log(v);\r\n            if (v.name == \"code\") {\r\n              one[\"value\"] = v.value;\r\n            } else if (v.name == \"name\") {\r\n              one[\"label\"] = v.value;\r\n            }\r\n          });\r\n          _this.industryListOptions.push(one);\r\n        });\r\n      })\r\n      .catch(res => {});\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n\r\n    onSearch() {\r\n      this.form.page = 1;\r\n      this.getList();\r\n    },\r\n\r\n    onReset() {\r\n      this.form.name = \"\";\r\n      this.form.type = \"-1\";\r\n    },\r\n\r\n    getList() {\r\n      this.listLoading = true;\r\n      brandLstApi(this.form)\r\n        .then(res => {\r\n          this.tableData.data = res.list;\r\n          this.tableData.total = res.total;\r\n          this.listLoading = false;\r\n        })\r\n        .catch(res => {\r\n          this.listLoading = false;\r\n          this.$message.error(this.$t(\"common.fetchDataFailed\"));\r\n        });\r\n    },\r\n\r\n    pageChange(page) {\r\n      this.form.page = page;\r\n      this.getList();\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.form.limit = val;\r\n      this.getList();\r\n    },\r\n\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.multipleSelection = selection;\r\n    },\r\n\r\n    handleDelete(rowId, idx) {\r\n      let _this = this;\r\n      let rows = [];\r\n\r\n      this.$confirm(\r\n        this.$t(\"brand.confirmOperation\"),\r\n        this.$t(\"brand.prompt\"),\r\n        {\r\n          confirmButtonText: this.$t(\"brand.confirm\"),\r\n          cancelButtonText: this.$t(\"brand.cancel\"),\r\n          type: \"warning\",\r\n          showClose: false\r\n        }\r\n      ).then(() => {\r\n        // 状态流转：任何状态 → \"-1\"(已删除)\r\n        let item = { id: rowId, status: this.BRAND_STATUS.DELETED };\r\n        rows.push(item);\r\n        batchUpdateBrandInfo(rows)\r\n          .then(res => {\r\n            _this.getList();\r\n          })\r\n          .catch(res => {});\r\n      });\r\n    },\r\n\r\n    onAdd() {\r\n      this.isEditMode = false;\r\n      this.dform = {\r\n        id: \"\",\r\n        name: \"\",\r\n        image: \"\",\r\n        logoUrl: \"\",\r\n        industry: \"\",\r\n        platform: \"\",\r\n        status: this.BRAND_STATUS.ONLINE, // 新增品牌默认为已上架状态\r\n        contactPhone: \"\",\r\n        contactPerson: \"\"\r\n      };\r\n      this.brandDialogVisible = true;\r\n    },\r\n\r\n    handleCloseBrandDialog() {\r\n      this.brandDialogVisible = false;\r\n      this.isEditMode = false;\r\n    },\r\n\r\n    editBrand(row, idx) {\r\n      this.isEditMode = true;\r\n      this.dform = { ...row }; // 使用展开运算符避免直接引用\r\n      this.brandDialogVisible = true;\r\n    },\r\n\r\n    onSubBrand() {\r\n      if (this.isEditMode) {\r\n        // 编辑模式，调用更新接口\r\n        updateBrandInfo(this.dform)\r\n          .then(res => {\r\n            this.brandDialogVisible = false;\r\n            this.isEditMode = false;\r\n            this.$message.success(this.$t(\"common.operationSuccess\"));\r\n            this.getList(); // 刷新列表\r\n          })\r\n          .catch(res => {\r\n            this.$message.error(this.$t(\"common.operationFailed\"));\r\n          });\r\n      } else {\r\n        // 新增模式，调用新增接口\r\n        addBrand(this.dform)\r\n          .then(res => {\r\n            this.brandDialogVisible = false;\r\n            this.$message.success(this.$t(\"common.operationSuccess\"));\r\n            this.getList(); // 刷新列表\r\n          })\r\n          .catch(res => {\r\n            this.$message.error(this.$t(\"common.operationFailed\"));\r\n          });\r\n      }\r\n    },\r\n\r\n    handleUpdate(row, idx) {\r\n      let _this = this;\r\n      let rows = [];\r\n      this.$confirm(\r\n        this.$t(\"brand.confirmOperation\"),\r\n        this.$t(\"brand.prompt\"),\r\n        {\r\n          confirmButtonText: _this.$t(\"brand.confirm\"),\r\n          cancelButtonText: _this.$t(\"brand.cancel\"),\r\n          type: \"warning\",\r\n          showClose: false\r\n        }\r\n      ).then(() => {\r\n        let item = { id: row.id };\r\n        // 根据状态使用规范进行状态流转\r\n        if (row.status == this.BRAND_STATUS.ONLINE) {\r\n          // 已上架 → 已下架\r\n          item.status = this.BRAND_STATUS.SUSPENDED;\r\n        } else if (row.status == this.BRAND_STATUS.SUSPENDED) {\r\n          // 已下架 → 已上架\r\n          item.status = this.BRAND_STATUS.ONLINE;\r\n        } else if (row.status == this.BRAND_STATUS.OFFLINE) {\r\n          // 待上架 → 已上架\r\n          item.status = this.BRAND_STATUS.ONLINE;\r\n        }\r\n        rows.push(item)\r\n        batchUpdateBrandInfo(rows)\r\n          .then(res => {\r\n            _this.getList();\r\n          })\r\n          .catch(res => {});\r\n      });\r\n    },\r\n\r\n    batchHandle(type) {\r\n      let _this = this;\r\n      let rows = [];\r\n      if(this.multipleSelection.length<=0) {\r\n        this.$alert(this.$t(\"brand.selectTip\"), this.$t('brand.prompt'), {\r\n          confirmButtonText: _this.$t(\"brand.confirm\"),\r\n          type: 'warning',\r\n          showConfirmButton:false\r\n        });\r\n      }else{\r\n          this.$confirm(\r\n            this.$t(\"brand.confirmOperation\"),\r\n            this.$t(\"brand.prompt\"),\r\n            {\r\n              confirmButtonText: this.$t(\"brand.confirm\"),\r\n              cancelButtonText: this.$t(\"brand.cancel\"),\r\n              type: \"warning\",\r\n              showClose: false\r\n            }\r\n          ).then(() => {\r\n            _this.multipleSelection.forEach(row => {\r\n              let item = { id: row.id };\r\n              // 根据状态使用规范设置目标状态\r\n              if (type == \"online\") {\r\n                // 状态流转：待上架/已下架 → 已上架\r\n                item[\"status\"] = this.BRAND_STATUS.ONLINE;\r\n              } else if (type == \"outline\") {\r\n                // 状态流转：已上架 → 已下架\r\n                item[\"status\"] = this.BRAND_STATUS.SUSPENDED;\r\n              } else if (type == \"delete\") {\r\n                // 状态流转：任何状态 → 已删除\r\n                item[\"status\"] = this.BRAND_STATUS.DELETED;\r\n              }\r\n              rows.push(item);\r\n            });\r\n            if (rows.length > 0) {\r\n              this.listLoading = true;\r\n              batchUpdateBrandInfo(rows)\r\n                .then(res => {\r\n                  _this.getList();\r\n                })\r\n                .catch(res => {});\r\n            }\r\n          });\r\n      }\r\n    },\r\n\r\n    isHighCashbackChange(rowId, val) {\r\n      let _this = this;\r\n      let rows = [];\r\n\r\n      let item = { id: rowId };\r\n      if (val) {\r\n        item[\"isHighCashback\"] = true;\r\n      } else {\r\n        item[\"isHighCashback\"] = false;\r\n      }\r\n      rows.push(item);\r\n      batchUpdateBrandInfo(rows)\r\n        .then(res => {\r\n          _this.getList();\r\n        })\r\n        .catch(res => {});\r\n    },\r\n\r\n    isHotChange(rowId, val) {\r\n      let _this = this;\r\n      let rows = [];\r\n      let item = { id: rowId };\r\n      if (val) {\r\n        item[\"isHot\"] = true;\r\n      } else {\r\n        item[\"isHot\"] = false;\r\n      }\r\n      rows.push(item);\r\n      batchUpdateBrandInfo(rows)\r\n        .then(res => {\r\n          _this.getList();\r\n        })\r\n        .catch(res => {});\r\n    }\r\n  }\r\n};\r\n", null]}