
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/revenue_info.dart';
import 'package:milestone/pages/withdrawal/withdrawal_screen.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/string.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/loading_dialog.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:logger/logger.dart';

class TaskCenterScreen extends StatefulWidget {
  const TaskCenterScreen({super.key});

  @override
  State<TaskCenterScreen> createState() => _TaskCenterScreenState();
}

class _TaskCenterScreenState extends State<TaskCenterScreen> {
  // 默认任务数据（API加载失败时使用）
  int defaultTargetInviteCount = 3;
  int defaultTargetOrderCount = 1;
  double defaultRewardAmount = 50000; // 每次完成任务可兑换的奖励金额（印尼盾）

  // 拉新奖励配置数据
  Map<String, dynamic>? referralRewardConfig;
  bool isLoadingConfig = true;

  @override
  void initState() {
    super.initState();
    _loadReferralRewardConfig();
  }

  // 加载拉新奖励配置
  Future<void> _loadReferralRewardConfig() async {
    try {
      final response = await networkApiClient.getReferralRewardConfig();
      Logger().d('API原始响应: $response');

      // 检查响应结构
      if (response['data'] != null) {
        final config = response['data'];
        setState(() {
          referralRewardConfig = config;
          isLoadingConfig = false;
        });
        Logger().d('拉新奖励配置加载成功: $config');
        Logger().d('referralCount: ${config['referralCount']}, firstOrderCount: ${config['firstOrderCount']}, rewardAmount: ${config['rewardAmount']}');
      } else {
        setState(() {
          referralRewardConfig = response;
          isLoadingConfig = false;
        });
        Logger().d('使用整个响应作为配置: $response');
      }
    } catch (e) {
      setState(() {
        isLoadingConfig = false;
      });
      Logger().e('加载拉新奖励配置失败: $e');
    }
  }

  // 安全地将动态类型转换为double
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    // 处理BigDecimal等其他数字类型
    if (value is num) return value.toDouble();
    // 尝试转换为字符串再解析
    try {
      return double.parse(value.toString());
    } catch (e) {
      Logger().w('无法解析为double: $value, 错误: $e');
      return null;
    }
  }

  // 安全地将动态类型转换为int
  int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value);
    }
    // 处理其他数字类型
    if (value is num) return value.toInt();
    // 尝试转换为字符串再解析
    try {
      return int.parse(value.toString());
    } catch (e) {
      Logger().w('无法解析为int: $value, 错误: $e');
      return null;
    }
  }

  // 获取用户余额
  double _getUserBalance() {
    return _parseDouble(referralRewardConfig?['userBalance']) ?? 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).task_center_title,
      backgroundImage: Assets.imagesBgBrandHomeTop,
      child: Column(
        children: [
          // 顶部留白，让背景图片显示
          SizedBox(height: 60),
          // 固定的收入卡片
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildIncomeCard(context),
          ),
          const SizedBox(height: 24),
          // 固定的任务列表标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                S.of(context).task_daily_tasks,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // 可滚动的任务列表区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15.0, sigmaY: 15.0),
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.08),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.6),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.15),
                            blurRadius: 15,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: _buildTaskGrid(context),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20), // 底部留白
        ],
      ),
    );
  }

  Widget _buildIncomeCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildIncomeItem(
                  context,
                  S.of(context).task_cash_income,
                  "Rp${_getUserBalance().formatIDR()}",
                  S.of(context).task_withdrawable_amount,
                  Icons.account_balance_wallet,
                  incomeColor,
                ),
              ),
              const SizedBox(width: 16),
              _buildWithdrawButton(context),
            ],
          ),

        ],
      ),
    );
  }

  Widget _buildIncomeItem(
    BuildContext context,
    String title,
    String amount,
    String subtitle,
    IconData icon,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: iconColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                color: secondaryTextColor,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          amount,
          style: TextStyle(
            color: primaryTextColor,
            fontSize: 24,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(
            color: secondaryTextColor.withValues(alpha: 0.7),
            fontSize: 10,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildWithdrawButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 检查登录状态
        if (!isLogin()) {
          makeToast("请先登录");
          return;
        }
        
        // 创建收入信息用于提现页面
        double userBalance = _getUserBalance();
        final revenueInfo = RevenueInfo(
          estimatedTotalAmount: userBalance.toString(),
          estimatedTodayAmount: "0",
          pendingAmount: "0",
          receivedAmount: userBalance.toString(),
          withdrawableAmount: userBalance.toString(),
        );
        
        // 跳转到提现页面
        customRouter(context, WithdrawalScreen(revenueInfo: revenueInfo));
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [selectedTabColor, expenseColor],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          S.of(context).task_withdraw,
          style: TextStyle(
            color: fourthTextColor,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildTaskSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16),
          child: Text(
            S.of(context).task_daily_tasks,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        _buildTaskGrid(context),
      ],
    );
  }

  Widget _buildTaskGrid(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          // 直接显示邀新首单任务，不用毛玻璃容器
          _buildInviteRewardTask(context),
          const SizedBox(height: 20), // 底部额外留白
        ],
      ),
    );
  }

  Widget _buildInviteRewardTask(BuildContext context) {
    // 如果配置还在加载中，显示加载状态
    if (isLoadingConfig) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: CircularProgressIndicator(color: incomeColor),
        ),
      );
    }

    // 从API配置中获取数据，如果没有则使用默认值
    int apiTargetInviteCount = _parseInt(referralRewardConfig?['referralCount']) ?? defaultTargetInviteCount;
    int apiTargetOrderCount = _parseInt(referralRewardConfig?['firstOrderCount']) ?? defaultTargetOrderCount;

    // 获取真实的邀请和订单数据
    int totalReferralCount = _parseInt(referralRewardConfig?['totalReferralCount']) ?? 0;
    int redeemedReferralCount = _parseInt(referralRewardConfig?['redeemedReferralCount']) ?? 0;
    int totalFirstOrderCount = _parseInt(referralRewardConfig?['totalFirstOrderCount']) ?? 0;
    int redeemedFirstOrderCount = _parseInt(referralRewardConfig?['redeemedFirstOrderCount']) ?? 0;

    // 计算当前进度
    int currentInviteCount = totalReferralCount - redeemedReferralCount;
    int currentOrderCount = totalFirstOrderCount - redeemedFirstOrderCount;

    // 调试信息
    Logger().d('API配置数据: $referralRewardConfig');
    Logger().d('目标邀请数量: $apiTargetInviteCount (API: ${referralRewardConfig?['referralCount']}, 默认: $defaultTargetInviteCount)');
    Logger().d('目标首单数量: $apiTargetOrderCount (API: ${referralRewardConfig?['firstOrderCount']}, 默认: $defaultTargetOrderCount)');
    Logger().d('当前邀请进度: $currentInviteCount/$apiTargetInviteCount');
    Logger().d('当前订单进度: $currentOrderCount/$apiTargetOrderCount');

    bool inviteConditionMet = currentInviteCount >= apiTargetInviteCount;
    bool orderConditionMet = currentOrderCount >= apiTargetOrderCount;
    bool canComplete = inviteConditionMet && orderConditionMet;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.75),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [selectedTabColor.withValues(alpha: 0.8), expenseColor.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.card_giftcard,
                  color: fourthTextColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context).task_invite_reward,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      canComplete
                        ? S.of(context).task_conditions_met
                        : S.of(context).task_conditions_not_met,
                      style: TextStyle(
                        color: canComplete ? incomeColor : highlightTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (canComplete) {
                    _completeTask();
                  } else {
                    makeToast(S.of(context).task_developing);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: canComplete ? incomeColor : secondaryTextColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    S.of(context).task_go_claim,
                    style: TextStyle(
                      color: fourthTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildRewardAmount(context),
          const SizedBox(height: 12),
          _buildProgressSection(context),
          const SizedBox(height: 12),
          _buildRecordButton(context),
        ],
      ),
    );
  }

  Widget _buildRewardAmount(BuildContext context) {
    // 从API配置中获取奖励金额，如果没有则使用默认值
    double apiRewardAmount = _parseDouble(referralRewardConfig?['rewardAmount']) ?? defaultRewardAmount;

    // 调试信息
    Logger().d('奖励金额: $apiRewardAmount (API: ${referralRewardConfig?['rewardAmount']}, 默认: $defaultRewardAmount)');

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            incomeColor.withValues(alpha: 0.1),
            incomeColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: incomeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: incomeColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.monetization_on,
              color: incomeColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).task_reward_amount,
                  style: TextStyle(
                    color: secondaryTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "Rp${apiRewardAmount.formatIDR()}",
                  style: TextStyle(
                    color: incomeColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: incomeColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              S.of(context).task_per_completion,
              style: TextStyle(
                color: fourthTextColor,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    // 从API配置中获取目标数量，如果没有则使用默认值
    int apiTargetInviteCount = _parseInt(referralRewardConfig?['referralCount']) ?? defaultTargetInviteCount;
    int apiTargetOrderCount = _parseInt(referralRewardConfig?['firstOrderCount']) ?? defaultTargetOrderCount;

    // 获取真实的邀请和订单数据
    int totalReferralCount = _parseInt(referralRewardConfig?['totalReferralCount']) ?? 0;
    int redeemedReferralCount = _parseInt(referralRewardConfig?['redeemedReferralCount']) ?? 0;
    int totalFirstOrderCount = _parseInt(referralRewardConfig?['totalFirstOrderCount']) ?? 0;
    int redeemedFirstOrderCount = _parseInt(referralRewardConfig?['redeemedFirstOrderCount']) ?? 0;

    // 计算当前进度
    int currentInviteCount = totalReferralCount - redeemedReferralCount;
    int currentOrderCount = totalFirstOrderCount - redeemedFirstOrderCount;

    return Column(
      children: [
        _buildProgressItem(
          context,
          S.of(context).task_invite_progress,
          currentInviteCount,
          apiTargetInviteCount,
          Icons.person_add,
        ),
        const SizedBox(height: 16),
        _buildProgressItem(
          context,
          S.of(context).task_order_progress,
          currentOrderCount,
          apiTargetOrderCount,
          Icons.shopping_cart,
        ),
      ],
    );
  }

  Widget _buildProgressItem(BuildContext context, String label, int current, int target, IconData icon) {
    bool isCompleted = current >= target;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted ? incomeColor : secondaryTextColor.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isCompleted ? incomeColor : secondaryTextColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "($current/$target)",
                  style: TextStyle(
                    color: isCompleted ? incomeColor : highlightTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          if (isCompleted)
            Icon(
              Icons.check_circle,
              color: incomeColor,
              size: 24,
            ),
        ],
      ),
    );
  }

  void _completeTask() async {
    try {
      showLoadingDialog();

      // 调用API领取奖励
      bool success = await networkApiClient.claimInviteReward();

      dismissLoadingDialog();

      if (success) {
        makeToast("奖励领取成功！已自动领取所有可用奖励");

        // 重新加载配置以获取最新数据
        _loadReferralRewardConfig();
      } else {
        makeToast("奖励领取失败，请稍后重试");
      }
    } catch (e) {
      dismissLoadingDialog();
      Logger().e('领取奖励失败: $e');

      // 根据错误类型显示不同的提示
      String errorMessage = "奖励领取失败";
      if (e.toString().contains("邀请人数不足")) {
        errorMessage = "邀请人数不足，请继续邀请好友";
      } else if (e.toString().contains("首单数量不足")) {
        errorMessage = "首单数量不足，请等待被邀请人完成首单";
      } else if (e.toString().contains("登录")) {
        errorMessage = "登录已过期，请重新登录";
      }

      makeToast(errorMessage);
    }
  }

  Widget _buildRecordButton(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: () {
          _showRecordDialog(context);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: primaryTextColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.history,
                color: primaryTextColor,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                S.of(context).task_view_record,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRecordDialog(BuildContext context) {
    // 从API配置中获取真实数据
    int totalInvites = _parseInt(referralRewardConfig?['totalReferralCount']) ?? 0;
    int redeemedInvites = _parseInt(referralRewardConfig?['redeemedReferralCount']) ?? 0;
    int totalOrders = _parseInt(referralRewardConfig?['totalFirstOrderCount']) ?? 0;
    int redeemedOrders = _parseInt(referralRewardConfig?['redeemedFirstOrderCount']) ?? 0;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      S.of(context).task_record_title,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: secondaryTextColor,
                        size: 20,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                _buildRecordItem(
                  context,
                  S.of(context).task_total_invites,
                  totalInvites.toString(),
                  Icons.person_add,
                ),
                const SizedBox(height: 12),
                _buildRecordItem(
                  context,
                  S.of(context).task_redeemed_invites,
                  redeemedInvites.toString(),
                  Icons.check_circle,
                ),
                const SizedBox(height: 12),
                _buildRecordItem(
                  context,
                  S.of(context).task_total_orders,
                  totalOrders.toString(),
                  Icons.shopping_cart,
                ),
                const SizedBox(height: 12),
                _buildRecordItem(
                  context,
                  S.of(context).task_redeemed_orders,
                  redeemedOrders.toString(),
                  Icons.check_circle,
                ),
                const SizedBox(height: 20),
                Center(
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      decoration: BoxDecoration(
                        color: incomeColor,
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: Text(
                        S.of(context).task_close,
                        style: TextStyle(
                          color: fourthTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecordItem(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: secondaryTextColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: incomeColor,
            size: 18,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: incomeColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

}
