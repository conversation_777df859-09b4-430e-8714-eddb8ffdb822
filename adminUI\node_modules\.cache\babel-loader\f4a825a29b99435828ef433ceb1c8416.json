{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDetail.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport PriceChange from \"../components/PriceChange\";\nimport ClipboardJS from \"clipboard\";\nimport { orderDetailApi } from '@/api/order';\nimport { required, num } from \"@/utils/validate\";\nimport { validatorDefaultCatch } from \"@/libs/dialog\";\nimport { isWriteOff } from \"@/utils\";\nexport default {\n  name: \"AdminOrder\",\n  components: {\n    PriceChange: PriceChange\n  },\n  props: {},\n  data: function data() {\n    return {\n      isWriteOff: isWriteOff(),\n      order: false,\n      change: false,\n      orderId: '',\n      orderInfo: {},\n      status: 0,\n      title: \"\",\n      payType: \"\",\n      types: \"\"\n    };\n  },\n  watch: {\n    \"$route.params.id\": function $routeParamsId(newVal) {\n      var that = this;\n      if (newVal != undefined) {\n        that.orderId = newVal;\n        that.getIndex();\n      }\n    }\n  },\n  mounted: function mounted() {\n    // this.orderId = this.$route.params.id;\n    this.getIndex();\n    this.$nextTick(function () {\n      var _this = this;\n      var copybtn = document.getElementsByClassName(\"copy-data\");\n      var clipboard = new ClipboardJS(copybtn);\n      clipboard.on(\"success\", function () {\n        _this.$dialog.success(\"复制成功\");\n      });\n    });\n  },\n  methods: {\n    more: function more() {\n      this.order = !this.order;\n    },\n    modify: function modify(status) {\n      this.change = true;\n      this.status = status;\n    },\n    changeclose: function changeclose(msg) {\n      this.change = msg;\n      this.getIndex();\n    },\n    getIndex: function getIndex() {\n      var _this2 = this;\n      var that = this;\n      orderDetailApi({\n        orderNo: this.$route.params.id\n      }).then(function (res) {\n        that.orderInfo = res;\n        that.types = res.statusStr.key;\n        that.title = res.statusStr.value;\n        that.payType = res.payTypeStr;\n        _this2.$nextTick(function () {\n          var _this3 = this;\n          var copybtn = document.getElementsByClassName(\"copy-data\");\n          var clipboard = new ClipboardJS(copybtn);\n          clipboard.on(\"success\", function () {\n            _this3.$dialog.success(\"复制成功\");\n          });\n        });\n      }, function (err) {\n        that.$dialog.error(err.msg);\n      });\n    },\n    // async savePrice(opt) {\n    //   let that = this,\n    //     data = {},\n    //     price = opt.price,\n    //     remark = opt.remark,\n    //     refundStatus = that.orderInfo.refundStatus,\n    //     refundPrice = opt.refundPrice;\n    //   if (that.status == 0 && refundStatus === 0) {\n    //     try {\n    //       await this.$validator({\n    //         price: [\n    //           required(required.message(\"金额\")),\n    //           num(num.message(\"金额\"))\n    //         ]\n    //       }).validate({ price });\n    //     } catch (e) {\n    //       return validatorDefaultCatch(e);\n    //     }\n    //     data.price = price;\n    //     data.orderId  = opt.orderId;\n    //     setAdminOrderPrice(data).then(\n    //       function() {\n    //         that.change = false;\n    //         that.$dialog.success(\"改价成功\");\n    //         that.getIndex();\n    //       },\n    //       function() {\n    //         that.change = false;\n    //         that.$dialog.error(\"改价失败\");\n    //       }\n    //     );\n    //   } else if (that.status == 0 && that.orderInfo.refund_status === 1) {\n    //     try {\n    //       await this.$validator({\n    //         refund_price: [\n    //           required(required.message(\"金额\")),\n    //           num(num.message(\"金额\"))\n    //         ]\n    //       }).validate({ refund_price });\n    //     } catch (e) {\n    //       return validatorDefaultCatch(e);\n    //     }\n    //     data.price = refund_price;\n    //     data.type = opt.type;\n    //     setOrderRefund(data).then(\n    //       res => {\n    //         that.change = false;\n    //         that.$dialog.success(res.msg);\n    //         that.getIndex();\n    //       },\n    //       err => {\n    //         that.change = false;\n    //         that.$dialog.error(err.msg);\n    //         that.getIndex();\n    //       }\n    //     );\n    //   } else {\n    //     try {\n    //       await this.$validator({\n    //         remark: [required(required.message(\"备注\"))]\n    //       }).validate({ remark });\n    //     } catch (e) {\n    //       return validatorDefaultCatch(e);\n    //     }\n    //     data.remark = remark;\n    //     setAdminOrderRemark(data).then(\n    //       res => {\n    //         that.change = false;\n    //         that.$dialog.success(res.msg);\n    //         that.getIndex();\n    //       },\n    //       err => {\n    //         that.change = false;\n    //         that.$dialog.error(err.msg);\n    //       }\n    //     );\n    //   }\n    // },\n    offlinePay: function offlinePay() {\n      var _this4 = this;\n      setOfflinePay({\n        orderId: this.orderInfo.orderId\n      }).then(function (res) {\n        _this4.$dialog.success(res.msg);\n        _this4.getIndex();\n      }, function (err) {\n        _this4.$dialog.error(err.msg);\n      });\n    }\n  }\n};", null]}