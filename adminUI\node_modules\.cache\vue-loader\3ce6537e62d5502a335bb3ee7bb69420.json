{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue?vue&type=template&id=5ab71352", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue", "mtime": 1754269254568}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"100px\" class=\"demo-ruleForm\" >\n  <el-form-item>\n    <el-alert :title=\"$t('user.levelUpgrade.changeWarning')\" type=\"warning\"></el-alert>\n  </el-form-item>\n  <el-form-item :label=\"$t('user.center.userLevel')\" label-width=\"100px\">\n    <el-select v-model=\"ruleForm.levelId\" clearable :placeholder=\"$t('common.pleaseSelect')\" @change=\"currentSel\">\n      <el-option\n        v-for=\"item in levelList\"\n        :key=\"item.grade\"\n        :label=\"item.name\"\n        :value=\"item.id\"\n      >\n        <span style=\"float: left\">{{ item.name }}</span>\n        <span style=\"float: right; color: #8492a6; font-size: 13px\">\n          {{ getUpgradeTypeText(item.upgradeType) }}\n          <span v-if=\"item.upgradeType === 1\"> - Rp {{ item.upgradePrice }}</span>\n        </span>\n      </el-option>\n    </el-select>\n  </el-form-item>\n  <el-form-item :label=\"$t('user.levelUpgrade.deductExperience')\" label-width=\"100px\" v-if=\"grade =='' ? false : grade < levelInfo.gradeLevel\">\n    <el-switch v-model=\"ruleForm.isSub\"></el-switch>\n  </el-form-item>\n  <el-form-item>\n    <el-button @click=\"resetForm('ruleForm')\">{{ $t('common.cancel') }}</el-button>\n    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">{{ $t('common.confirm') }}</el-button>\n  </el-form-item>\n</el-form>\n", null]}