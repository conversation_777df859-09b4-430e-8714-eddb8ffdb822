(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55c0741e"],{"11ed":function(t,e,n){"use strict";n("7620")},7620:function(t,e,n){},"9add":function(t,e,n){"use strict";n.d(e,"f",(function(){return o})),n.d(e,"j",(function(){return i})),n.d(e,"a",(function(){return l})),n.d(e,"e",(function(){return r})),n.d(e,"h",(function(){return s})),n.d(e,"i",(function(){return c})),n.d(e,"g",(function(){return d})),n.d(e,"c",(function(){return u})),n.d(e,"d",(function(){return b})),n.d(e,"b",(function(){return m})),n.d(e,"k",(function(){return f}));var a=n("b775");function o(t){return Object(a["a"])({url:"/admin/brand/list",method:"GET",params:t})}function i(t){return Object(a["a"])({url:"/admin/brand/update",method:"POST",data:t})}function l(t){return Object(a["a"])({url:"/admin/brand/add",method:"POST",data:t})}function r(t){return Object(a["a"])({url:"/admin/brand/batchUpdate",method:"POST",data:t})}function s(t){return Object(a["a"])({url:"/admin/system/group/data/list?gid="+t,method:"GET"})}function c(t){return Object(a["a"])({url:"/admin/store/product/list",method:"GET",params:t})}function d(t,e){return Object(a["a"])({url:"/admin/store/product/importProduct?form="+t+"&url="+e,method:"POST"})}function u(t){return Object(a["a"])({url:"/admin/store/product/batch/putOnShell",method:"POST",data:t})}function b(t){return Object(a["a"])({url:"/admin/store/product/batch/offShell",method:"POST",data:t})}function m(t){return Object(a["a"])({url:"/admin/store/product/batch/delete",method:"POST",data:t})}function f(t){return Object(a["a"])({url:"/admin/store/product/update",method:"POST",data:t})}},cd1b:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"divBox relative"},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("div",{staticClass:"container mt-1"},[n("el-form",{attrs:{inline:"",size:"small"}},[n("el-form-item",{attrs:{label:t.$t("brand.search")}},[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("brand.brandNameInput"),size:"small",clearable:""},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.status")}},[n("el-select",{attrs:{placeholder:t.$t("brand.pleaseSelect")},model:{value:t.form.type,callback:function(e){t.$set(t.form,"type",e)},expression:"form.type"}},t._l(t.statusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:t.$t(e.label),value:e.value}})})),1)],1)],1)],1),t._v(" "),n("el-button",{staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:t.onSearch}},[t._v(t._s(t.$t("brand.query")))]),t._v(" "),n("el-button",{staticClass:"mr10",attrs:{size:"small",type:""},on:{click:t.onReset}},[t._v(t._s(t.$t("brand.reset")))]),t._v(" "),n("div",{staticClass:"acea-row padtop-10"},[n("el-button",{attrs:{size:"small",type:"success"},on:{click:t.onAdd}},[t._v(t._s(t.$t("brand.addBrand")))]),t._v(" "),n("el-button",{attrs:{size:"small",disabled:0===t.multipleSelection.length},on:{click:function(e){return t.batchHandle("online")}}},[t._v(t._s(t.$t("brand.batchOnline")))]),t._v(" "),n("el-button",{attrs:{size:"small",disabled:0===t.multipleSelection.length},on:{click:function(e){return t.batchHandle("outline")}}},[t._v(t._s(t.$t("brand.batchOffline")))]),t._v(" "),n("el-button",{attrs:{size:"small",disabled:0===t.multipleSelection.length},on:{click:function(e){return t.batchHandle("delete")}}},[t._v(t._s(t.$t("brand.batchDelete")))])],1)],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData.data,size:"mini","highlight-current-row":!0,"header-cell-style":{fontWeight:"bold"}},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.brandLogo"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("div",{staticClass:"demo-image__preview"},[n("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:t.row.logoUrl,"preview-src-list":[t.row.logoUrl]}})],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.brandName"),"min-width":"80",prop:"name"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.industry"),"min-width":"160","show-overflow-tooltip":!0,prop:"industry"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.platform"),"min-width":"90",align:"center",prop:"platform"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.productCount"),"min-width":"100",align:"center",prop:"productCount"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.createTime"),"min-width":"120",align:"center",prop:"gmtCreate"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.creator"),"min-width":"120",align:"center",prop:"creator"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.isHot"),"min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:function(n){return t.isHotChange(e.row.id,e.row.isHot)}},model:{value:e.row.isHot,callback:function(n){t.$set(e.row,"isHot",n)},expression:"scope.row.isHot"}})]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("brand.isHighCashback"),"min-width":"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},on:{change:function(n){return t.isHighCashbackChange(e.row.id,e.row.isHighCashback)}},model:{value:e.row.isHighCashback,callback:function(n){t.$set(e.row,"isHighCashback",n)},expression:"scope.row.isHighCashback"}})]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("product.action"),"min-width":"150",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("router-link",{attrs:{to:{path:"/brand/product/list",query:{brand:e.row.code}}}},[n("el-button",{staticClass:"mr10",attrs:{size:"small",type:"text"}},[t._v(t._s(t.$t("brand.productList")))])],1),t._v(" "),n("el-button",{staticClass:"mr10",attrs:{type:"text",size:"small"},on:{click:function(n){return t.editBrand(e.row,e.$index)}}},[t._v(t._s(t.$t("brand.edit")))]),t._v(" "),n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(n){return t.handleDelete(e.row.id,e.$index)}}},[t._v(t._s(t.$t("brand.delete")))])]}}])})],1),t._v(" "),n("div",{staticClass:"block"},[n("el-pagination",{attrs:{"page-sizes":[20,40,60,80],"page-size":t.form.limit,"current-page":t.form.page,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.pageChange}})],1)],1),t._v(" "),n("el-dialog",{attrs:{title:t.isEditMode?t.$t("brand.editDialogTitle"):t.$t("brand.addDialogTitle"),visible:t.brandDialogVisible,width:"500px","before-close":t.handleCloseBrandDialog},on:{"update:visible":function(e){t.brandDialogVisible=e}}},[n("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"dform",staticClass:"mt20",attrs:{model:t.dform,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-form-item",{attrs:{label:t.$t("brand.brandName")}},[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("brand.brandNameInput"),size:"small",clearable:""},model:{value:t.dform.name,callback:function(e){t.$set(t.dform,"name",e)},expression:"dform.name"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.brandLogo")}},[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("brand.brandLogoInput"),size:"small",clearable:""},model:{value:t.dform.logoUrl,callback:function(e){t.$set(t.dform,"logoUrl",e)},expression:"dform.logoUrl"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.industry")}},[n("el-select",{attrs:{placeholder:t.$t("brand.pleaseSelect")},model:{value:t.dform.industry,callback:function(e){t.$set(t.dform,"industry",e)},expression:"dform.industry"}},t._l(t.industryListOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.platform")}},[n("el-select",{attrs:{placeholder:t.$t("brand.pleaseSelect")},model:{value:t.dform.platform,callback:function(e){t.$set(t.dform,"platform",e)},expression:"dform.platform"}},t._l(t.platformOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:t.$t(e.label),value:e.value}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.contactPerson")}},[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("brand.contactPerson"),size:"small",clearable:""},model:{value:t.dform.contactPerson,callback:function(e){t.$set(t.dform,"contactPerson",e)},expression:"dform.contactPerson"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.contactPhone")}},[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("brand.contactPhone"),size:"small",clearable:""},model:{value:t.dform.contactPhone,callback:function(e){t.$set(t.dform,"contactPhone",e)},expression:"dform.contactPhone"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("brand.status")}},[n("el-select",{attrs:{placeholder:t.$t("brand.pleaseSelect")},model:{value:t.dform.status,callback:function(e){t.$set(t.dform,"status",e)},expression:"dform.status"}},t._l(t.editStatusOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:t.$t(e.label),value:e.value}})})),1)],1)],1),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:t.onSubBrand}},[t._v(t._s(t.isEditMode?t.$t("brand.update"):t.$t("brand.confirm")))]),t._v(" "),n("el-button",{on:{click:t.handleCloseBrandDialog}},[t._v(t._s(t.$t("brand.cancel")))])],1)],1)],1)},o=[],i=n("9add"),l=(n("5f87"),n("e350"));function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){d(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function d(t,e,n){return(e=u(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function u(t){var e=b(t,"string");return"symbol"==r(e)?e:e+""}function b(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var m={name:"BrandProductList",data:function(){return{pictureType:"maintain",BRAND_STATUS:{DELETED:"-1",OFFLINE:"0",ONLINE:"1",SUSPENDED:"2"},statusOptions:[{value:"-1",label:"brand.pleaseSelect"},{value:"0",label:"brand.isOutline"},{value:"1",label:"brand.isOnline"},{value:"2",label:"brand.isOuted"}],locationOptions:[{value:"all",label:"common.pleaseSelect"},{value:"yes",label:"common.yes"},{value:"no",label:"common.no"}],industryOptions:[{value:"1",label:"2"}],platformOptions:[{value:"tiktok",label:"brand.platformTiktok"},{value:"shopee",label:"brand.platformShopee"}],typeOptions:[{value:"1",label:"common.yes"},{value:"0",label:"common.no"}],editStatusOptions:[{value:"0",label:"brand.isOutline"},{value:"1",label:"brand.isOnline"},{value:"2",label:"brand.isOuted"}],categoryOptions:[],loading:!1,listLoading:!1,tableData:{data:[],total:0},form:{page:1,limit:20,name:"",type:"-1"},dform:{id:"",name:"",image:"",logoUrl:"",industry:"",platform:"",status:"",contactPhone:"",contactPerson:""},brandDialogVisible:!1,isEditMode:!1,multipleSelection:[],industryListOptions:[]}},mounted:function(){var t=this;this.getList(),Object(i["h"])(74).then((function(e){e.list.forEach((function(e){var n=e.value,a=JSON.parse(n),o={};a.fields.forEach((function(t){console.log(t),"code"==t.name?o["value"]=t.value:"name"==t.name&&(o["label"]=t.value)})),t.industryListOptions.push(o)}))})).catch((function(t){}))},methods:{checkPermi:l["a"],onSearch:function(){this.form.page=1,this.getList()},onReset:function(){this.form.name="",this.form.type="-1"},getList:function(){var t=this;this.listLoading=!0,Object(i["f"])(this.form).then((function(e){t.tableData.data=e.list,t.tableData.total=e.total,t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error(t.$t("common.fetchDataFailed"))}))},pageChange:function(t){this.form.page=t,this.getList()},handleSizeChange:function(t){this.form.limit=t,this.getList()},handleSelectionChange:function(t){this.multipleSelection=t},handleDelete:function(t,e){var n=this,a=this,o=[];this.$confirm(this.$t("brand.confirmOperation"),this.$t("brand.prompt"),{confirmButtonText:this.$t("brand.confirm"),cancelButtonText:this.$t("brand.cancel"),type:"warning",showClose:!1}).then((function(){var e={id:t,status:n.BRAND_STATUS.DELETED};o.push(e),Object(i["e"])(o).then((function(t){a.getList()})).catch((function(t){}))}))},onAdd:function(){this.isEditMode=!1,this.dform={id:"",name:"",image:"",logoUrl:"",industry:"",platform:"",status:this.BRAND_STATUS.ONLINE,contactPhone:"",contactPerson:""},this.brandDialogVisible=!0},handleCloseBrandDialog:function(){this.brandDialogVisible=!1,this.isEditMode=!1},editBrand:function(t,e){this.isEditMode=!0,this.dform=c({},t),this.brandDialogVisible=!0},onSubBrand:function(){var t=this;this.isEditMode?Object(i["j"])(this.dform).then((function(e){t.brandDialogVisible=!1,t.isEditMode=!1,t.$message.success(t.$t("common.operationSuccess")),t.getList()})).catch((function(e){t.$message.error(t.$t("common.operationFailed"))})):Object(i["a"])(this.dform).then((function(e){t.brandDialogVisible=!1,t.$message.success(t.$t("common.operationSuccess")),t.getList()})).catch((function(e){t.$message.error(t.$t("common.operationFailed"))}))},handleUpdate:function(t,e){var n=this,a=this,o=[];this.$confirm(this.$t("brand.confirmOperation"),this.$t("brand.prompt"),{confirmButtonText:a.$t("brand.confirm"),cancelButtonText:a.$t("brand.cancel"),type:"warning",showClose:!1}).then((function(){var e={id:t.id};t.status==n.BRAND_STATUS.ONLINE?e.status=n.BRAND_STATUS.SUSPENDED:(t.status==n.BRAND_STATUS.SUSPENDED||t.status==n.BRAND_STATUS.OFFLINE)&&(e.status=n.BRAND_STATUS.ONLINE),o.push(e),Object(i["e"])(o).then((function(t){a.getList()})).catch((function(t){}))}))},batchHandle:function(t){var e=this,n=this,a=[];this.multipleSelection.length<=0?this.$alert(this.$t("brand.selectTip"),this.$t("brand.prompt"),{confirmButtonText:n.$t("brand.confirm"),type:"warning",showConfirmButton:!1}):this.$confirm(this.$t("brand.confirmOperation"),this.$t("brand.prompt"),{confirmButtonText:this.$t("brand.confirm"),cancelButtonText:this.$t("brand.cancel"),type:"warning",showClose:!1}).then((function(){n.multipleSelection.forEach((function(n){var o={id:n.id};"online"==t?o["status"]=e.BRAND_STATUS.ONLINE:"outline"==t?o["status"]=e.BRAND_STATUS.SUSPENDED:"delete"==t&&(o["status"]=e.BRAND_STATUS.DELETED),a.push(o)})),a.length>0&&(e.listLoading=!0,Object(i["e"])(a).then((function(t){n.getList()})).catch((function(t){})))}))},isHighCashbackChange:function(t,e){var n=this,a=[],o={id:t};o["isHighCashback"]=!!e,a.push(o),Object(i["e"])(a).then((function(t){n.getList()})).catch((function(t){}))},isHotChange:function(t,e){var n=this,a=[],o={id:t};o["isHot"]=!!e,a.push(o),Object(i["e"])(a).then((function(t){n.getList()})).catch((function(t){}))}}},f=m,h=(n("11ed"),n("2877")),p=Object(h["a"])(f,a,o,!1,null,"1dbd976e",null);e["default"]=p.exports}}]);