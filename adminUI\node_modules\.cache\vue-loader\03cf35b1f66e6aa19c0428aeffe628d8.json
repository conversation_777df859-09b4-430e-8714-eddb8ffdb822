{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue?vue&type=style&index=0&id=a9ba4704&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754138267094}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754138276757}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754138271537}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.views {\n  font-size: 0.16rem;\n  background: #c68937;\n  border-radius: 4px;\n  color: #fff;\n  padding: 0.05rem 0.02rem 0.05rem 0.08rem;\n  margin-left: 0.1rem;\n}\n.views-jian {\n  font-size: 0.1rem;\n}\n.WriteOff {\n  width: 5.6rem;\n  height: 8rem;\n  background-color: #fff;\n  border-radius: 0.2rem;\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-top: -4rem;\n  margin-left: -2.8rem;\n  z-index: 99;\n  padding-top: 0.55rem;\n}\n.WriteOff .pictrue {\n  width: 3.4rem;\n  height: 3.4rem;\n  margin: 0 auto;\n}\n.WriteOff .pictrue img {\n  width: 100%;\n  height: 100%;\n  display: block;\n  border-radius: 0.1rem;\n}\n.WriteOff .num {\n  font-size: 0.3rem;\n  color: #666;\n  margin: 0.28rem 0 0.3rem 0;\n}\n.WriteOff .num .see {\n  font-size: 0.16rem;\n  color: #fff;\n  border-radius: 0.04rem;\n  background-color: #c68937;\n  padding-left: 0.05rem;\n  margin-left: 0.12rem;\n}\n.WriteOff .num .see .iconfont {\n  font-size: 0.15rem;\n}\n.WriteOff .tip {\n  font-size: 0.36rem;\n  color: #282828;\n  text-align: center;\n  border-top: 1px dashed #ccc;\n  padding-top: 0.4rem;\n  position: relative;\n}\n.WriteOff .tip:after {\n  content: \"\";\n  position: absolute;\n  width: 0.25rem;\n  height: 0.25rem;\n  border-radius: 50%;\n  background-color: #7f7f7f;\n  right: -0.125rem;\n  top: -0.125rem;\n}\n.WriteOff .tip:before {\n  content: \"\";\n  position: absolute;\n  width: 0.25rem;\n  height: 0.25rem;\n  border-radius: 50%;\n  background-color: #7f7f7f;\n  left: -0.125rem;\n  top: -0.125rem;\n}\n.WriteOff .sure {\n  font-size: 0.32rem;\n  color: #fff;\n  text-align: center;\n  line-height: 0.82rem;\n  height: 0.82rem;\n  width: 4.6rem;\n  border-radius: 0.41rem;\n  margin: 0.4rem auto 0 auto;\n  background-image: linear-gradient(to right, #f67a38 0%, #f11b09 100%);\n  background-image: -webkit-linear-gradient(to right, #f67a38 0%, #f11b09 100%);\n  background-image: -moz-linear-gradient(to right, #f67a38 0%, #f11b09 100%);\n}\n.WriteOff .sure.cancel {\n  background-image: none;\n  color: #999;\n  margin-top: 0.1rem;\n}\n", null]}