{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { wechatMenuApi, wechatMenuAddApi } from '@/api/wxApi'\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: 'WechatMenus',\n  data() {\n    return {\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 24\n      },\n      grid2: {\n        xl: 16,\n        lg: 16,\n        md: 16,\n        sm: 16,\n        xs: 24\n      },\n      modal2: false,\n      formValidate: {\n        name: '',\n        type: 'click',\n        appid: '',\n        url: '',\n        key: '',\n        pagepath: '',\n        id: 0\n      },\n      ruleValidate: {\n        name: [\n          { required: true, message: '请填写菜单名称', trigger: 'blur' }\n        ],\n        key: [\n          { required: true, message: '请填写关键字', trigger: 'blur' }\n        ],\n        appid: [\n          { required: true, message: '请填写appid', trigger: 'blur' }\n        ],\n        pagepath: [\n          { required: true, message: '请填写小程序路径', trigger: 'blur' }\n        ],\n        url: [\n          { required: true, message: '请填写跳转地址', trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择规则状态', trigger: 'change' }\n        ]\n      },\n      parentMenuId: null,\n      list: [],\n      checkedMenuId: null,\n      isTrue: false\n    }\n  },\n  mounted() {\n    this.getMenus()\n    if (this.list.length) {\n      this.formValidate = this.list[this.activeClass]\n    } else {\n      return this.formValidate\n    }\n  },\n  methods: {\n    // 添加一级字段函数\n    defaultMenusData() {\n      return {\n        type: 'click',\n        name: '',\n        sub_button: []\n      }\n    },\n    // 添加二级字段函数\n    defaultChildData() {\n      return {\n        type: 'click',\n        name: ''\n      }\n    },\n    // 获取 菜单\n    getMenus() {\n      wechatMenuApi().then(async res => {\n        const data = res.menu\n        this.list = data.button\n      })\n    },\n    // 点击保存提交\n    submenus:Debounce(function(name) {\n      if (this.isTrue && !this.checkedMenuId && this.checkedMenuId !== 0) {\n        this.putData()\n      } else {\n        this.$refs[name].validate((valid) => {\n          if (valid) {\n            this.putData()\n          } else {\n            if (!this.check()) return false\n          }\n        })\n      }\n    }),\n    // 新增data\n    putData() {\n      const data = {\n        button: this.list\n      }\n      wechatMenuAddApi(data).then(async res => {\n        this.$message.success('提交成功')\n        this.checkedMenuId = null\n        this.formValidate = {}\n        this.isTrue = false\n      })\n    },\n    // 点击元素\n    gettem(item, index, pid) {\n      this.checkedMenuId = index\n      this.formValidate = item\n      this.parentMenuId = pid\n      this.isTrue = true\n    },\n    // 增加二级\n    add(item, index) {\n      if (!this.check()) return false\n      if (item.sub_button.length < 5) {\n        const data = this.defaultChildData()\n        const id = item.sub_button.length\n        item.sub_button.push(data)\n        this.formValidate = data\n        this.checkedMenuId = id\n        this.parentMenuId = index\n        this.isTrue = true\n      }\n    },\n    // 增加一级\n    addtext() {\n      if (!this.check()) return false\n      const data = this.defaultMenusData()\n      const id = this.list.length\n      this.list.push(data)\n      this.formValidate = data\n      this.checkedMenuId = id\n      this.parentMenuId = null\n      this.isTrue = true\n    },\n    // 判断函数\n    check: function() {\n      const reg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?/\n      if (this.checkedMenuId === null) return true\n      if (!this.isTrue) return true\n      if (!this.formValidate.name) {\n        this.$message.warning('请输入按钮名称!')\n        return false\n      }\n      if (this.formValidate.type === 'click' && !this.formValidate.key) {\n        this.$message.warning('请输入关键字!')\n        return false\n      }\n      if (this.formValidate.type === 'view' && !(reg.test(this.formValidate.url))) {\n        this.$message.warning('请输入正确的跳转地址!')\n        return false\n      }\n      if (this.formValidate.type === 'miniprogram' &&\n        (!this.formValidate.appid ||\n          !this.formValidate.pagepath ||\n          !this.formValidate.url)) {\n        this.$message.warning('请填写完整小程序配置!')\n        return false\n      }\n      return true\n    },\n    // 删除\n    deltMenus() {\n      if (this.isTrue) {\n        this.$modalSure().then(() => {\n          this.del()\n        })\n      } else {\n        this.$message.warning('请选择菜单!')\n      }\n    },\n    // 确认删除\n    del() {\n      this.parentMenuId === null ? this.list.splice(this.checkedMenuId, 1) : this.list[this.parentMenuId].sub_button.splice(this.checkedMenuId, 1)\n      this.parentMenuId = null\n      this.formValidate = {\n        name: '',\n        type: 'click',\n        appid: '',\n        url: '',\n        key: '',\n        pagepath: '',\n        id: 0\n      }\n      this.isTrue = false\n      this.modal2 = false\n      this.checkedMenuId = null\n      this.$refs['formValidate'].resetFields()\n      this.submenus('formValidate')\n    }\n  }\n}\n", null]}