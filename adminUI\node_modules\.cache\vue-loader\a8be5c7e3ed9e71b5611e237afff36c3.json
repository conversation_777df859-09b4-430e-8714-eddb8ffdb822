{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { replyListApi, replyDeleteApi, replyUpdateApi, replyStatusApi } from '@/api/wxApi'\nimport { getToken } from '@/utils/auth'\nimport { checkPermi } from \"@/utils/permission\"; \nexport default {\n  name: 'Wechat<PERSON>eyword',\n  data() {\n    return {\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: '',\n        type: ''\n      },\n      listLoading: true\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    seachList() {\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    onchangeIsShow(row) {\n      replyStatusApi({id:row.id, status:row.status}).then(() => {\n        this.$message.success('修改成功');\n        this.getList();\n      }).catch(()=>{\n        row.status = !row.status\n      })\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      replyListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(res => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure().then(() => {\n        replyDeleteApi({id: id}).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      })\n    }\n  }\n}\n", null]}