{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue?vue&type=style&index=0&id=7a8e852e&prod&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754138267094}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754138276757}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754138271537}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754138265892}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*input{*/\n  /*line-height: normal; box-sizing:border-box;*/\n  /*-webkit-text-size-adjust: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); font-family: 'PingFang SC', 'STHeitiSC-Light', 'Helvetica-Light', arial, sans-serif, 'Droid Sans Fallback'; color:#333;*/\n  /*outline:none; border:none; -webkit-appearance:none;border-radius: 0; background:none;*/\n/*}*/\n/*input,input[type=\"search\"],button,select,option,textarea,a{ outline:none; border:0; -webkit-appearance:none;border-radius: 0; background:none;-webkit-box-sizing:border-box;box-sizing:border-box;}*/\n/*button, input, select, textarea { font-size:100%; }*/\n.deliver-goods header{width:100%;background-color:#fff;margin-top:0.1rem;}\n.deliver-goods header .order-num{padding:0 0.3rem;border-bottom:1px solid #f5f5f5;height:0.67rem;}\n.deliver-goods header .order-num .num{width:4.3rem;font-size:0.26rem;color:#282828;position:relative;}\n.deliver-goods header .order-num .num:after{position:absolute;content:'';width:1px;height:0.3rem;background-color:#ddd;top:50%;margin-top:-0.15rem;right:0;}\n.deliver-goods header .order-num .name{width:2.6rem;font-size:0.26rem;color:#282828;text-align: center;}\n.deliver-goods header .order-num .name .iconfont{font-size:0.35rem;color:#477ef3;vertical-align:middle;margin-right:0.1rem;}\n.deliver-goods header .address{font-size:0.26rem;color:#868686;background-color:#fff;padding:0.3rem;}\n.deliver-goods header .address .name{font-size:0.3rem;color:#282828;margin-bottom:0.1rem;}\n.deliver-goods header .address .name .phone{margin-left:0.4rem;}\n.deliver-goods header .line{width:100%;height:0.03rem;}\n.deliver-goods header .line img{width:100%;height:100%;display:block;}\n.deliver-goods .wrapper{width:100%;background-color:#fff;}\n.deliver-goods .wrapper .item{border-bottom:1px solid #f0f0f0;padding:0 0.3rem;height:0.96rem;font-size:0.32rem;color:#282828;position:relative;}\n.deliver-goods .wrapper .item .mode{width:4.6rem;height:100%;text-align:right;}\n.deliver-goods .wrapper .item .mode .iconfont{font-size:0.3rem;margin-left:0.13rem;}\n.deliver-goods .wrapper .item .mode .goods~.goods{margin-left:0.3rem;}\n.deliver-goods .wrapper .item .mode .goods{color:#bbb;}\n.deliver-goods .wrapper .item .mode .goods.on{color:#477ef3;}\n.deliver-goods .wrapper .item .icon-up{position:absolute;font-size:0.35rem;color:#2c2c2c;right:0.3rem;}\n.deliver-goods .wrapper .item select{direction: rtl;padding-right:0.6rem;position: relative;z-index: 2;}\n.deliver-goods .wrapper .item input::placeholder{color:#bbb;}\n.deliver-goods .confirm{font-size:0.32rem;color:#fff;width:100%;height:1rem;background-color:#477ef3;text-align:center;line-height:1rem;position:fixed;bottom:0;}\n", null]}