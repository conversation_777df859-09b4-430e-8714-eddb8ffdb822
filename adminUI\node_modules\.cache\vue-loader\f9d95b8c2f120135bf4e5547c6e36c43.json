{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=template&id=37212a44&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"icon-dialog\"},[_c('el-dialog',_vm._g(_vm._b({attrs:{\"width\":\"980px\",\"modal-append-to-body\":false},on:{\"open\":_vm.onOpen,\"close\":_vm.onClose}},'el-dialog',_vm.$attrs,false),_vm.$listeners),[_c('div',{attrs:{\"slot\":\"title\"},slot:\"title\"},[_vm._v(\"\\n      选择图标\\n      \"),_c('el-input',{style:({width: '260px'}),attrs:{\"size\":\"mini\",\"placeholder\":\"请输入图标名称\",\"prefix-icon\":\"el-icon-search\",\"clearable\":\"\"},model:{value:(_vm.key),callback:function ($$v) {_vm.key=$$v},expression:\"key\"}})],1),_vm._v(\" \"),_c('ul',{staticClass:\"icon-ul\"},_vm._l((_vm.iconList),function(icon){return _c('li',{key:icon,class:_vm.active===icon?'active-item':'',on:{\"click\":function($event){return _vm.onSelect(icon)}}},[_c('i',{class:icon}),_vm._v(\" \"),_c('div',[_vm._v(_vm._s(icon))])])}),0)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}