{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["export default {\n  name: 'MenuItem',\n  functional: true,\n  props: {\n    icon: {\n      type: String,\n      default: ''\n    },\n    title: {\n      type: String,\n      default: ''\n    }\n  },\n  render: function render(h, context) {\n    var _context$props = context.props,\n      icon = _context$props.icon,\n      title = _context$props.title;\n    var vnodes = [];\n    if (icon) {\n      var ic = 'el-icon-' + icon;\n      vnodes.push(h(\"i\", {\n        \"style\": \"color:#ffffff;\",\n        \"class\": ic\n      }));\n    }\n    if (title) {\n      vnodes.push(h(\"span\", {\n        \"slot\": 'title'\n      }, [title]));\n    }\n    return vnodes;\n  }\n};", null]}