package com.genco.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.genco.common.model.user.User;
import com.genco.common.response.UserSpreadPeopleItemResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 用户表 Mapper 接口
 */
public interface UserDao extends BaseMapper<User> {

    List<UserSpreadPeopleItemResponse> getSpreadPeopleList(Map<String, Object> map);

    List<User> findAdminList(Map<String, Object> map);

    /**
     * 使用悲观锁查询用户信息
     *
     * @param userId 用户ID
     * @return User
     */
    @Select("SELECT * FROM eb_user WHERE uid = #{userId} FOR UPDATE")
    User selectForUpdate(@Param("userId") Integer userId);
}
