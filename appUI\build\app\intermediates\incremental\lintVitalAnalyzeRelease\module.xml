<lint-module
    format="1"
    dir="C:\Users\<USER>\Desktop\project\genco\appUI\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="C:\Users\<USER>\Desktop\project\genco\appUI\build\app"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
