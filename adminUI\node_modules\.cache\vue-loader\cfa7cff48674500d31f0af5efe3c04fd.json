{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { topUpLogListApi, balanceApi, topUpLogDeleteApi, refundApi } from '@/api/financial'\nimport cardsData from '@/components/cards/index'\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'AccountsBill',\n  components: { cardsData, zbParser },\n  data() {\n    return {\n      editData: {},\n      isCreate: 1,\n      cardLists: [],\n      timeVal: [],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        uid: '',\n       // paid: '',\n        dateLimit: '',\n        keywords: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      dialogVisible: false\n    }\n  },\n  mounted() {\n    this.getList()\n    this.getStatistics()\n  },\n  methods: {\n    checkPermi,\n    resetForm(formValue) {\n      this.handleClose();\n    },\n    handlerSubmit(formValue) {\n      refundApi(formValue).then(data => {\n        this.$message.success('操作成功')\n        this.dialogVisible = false\n        this.editData = {}\n        this.getList()\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n      this.editData = {}\n    },\n    handleRefund(row) {\n      if(row.price == row.refundPrice) return this.$message.waiting('已退完支付金额！不能再退款了 ！');\n      if(row.rechargeType === 'balance') return this.$message.waiting('佣金转入余额，不能退款 ！');\n      this.editData.orderId = row.orderId\n      this.editData.id = row.id\n      this.dialogVisible = true\n    },\n    handleDelete(row, idx) {\n      this.$modalSure().then(() => {\n        topUpLogDeleteApi( {id:row.id} ).then(() => {\n          this.$message.success('删除成功')\n          this.getList(this.tableFrom.page)\n        })\n      })\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.tableFrom.dateLimit = tab\n      this.timeVal = []\n      this.tableFrom.page = 1;\n      this.getList()\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1;\n      this.getList()\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      topUpLogListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    // 统计\n    getStatistics() {\n      balanceApi().then(res => {\n        const stat = res\n        this.cardLists = [\n          { name: '充值总金额', count: stat.total, color:'#1890FF',class:'one',icon:'iconchongzhijine' },\n          { name: '小程序充值金额', count: stat.routine, color:'#A277FF',class:'two',icon:'iconweixinzhifujine' },\n          { name: '公众号充值金额', count: stat.weChat, color:'#EF9C20',class:'three',icon:'iconyuezhifujine1' }\n        ]\n      })\n    }\n  }\n}\n", null]}