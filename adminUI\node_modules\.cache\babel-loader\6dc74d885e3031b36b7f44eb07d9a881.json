{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\Screenfull\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n\nimport screenfull from 'screenfull';\nexport default {\n  name: 'Screenfull',\n  data: function data() {\n    return {\n      isFullscreen: false\n    };\n  },\n  mounted: function mounted() {\n    this.init();\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.destroy();\n  },\n  methods: {\n    click: function click() {\n      if (!screenfull.enabled) {\n        this.$message({\n          message: 'you browser can not work',\n          type: 'warning'\n        });\n        return false;\n      }\n      screenfull.toggle();\n    },\n    change: function change() {\n      this.isFullscreen = screenfull.isFullscreen;\n    },\n    init: function init() {\n      if (screenfull.enabled) {\n        screenfull.on('change', this.change);\n      }\n    },\n    destroy: function destroy() {\n      if (screenfull.enabled) {\n        screenfull.off('change', this.change);\n      }\n    }\n  }\n};", null]}