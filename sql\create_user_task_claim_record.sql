-- 创建用户任务兑换记录表
CREATE TABLE `eb_user_task_claim_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型：invite_first_order',
  `task_config_snapshot` json DEFAULT NULL COMMENT '任务配置快照',
  `claim_groups` int(11) NOT NULL COMMENT '本次兑换组数',
  `total_claim_groups` int(11) NOT NULL COMMENT '累计兑换组数',
  `referral_count_required` int(11) NOT NULL COMMENT '单组所需邀请人数',
  `first_order_count_required` int(11) NOT NULL COMMENT '单组所需首单人数',
  `referral_count_used` int(11) NOT NULL COMMENT '本次消耗邀请人数',
  `first_order_count_used` int(11) NOT NULL COMMENT '本次消耗首单人数',
  `reward_amount_per_group` decimal(10,2) NOT NULL COMMENT '单组奖励金额',
  `total_reward_amount` decimal(10,2) NOT NULL COMMENT '本次总奖励金额',
  `bill_id` int(11) DEFAULT NULL COMMENT '关联账单ID',
  `claim_time` datetime NOT NULL COMMENT '兑换时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid_task_claim_time` (`uid`, `task_type`, `claim_time`),
  KEY `idx_uid_task_type` (`uid`, `task_type`),
  KEY `idx_claim_time` (`claim_time`),
  KEY `idx_bill_id` (`bill_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户任务兑换记录表';
