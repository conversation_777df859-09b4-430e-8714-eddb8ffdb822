{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue", "mtime": 1754301662333}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { productLstApi, productUpdateApi } from \"@/api/store\";\r\nimport {\r\n  batchPutOn,\r\n  batchPutoff,\r\n  updateProductInfo\r\n} from \"@/api/brand\";\r\n\r\n\r\nexport default {\r\n  name: \"AppHome\",\r\n  data() {\r\n    return {\r\n      statusOptions: [\r\n        { value: -1, label: this.$t(\"all\") },\r\n        { value: 1, label: this.$t(\"online\") },\r\n        { value: 0, label: this.$t(\"offline\") }\r\n      ],\r\n      form: {\r\n        keywords: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        type: \"1\",\r\n        isShow:\"\"\r\n      },\r\n      tableData: [],\r\n      levelList: [],\r\n      levelData: [],\r\n      loading: false\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    formatAmount(s){\r\n        if(s == undefined) {\r\n            s = 0\r\n        }\r\n        let s1 = (s/1000).toFixed(3)\r\n        return s1\r\n    },\r\n    onChangeType() {\r\n      this.getList();\r\n    },\r\n    // 列表\r\n    getList(num) {\r\n      this.form.page = 1;\r\n      this.loading = true;\r\n      productLstApi(this.form)\r\n        .then(res => {\r\n          this.tableData = res.list;\r\n          this.form.total = res.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    //切换页数\r\n    pageChange(index) {\r\n      this.form.page = index;\r\n      this.getList();\r\n    },\r\n    //切换显示条数\r\n    sizeChange(index) {\r\n      this.form.limit = index;\r\n      this.getList();\r\n    },\r\n    resetForm() {\r\n      this.form.keywords = \"\";\r\n      this.form.page = 1;\r\n      this.form.limit = 20;\r\n      this.getList();\r\n    },\r\n    formatTime(t) {\r\n      let date = new Date(t * 1000);\r\n      let year = date.getFullYear();\r\n      let month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n      let day = String(date.getDate()).padStart(2, \"0\");\r\n      let hours = String(date.getHours()).padStart(2, \"0\");\r\n      let minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n      let seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    handleUpdate(row, type) {\r\n      let _this = this;\r\n      this.$confirm(\r\n        this.$t(\"brand.confirmOperation\"),\r\n        this.$t(\"brand.prompt\"),\r\n        {\r\n          confirmButtonText: this.$t(\"brand.confirm\"),\r\n          cancelButtonText: this.$t(\"brand.cancel\"),\r\n          type: \"warning\",\r\n          showClose: false\r\n        }\r\n      ).then(() => {\r\n        let params = { ids: [row.id] };\r\n        if (row.isShow) {\r\n          batchPutoff(params).then(res => {\r\n            _this.getList();\r\n          });\r\n        } else {\r\n          batchPutOn(params).then(res => {\r\n            _this.getList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    formatRate(s) {\r\n      return parseInt(s * 10000) / 100 + \"%\";\r\n    },\r\n    handleDelete(row) {\r\n      let _this = this;\r\n      let item = { id: row.id };\r\n      if(this.form.type == 1){\r\n        item['isHot'] = false\r\n      }else if(this.form.type == 2){\r\n        item['isBenefit'] = false\r\n      }else if(this.form.type == 3){\r\n        item['isBest'] = false\r\n      }\r\n      this.$confirm(\r\n        this.$t(\"brand.confirmOperation\"),\r\n        this.$t(\"brand.prompt\"),\r\n        {\r\n          confirmButtonText: this.$t(\"brand.confirm\"),\r\n          cancelButtonText: this.$t(\"brand.cancel\"),\r\n          type: \"warning\",\r\n          showClose: false\r\n        }\r\n      ).then(() => {\r\n        _this.loading = true;\r\n        updateProductInfo(item)\r\n        .then(res => {\r\n          _this.getList();\r\n        })\r\n        .catch(res => {});\r\n      });\r\n    }\r\n  }\r\n};\r\n", null]}