package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推广人信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserSpreadPeopleItemResponse对象", description = "推广人信息")
public class UserSpreadTeamResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预计总收入")
    private BigDecimal estimatedCommission;

    @ApiModelProperty(value = "预计今日收入")
    private BigDecimal estimatedCommissionToday;

    @ApiModelProperty(value = "实际收入")
    private BigDecimal actualCommission;

    @ApiModelProperty(value = "实际今日收入")
    private BigDecimal actualCommissionToday;

    @ApiModelProperty(value = "邀请奖励")
    private BigDecimal inviteReward;

    @ApiModelProperty(value = "今日邀请奖励")
    private BigDecimal inviteRewardToday;

    @ApiModelProperty(value = "购物奖励")
    private BigDecimal shoppingReward;

    @ApiModelProperty(value = "今日购物奖励")
    private BigDecimal shoppingRewardToday;

    @ApiModelProperty(value = "累计邀请人数")
    private Integer inviteCount;

    @ApiModelProperty(value = "今日邀请人数")
    private Integer inviteCountToday;

    @ApiModelProperty(value = "累计邀请银牌代理")
    private Integer inviteLevel1Count;

    @ApiModelProperty(value = "累计邀请金牌代理")
    private Integer inviteLevel2Count;

    @ApiModelProperty(value = "累计邀请钻石代理")
    private Integer inviteLevel3Count;

    @ApiModelProperty(value = "累计邀请普通用户")
    private Integer inviteNormalCount;
}
