{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue", "mtime": 1754275430525}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userListApi, groupListApi, levelListApi, levelUseApi, levelDeleteApi } from '@/api/user'\nimport creatGrade from './creatGrade'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'Grade',\n  filters: {\n    typeFilter(status) {\n      return this.$t(`user.grade.userTypes.${status}`) || status\n    }\n  },\n  components: {creatGrade},\n  data() {\n    return {\n      listLoading: true,\n      userInfo:{},\n      tableData: {\n        data: [],\n        total: 0,\n      }\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    seachList() {\n      this.getList()\n    },\n    add() {\n      this.$refs.grades.dialogVisible = true\n      this.userInfo = {};\n    },\n    edit(id) {\n      // this.$refs.grades.info(id)\n      this.userInfo = id;\n      this.$refs.grades.dialogVisible = true\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      levelListApi().then(res => {\n        this.tableData.data = res\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure(this.$t('user.grade.deleteConfirm')).then(() => {\n        levelDeleteApi(id).then(() => {\n          this.$message.success(this.$t('user.grade.deleteSuccess'))\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    },\n    onchangeIsShow(row) {\n      if(row.isShow == false){\n        row.isShow = !row.isShow\n        levelUseApi({id: row.id, isShow:row.isShow}).then(() => {\n          this.$message.success(this.$t('user.grade.updateSuccess'))\n          this.getList()\n        }).catch(()=>{\n          row.isShow = !row.isShow\n        })\n      }else{\n        this.$modalSure(this.$t('user.grade.hideConfirm')).then(() => {\n          row.isShow = !row.isShow\n          levelUseApi({id: row.id, isShow:row.isShow}).then(() => {\n            this.$message.success(this.$t('user.grade.updateSuccess'))\n            this.getList()\n          }).catch(()=>{\n            row.isShow = !row.isShow\n          })\n        })\n      }\n    },\n    // 获取升级方式名称\n    getUpgradeTypeName(type) {\n      return this.$t(`user.grade.upgradeTypes.${type}`) || this.$t('common.unknown')\n    },\n    // 获取升级方式颜色\n    getUpgradeTypeColor(type) {\n      const colorMap = {\n        0: 'success',\n        1: 'warning',\n        2: 'info',\n        3: 'primary'\n      }\n      return colorMap[type] || 'info'\n    }\n  }\n}\n", null]}