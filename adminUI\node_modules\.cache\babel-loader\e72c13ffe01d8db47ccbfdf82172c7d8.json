{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { isNumberStr } from '../utils/index';\nimport { getTreeNodeId, saveTreeNodeId } from '../utils/db';\nvar id = getTreeNodeId();\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data: function data() {\n    return {\n      id: id,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [{\n          required: true,\n          message: '请输入选项名',\n          trigger: 'blur'\n        }],\n        value: [{\n          required: true,\n          message: '请输入选项值',\n          trigger: 'blur'\n        }]\n      },\n      dataType: 'string',\n      dataTypeOptions: [{\n        label: '字符串',\n        value: 'string'\n      }, {\n        label: '数字',\n        value: 'number'\n      }]\n    };\n  },\n  computed: {},\n  watch: {\n    // eslint-disable-next-line func-names\n    'formData.value': function formDataValue(val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string';\n    },\n    id: function id(val) {\n      saveTreeNodeId(val);\n    }\n  },\n  created: function created() {},\n  mounted: function mounted() {},\n  methods: {\n    onOpen: function onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      };\n    },\n    onClose: function onClose() {},\n    close: function close() {\n      this.$emit('update:visible', false);\n    },\n    handelConfirm: function handelConfirm() {\n      var _this = this;\n      this.$refs.elForm.validate(function (valid) {\n        if (!valid) return;\n        if (_this.dataType === 'number') {\n          _this.formData.value = parseFloat(_this.formData.value);\n        }\n        _this.formData.id = _this.id++;\n        _this.$emit('commit', _this.formData);\n        _this.close();\n      });\n    }\n  }\n};", null]}