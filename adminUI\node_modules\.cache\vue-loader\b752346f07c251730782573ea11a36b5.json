{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {orderSendApi, sheetInfoApi} from '@/api/order'\nimport {expressAllApi, exportTempApi} from '@/api/sms'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport {Debounce} from '@/utils/validate'\nconst validatePhone = (rule, value, callback) => {\n  if (!value) {\n    return callback(new Error('请填写手机号'));\n  } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n    callback(new Error('手机号格式不正确!'));\n  } else {\n    callback();\n  }\n};\nexport default {\n  name: 'orderSend',\n  props: {\n    orderId: String\n  },\n  data() {\n    return {\n      formItem: {\n        type: '1',\n        expressRecordType: '1',\n        expressId: '',\n        expressCode: '',\n        deliveryName: '',\n        deliveryTel: '',\n        // expressName: '',\n        expressNumber: '',\n        expressTempId: '',\n        toAddr: '',\n        toName: '',\n        toTel: '',\n        orderNo: ''\n      },\n      modals: false,\n      express: [],\n      exportTempList: [],\n      tempImg: '',\n      rules: {\n        toName: [\n          {required: true, message: '请输寄件人姓名', trigger: 'blur'}\n        ],\n        toTel: [\n          {required: true, validator: validatePhone, trigger: 'blur'}\n        ],\n        toAddr: [\n          {required: true, message: '请输入寄件人地址', trigger: 'blur'}\n        ],\n        expressCode: [\n          {required: true, message: '请选择快递公司', trigger: 'change'}\n        ],\n        expressNumber: [\n          {required: true, message: '请输入快递单号', trigger: 'blur'}\n        ],\n        expressTempId: [\n          {required: true, message: '请选择电子面单', trigger: 'change'}\n        ],\n        deliveryName: [\n          {required: true, message: '请输入送货人姓名', trigger: 'blur'}\n        ],\n        deliveryTel: [\n          {required: true, validator: validatePhone, trigger: 'blur'}\n        ]\n      },\n      expressType: 'normal'\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    checkPermi,\n    // 默认信息\n    sheetInfo() {\n      sheetInfoApi().then(async res => {\n        this.formItem.toAddr = res.exportToAddress || '';\n        this.formItem.toName = res.exportToName || '';\n        this.formItem.toTel = res.exportToTel || '';\n      })\n    },\n    // 快递公司选择\n    onChangeExport(val) {\n      this.formItem.expressTempId = '';\n      if (this.formItem.expressRecordType === '2') this.exportTemp(val);\n    },\n    // 电子面单模板\n    exportTemp(val) {\n      exportTempApi({com: val}).then(async res => {\n        this.exportTempList = res.data.data || [];\n      })\n    },\n    onChangeImg(item) {\n      this.exportTempList.map(i => {\n        if (i.temp_id === item) this.tempImg = i.pic\n      })\n    },\n    changeRadioType() {\n      this.formItem.expressId = ''\n      this.formItem.expressCode = ''\n    },\n    changeRadio(o) {\n      if (o == 2){\n        this.expressType = 'elec'\n      }else{\n        this.expressType = 'normal'\n      }\n      this.formItem.expressId = ''\n      this.formItem.expressCode = ''\n      this.getList();\n    },\n    // 物流公司列表\n    getList() {\n      expressAllApi({type: this.expressType}).then(async res => {\n        this.express = res\n      })\n    },\n    // 提交\n    putSend:Debounce(function(name) {\n      this.formItem.orderNo = this.orderId;\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          orderSendApi(this.formItem).then(async => {\n            this.$message.success('发送货成功');\n            this.modals = false;\n            this.$refs[name].resetFields();\n            this.$emit('submitFail')\n          })\n        } else {\n          this.$message.error('请填写信息');\n        }\n      })\n    }),\n    handleClose() {\n      this.cancel('formItem');\n    },\n    cancel(name) {\n      this.modals = false;\n      this.$refs[name].resetFields();\n      this.formItem.type = '1';\n      this.formItem.expressRecordType = '1';\n    }\n  }\n}\n", null]}