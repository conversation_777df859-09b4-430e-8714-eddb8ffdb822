<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.StoreProductShareRecordDao">
    <insert id="insert" parameterType="com.genco.common.model.product.StoreProductShareRecord">
        INSERT INTO eb_store_product_share_record
        (user_id, tiktok_uid, user_account, origin_url, share_url, channel, product_id, product_name, product_price, product_cashback_rate, user_cashback_rate, operate_time)
        VALUES
        (#{userId}, #{tiktokUid}, #{userAccount}, #{originUrl}, #{shareUrl}, #{channel}, #{productId}, #{productName}, #{productPrice}, #{productCashbackRate}, #{userCashbackRate}, #{operateTime})
    </insert>
    <select id="selectByCondition" resultType="com.genco.common.model.product.StoreProductShareRecord">
        SELECT * FROM eb_store_product_share_record
        <where>
            <if test="param.userId != null">AND user_id = #{param.userId}</if>
            <if test="param.tiktokUid != null and param.tiktokUid != ''">AND tiktok_uid = #{param.tiktokUid}</if>
            <if test="param.userAccount != null and param.userAccount != ''">AND user_account LIKE CONCAT('%', #{param.userAccount}, '%')</if>
            <if test="param.productId != null and param.productId != ''">AND product_id = #{param.productId}</if>
            <if test="param.productName != null and param.productName != ''">AND product_name LIKE CONCAT('%', #{param.productName}, '%')</if>
            <if test="param.channel != null and param.channel != ''">AND channel = #{param.channel}</if>
            <if test="param.operateTimeStart != null and param.operateTimeStart != ''">AND operate_time &gt;= #{param.operateTimeStart}</if>
            <if test="param.operateTimeEnd != null and param.operateTimeEnd != ''">AND operate_time &lt;= #{param.operateTimeEnd}</if>
        </where>
        ORDER BY operate_time DESC
    </select>
</mapper> 