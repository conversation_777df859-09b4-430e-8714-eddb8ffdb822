{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { topUpLogListApi, balanceApi, topUpLogDeleteApi, refundApi } from '@/api/financial';\nimport cardsData from '@/components/cards/index';\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'AccountsBill',\n  components: {\n    cardsData: cardsData,\n    zbParser: zbParser\n  },\n  data: function data() {\n    return {\n      editData: {},\n      isCreate: 1,\n      cardLists: [],\n      timeVal: [],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        uid: '',\n        // paid: '',\n        dateLimit: '',\n        keywords: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      dialogVisible: false\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    checkPermi: checkPermi,\n    resetForm: function resetForm(formValue) {\n      this.handleClose();\n    },\n    handlerSubmit: function handlerSubmit(formValue) {\n      var _this = this;\n      refundApi(formValue).then(function (data) {\n        _this.$message.success('操作成功');\n        _this.dialogVisible = false;\n        _this.editData = {};\n        _this.getList();\n      });\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.editData = {};\n    },\n    handleRefund: function handleRefund(row) {\n      if (row.price == row.refundPrice) return this.$message.waiting('已退完支付金额！不能再退款了 ！');\n      if (row.rechargeType === 'balance') return this.$message.waiting('佣金转入余额，不能退款 ！');\n      this.editData.orderId = row.orderId;\n      this.editData.id = row.id;\n      this.dialogVisible = true;\n    },\n    handleDelete: function handleDelete(row, idx) {\n      var _this2 = this;\n      this.$modalSure().then(function () {\n        topUpLogDeleteApi({\n          id: row.id\n        }).then(function () {\n          _this2.$message.success('删除成功');\n          _this2.getList(_this2.tableFrom.page);\n        });\n      });\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.tableFrom.dateLimit = tab;\n      this.timeVal = [];\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this3 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      topUpLogListApi(this.tableFrom).then(function (res) {\n        _this3.tableData.data = res.list;\n        _this3.tableData.total = res.total;\n        _this3.listLoading = false;\n      }).catch(function () {\n        _this3.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 统计\n    getStatistics: function getStatistics() {\n      var _this4 = this;\n      balanceApi().then(function (res) {\n        var stat = res;\n        _this4.cardLists = [{\n          name: '充值总金额',\n          count: stat.total,\n          color: '#1890FF',\n          class: 'one',\n          icon: 'iconchongzhijine'\n        }, {\n          name: '小程序充值金额',\n          count: stat.routine,\n          color: '#A277FF',\n          class: 'two',\n          icon: 'iconweixinzhifujine'\n        }, {\n          name: '公众号充值金额',\n          count: stat.weChat,\n          color: '#EF9C20',\n          class: 'three',\n          icon: 'iconyuezhifujine1'\n        }];\n      });\n    }\n  }\n};", null]}