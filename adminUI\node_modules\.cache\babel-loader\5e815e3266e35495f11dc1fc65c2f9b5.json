{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { replyListApi, replyDeleteApi, replyUpdateApi, replyStatusApi } from '@/api/wxApi';\nimport { getToken } from '@/utils/auth';\nimport { checkPermi } from \"@/utils/permission\";\nexport default {\n  name: 'WechatKeyword',\n  data: function data() {\n    return {\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: '',\n        type: ''\n      },\n      listLoading: true\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    checkPermi: checkPermi,\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this = this;\n      replyStatusApi({\n        id: row.id,\n        status: row.status\n      }).then(function () {\n        _this.$message.success('修改成功');\n        _this.getList();\n      }).catch(function () {\n        row.status = !row.status;\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n      replyListApi(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this3 = this;\n      this.$modalSure().then(function () {\n        replyDeleteApi({\n          id: id\n        }).then(function () {\n          _this3.$message.success('删除成功');\n          _this3.getList();\n        });\n      });\n    }\n  }\n};", null]}