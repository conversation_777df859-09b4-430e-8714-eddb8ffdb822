{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue", "mtime": 1754275430525}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userListApi, groupListApi, levelListApi, levelUseApi, levelDeleteApi } from '@/api/user';\nimport creatGrade from './creatGrade';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'Grade',\n  filters: {\n    typeFilter: function typeFilter(status) {\n      return this.$t(\"user.grade.userTypes.\".concat(status)) || status;\n    }\n  },\n  components: {\n    creatGrade: creatGrade\n  },\n  data: function data() {\n    return {\n      listLoading: true,\n      userInfo: {},\n      tableData: {\n        data: [],\n        total: 0\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    checkPermi: checkPermi,\n    seachList: function seachList() {\n      this.getList();\n    },\n    add: function add() {\n      this.$refs.grades.dialogVisible = true;\n      this.userInfo = {};\n    },\n    edit: function edit(id) {\n      // this.$refs.grades.info(id)\n      this.userInfo = id;\n      this.$refs.grades.dialogVisible = true;\n    },\n    // 列表\n    getList: function getList() {\n      var _this = this;\n      this.listLoading = true;\n      levelListApi().then(function (res) {\n        _this.tableData.data = res;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this2 = this;\n      this.$modalSure(this.$t('user.grade.deleteConfirm')).then(function () {\n        levelDeleteApi(id).then(function () {\n          _this2.$message.success(_this2.$t('user.grade.deleteSuccess'));\n          _this2.tableData.data.splice(idx, 1);\n        });\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this3 = this;\n      if (row.isShow == false) {\n        row.isShow = !row.isShow;\n        levelUseApi({\n          id: row.id,\n          isShow: row.isShow\n        }).then(function () {\n          _this3.$message.success(_this3.$t('user.grade.updateSuccess'));\n          _this3.getList();\n        }).catch(function () {\n          row.isShow = !row.isShow;\n        });\n      } else {\n        this.$modalSure(this.$t('user.grade.hideConfirm')).then(function () {\n          row.isShow = !row.isShow;\n          levelUseApi({\n            id: row.id,\n            isShow: row.isShow\n          }).then(function () {\n            _this3.$message.success(_this3.$t('user.grade.updateSuccess'));\n            _this3.getList();\n          }).catch(function () {\n            row.isShow = !row.isShow;\n          });\n        });\n      }\n    },\n    // 获取升级方式名称\n    getUpgradeTypeName: function getUpgradeTypeName(type) {\n      return this.$t(\"user.grade.upgradeTypes.\".concat(type)) || this.$t('common.unknown');\n    },\n    // 获取升级方式颜色\n    getUpgradeTypeColor: function getUpgradeTypeColor(type) {\n      var colorMap = {\n        0: 'success',\n        1: 'warning',\n        2: 'info',\n        3: 'primary'\n      };\n      return colorMap[type] || 'info';\n    }\n  }\n};", null]}