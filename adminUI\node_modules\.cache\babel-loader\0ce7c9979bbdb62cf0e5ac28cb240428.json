{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\utils\\loadScript.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\utils\\loadScript.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["var callbacks = {};\n\n/**\n * 加载一个远程脚本\n * @param {String} src 一个远程脚本\n * @param {Function} callback 回调\n */\nfunction loadScript(src, callback) {\n  var existingScript = document.getElementById(src);\n  var cb = callback || function () {};\n  if (!existingScript) {\n    callbacks[src] = [];\n    var $script = document.createElement('script');\n    $script.src = src;\n    $script.id = src;\n    $script.async = 1;\n    document.body.appendChild($script);\n    var onEnd = 'onload' in $script ? stdOnEnd.bind($script) : ieOnEnd.bind($script);\n    onEnd($script);\n  }\n  callbacks[src].push(cb);\n  function stdOnEnd(script) {\n    var _this = this;\n    script.onload = function () {\n      _this.onerror = _this.onload = null;\n      callbacks[src].forEach(function (item) {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n    script.onerror = function () {\n      _this.onerror = _this.onload = null;\n      cb(new Error(\"Failed to load \".concat(src)), script);\n    };\n  }\n  function ieOnEnd(script) {\n    var _this2 = this;\n    script.onreadystatechange = function () {\n      if (_this2.readyState !== 'complete' && _this2.readyState !== 'loaded') return;\n      _this2.onreadystatechange = null;\n      callbacks[src].forEach(function (item) {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n  }\n}\n\n/**\n * 顺序加载一组远程脚本\n * @param {Array} list 一组远程脚本\n * @param {Function} cb 回调\n */\nexport function loadScriptQueue(list, cb) {\n  var first = list.shift();\n  list.length ? loadScript(first, function () {\n    return loadScriptQueue(list, cb);\n  }) : loadScript(first, cb);\n}\nexport default loadScript;", null]}