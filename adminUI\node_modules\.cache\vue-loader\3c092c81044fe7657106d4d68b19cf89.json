{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getToken } from '@/utils/auth'\nimport { replySave<PERSON>pi, replyEdit<PERSON>pi, replyInfoApi, replyList<PERSON><PERSON>, keywordsInfo<PERSON>pi, replyUpdate<PERSON>pi } from '@/api/wxApi'\nimport { wechatUploadApi } from '@/api/systemSetting'\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: 'Index',\n  components: { },\n  data() {\n    const validateContent = (rule, value, callback) => {\n      if (this.formValidate.type === 'text') {\n        if (this.formValidate.contents.content === '') {\n          callback(new Error('请填写规则内容'))\n        } else {\n          callback()\n        }\n      }\n    }\n    const validateSrc = (rule, value, callback) => {\n      if (this.formValidate.type === 'image' && this.formValidate.contents.mediaId === '') {\n        callback(new Error('请上传'))\n      } else {\n        callback()\n      }\n    }\n    const validateVal = (rule, value, callback) => {\n      if (this.labelarr.length === 0) {\n        callback(new Error('请输入后回车'))\n      } else {\n        callback()\n      }\n    }\n    return {\n      loading: false,\n      visible: false,\n      grid: {\n        xl: 7,\n        lg: 12,\n        md: 10,\n        sm: 24,\n        xs: 24\n      },\n      delfromData: {},\n      isShow: false,\n      maxCols: 3,\n      scrollerHeight: '600',\n      contentTop: '130',\n      contentWidth: '98%',\n      modals: false,\n      val: '',\n      formatImg: ['jpg', 'jpeg', 'png', 'bmp', 'gif'],\n      formatVoice: ['mp3', 'wma', 'wav', 'amr'],\n      header: {},\n      formValidate: {\n        status: true,\n        type: '',\n        keywords: '',\n        contents: {\n          content: '',\n          articleData:{},\n          mediaId: '',\n          srcUrl: '',\n          articleId: null\n        },\n        id: null\n      },\n      ruleValidate: {\n        val: [\n          { required: true, validator: validateVal, trigger: 'blur' }\n        ],\n        type: [\n          { required: true, message: '请选择消息类型', trigger: 'change' }\n        ],\n        content: [\n          { required: true, validator: validateContent, trigger: 'blur' }\n        ],\n        mediaId: [\n          { required: true, validator: validateSrc, trigger: 'change' }\n        ]\n      },\n      labelarr: [],\n      myHeaders: { 'X-Token': getToken() }\n    }\n  },\n  computed: {\n    fileUrl() {\n      return https + `/wechat/reply/upload/image`\n    },\n    voiceUrl() {\n      return https + `/wechat/reply/upload/voice`\n    },\n    httpsURL() {\n      return process.env.VUE_APP_BASE_API.replace('api/', '')\n    }\n  },\n  watch: {\n    $route(to, from) {\n      if (this.$route.params.id) {\n        // this.formValidate.keywords = this.$route.params.key\n        this.details()\n      } else {\n        // this.labelarr = []\n        // this.$refs['formValidate'].resetFields()\n      }\n    }\n  },\n  mounted() {\n    if (this.$route.params.id) {\n      this.details()\n    }\n    if (this.$route.path.indexOf('keyword') === -1) {\n      this.followDetails()\n    }\n  },\n  methods: {\n    change (e) {\n      this.$forceUpdate()\n    },\n    // 上传\n    handleUploadForm(param){\n      const formData = new FormData()\n      formData.append('media', param.file)\n      let loading = this.$loading({\n        lock: true,\n        text: '上传中，请稍候...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n      wechatUploadApi(formData, {type: this.formValidate.type === 'image'?'image':'voice'}).then(res => {\n        loading.close()\n        this.formValidate.contents.mediaId = res.mediaId\n        this.formValidate.contents.srcUrl = res.url\n        this.$message.success('上传成功')\n      }).catch(() => {\n        loading.close()\n      })\n    },\n    changePic() {\n      const _this = this\n      this.$modalArticle(function(row) {\n        _this.formValidate.contents.articleData ={\n          title: row.title,\n          imageInput : row.imageInput\n        }\n        _this.formValidate.contents.articleId = row.id\n      })\n    },\n    handleClosePic() {\n      this.visible = false\n    },\n    // 详情\n    details() {\n      this.loading = true\n      replyInfoApi({id:this.$route.params.id}).then(async res => {\n        const info = res || null\n        this.formValidate = {\n          status: info.status,\n          type: info.type,\n          keywords: info.keywords,\n          id: info.id,\n          contents: {\n            content: JSON.parse(info.data).content,\n            mediaId: JSON.parse(info.data).mediaId,\n            srcUrl: JSON.parse(info.data).srcUrl,\n            articleData: JSON.parse(info.data).articleData\n          }\n        }\n        this.labelarr = info.keywords.split(',') || []\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 关注回复，无效关键词详情\n    followDetails() {\n      this.loading = true\n      keywordsInfoApi({ keywords: this.$route.path.indexOf('follow') !== -1 ? 'subscribe' : 'default'}).then(async res => {\n        const info = res || null\n        this.formValidate = {\n          status: info.status,\n          type: info.type,\n          keywords: info.keywords,\n          data: '',\n          id: info.id,\n          contents: {\n            content: JSON.parse(info.data).content || '',\n            mediaId: JSON.parse(info.data).mediaId || '',\n            srcUrl: JSON.parse(info.data).srcUrl || '',\n            articleData: JSON.parse(info.data).articleData || {},\n          }\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n        // if (res.message === '数据不存在') return\n        // this.$message.error(res.message)\n      })\n    },\n    // 下拉选择\n    RuleFactor(type) {\n      switch (type) {\n        case 'text':\n          this.formValidate.contents.mediaId = ''\n          this.formValidate.contents.srcUrl = ''\n          this.formValidate.contents.articleData = {}\n          break\n        case 'news':\n          this.formValidate.contents.mediaId = ''\n          this.formValidate.contents.content = ''\n          this.formValidate.contents.srcUrl = ''\n          this.formValidate.contents.articleData = {}\n          break\n        default:\n          this.formValidate.contents.content = ''\n          this.formValidate.contents.mediaId = ''\n          this.formValidate.contents.articleData = {}\n      }\n      // this.$refs['formValidate'].resetFields();\n    },\n    handleClose(tag) {\n      const index = this.labelarr.indexOf(tag)\n      this.labelarr.splice(index, 1)\n    },\n    addlabel() {\n      const count = this.labelarr.indexOf(this.val)\n      if (count === -1) {\n        this.labelarr.push(this.val)\n      }\n      this.val = ''\n    },\n    // 保存\n    submenus:Debounce(function(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.formValidate.keywords = this.labelarr.join(',')\n          this.formValidate.data = JSON.stringify(this.formValidate.contents)\n          if (this.$route.path.indexOf('keyword') !== -1) {\n            this.$route.params.id ? replyUpdateApi({id:this.$route.params.id}, this.formValidate).then(async res => {\n              this.operation()\n            }).catch(res => {\n              this.$message.error(res.message)\n            }) : replySaveApi(this.formValidate).then(async res => {\n              this.operation()\n            }).catch(res => {\n              this.$message.error(res.message)\n            })\n          } else {\n            this.$route.path.indexOf('follow') !== -1 ? this.formValidate.keywords = 'subscribe' : this.formValidate.keywords ='default'\n            this.formValidate.id !== null ?  replyUpdateApi({id:this.formValidate.id}, this.formValidate).then(async res => {\n              this.$message.success('操作成功')\n            }) : replySaveApi(this.formValidate).then(async res => {\n              this.operation()\n            }).catch(res => {\n              this.$message.error(res.message)\n            })\n          }\n        } else {\n          return false\n        }\n      })\n    }),\n    // 保存成功操作\n    operation() {\n      this.$modalSure('继续添加').then(() => {\n        setTimeout(() => {\n          this.labelarr = []\n          this.val = ''\n          this.$refs['formValidate'].resetFields()\n          this.formValidate.contents.mediaId = ''\n        }, 1000)\n      }).catch(() => {\n        setTimeout(() => {\n          this.$router.push({ path: `/appSetting/publicAccount/wxReply/keyword` })\n        }, 500)\n      })\n    }\n  }\n}\n", null]}