{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { orderStatisticsApi, statisticsDataApi } from '@/api/order';\nimport statisticsData from \"../components/statisticsData\";\nimport Loading from \"../components/Loading\";\nexport default {\n  name: \"OrderIndex\",\n  components: {\n    Loading: Loading,\n    statisticsData: statisticsData\n  },\n  props: {},\n  data: function data() {\n    return {\n      census: [],\n      list: [],\n      where: {\n        page: 1,\n        limit: 10\n      },\n      loaded: false,\n      loading: false\n    };\n  },\n  created: function created() {\n    import('@/assets/js/media_750');\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.getIndex();\n    this.getList();\n    this.$scroll(this.$refs.container, function () {\n      !_this.loading && _this.getList();\n    });\n  },\n  methods: {\n    getIndex: function getIndex() {\n      var _this2 = this;\n      orderStatisticsApi().then(function (res) {\n        _this2.census = res;\n      }, function (err) {\n        _this2.$dialog.message(err.message);\n      });\n    },\n    getList: function getList() {\n      var _this3 = this;\n      if (this.loading || this.loaded) return;\n      this.loading = true;\n      statisticsDataApi(this.where).then(function (res) {\n        _this3.loading = false;\n        _this3.loaded = res.length < _this3.where.limit;\n        _this3.list.push.apply(_this3.list, res);\n        _this3.where.page = _this3.where.page + 1;\n      }, function (error) {\n        _this3.$dialog.message(error.message);\n      }, 300);\n    }\n  }\n};", null]}