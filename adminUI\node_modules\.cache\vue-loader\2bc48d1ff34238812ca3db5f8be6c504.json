{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\userDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\userDetails.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { infobyconditionApi, topdetailApi } from '@/api/user'\nimport { integralListApi } from '@/api/marketing'\nexport default {\n  name: \"UserDetails\",\n  props:{\n    uid: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      columns: [],\n      Visible: false,\n      list: [\n        { val: '0', label: 'userDetails.consumeRecord' },\n        { val: '1', label: 'userDetails.integralDetail' },\n        { val: '2', label: 'userDetails.signInRecord' },\n        { val: '3', label: 'userDetails.coupons' },\n        { val: '4', label: 'userDetails.balanceChange' },\n        { val: '5', label: 'userDetails.friendRelation' }\n      ],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 6,\n        type : '0',\n        userId : ''\n      },\n      psInfo: null\n    }\n  },\n  mounted(){\n    if(this.uid){\n      this.getHeader()\n      this.getInfo()\n    }\n\n  },\n  methods: {\n    changeType(key) {\n      this.tableFrom.type = key\n      if(key === '1'){\n        this.integral()\n      }else{\n        this.getInfo()\n      }\n    },\n    integral() {\n      this.loading = true\n      integralListApi({ limit:this.tableFrom.limit, page: this.tableFrom.page}, {uid:this.uid}).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.columns = [\n          {\n            title: '来源/用途',\n            key: 'title',\n            minWidth: 120\n          },\n          {\n            title: '积分变化',\n            key: 'integral',\n            minWidth: 120\n          },\n          {\n            title: '变化后积分',\n            key: 'balance',\n            minWidth: 120\n          },\n          {\n            title: '日期',\n            key: 'updateTime',\n            minWidth: 120\n          },\n          {\n            title: '备注',\n            key: 'mark',\n            minWidth: 120\n          }\n        ]\n        this.loading = false\n      }).catch(res => {\n        this.loading = false\n      })\n    },\n    getInfo() {\n      this.tableFrom.userId = this.uid\n      this.loading = true\n      infobyconditionApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        switch (this.tableFrom.type) {\n          case '0':\n            this.columns = [\n              {\n                title: '订单ID',\n                key: 'orderId',\n                minWidth: 250\n              },\n              {\n                title: '收货人',\n                key: 'realName',\n                minWidth: 90\n              },\n              {\n                title: '商品数量',\n                key: 'totalNum',\n                minWidth: 80\n              },\n              {\n                title: '商品总价',\n                key: 'totalPrice',\n                minWidth: 90\n              },\n              {\n                title: '实付金额',\n                key: 'payPrice',\n                minWidth: 90\n              },\n              {\n                title: '交易完成时间',\n                key: 'payTime',\n                minWidth: 160\n              }\n            ]\n            break;\n          case '2':\n            this.columns = [\n              {\n                title: '动作',\n                key: 'title',\n                minWidth: 120\n              },\n              {\n                title: '获得积分',\n                key: 'number',\n                minWidth: 120\n              },\n              {\n                title: '签到时间',\n                key: 'createTime',\n                minWidth: 120\n              },\n              {\n                title: '备注',\n                key: 'title',\n                minWidth: 120\n              }\n            ]\n            break;\n          case '3':\n            this.columns = [\n              {\n                title: '优惠券名称',\n                key: 'name',\n                minWidth: 120\n              },\n              {\n                title: '面值',\n                key: 'money',\n                minWidth: 120\n              },\n              {\n                title: '有效期',\n                key: 'endTime',\n                minWidth: 120\n              },\n              {\n                title: '最低消费额',\n                key: 'minPrice',\n                minWidth: 120\n              },\n              {\n                title: '兑换时间',\n                key: 'updateTime',\n                minWidth: 120\n              }\n            ]\n            break;\n          case '4':\n            this.columns = [\n              {\n                title: '变动金额',\n                key: 'number',\n                minWidth: 120\n              },\n              {\n                title: '变动后',\n                key: 'balance',\n                minWidth: 120\n              },\n              {\n                title: '类型',\n                key: 'title',\n                minWidth: 120\n              },\n              {\n                title: '创建时间',\n                key: 'add_time',\n                minWidth: 120\n              },\n              {\n                title: '备注',\n                key: 'mark',\n                minWidth: 120\n              }\n            ]\n            break;\n          default:\n            this.columns = [\n              {\n                title: 'ID',\n                key: 'uid',\n                minWidth: 120\n              },\n              {\n                title: '昵称',\n                key: 'nickname',\n                minWidth: 120\n              },\n              {\n                title: '等级',\n                key: 'level',\n                minWidth: 120\n              },\n              {\n                title: '加入时间',\n                key: 'createTime',\n                minWidth: 120\n              }\n            ]\n        }\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      if(this.tableFrom.type === '1'){\n        this.integral()\n      }else{\n        this.getInfo()\n      }\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      if(this.tableFrom.type === '1'){\n        this.integral()\n      }else{\n        this.getInfo()\n      }\n    },\n    getHeader() {\n      topdetailApi({userId : this.uid}).then(res => {\n        this.psInfo = res\n      })\n    }\n  }\n}\n", null]}