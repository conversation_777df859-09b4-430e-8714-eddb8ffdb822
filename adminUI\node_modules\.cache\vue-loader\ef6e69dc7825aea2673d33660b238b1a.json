{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=template&id=1907ea3f&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754382949112}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('product.search')}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('product.enterProductName'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.form.keywords),callback:function ($$v) {_vm.$set(_vm.form, \"keywords\", $$v)},expression:\"form.keywords\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.status')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('product.pleaseSelect')},model:{value:(_vm.form.isShow),callback:function ($$v) {_vm.$set(_vm.form, \"isShow\", $$v)},expression:\"form.isShow\"}},_vm._l((_vm.statusOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t('product.' + item.label),\"value\":item.value}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.brandName')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('product.pleaseSelect'),\"clearable\":\"\"},model:{value:(_vm.form.brand),callback:function ($$v) {_vm.$set(_vm.form, \"brand\", $$v)},expression:\"form.brand\"}},_vm._l((_vm.brandOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.onSearch}},[_vm._v(\"\\n         \"+_vm._s(_vm.$t(\"product.query\"))+\"\\n       \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":_vm.onReset}},[_vm._v(\"\\n         \"+_vm._s(_vm.$t(\"product.reset\"))+\"\\n       \")]),_vm._v(\" \"),_c('div',{staticClass:\"acea-row padtop-10\"},[_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"success\"},on:{\"click\":_vm.onAdd}},[_vm._v(\"\\n           \"+_vm._s(_vm.$t(\"product.addProduct\"))+\"\\n         \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.batchHandle('online')}}},[_vm._v(\"\\n           \"+_vm._s(_vm.$t(\"product.batchOnline\"))+\"\\n         \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.batchHandle('outline')}}},[_vm._v(\"\\n           \"+_vm._s(_vm.$t(\"product.batchOffline\"))+\"\\n         \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":function($event){return _vm.batchHandle('delete')}}},[_vm._v(\"\\n           \"+_vm._s(_vm.$t(\"product.batchDelete\"))+\"\\n         \")])],1)],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData.data,\"size\":\"mini\",\"highlight-current-row\":true,\"header-cell-style\":{ fontWeight: 'bold' }},on:{\"selection-change\":_vm.handleSelection}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productImage'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.image || '',\"preview-src-list\":[scope.row.image || '']}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productName'),\"min-width\":\"160\",\"show-overflow-tooltip\":true,\"prop\":\"storeName\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productPrice'),\"min-width\":\"90\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n           \"+_vm._s(_vm.formatAmount(scope.row.price))+\"\\n       \")]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.cashbackRate'),\"min-width\":\"100\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatRate(scope.row.cashBackRate)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.isHot'),\"min-width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.isShowChange(scope.row, scope.row.isHot, 'isHot')}},model:{value:(scope.row.isHot),callback:function ($$v) {_vm.$set(scope.row, \"isHot\", $$v)},expression:\"scope.row.isHot\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.isBenefit'),\"min-width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.isShowChange(scope.row, scope.row.isBenefit, 'isBenefit')}},model:{value:(scope.row.isBenefit),callback:function ($$v) {_vm.$set(scope.row, \"isBenefit\", $$v)},expression:\"scope.row.isBenefit\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.isTikTok'),\"min-width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.isShowChange(scope.row, scope.row.isBest, 'isBest')}},model:{value:(scope.row.isBest),callback:function ($$v) {_vm.$set(scope.row, \"isBest\", $$v)},expression:\"scope.row.isBest\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.addTime'),\"min-width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatTime(scope.row.addTime)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.online'),\"min-width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-switch',{attrs:{\"active-value\":true,\"inactive-value\":false},on:{\"change\":function($event){return _vm.handleUpdate(scope.row)}},model:{value:(scope.row.isShow),callback:function ($$v) {_vm.$set(scope.row, \"isShow\", $$v)},expression:\"scope.row.isShow\"}})]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.action'),\"min-width\":\"100\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{staticClass:\"mr10\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editProduct(scope.row, scope.$index)}}},[_vm._v(\"\\n             \"+_vm._s(_vm.$t(\"product.edit\"))+\"\\n           \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row, scope.$index)}}},[_vm._v(\"\\n             \"+_vm._s(_vm.$t(\"product.delete\"))+\"\\n           \")])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"block\"},[_c('el-pagination',{attrs:{\"page-sizes\":[20, 40, 60, 80],\"page-size\":_vm.form.limit,\"current-page\":_vm.form.page,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.tableData.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.pageChange}})],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":_vm.isEditMode ? _vm.$t('product.editDialogTitle') : _vm.$t('product.addDialogTitle'),\"visible\":_vm.productDialogVisible,\"width\":\"540px\",\"before-close\":_vm.handleCloseProductDialog},on:{\"update:visible\":function($event){_vm.productDialogVisible=$event}}},[_c('el-form',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"dform\",staticClass:\"mt24\",attrs:{\"model\":_vm.dform,\"label-width\":\"160px\"},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('product.enterProductLink')}},[_c('el-input',{staticClass:\"selWidth width200\",attrs:{\"placeholder\":\"please input link\",\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.url),callback:function ($$v) {_vm.url=$$v},expression:\"url\"}}),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\"},on:{\"click\":_vm.fetchProduct}},[_vm._v(\"\\n           \"+_vm._s(_vm.$t(\"product.fetchProductInfo\"))+\"\\n         \")])],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('brand.brandName')}},[_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"}},[_c('el-select',{staticStyle:{\"flex\":\"1\"},attrs:{\"placeholder\":_vm.$t('brand.pleaseSelect'),\"loading\":_vm.brandLoading,\"filterable\":\"\",\"clearable\":\"\"},model:{value:(_vm.brandName),callback:function ($$v) {_vm.brandName=$$v},expression:\"brandName\"}},_vm._l((_vm.brandOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"small\",\"icon\":\"el-icon-refresh\",\"loading\":_vm.brandLoading,\"title\":_vm.$t('brand.refreshBrands') || '刷新品牌数据'},on:{\"click\":_vm.refreshBrands}})],1)]),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.productName')}},[_c('el-input',{staticClass:\"selWidth readonly-input\",attrs:{\"readonly\":\"\",\"placeholder\":_vm.$t('product.enterProductName'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.storeName),callback:function ($$v) {_vm.$set(_vm.dform, \"storeName\", $$v)},expression:\"dform.storeName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.productImage')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":_vm.dform.image,\"preview-src-list\":[_vm.dform.image]}})],1)]}}])}),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.productPrice')}},[_c('el-input',{staticClass:\"selWidth readonly-input\",attrs:{\"readonly\":\"\",\"placeholder\":_vm.$t('product.enterProductPrice'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.price),callback:function ($$v) {_vm.$set(_vm.dform, \"price\", $$v)},expression:\"dform.price\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.cashbackRate')}},[_c('el-input',{staticClass:\"selWidth readonly-input\",attrs:{\"readonly\":\"\",\"placeholder\":_vm.$t('product.enterCashbackRate'),\"size\":\"small\",\"clearable\":\"\"},model:{value:(_vm.dform.forMatCashBackRate),callback:function ($$v) {_vm.$set(_vm.dform, \"forMatCashBackRate\", $$v)},expression:\"dform.forMatCashBackRate\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.isOnline')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('product.pleaseSelect')},model:{value:(_vm.status),callback:function ($$v) {_vm.status=$$v},expression:\"status\"}},_vm._l((_vm.typeOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t('product.' + item.label),\"value\":item.value}})}),1)],1)],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.onSubProduct}},[_vm._v(_vm._s(_vm.$t(\"product.confirm\")))]),_vm._v(\" \"),_c('el-button',{on:{\"click\":_vm.handleCloseProductDialog}},[_vm._v(_vm._s(_vm.$t(\"product.cancel\")))])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}