<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <div class="container mt-1">
          <el-form inline size="small">
            <el-form-item :label="$t('brand.search')">
              <el-input
                v-model="form.name"
                :placeholder="$t('brand.brandNameInput')"
                class="selWidth"
                size="small"
                clearable
              />
            </el-form-item>
            <el-form-item :label="$t('brand.status')">
              <el-select
                v-model="form.type"
                :placeholder="$t('brand.pleaseSelect')"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <el-button size="small" type="primary" class="mr10" @click="onSearch">{{
          $t("brand.query")
        }}</el-button>
        <el-button size="small" type="" class="mr10" @click="onReset">{{
          $t("brand.reset")
        }}</el-button>

        <div class="acea-row padtop-10">
          <el-button size="small" type="success" @click="onAdd">{{
            $t("brand.addBrand")
          }}</el-button>
          <el-button
            size="small"
            :disabled="multipleSelection.length === 0"
            @click="batchHandle('online')"
          >{{
            $t("brand.batchOnline")
          }}</el-button>
          <el-button
            size="small"
            :disabled="multipleSelection.length === 0"
            @click="batchHandle('outline')"
          >{{
            $t("brand.batchOffline")
          }}</el-button>
          <el-button
            size="small"
            :disabled="multipleSelection.length === 0"
            @click="batchHandle('delete')"
          >{{
            $t("brand.batchDelete")
          }}</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="mini"
        :highlight-current-row="true"
        :header-cell-style="{ fontWeight: 'bold' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$t('brand.brandLogo')" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.logoUrl"
                :preview-src-list="[scope.row.logoUrl]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('brand.brandName')"
          min-width="80"
          prop="name"
        />
        <el-table-column
          :label="$t('brand.industry')"
          min-width="160"
          :show-overflow-tooltip="true"
          prop="industry"
        />
        <el-table-column
          :label="$t('brand.platform')"
          min-width="90"
          align="center"
          prop="platform"
        />
        <el-table-column
          :label="$t('brand.productCount')"
          min-width="100"
          align="center"
          prop="productCount"
        />
        <!-- <el-table-column
          :label="$t('brand.maxCashback')"
          min-width="100"
          align="center"
          prop="maxCashBackRate"
        /> -->
        <!-- <el-table-column
          :label="$t('brand.soldCount')"
          min-width="120"
          align="center"
          prop="productSoldCount"
        /> -->
        <!-- <el-table-column
          :label="$t('brand.soldAmount')"
          min-width="100"
          align="center"
          prop="productSoldAmount"
        /> -->
        <!-- <el-table-column
          :label="$t('brand.cashbackAmount')"
          min-width="100"
          align="center"
          prop="productCashbackAmount"
        /> -->
        <!-- <el-table-column
          :label="$t('brand.shareCount')"
          min-width="120"
          align="center"
          prop="productShareCount"
        /> -->
        <el-table-column
          :label="$t('brand.createTime')"
          min-width="120"
          align="center"
          prop="gmtCreate"
        />
        <el-table-column
          :label="$t('brand.creator')"
          min-width="120"
          align="center"
          prop="creator"
        />
        <el-table-column
          :label="$t('brand.isHot')"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isHot"
              :active-value="true"
              :inactive-value="false"
              @change="isHotChange(scope.row.id, scope.row.isHot)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('brand.isHighCashback')"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isHighCashback"
              :active-value="true"
              :inactive-value="false"
              @change="
                isHighCashbackChange(scope.row.id, scope.row.isHighCashback)
              "
            />
          </template>
        </el-table-column>

     <!--    <el-table-column
          :label="$t('brand.statusLabel')"
          min-width="120"
          align="center"
        >
         <template slot-scope="scope">
            <span v-if="scope.row.status=='1'">{{$t("product.isOnline")}}</span><span v-else>{{$t("product.isOutline")}}</span>
          </template>
        </el-table-column> -->

        <el-table-column
          :label="$t('product.action')"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <!-- <el-button
              type="text"
              size="small"
              class="mr10"
              @click="handleUpdate(scope.row, scope.$index)"
            >
              <span v-if="scope.row.status == '1'">{{
                $t("brand.offline")
              }}</span>
              <span v-else>{{ $t("brand.online") }}</span>
            </el-button> -->
            <router-link :to=" { path:'/brand/product/list',query: {brand:scope.row.code} } ">
              <el-button size="small" type="text" class="mr10">{{ $t("brand.productList")}}</el-button>
            </router-link>
            <el-button
              type="text"
              size="small"
              class="mr10"
              @click="editBrand(scope.row, scope.$index)"
              >{{ $t("brand.edit") }}</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              >{{ $t("brand.delete") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="form.limit"
          :current-page="form.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑品牌对话框 -->
    <el-dialog
      :title="isEditMode ? $t('brand.editDialogTitle') : $t('brand.addDialogTitle')"
      :visible.sync="brandDialogVisible"
      width="500px"
      :before-close="handleCloseBrandDialog"
    >
      <el-form
        class="mt20"
        ref="dform"
        :model="dform"
        label-width="120px"
        @submit.native.prevent
        v-loading="loading"
      >
        <el-form-item :label="$t('brand.brandName')">
          <el-input
            v-model="dform.name"
            :placeholder="$t('brand.brandNameInput')"
            class="selWidth"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('brand.brandLogo')">
          <el-input
            v-model="dform.logoUrl"
            :placeholder="$t('brand.brandLogoInput')"
            class="selWidth"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('brand.industry')">
          <el-select
            v-model="dform.industry"
            :placeholder="$t('brand.pleaseSelect')"
          >
            <el-option
              v-for="item in industryListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('brand.platform')">
          <el-select
            v-model="dform.platform"
            :placeholder="$t('brand.pleaseSelect')"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="$t(item.label)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('brand.contactPerson')">
          <el-input
            v-model="dform.contactPerson"
            :placeholder="$t('brand.contactPerson')"
            class="selWidth"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('brand.contactPhone')">
          <el-input
            v-model="dform.contactPhone"
            :placeholder="$t('brand.contactPhone')"
            class="selWidth"
            size="small"
            clearable
          />
        </el-form-item>
        <el-form-item :label="$t('brand.status')">
          <el-select
            v-model="dform.status"
            :placeholder="$t('brand.pleaseSelect')"
          >
            <el-option
              v-for="item in editStatusOptions"
              :key="item.value"
              :label="$t(item.label)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSubBrand">{{
          isEditMode ? $t("brand.update") : $t("brand.confirm")
        }}</el-button>
        <el-button @click="handleCloseBrandDialog">{{
          $t("brand.cancel")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { productLstApi, productDeleteApi, categoryApi, putOnShellApi, offShellApi, productHeadersApi, productExportApi, restoreApi, productExcelApi } from '@/api/store'
import {
  brandLstApi,
  addBrand,
  updateBrandInfo,
  batchUpdateBrandInfo,
  industryLstApi
} from "@/api/brand";
import { getToken } from "@/utils/auth";
// import taoBao from './taoBao'
// import UploadIndex from '@/components/uploadPicture/index.vue'

import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  name: "BrandProductList",
  // components: { UploadIndex },
  data() {
    return {
      pictureType: "maintain",
      // 品牌状态常量
      BRAND_STATUS: {
        DELETED: "-1",    // 已删除
        OFFLINE: "0",     // 待上架
        ONLINE: "1",      // 已上架
        SUSPENDED: "2"    // 已下架
      },
      /*
       * 品牌状态使用规范：
       * - 新增品牌：默认 "1"(已上架)，可选待上架
       * - 审核通过：从 "0" → "1"(已上架)
       * - 临时下架：从 "1" → "2"(已下架)
       * - 重新上架：从 "2" → "1"(已上架)
       * - 逻辑删除：任何状态 → "-1"(已删除)
       */
      statusOptions: [
        {
          value: "-1",
          label: "brand.pleaseSelect"
        },
        {
          value: "0", // BRAND_STATUS.OFFLINE
          label: "brand.isOutline"
        },
        {
          value: "1", // BRAND_STATUS.ONLINE
          label: "brand.isOnline"
        },
        {
          value: "2", // BRAND_STATUS.SUSPENDED
          label: "brand.isOuted"
        }
      ],
      locationOptions: [
        {
          value: "all",
          label: "common.pleaseSelect"
        },
        {
          value: "yes",
          label: "common.yes"
        },
        {
          value: "no",
          label: "common.no"
        }
      ],
      industryOptions: [
        {
          value: "1",
          label: "2"
        }
      ],
      platformOptions: [
        {
          value: "tiktok",
          label: "brand.platformTiktok"
        },
        {
          value: "shopee",
          label: "brand.platformShopee"
        }
      ],
      typeOptions: [
        {
          value: "1",
          label: "common.yes"
        },
        {
          value: "0",
          label: "common.no"
        }
      ],
      editStatusOptions: [
        {
          value: "0", // BRAND_STATUS.OFFLINE - 待上架
          label: "brand.isOutline"
        },
        {
          value: "1", // BRAND_STATUS.ONLINE - 已上架
          label: "brand.isOnline"
        },
        {
          value: "2", // BRAND_STATUS.SUSPENDED - 已下架
          label: "brand.isOuted"
        }
      ],
      categoryOptions: [],
      loading: false,
      listLoading: false,
      tableData: {
        data: [],
        total: 0
      },
      form: {
        page: 1,
        limit: 20,
        name: "",
        type: "-1"
      },
      dform: {
        id: "",
        name: "",
        image: "",
        logoUrl: "",
        industry: "",
        platform: "",
        status: "",
        contactPhone: "",
        contactPerson: ""
      },
      brandDialogVisible: false,
      isEditMode: false, // 标识是否为编辑模式
      multipleSelection: [],
      industryListOptions: []
    };
  },
  mounted() {
    let _this = this;
    this.getList();
    industryLstApi(74)
      .then(res => {
        res.list.forEach(item => {
          let values = item.value;
          let fields = JSON.parse(values);
          let one = {};

          fields.fields.forEach(v => {
            console.log(v);
            if (v.name == "code") {
              one["value"] = v.value;
            } else if (v.name == "name") {
              one["label"] = v.value;
            }
          });
          _this.industryListOptions.push(one);
        });
      })
      .catch(res => {});
  },
  methods: {
    checkPermi,

    onSearch() {
      this.form.page = 1;
      this.getList();
    },

    onReset() {
      this.form.name = "";
      this.form.type = "-1";
    },

    getList() {
      this.listLoading = true;
      brandLstApi(this.form)
        .then(res => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(res => {
          this.listLoading = false;
          this.$message.error(this.$t("common.fetchDataFailed"));
        });
    },

    pageChange(page) {
      this.form.page = page;
      this.getList();
    },

    handleSizeChange(val) {
      this.form.limit = val;
      this.getList();
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },

    handleDelete(rowId, idx) {
      let _this = this;
      let rows = [];

      this.$confirm(
        this.$t("brand.confirmOperation"),
        this.$t("brand.prompt"),
        {
          confirmButtonText: this.$t("brand.confirm"),
          cancelButtonText: this.$t("brand.cancel"),
          type: "warning",
          showClose: false
        }
      ).then(() => {
        // 状态流转：任何状态 → "-1"(已删除)
        let item = { id: rowId, status: this.BRAND_STATUS.DELETED };
        rows.push(item);
        batchUpdateBrandInfo(rows)
          .then(res => {
            _this.getList();
          })
          .catch(res => {});
      });
    },

    onAdd() {
      this.isEditMode = false;
      this.dform = {
        id: "",
        name: "",
        image: "",
        logoUrl: "",
        industry: "",
        platform: "",
        status: this.BRAND_STATUS.ONLINE, // 新增品牌默认为已上架状态
        contactPhone: "",
        contactPerson: ""
      };
      this.brandDialogVisible = true;
    },

    handleCloseBrandDialog() {
      this.brandDialogVisible = false;
      this.isEditMode = false;
    },

    editBrand(row, idx) {
      this.isEditMode = true;
      this.dform = { ...row }; // 使用展开运算符避免直接引用
      this.brandDialogVisible = true;
    },

    onSubBrand() {
      if (this.isEditMode) {
        // 编辑模式，调用更新接口
        updateBrandInfo(this.dform)
          .then(res => {
            this.brandDialogVisible = false;
            this.isEditMode = false;
            this.$message.success(this.$t("common.operationSuccess"));
            this.getList(); // 刷新列表
          })
          .catch(res => {
            this.$message.error(this.$t("common.operationFailed"));
          });
      } else {
        // 新增模式，调用新增接口
        addBrand(this.dform)
          .then(res => {
            this.brandDialogVisible = false;
            this.$message.success(this.$t("common.operationSuccess"));
            this.getList(); // 刷新列表
          })
          .catch(res => {
            this.$message.error(this.$t("common.operationFailed"));
          });
      }
    },

    handleUpdate(row, idx) {
      let _this = this;
      let rows = [];
      this.$confirm(
        this.$t("brand.confirmOperation"),
        this.$t("brand.prompt"),
        {
          confirmButtonText: _this.$t("brand.confirm"),
          cancelButtonText: _this.$t("brand.cancel"),
          type: "warning",
          showClose: false
        }
      ).then(() => {
        let item = { id: row.id };
        // 根据状态使用规范进行状态流转
        if (row.status == this.BRAND_STATUS.ONLINE) {
          // 已上架 → 已下架
          item.status = this.BRAND_STATUS.SUSPENDED;
        } else if (row.status == this.BRAND_STATUS.SUSPENDED) {
          // 已下架 → 已上架
          item.status = this.BRAND_STATUS.ONLINE;
        } else if (row.status == this.BRAND_STATUS.OFFLINE) {
          // 待上架 → 已上架
          item.status = this.BRAND_STATUS.ONLINE;
        }
        rows.push(item)
        batchUpdateBrandInfo(rows)
          .then(res => {
            _this.getList();
          })
          .catch(res => {});
      });
    },

    batchHandle(type) {
      let _this = this;
      let rows = [];
      if(this.multipleSelection.length<=0) {
        this.$alert(this.$t("brand.selectTip"), this.$t('brand.prompt'), {
          confirmButtonText: _this.$t("brand.confirm"),
          type: 'warning',
          showConfirmButton:false
        });
      }else{
          this.$confirm(
            this.$t("brand.confirmOperation"),
            this.$t("brand.prompt"),
            {
              confirmButtonText: this.$t("brand.confirm"),
              cancelButtonText: this.$t("brand.cancel"),
              type: "warning",
              showClose: false
            }
          ).then(() => {
            _this.multipleSelection.forEach(row => {
              let item = { id: row.id };
              // 根据状态使用规范设置目标状态
              if (type == "online") {
                // 状态流转：待上架/已下架 → 已上架
                item["status"] = this.BRAND_STATUS.ONLINE;
              } else if (type == "outline") {
                // 状态流转：已上架 → 已下架
                item["status"] = this.BRAND_STATUS.SUSPENDED;
              } else if (type == "delete") {
                // 状态流转：任何状态 → 已删除
                item["status"] = this.BRAND_STATUS.DELETED;
              }
              rows.push(item);
            });
            if (rows.length > 0) {
              this.listLoading = true;
              batchUpdateBrandInfo(rows)
                .then(res => {
                  _this.getList();
                })
                .catch(res => {});
            }
          });
      }
    },

    isHighCashbackChange(rowId, val) {
      let _this = this;
      let rows = [];

      let item = { id: rowId };
      if (val) {
        item["isHighCashback"] = true;
      } else {
        item["isHighCashback"] = false;
      }
      rows.push(item);
      batchUpdateBrandInfo(rows)
        .then(res => {
          _this.getList();
        })
        .catch(res => {});
    },

    isHotChange(rowId, val) {
      let _this = this;
      let rows = [];
      let item = { id: rowId };
      if (val) {
        item["isHot"] = true;
      } else {
        item["isHot"] = false;
      }
      rows.push(item);
      batchUpdateBrandInfo(rows)
        .then(res => {
          _this.getList();
        })
        .catch(res => {});
    }
  }
};
</script>

<style scoped lang="scss">
.padtop-10 {
  margin-top: 10px;
}
</style>
