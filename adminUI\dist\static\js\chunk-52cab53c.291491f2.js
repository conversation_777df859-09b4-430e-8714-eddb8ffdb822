(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52cab53c"],{"2f2c":function(t,e,a){"use strict";a.d(e,"b",(function(){return u})),a.d(e,"c",(function(){return d})),a.d(e,"r",(function(){return m})),a.d(e,"d",(function(){return f})),a.d(e,"a",(function(){return p})),a.d(e,"g",(function(){return h})),a.d(e,"h",(function(){return b})),a.d(e,"j",(function(){return g})),a.d(e,"i",(function(){return v})),a.d(e,"e",(function(){return y})),a.d(e,"o",(function(){return V})),a.d(e,"q",(function(){return w})),a.d(e,"l",(function(){return _})),a.d(e,"m",(function(){return k})),a.d(e,"n",(function(){return x})),a.d(e,"p",(function(){return $})),a.d(e,"k",(function(){return T})),a.d(e,"f",(function(){return C}));var r=a("b775");function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function s(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(Object(a),!0).forEach((function(e){o(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function o(t,e,a){return(e=l(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function l(t){var e=c(t,"string");return"symbol"==i(e)?e:e+""}function c(t,e){if("object"!=i(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){return Object(r["a"])({url:"/admin/system/city/list",method:"get",params:s({},t)})}function d(){return Object(r["a"])({url:"/admin/system/city/list/tree",method:"get"})}function m(t){return Object(r["a"])({url:"/admin/system/city/update/status",method:"post",params:s({},t)})}function f(t){return Object(r["a"])({url:"/admin/system/city/update",method:"post",params:s({},t)})}function p(t){return Object(r["a"])({url:"/admin/system/city/info",method:"get",params:s({},t)})}function h(t){return Object(r["a"])({url:"/admin/express/list",method:"get",params:s({},t)})}function b(){return Object(r["a"])({url:"/admin/express/sync/express",method:"post"})}function g(t){return Object(r["a"])({url:"/admin/express/update/show",method:"post",data:t})}function v(t){return Object(r["a"])({url:"/admin/express/update",method:"post",data:t})}function y(t){return Object(r["a"])({url:"/admin/express/delete",method:"GET",params:s({},t)})}function V(t){return Object(r["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:s({},t)})}function w(t){return Object(r["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:s({},t)})}function _(t){return Object(r["a"])({url:"/admin/express/shipping/free/list",method:"get",params:s({},t)})}function k(t){return Object(r["a"])({url:"admin/express/shipping/region/list",method:"get",params:s({},t)})}function x(t){return Object(r["a"])({url:"admin/express/shipping/templates/save",method:"post",data:t})}function $(t,e){return Object(r["a"])({url:"admin/express/shipping/templates/update",method:"post",data:t,params:s({},e)})}function T(t){return Object(r["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:t})}function C(t){return Object(r["a"])({url:"admin/express/info",method:"get",params:s({},t)})}},"43c0":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("el-steps",{attrs:{active:t.currentTab,"align-center":"","finish-status":"success"}},[a("el-step",{attrs:{title:"商品信息"}}),t._v(" "),a("el-step",{attrs:{title:"商品详情"}}),t._v(" "),a("el-step",{attrs:{title:"其他设置"}})],1)],1),t._v(" "),a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.fullscreenLoading,expression:"fullscreenLoading"}],ref:"formValidate",staticClass:"formValidate mt20",attrs:{rules:t.ruleValidate,model:t.formValidate,"label-width":"120px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-row",{directives:[{name:"show",rawName:"v-show",value:0===t.currentTab,expression:"currentTab === 0"}],attrs:{gutter:24}},[a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品名称：",prop:"storeName"}},[a("el-input",{attrs:{maxlength:"249",placeholder:"请输入商品名称",disabled:t.isDisabled},model:{value:t.formValidate.storeName,callback:function(e){t.$set(t.formValidate,"storeName",e)},expression:"formValidate.storeName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品分类：",prop:"cateIds"}},[a("el-cascader",{staticClass:"selWidth",attrs:{options:t.merCateList,props:t.props2,clearable:"","show-all-levels":!1,disabled:t.isDisabled},model:{value:t.formValidate.cateIds,callback:function(e){t.$set(t.formValidate,"cateIds",e)},expression:"formValidate.cateIds"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品关键字：",prop:"keyword"}},[a("el-input",{attrs:{placeholder:"请输入商品关键字",disabled:t.isDisabled},model:{value:t.formValidate.keyword,callback:function(e){t.$set(t.formValidate,"keyword",e)},expression:"formValidate.keyword"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"单位：",prop:"unitName"}},[a("el-input",{attrs:{placeholder:"请输入单位",disabled:t.isDisabled},model:{value:t.formValidate.unitName,callback:function(e){t.$set(t.formValidate,"unitName",e)},expression:"formValidate.unitName"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品简介：",prop:"storeInfo"}},[a("el-input",{attrs:{type:"textarea",maxlength:"250",rows:3,placeholder:"请输入商品简介",disabled:t.isDisabled},model:{value:t.formValidate.storeInfo,callback:function(e){t.$set(t.formValidate,"storeInfo",e)},expression:"formValidate.storeInfo"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid2,!1),[a("el-form-item",{attrs:{label:"商品封面图：",prop:"image"}},[a("div",{staticClass:"upLoadPicBox",attrs:{disabled:t.isDisabled},on:{click:function(e){return t.modalPicTap("1")}}},[t.formValidate.image?a("div",{staticClass:"pictrue"},[a("img",{attrs:{src:t.formValidate.image}})]):a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])])],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品轮播图：",prop:"sliderImages"}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.sliderImages,(function(e,r){return a("div",{key:r,staticClass:"pictrue",attrs:{draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnter(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[a("img",{attrs:{src:e}}),t._v(" "),t.isDisabled?t._e():a("i",{staticClass:"el-icon-error btndel",on:{click:function(e){return t.handleRemove(r)}}})])})),t._v(" "),t.formValidate.sliderImages.length<10&&!t.isDisabled?a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("2")}}},[a("div",{staticClass:"upLoad"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])]):t._e()],2)])],1),t._v(" "),a("el-col",{attrs:{xs:18,sm:18,md:18,lg:12,xl:12}},[a("el-form-item",{attrs:{label:"运费模板：",prop:"tempId"}},[a("el-select",{staticClass:"mr20",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:t.isDisabled},model:{value:t.formValidate.tempId,callback:function(e){t.$set(t.formValidate,"tempId",e)},expression:"formValidate.tempId"}},t._l(t.shippingList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),a("el-col",{attrs:{xs:6,sm:6,md:6,lg:12,xl:12}},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.isDisabled,expression:"!isDisabled"}],staticClass:"mr15",on:{click:t.addTem}},[t._v("运费模板")])],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品规格：",props:"specType"}},[a("el-radio-group",{attrs:{disabled:t.isDisabled},on:{change:function(e){return t.onChangeSpec(t.formValidate.specType)}},model:{value:t.formValidate.specType,callback:function(e){t.$set(t.formValidate,"specType",e)},expression:"formValidate.specType"}},[a("el-radio",{staticClass:"radio",attrs:{label:!1}},[t._v("单规格")]),t._v(" "),a("el-radio",{attrs:{label:!0}},[t._v("多规格")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"佣金设置：",props:"isSub"}},[a("el-radio-group",{attrs:{disabled:t.isDisabled},on:{change:function(e){return t.onChangetype(t.formValidate.isSub)}},model:{value:t.formValidate.isSub,callback:function(e){t.$set(t.formValidate,"isSub",e)},expression:"formValidate.isSub"}},[a("el-radio",{staticClass:"radio",attrs:{label:!0}},[t._v("单独设置")]),t._v(" "),a("el-radio",{attrs:{label:!1}},[t._v("默认设置")])],1)],1)],1),t._v(" "),t.formValidate.specType&&!t.isDisabled?a("el-col",{staticClass:"noForm",attrs:{span:24}},[a("el-form-item",{attrs:{label:"选择规格：",prop:""}},[a("div",{staticClass:"acea-row"},[a("el-select",{model:{value:t.formValidate.selectRule,callback:function(e){t.$set(t.formValidate,"selectRule",e)},expression:"formValidate.selectRule"}},t._l(t.ruleList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.ruleName,value:t.id}})})),1),t._v(" "),a("el-button",{staticClass:"mr20",attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确认")]),t._v(" "),a("el-button",{staticClass:"mr15",on:{click:t.addRule}},[t._v("添加规格")])],1)]),t._v(" "),a("el-form-item",t._l(t.formValidate.attr,(function(e,r){return a("div",{key:r},[a("div",{staticClass:"acea-row row-middle"},[a("span",{staticClass:"mr5"},[t._v(t._s(e.attrName))]),a("i",{staticClass:"el-icon-circle-close",on:{click:function(e){return t.handleRemoveAttr(r)}}})]),t._v(" "),a("div",{staticClass:"rulesBox"},[t._l(e.attrValue,(function(r,i){return a("el-tag",{key:i,staticClass:"mb5 mr10",attrs:{closable:"",size:"medium","disable-transitions":!1},on:{close:function(a){return t.handleClose(e.attrValue,i)}}},[t._v("\n                    "+t._s(r)+"\n                  ")])})),t._v(" "),e.inputVisible?a("el-input",{ref:"saveTagInput",refInFor:!0,staticClass:"input-new-tag",attrs:{size:"small"},on:{blur:function(a){return t.createAttr(e.attrValue.attrsVal,r)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.createAttr(e.attrValue.attrsVal,r)}},model:{value:e.attrValue.attrsVal,callback:function(a){t.$set(e.attrValue,"attrsVal",a)},expression:"item.attrValue.attrsVal"}}):a("el-button",{staticClass:"button-new-tag",attrs:{size:"small"},on:{click:function(a){return t.showInput(e)}}},[t._v("+ 添加")])],2)])})),0),t._v(" "),t.isBtn?a("el-col",[a("el-col",{attrs:{xl:6,lg:9,md:9,sm:24,xs:24}},[a("el-form-item",{attrs:{label:"规格："}},[a("el-input",{attrs:{placeholder:"请输入规格"},model:{value:t.formDynamic.attrsName,callback:function(e){t.$set(t.formDynamic,"attrsName",e)},expression:"formDynamic.attrsName"}})],1)],1),t._v(" "),a("el-col",{attrs:{xl:6,lg:9,md:9,sm:24,xs:24}},[a("el-form-item",{attrs:{label:"规格值："}},[a("el-input",{attrs:{placeholder:"请输入规格值"},model:{value:t.formDynamic.attrsVal,callback:function(e){t.$set(t.formDynamic,"attrsVal",e)},expression:"formDynamic.attrsVal"}})],1)],1),t._v(" "),a("el-col",{attrs:{xl:12,lg:6,md:6,sm:24,xs:24}},[a("el-form-item",{staticClass:"noLeft"},[a("el-button",{staticClass:"mr15",attrs:{type:"primary"},on:{click:t.createAttrName}},[t._v("确定")]),t._v(" "),a("el-button",{on:{click:t.offAttrName}},[t._v("取消")])],1)],1)],1):t._e(),t._v(" "),t.isBtn?t._e():a("el-form-item",[a("el-button",{staticClass:"mr15",attrs:{type:"primary",icon:"md-add"},on:{click:t.addBtn}},[t._v("添加新规格")])],1)],1):t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType&&!t.isDisabled?a("el-col",{staticClass:"noForm",attrs:{span:24}},[a("el-form-item",{attrs:{label:"批量设置："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.oneFormBatch,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","pi")}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1369417214)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{maxlength:"9",min:"0.01"},on:{blur:function(a){return t.keyupEvent(r,e.row[r],e.$index,1)}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),t.formValidate.isSub?[a("el-table-column",{attrs:{align:"center",label:"一级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0,max:e.row.price},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,99765219)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"二级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{type:"number",min:0,max:e.row.price},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,1573915567)})]:t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"}},[[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:t.batchAdd}},[t._v("批量添加")])]],2)],2)],1)],1):t._e(),t._v(" "),a("el-col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[!1===t.formValidate.specType?a("el-form-item",[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.OneattrValue,border:"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(e){return t.modalPicTap("1","dan","pi")}}},[t.formValidate.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,1357914119)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,maxlength:"9",min:"0.01"},on:{blur:function(a){return t.keyupEvent(r,e.row[r],e.$index,2)}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),t.formValidate.isSub?[a("el-table-column",{attrs:{align:"center",label:"一级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,3549059968)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"二级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,342384460)})]:t._e()],2)],1):t._e(),t._v(" "),t.$route.params.id&&t.showAll?a("el-form-item",{attrs:{label:"全部sku："}},[a("el-button",{attrs:{type:"default",disabled:t.isDisabled},on:{click:function(e){return t.showAllSku()}}},[t._v("展示")])],1):t._e(),t._v(" "),t.formValidate.attr.length>0&&t.formValidate.specType?a("el-form-item",{staticClass:"labeltop",class:t.isDisabled?"disLabel":"disLabelmoren",attrs:{label:"商品属性："}},[a("el-table",{staticClass:"tabNumWidth",attrs:{data:t.ManyAttrValue,border:"",size:"mini"}},[t.manyTabDate?t._l(t.manyTabDate,(function(e,r){return a("el-table-column",{key:r,attrs:{align:"center",label:t.manyTabTit[r].title,"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"priceBox",domProps:{textContent:t._s(e.row[r])}})]}}],null,!0)})})):t._e(),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"图片","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"upLoadPicBox",on:{click:function(a){return t.modalPicTap("1","duo",e.$index)}}},[e.row.image?a("div",{staticClass:"pictrue tabPic"},[a("img",{attrs:{src:e.row.image}})]):a("div",{staticClass:"upLoad tabPic"},[a("i",{staticClass:"el-icon-camera cameraIconfont"})])])]}}],null,!1,3478746955)}),t._v(" "),t._l(t.attrValue,(function(e,r){return a("el-table-column",{key:r,attrs:{label:t.formThead[r].title,align:"center","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,maxlength:"9",min:"0.01"},on:{blur:function(a){return t.keyupEvent(r,e.row[r],e.$index,3)}},model:{value:e.row[r],callback:function(a){t.$set(e.row,r,a)},expression:"scope.row[iii]"}})]}}],null,!0)})})),t._v(" "),t.formValidate.isSub?a("el-table-column",{attrs:{align:"center",label:"一级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0,max:e.row.price},model:{value:e.row.brokerage,callback:function(a){t.$set(e.row,"brokerage",a)},expression:"scope.row.brokerage"}})]}}],null,!1,2857277871)}):t._e(),t._v(" "),t.formValidate.isSub?a("el-table-column",{attrs:{align:"center",label:"二级返佣(元)","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticClass:"priceBox",attrs:{disabled:t.isDisabled,type:"number",min:0,max:e.row.price},model:{value:e.row.brokerageTwo,callback:function(a){t.$set(e.row,"brokerageTwo",a)},expression:"scope.row.brokerageTwo"}})]}}],null,!1,3609981283)}):t._e(),t._v(" "),t.isDisabled?t._e():a("el-table-column",{key:"3",attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"submission",attrs:{type:"text"},on:{click:function(a){return t.delAttrTable(e.$index)}}},[t._v("删除")])]}}],null,!1,2803824461)})],2)],1):t._e()],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab&&!t.isDisabled,expression:"currentTab === 1 && !isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("Tinymce",{model:{value:t.formValidate.content,callback:function(e){t.$set(t.formValidate,"content",e)},expression:"formValidate.content"}})],1)],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:1===t.currentTab&&t.isDisabled,expression:"currentTab === 1 && isDisabled"}]},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品详情："}},[a("span",{domProps:{innerHTML:t._s(t.formValidate.content||"无")}})])],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2===t.currentTab,expression:"currentTab === 2"}]},[a("el-col",{attrs:{span:24}},[a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"排序："}},[a("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.sort,callback:function(e){t.$set(t.formValidate,"sort",e)},expression:"formValidate.sort"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"积分："}},[a("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.giveIntegral,callback:function(e){t.$set(t.formValidate,"giveIntegral",e)},expression:"formValidate.giveIntegral"}})],1)],1),t._v(" "),a("el-col",t._b({},"el-col",t.grid,!1),[a("el-form-item",{attrs:{label:"虚拟销量："}},[a("el-input-number",{attrs:{min:0,placeholder:"请输入排序",disabled:t.isDisabled},model:{value:t.formValidate.ficti,callback:function(e){t.$set(t.formValidate,"ficti",e)},expression:"formValidate.ficti"}})],1)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"商品推荐："}},[a("el-checkbox-group",{attrs:{size:"small",disabled:t.isDisabled},on:{change:t.onChangeGroup},model:{value:t.checkboxGroup,callback:function(e){t.checkboxGroup=e},expression:"checkboxGroup"}},t._l(t.recommend,(function(e,r){return a("el-checkbox",{key:r,attrs:{label:e.value}},[t._v(t._s(e.name))])})),1)],1)],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"活动优先级："}},[a("div",{staticClass:"color-list acea-row row-middle"},[t._l(t.formValidate.activity,(function(e){return a("div",{key:e,staticClass:"color-item",class:t.activity[e],attrs:{disabled:t.isDisabled,draggable:"true"},on:{dragstart:function(a){return t.handleDragStart(a,e)},dragover:function(a){return a.preventDefault(),t.handleDragOver(a,e)},dragenter:function(a){return t.handleDragEnterFont(a,e)},dragend:function(a){return t.handleDragEnd(a,e)}}},[t._v(t._s(e))])})),t._v(" "),a("div",{staticClass:"tip"},[t._v("可拖动按钮调整活动的优先展示顺序")])],2)])],1),t._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"proCoupon",attrs:{label:"优惠券："}},[a("div",{staticClass:"acea-row"},[t._l(t.formValidate.coupons,(function(e,r){return a("el-tag",{key:r,staticClass:"mr10 mb10",attrs:{closable:!t.isDisabled,"disable-transitions":!1},on:{close:function(a){return t.handleCloseCoupon(e)}}},[t._v("\n                  "+t._s(e.name)+"\n                ")])})),t._v(" "),t.isDisabled?t._e():a("el-button",{staticClass:"mr15",on:{click:t.addCoupon}},[t._v("选择优惠券")])],2)])],1)],1),t._v(" "),a("el-form-item",[a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab>0,expression:"currentTab>0"}],staticClass:"submission priamry_border",on:{click:t.handleSubmitUp}},[t._v("上一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:t.currentTab<2,expression:"currentTab<2"}],staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmitNest("formValidate")}}},[t._v("下一步")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:(2===t.currentTab||t.$route.params.id)&&!t.isDisabled,expression:"(currentTab===2 || $route.params.id) && !isDisabled"}],staticClass:"submission",attrs:{type:"primary"},on:{click:function(e){return t.handleSubmit("formValidate")}}},[t._v("提交")])],1)],1)],1),t._v(" "),a("CreatTemplates",{ref:"addTemplates",on:{getList:t.getShippingList}})],1)},i=[],n=a("8256"),s=a("73f5"),o=a("b7be"),l=a("2f2c"),c=a("e7ac"),u=(a("fca7"),a("ff3a")),d=a("5317"),m=a("61f7");function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t){return g(t)||b(t)||k(t)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function g(t){if(Array.isArray(t))return x(t)}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function n(a,r,i,n){var l=r&&r.prototype instanceof o?r:o,c=Object.create(l.prototype);return y(c,"_invoke",function(a,r,i){var n,o,l,c=0,u=i||[],d=!1,m={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,a){return n=e,o=0,l=t,m.n=a,s}};function f(a,r){for(o=a,l=r,e=0;!d&&c&&!i&&e<u.length;e++){var i,n=u[e],f=m.p,p=n[2];a>3?(i=p===r)&&(l=n[(o=n[4])?5:(o=3,3)],n[4]=n[5]=t):n[0]<=f&&((i=a<2&&f<n[1])?(o=0,m.v=r,m.n=n[1]):f<p&&(i=a<3||n[0]>r||r>p)&&(n[4]=a,n[5]=r,m.n=p,o=0))}if(i||a>1)return s;throw d=!0,r}return function(i,u,p){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,p),o=u,l=p;(e=o<2?t:l)||!d;){n||(o?o<3?(o>1&&(m.n=-1),f(o,l)):m.n=l:m.v=l);try{if(c=2,n){if(o||(i="next"),e=n[i]){if(!(e=e.call(n,l)))throw TypeError("iterator result is not an object");if(!e.done)return e;l=e.value,o<2&&(o=0)}else 1===o&&(e=n.return)&&e.call(n),o<2&&(l=TypeError("The iterator does not provide a '"+i+"' method"),o=1);n=t}else if((e=(d=m.n<0)?l:a.call(r,m))!==s)break}catch(e){n=t,o=1,l=e}finally{c=1}}return{value:e,done:d}}}(a,i,n),!0),c}var s={};function o(){}function l(){}function c(){}e=Object.getPrototypeOf;var u=[][r]?e(e([][r]())):(y(e={},r,(function(){return this})),e),d=c.prototype=o.prototype=Object.create(u);function m(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,c):(t.__proto__=c,y(t,i,"GeneratorFunction")),t.prototype=Object.create(d),t}return l.prototype=c,y(d,"constructor",c),y(c,"constructor",l),l.displayName="GeneratorFunction",y(c,i,"GeneratorFunction"),y(d),y(d,i,"Generator"),y(d,r,(function(){return this})),y(d,"toString",(function(){return"[object Generator]"})),(v=function(){return{w:n,m:m}})()}function y(t,e,a,r){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}y=function(t,e,a,r){function n(e,a){y(t,e,(function(t){return this._invoke(e,a,t)}))}e?i?i(t,e,{value:a,enumerable:!r,configurable:!r,writable:!r}):t[e]=a:(n("next",0),n("throw",1),n("return",2))},y(t,e,a,r)}function V(t,e,a,r,i,n,s){try{var o=t[n](s),l=o.value}catch(t){return void a(t)}o.done?e(l):Promise.resolve(l).then(r,i)}function w(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var n=t.apply(e,a);function s(t){V(n,r,i,s,o,"next",t)}function o(t){V(n,r,i,s,o,"throw",t)}s(void 0)}))}}function _(t,e){var a="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=k(t))||e&&t&&"number"==typeof t.length){a&&(t=a);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,s=!0,o=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return s=t.done,t},e:function(t){o=!0,n=t},f:function(){try{s||null==a.return||a.return()}finally{if(o)throw n}}}}function k(t,e){if(t){if("string"==typeof t)return x(t,e);var a={}.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?x(t,e):void 0}}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=Array(e);a<e;a++)r[a]=t[a];return r}function $(t,e,a){return(e=T(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function T(t){var e=C(t,"string");return"symbol"==f(e)?e:e+""}function C(t,e){if("object"!=f(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,e||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var O={image:"",sliderImages:[],videoLink:"",sliderImage:"",storeName:"",storeInfo:"",keyword:"",cateIds:[],cateId:null,unitName:"",sort:0,giveIntegral:0,ficti:0,isShow:!1,isBenefit:!1,isNew:!1,isGood:!1,isHot:!1,isBest:!1,tempId:"",attrValue:[{image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0}],attr:[],selectRule:"",isSub:!1,content:"",specType:!1,id:0,couponIds:[],coupons:[],activity:["默认","秒杀","砍价","拼团"]},S={price:{title:"售价"},cost:{title:"成本价"},otPrice:{title:"原价"},stock:{title:"库存"},barCode:{title:"商品编号"},weight:{title:"重量（KG）"},volume:{title:"体积(m³)"}},I={name:"ProductProductAdd",components:{Templates:d["a"],CreatTemplates:u["a"],Tinymce:n["a"]},data:function(){return{isDisabled:"1"===this.$route.params.isDisabled,activity:{"默认":"red","秒杀":"blue","砍价":"green","拼团":"yellow"},props2:{children:"child",label:"name",value:"id",multiple:!0,emitPath:!1},checkboxGroup:[],recommend:[],tabs:[],fullscreenLoading:!1,props:{multiple:!0},active:0,OneattrValue:[Object.assign({},O.attrValue[0])],ManyAttrValue:[Object.assign({},O.attrValue[0])],ruleList:[],merCateList:[],shippingList:[],formThead:Object.assign({},S),formValidate:Object.assign({},O),formDynamics:{ruleName:"",ruleValue:[]},tempData:{page:1,limit:9999},manyTabTit:{},manyTabDate:{},grid2:{xl:12,lg:12,md:12,sm:24,xs:24},formDynamic:{attrsName:"",attrsVal:""},isBtn:!1,manyFormValidate:[],currentTab:0,isChoice:"",grid:{xl:8,lg:8,md:12,sm:24,xs:24},ruleValidate:{storeName:[{required:!0,message:"请输入商品名称",trigger:"blur"}],cateIds:[{required:!0,message:"请选择商品分类",trigger:"change",type:"array",min:"1"}],keyword:[{required:!0,message:"请输入商品关键字",trigger:"blur"}],unitName:[{required:!0,message:"请输入单位",trigger:"blur"}],storeInfo:[{required:!0,message:"请输入商品简介",trigger:"blur"}],tempId:[{required:!0,message:"请选择运费模板",trigger:"change"}],image:[{required:!0,message:"请上传商品图",trigger:"change"}],sliderImages:[{required:!0,message:"请上传商品轮播图",type:"array",trigger:"change"}],specType:[{required:!0,message:"请选择商品规格",trigger:"change"}]},attrInfo:{},tableFrom:{page:1,limit:9999,keywords:""},tempRoute:{},keyNum:0,isAttr:!1,showAll:!1,videoLink:""}},computed:{attrValue:function(){var t=Object.assign({},O.attrValue[0]);return delete t.image,t},oneFormBatch:function(){var t=[Object.assign({},O.attrValue[0])];return delete t[0].barCode,t}},watch:{"formValidate.attr":{handler:function(t){this.formValidate.specType&&this.isAttr&&this.watCh(t)},immediate:!1,deep:!0}},created:function(){this.tempRoute=Object.assign({},this.$route),this.$route.params.id&&this.formValidate.specType&&this.$watch("formValidate.attr",this.watCh)},mounted:function(){this.formValidate.sliderImages=[],this.$route.params.id&&(this.setTagsViewTitle(),this.getInfo()),this.getCategorySelect(),this.getShippingList(),this.getGoodsType()},methods:{keyupEvent:function(t,e,a,r){if("barCode"!==t){var i=/^\D*([0-9]\d*\.?\d{0,2})?.*$/;switch(r){case 1:this.oneFormBatch[a][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.oneFormBatch[a],t,e.toString().replace(i,"$1"));break;case 2:this.OneattrValue[a][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.OneattrValue[a],t,e.toString().replace(i,"$1"));break;default:this.ManyAttrValue[a][t]=0==e?"stock"===t?0:.01:"stock"===t?parseInt(e):this.$set(this.ManyAttrValue[a],t,e.toString().replace(i,"$1"));break}}},handleCloseCoupon:function(t){this.isAttr=!0,this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(t),1),this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(t.id),1)},addCoupon:function(){var t=this;this.$modalCoupon("wu",this.keyNum+=1,this.formValidate.coupons,(function(e){t.formValidate.couponIds=[],t.formValidate.coupons=e,e.map((function(e){t.formValidate.couponIds.push(e.id)}))}),"")},setTagsViewTitle:function(){var t=this.isDisabled?"商品详情":"编辑商品",e=Object.assign({},this.tempRoute,{title:"".concat(t,"-").concat(this.$route.params.id)});this.$store.dispatch("tagsView/updateVisitedView",e)},onChangeGroup:function(){this.checkboxGroup.includes("isGood")?this.formValidate.isGood=!0:this.formValidate.isGood=!1,this.checkboxGroup.includes("isBenefit")?this.formValidate.isBenefit=!0:this.formValidate.isBenefit=!1,this.checkboxGroup.includes("isBest")?this.formValidate.isBest=!0:this.formValidate.isBest=!1,this.checkboxGroup.includes("isNew")?this.formValidate.isNew=!0:this.formValidate.isNew=!1,this.checkboxGroup.includes("isHot")?this.formValidate.isHot=!0:this.formValidate.isHot=!1},watCh:function(t){var e=this,a={},r={};this.formValidate.attr.forEach((function(t,e){a[t.attrName]={title:t.attrName},r[t.attrName]=""})),this.ManyAttrValue=this.attrFormat(t),this.ManyAttrValue.forEach((function(t,a){var r=Object.values(t.attrValue).sort().join("/");e.attrInfo[r]&&(e.ManyAttrValue[a]=e.attrInfo[r])})),this.attrInfo=[],this.ManyAttrValue.forEach((function(t){e.attrInfo[Object.values(t.attrValue).sort().join("/")]=t})),this.manyTabTit=a,this.manyTabDate=r,this.formThead=Object.assign({},this.formThead,a)},attrFormat:function(t){var e=[],a=[];return r(t);function r(t){if(t.length>1)t.forEach((function(r,i){0===i&&(e=t[i]["attrValue"]);var n=[];e&&(e.forEach((function(e){t[i+1]&&t[i+1]["attrValue"]&&t[i+1]["attrValue"].forEach((function(r){var s=(0!==i?"":t[i]["attrName"]+"_")+e+"$&"+t[i+1]["attrName"]+"_"+r;if(n.push(s),i===t.length-2){var o={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0};for(var l in s.split("$&").forEach((function(t,e){var a=t.split("_");o["attrValue"]||(o["attrValue"]={}),o["attrValue"][a[0]]=a.length>1?a[1]:""})),o.attrValue)o[l]=o.attrValue[l];a.push(o)}}))})),e=n.length?n:[])}));else{var r=[];t.forEach((function(t,e){t["attrValue"].forEach((function(e,i){for(var n in r[i]=t["attrName"]+"_"+e,a[i]={image:"",price:0,cost:0,otPrice:0,stock:0,barCode:"",weight:0,volume:0,brokerage:0,brokerage_two:0,attrValue:$({},t["attrName"],e)},a[i].attrValue)a[i][n]=a[i].attrValue[n]}))})),e.push(r.join("$&"))}return a}},addTem:function(){this.$refs.addTemplates.dialogVisible=!0,this.$refs.addTemplates.getCityList()},addRule:function(){var t=this;this.$modalAttr(this.formDynamics,(function(){t.productGetRule()}))},onChangeSpec:function(t){this.isAttr=!0,t&&this.productGetRule()},confirm:function(){var t=this;if(this.isAttr=!0,!this.formValidate.selectRule)return this.$message.warning("请选择属性");var e=[];this.ruleList.forEach((function(a){a.id===t.formValidate.selectRule&&a.ruleValue.forEach((function(t){e.push({attrName:t.value,attrValue:t.detail})})),t.formValidate.attr=e}))},getCategorySelect:function(){var t=this;Object(s["d"])({status:-1,type:1}).then((function(e){t.merCateList=t.filerMerCateList(e);var a=[];e.forEach((function(t,e){a[e]=t,t.child&&(a[e].child=t.child.filter((function(t){return!0===t.status})))})),t.merCateList=t.filerMerCateList(a)}))},filerMerCateList:function(t){return t.map((function(t){return t.child||(t.disabled=!0),t.label=t.name,t}))},productGetRule:function(){var t=this;Object(s["x"])(this.tableFrom).then((function(e){for(var a=e.list,r=0;r<a.length;r++)a[r].ruleValue=JSON.parse(a[r].ruleValue);t.ruleList=a}))},getShippingList:function(){var t=this;Object(l["o"])(this.tempData).then((function(e){t.shippingList=e.list}))},showInput:function(t){this.$set(t,"inputVisible",!0)},onChangetype:function(t){var e=this;1===t?(this.OneattrValue.map((function(t){e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)})),this.ManyAttrValue.map((function(t){e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)}))):(this.OneattrValue.map((function(t){delete t.brokerage,delete t.brokerageTwo,e.$set(t,"brokerage",null),e.$set(t,"brokerageTwo",null)})),this.ManyAttrValue.map((function(t){delete t.brokerage,delete t.brokerageTwo})))},delAttrTable:function(t){this.ManyAttrValue.splice(t,1)},batchAdd:function(){var t,e=_(this.ManyAttrValue);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.$set(a,"image",this.oneFormBatch[0].image),this.$set(a,"price",this.oneFormBatch[0].price),this.$set(a,"cost",this.oneFormBatch[0].cost),this.$set(a,"otPrice",this.oneFormBatch[0].otPrice),this.$set(a,"stock",this.oneFormBatch[0].stock),this.$set(a,"barCode",this.oneFormBatch[0].barCode),this.$set(a,"weight",this.oneFormBatch[0].weight),this.$set(a,"volume",this.oneFormBatch[0].volume),this.$set(a,"brokerage",this.oneFormBatch[0].brokerage),this.$set(a,"brokerageTwo",this.oneFormBatch[0].brokerageTwo)}}catch(r){e.e(r)}finally{e.f()}},addBtn:function(){this.clearAttr(),this.isBtn=!0},offAttrName:function(){this.isBtn=!1},clearAttr:function(){this.isAttr=!0,this.formDynamic.attrsName="",this.formDynamic.attrsVal=""},handleRemoveAttr:function(t){this.isAttr=!0,this.formValidate.attr.splice(t,1),this.manyFormValidate.splice(t,1)},handleClose:function(t,e){t.splice(e,1)},createAttrName:function(){if(this.isAttr=!0,this.formDynamic.attrsName&&this.formDynamic.attrsVal){var t={attrName:this.formDynamic.attrsName,attrValue:[this.formDynamic.attrsVal]};this.formValidate.attr.push(t);var e={};this.formValidate.attr=this.formValidate.attr.reduce((function(t,a){return!e[a.attrName]&&(e[a.attrName]=t.push(a)),t}),[]),this.clearAttr(),this.isBtn=!1}else this.$Message.warning("请添加完整的规格！")},createAttr:function(t,e){if(this.isAttr=!0,t){this.formValidate.attr[e].attrValue.push(t);var a={};this.formValidate.attr[e].attrValue=this.formValidate.attr[e].attrValue.reduce((function(t,e){return!a[e]&&(a[e]=t.push(e)),t}),[]),this.formValidate.attr[e].inputVisible=!1}else this.$message.warning("请添加属性")},showAllSku:function(){0==this.isAttr?(this.isAttr=!0,this.formValidate.specType&&this.isAttr&&this.watCh(this.formValidate.attr)):1==this.isAttr&&(this.isAttr=!1,this.getInfo())},getInfo:function(){var t=this;this.fullscreenLoading=!0,Object(s["l"])(this.$route.params.id).then(function(){var e=w(v().m((function e(a){var r,i,n,s,l,c;return v().w((function(e){while(1)switch(e.n){case 0:r=a,t.formValidate={image:t.$selfUtil.setDomain(r.image),sliderImage:r.sliderImage,sliderImages:JSON.parse(r.sliderImage),storeName:r.storeName,storeInfo:r.storeInfo,keyword:r.keyword,cateIds:r.cateId.split(","),cateId:r.cateId,unitName:r.unitName,sort:r.sort,isShow:r.isShow,isBenefit:r.isBenefit,isNew:r.isNew,isGood:r.isGood,isHot:r.isHot,isBest:r.isBest,tempId:r.tempId,attr:r.attr,attrValue:r.attrValue,selectRule:r.selectRule,isSub:r.isSub,content:t.$selfUtil.replaceImgSrcHttps(r.content),specType:r.specType,id:r.id,giveIntegral:r.giveIntegral,ficti:r.ficti,coupons:r.coupons,couponIds:r.couponIds,activity:r.activityStr?r.activityStr.split(","):["默认","秒杀","砍价","拼团"]},Object(o["B"])({type:3}).then((function(e){if(null!==t.formValidate.couponIds){var a=t.formValidate.couponIds.toString(),r=e.list,i={};for(var n in r)i[r[n].id]=r[n];var s,o=a.split(","),l=[],c=_(o);try{for(c.s();!(s=c.n()).done;){var u=s.value;i[u]&&l.push(i[u])}}catch(d){c.e(d)}finally{c.f()}t.$set(t.formValidate,"coupons",l)}})),i=JSON.parse(r.sliderImage),n=[],Object.keys(i).map((function(e){n.push(t.$selfUtil.setDomain(i[e]))})),t.formValidate.sliderImages=[].concat(n),r.isHot&&t.checkboxGroup.push("isHot"),r.isGood&&t.checkboxGroup.push("isGood"),r.isBenefit&&t.checkboxGroup.push("isBenefit"),r.isBest&&t.checkboxGroup.push("isBest"),r.isNew&&t.checkboxGroup.push("isNew"),t.productGetRule(),r.specType?(t.formValidate.attr=r.attr.map((function(t){return{attrName:t.attrName,attrValue:t.attrValues.split(",")}})),t.ManyAttrValue=r.attrValue,t.ManyAttrValue.forEach((function(e){e.image=t.$selfUtil.setDomain(e.image),e.attrValue=JSON.parse(e.attrValue),t.attrInfo[Object.values(e.attrValue).sort().join("/")]=e})),s=t.attrFormat(t.formValidate.attr),s.length!==t.ManyAttrValue.length?(t.$set(t,"showAll",!0),t.isAttr=!1):t.isAttr=!0,l={},c={},t.formValidate.attr.forEach((function(t,e){l[t.attrName]={title:t.attrName},c[t.attrName]=""})),t.formValidate.attrValue.forEach((function(t){for(var e in t.attrValue)t[e]=t.attrValue[e]})),t.manyTabTit=l,t.manyTabDate=c,t.formThead=Object.assign({},t.formThead,l)):t.OneattrValue=r.attrValue,t.fullscreenLoading=!1;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.fullscreenLoading=!1,t.$message.error(e.message)}))},handleRemove:function(t){this.formValidate.sliderImages.splice(t,1)},modalPicTap:function(t,e,a,r){var i=this;i.isDisabled||this.$modalUpload((function(r){if("1"!==t||e||(i.formValidate.image=r[0].sattDir,i.OneattrValue[0].image=r[0].sattDir),"2"===t&&!e){if(r.length>10)return this.$message.warning("最多选择10张图片！");if(r.length+i.formValidate.sliderImages.length>10)return this.$message.warning("最多选择10张图片！");r.map((function(t){i.formValidate.sliderImages.push(t.sattDir)}))}"1"===t&&"dan"===e&&(i.OneattrValue[0].image=r[0].sattDir),"1"===t&&"duo"===e&&(i.ManyAttrValue[a].image=r[0].sattDir),"1"===t&&"pi"===e&&(i.oneFormBatch[0].image=r[0].sattDir)}),t,"content")},handleSubmitUp:function(){this.currentTab--<0&&(this.currentTab=0)},handleSubmitNest:function(t){var e=this;this.$refs[t].validate((function(t){t?e.currentTab++>2&&(e.currentTab=0):e.formValidate.store_name&&e.formValidate.cate_id&&e.formValidate.keyword&&e.formValidate.unit_name&&e.formValidate.store_info&&e.formValidate.image&&e.formValidate.slider_image||e.$message.warning("请填写完整商品信息！")}))},handleSubmit:Object(m["a"])((function(t){var e=this;if(this.onChangeGroup(),this.formValidate.specType&&this.formValidate.attr.length<1)return this.$message.warning("请填写多规格属性！");if(this.formValidate.cateId=this.formValidate.cateIds.join(","),this.formValidate.sliderImage=JSON.stringify(this.formValidate.sliderImages),this.formValidate.specType){this.formValidate.attrValue=this.ManyAttrValue,this.formValidate.attr=this.formValidate.attr.map((function(t){return{attrName:t.attrName,id:t.id,attrValues:t.attrValue.join(",")}}));for(var a=0;a<this.formValidate.attrValue.length;a++)this.$set(this.formValidate.attrValue[a],"id",0),this.$set(this.formValidate.attrValue[a],"productId",0),this.$set(this.formValidate.attrValue[a],"attrValue",JSON.stringify(this.formValidate.attrValue[a].attrValue)),delete this.formValidate.attrValue[a].value0}else this.formValidate.attr=[{attrName:"规格",attrValues:"默认",id:this.$route.params.id?this.formValidate.attr[0].id:0}],this.OneattrValue.map((function(t){e.$set(t,"attrValue",JSON.stringify({"规格":"默认"}))})),this.formValidate.attrValue=this.OneattrValue;this.$refs[t].validate((function(t){t?(e.fullscreenLoading=!0,e.$route.params.id?Object(s["p"])(e.formValidate).then(function(){var t=w(v().m((function t(a){return v().w((function(t){while(1)switch(t.n){case 0:e.$message.success("编辑成功"),setTimeout((function(){e.$router.push({path:"/store/index"})}),500),e.fullscreenLoading=!1;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1})):Object(s["j"])(e.formValidate).then(function(){var t=w(v().m((function t(a){return v().w((function(t){while(1)switch(t.n){case 0:e.$message.success("新增成功"),setTimeout((function(){e.$router.push({path:"/store/index"})}),500),e.fullscreenLoading=!1;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){e.fullscreenLoading=!1}))):e.formValidate.storeName&&e.formValidate.cateId&&e.formValidate.keyword&&e.formValidate.unitName&&e.formValidate.storeInfo&&e.formValidate.image&&e.formValidate.sliderImages||e.$message.warning("请填写完整商品信息！")}))})),validate:function(t,e,a){!1===e&&this.$message.warning(a)},handleDragStart:function(t,e){this.isDisabled||(this.dragging=e)},handleDragEnd:function(t,e){this.isDisabled||(this.dragging=null)},handleDragOver:function(t){this.isDisabled||(t.dataTransfer.dropEffect="move")},handleDragEnter:function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var a=p(this.formValidate.sliderImages),r=a.indexOf(this.dragging),i=a.indexOf(e);a.splice.apply(a,[i,0].concat(p(a.splice(r,1)))),this.formValidate.sliderImages=a}},handleDragEnterFont:function(t,e){if(!this.isDisabled){if(t.dataTransfer.effectAllowed="move",e===this.dragging)return;var a=p(this.formValidate.activity),r=a.indexOf(this.dragging),i=a.indexOf(e);a.splice.apply(a,[i,0].concat(p(a.splice(r,1)))),this.formValidate.activity=a}},getGoodsType:function(){var t=this;Object(c["a"])({gid:70}).then((function(e){var a=e.list,r=[],i=[],n=[{name:"是否热卖",value:"isGood"}],s=[{name:"",value:"isHot",type:"2"},{name:"",value:"isBenefit",type:"4"},{name:"",value:"isBest",type:"1"},{name:"",value:"isNew",type:"3"}];a.forEach((function(t){var e={};e.value=JSON.parse(t.value),e.id=t.id,e.gid=t.gid,e.status=t.status,r.push(e)})),r.forEach((function(t){var e={};e.name=t.value.fields[1].value,e.status=t.status,e.type=t.value.fields[3].value,i.push(e)})),s.forEach((function(t){i.forEach((function(e){t.type==e.type&&n.push({name:e.name,value:t.value,type:t.type})}))})),t.recommend=n}))}}},D=I,N=(a("7832"),a("2877")),j=Object(N["a"])(D,r,i,!1,null,"0227d48d",null);e["default"]=j.exports},5317:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div")},i=[],n=a("2877"),s={},o=Object(n["a"])(s,r,i,!1,null,null,null);e["a"]=o.exports},7832:function(t,e,a){"use strict";a("f10e")},cf0d:function(t,e,a){},e16d:function(t,e,a){"use strict";a("cf0d")},e7ac:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return s})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return l}));var r=a("b775");function i(t){var e={id:t.id};return Object(r["a"])({url:"/admin/system/group/delete",method:"GET",params:e})}function n(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(r["a"])({url:"/admin/system/group/list",method:"GET",params:e})}function s(t){var e={formId:t.formId,info:t.info,name:t.name};return Object(r["a"])({url:"/admin/system/group/save",method:"POST",params:e})}function o(t){var e={formId:t.formId,info:t.info,name:t.name,id:t.id};return Object(r["a"])({url:"/admin/system/group/update",method:"POST",params:e})}function l(t){var e={gid:t.gid};return Object(r["a"])({url:"/admin/system/group/data/list",method:"GET",params:e})}},f10e:function(t,e,a){},ff3a:function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.dialogVisible?a("el-dialog",{attrs:{title:"运费模板",visible:t.dialogVisible,width:"1000px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[t.dialogVisible?a("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,"label-width":"120px",size:"mini",rules:t.rules}},[a("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[a("el-input",{staticClass:"withs",attrs:{placeholder:"请输入模板名称"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"计费方式",prop:"type"}},[a("el-radio-group",{on:{change:function(e){return t.changeRadio(t.ruleForm.type)}},model:{value:t.ruleForm.type,callback:function(e){t.$set(t.ruleForm,"type",e)},expression:"ruleForm.type"}},[a("el-radio",{attrs:{label:1}},[t._v("按件数")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("按重量")]),t._v(" "),a("el-radio",{attrs:{label:3}},[t._v("按体积")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"配送区域及运费",prop:"region"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"tempBox",staticStyle:{width:"100%"},attrs:{data:t.ruleForm.region,border:"",fit:"","highlight-current-row":"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"可配送区域","min-width":"260"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.$index?a("span",[t._v("默认全国")]):a("el-cascader",{staticStyle:{width:"98%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:"",filterable:""},on:{change:t.changeRegion},model:{value:e.row.city_ids,callback:function(a){t.$set(e.row,"city_ids",a)},expression:"scope.row.city_ids"}})]}}],null,!1,41555841)}),t._v(" "),a("el-table-column",{attrs:{"min-width":"130px",align:"center",label:t.columns.title,prop:"first"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:t.rules.first,prop:"region."+e.$index+".first"}},[a("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.first,callback:function(a){t.$set(e.row,"first",a)},expression:"scope.row.first"}})],1)]}}],null,!1,2918704294)}),t._v(" "),a("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"运费（元）",prop:"firstPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:t.rules.firstPrice,prop:"region."+e.$index+".firstPrice"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.firstPrice,callback:function(a){t.$set(e.row,"firstPrice",a)},expression:"scope.row.firstPrice"}})],1)]}}],null,!1,3560784729)}),t._v(" "),a("el-table-column",{attrs:{"min-width":"120px",align:"center",label:t.columns.title2,prop:"renewal"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:t.rules.renewal,prop:"region."+e.$index+".renewal"}},[a("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:e.row.renewal,callback:function(a){t.$set(e.row,"renewal",a)},expression:"scope.row.renewal"}})],1)]}}],null,!1,3001982106)}),t._v(" "),a("el-table-column",{attrs:{"class-name":"status-col",align:"center",label:"续费（元）","min-width":"120",prop:"renewalPrice"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-form-item",{attrs:{rules:t.rules.renewalPrice,prop:"region."+e.$index+".renewalPrice"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.row.renewalPrice,callback:function(a){t.$set(e.row,"renewalPrice",a)},expression:"scope.row.renewalPrice"}})],1)]}}],null,!1,1318028453)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.$index>0?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.confirmEdit(t.ruleForm.region,e.$index)}}},[t._v("\n              删除\n            ")]):t._e()]}}],null,!1,3477974826)})],1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addRegion(t.ruleForm.region)}}},[t._v("\n        添加配送区域\n      ")])],1),t._v(" "),a("el-form-item",{attrs:{label:"指定包邮",prop:"appoint"}},[a("el-radio-group",{model:{value:t.ruleForm.appoint,callback:function(e){t.$set(t.ruleForm,"appoint",e)},expression:"ruleForm.appoint"}},[a("el-radio",{attrs:{label:!0}},[t._v("开启")]),t._v(" "),a("el-radio",{attrs:{label:!1}},[t._v("关闭")])],1)],1),t._v(" "),!0===t.ruleForm.appoint?a("el-form-item",{attrs:{prop:"free"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.ruleForm.free,border:"",fit:"","highlight-current-row":"",size:"mini"}},[a("el-table-column",{attrs:{align:"center",label:"选择地区","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-cascader",{staticStyle:{width:"95%"},attrs:{options:t.cityList,props:t.props,"collapse-tags":"",clearable:""},model:{value:r.city_ids,callback:function(e){t.$set(r,"city_ids",e)},expression:"row.city_ids"}})]}}],null,!1,3891925036)}),t._v(" "),a("el-table-column",{attrs:{"min-width":"180px",align:"center",label:t.columns.title3},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input-number",{attrs:{"controls-position":"right","step-strictly":1===t.ruleForm.type,min:1===t.ruleForm.type?1:.1},model:{value:r.number,callback:function(e){t.$set(r,"number",e)},expression:"row.number"}})]}}],null,!1,2163935474)}),t._v(" "),a("el-table-column",{attrs:{"min-width":"120px",align:"center",label:"包邮金额（元）"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input-number",{attrs:{"controls-position":"right"},model:{value:r.price,callback:function(e){t.$set(r,"price",e)},expression:"row.price"}})]}}],null,!1,187737026)}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.confirmEdit(t.ruleForm.free,e.$index)}}},[t._v("\n              删除\n            ")])]}}],null,!1,4029474057)})],1)],1):t._e(),t._v(" "),!0===t.ruleForm.appoint?a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.addFree(t.ruleForm.free)}}},[t._v("\n        添加指定包邮区域\n      ")])],1):t._e(),t._v(" "),a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{staticClass:"withs",attrs:{placeholder:"请输入排序"},model:{value:t.ruleForm.sort,callback:function(e){t.$set(t.ruleForm,"sort",e)},expression:"ruleForm.sort"}})],1)],1):t._e(),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){return t.onClose("ruleForm")}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.onsubmit("ruleForm")}}},[t._v("确 定")])],1)],1):t._e()},i=[],n=a("2f2c"),s=a("5c96"),o={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]},l="重量（kg）",c="体积（m³）",u=[{title:"首件",title2:"续件",title3:"包邮件数"},{title:"首件".concat(l),title2:"续件".concat(l),title3:"包邮".concat(l)},{title:"首件".concat(c),title2:"续件".concat(c),title3:"包邮".concat(c)}],d={name:"CreatTemplates",components:{},data:function(){return{loading:!1,rules:{name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],free:[{type:"array",required:!0,message:"请至少添加一个地区",trigger:"change"}],appoint:[{required:!0,message:"请选择是否指定包邮",trigger:"change"}],undelivery:[{required:!0,message:"请选择是否指定区域不配送",trigger:"change"}],type:[{required:!0,message:"请选择计费方式",trigger:"change"}],region:[{required:!0,message:"请选择活动区域",trigger:"change"}],city_id3:[{type:"array",required:!0,message:"请至少选择一个地区",trigger:"change"}],first:[{required:!0,message:"请输入",trigger:"blur"}],renewal:[{required:!0,message:"请输入",trigger:"blur"}],firstPrice:[{required:!0,message:"请输入运费",trigger:"blur"}],renewalPrice:[{required:!0,message:"请输入续费",trigger:"blur"}]},nodeKey:"city_id",props:{children:"child",label:"name",value:"cityId",multiple:!0},dialogVisible:!1,ruleForm:Object.assign({},o),listLoading:!1,cityList:[],columns:{title:"首件",title2:"续件",title3:"包邮件数"},tempId:0,type:0}},mounted:function(){var t=this;setTimeout((function(){var e=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=e}),1e3)},methods:{changType:function(t){this.type=t},onClose:function(t){this.dialogVisible=!1,this.$refs[t].resetFields()},confirmEdit:function(t,e){t.splice(e,1)},popoverHide:function(){},handleClose:function(){this.dialogVisible=!1,this.ruleForm={name:"",type:1,appoint:!1,sort:0,region:[{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}],undelivery:0,free:[],undelives:{},city_id3:[]}},changeRegion:function(t){console.log(t)},changeRadio:function(t){this.columns=Object.assign({},u[t-1])},addRegion:function(t){t.push(Object.assign({},{first:0,firstPrice:0,renewal:0,renewalPrice:0,city_ids:[]}))},addFree:function(t){t.push(Object.assign({},{city_id:[],number:1,price:1,city_ids:[]}))},getInfo:function(t,e){var a=this;this.tempId=t;var r=s["Loading"].service({fullscreen:!0});n["q"]({id:t}).then((function(t){a.dialogVisible=!0;var e=t;a.ruleForm=Object.assign(a.ruleForm,{name:e.name,type:e.type,appoint:e.appoint,sort:e.sort}),a.columns=Object.assign({},u[a.ruleForm.type-1]),a.$nextTick((function(){r.close()})),a.shippingRegion(),e.appoint&&a.shippingFree()})).catch((function(t){a.$message.error(t.message),a.$nextTick((function(){r.close()}))}))},shippingRegion:function(){var t=this;n["m"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.region=e}))},shippingFree:function(){var t=this;n["l"]({tempId:this.tempId}).then((function(e){e.forEach((function(t,e){t.title=JSON.parse(t.title),t.city_ids=t.title})),t.ruleForm.free=e}))},getCityList:function(){var t=this;n["c"]().then((function(e){sessionStorage.setItem("cityList",JSON.stringify(e));var a=JSON.parse(sessionStorage.getItem("cityList"));t.cityList=a})).catch((function(e){t.$message.error(e.message)}))},change:function(t){return t.map((function(t){var e=[];t.city_ids.map((function(t){t.splice(0,1),e.push(t[0])})),t.city_id=e})),t},changeOne:function(t){var e=[];return t.map((function(t){t.splice(0,1),e.push(t[0])})),e},onsubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.loading=!0;var a={appoint:e.ruleForm.appoint,name:e.ruleForm.name,sort:e.ruleForm.sort,type:e.ruleForm.type};e.ruleForm.region.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var a=0;a<t.city_ids.length;a++)t.city_ids[a].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),a.shippingTemplatesRegionRequestList=e.ruleForm.region,a.shippingTemplatesRegionRequestList.forEach((function(t,e){})),e.ruleForm.appoint&&(e.ruleForm.free.forEach((function(t,e){t.title=t.city_ids.length>0?JSON.stringify(t.city_ids):JSON.stringify([[0,0]]);for(var a=0;a<t.city_ids.length;a++)t.city_ids[a].shift();t.cityId=t.city_ids.length>0?t.city_ids.join(","):"all"})),a.shippingTemplatesFreeRequestList=e.ruleForm.free,a.shippingTemplatesFreeRequestList.forEach((function(t,e){}))),0===e.type?n["n"](a).then((function(t){e.$message.success("操作成功"),e.handleClose(),e.$nextTick((function(){e.dialogVisible=!1})),setTimeout((function(){e.$emit("getList")}),600),e.loading=!1})):n["p"](a,{id:e.tempId}).then((function(t){e.$message.success("操作成功"),setTimeout((function(){e.$emit("getList"),e.handleClose()}),600),e.$nextTick((function(){e.dialogVisible=!1})),e.loading=!1}))}))},clear:function(){this.ruleForm.name="",this.ruleForm.sort=0}}},m=d,f=(a("e16d"),a("2877")),p=Object(f["a"])(m,r,i,!1,null,"44a816e5",null);e["a"]=p.exports}}]);