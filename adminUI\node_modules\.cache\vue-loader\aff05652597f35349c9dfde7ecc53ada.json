{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\edit.vue?vue&type=template&id=48a67e34&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\edit.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-form',{ref:\"ruleForm\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"用户编号：\"}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"disabled\":\"\"},model:{value:(_vm.ruleForm.id),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"id\", $$v)},expression:\"ruleForm.id\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"用户地址：\"}},[_c('el-input',{staticClass:\"selWidth\",model:{value:(_vm.ruleForm.addres),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"addres\", $$v)},expression:\"ruleForm.addres\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"用户备注：\"}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"type\":\"textarea\"},model:{value:(_vm.ruleForm.mark),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"mark\", $$v)},expression:\"ruleForm.mark\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"用户分组：\"}},[_c('el-select',{staticClass:\"selWidth\",attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\",\"filterable\":\"\"},model:{value:(_vm.ruleForm.groupId),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"groupId\", $$v)},expression:\"ruleForm.groupId\"}},_vm._l((_vm.groupList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.id,\"label\":item.groupName}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"用户标签：\"}},[_c('el-select',{staticClass:\"selWidth\",attrs:{\"placeholder\":\"请选择\",\"clearable\":\"\",\"filterable\":\"\",\"multiple\":\"\"},model:{value:(_vm.labelData),callback:function ($$v) {_vm.labelData=$$v},expression:\"labelData\"}},_vm._l((_vm.labelLists),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.id,\"label\":item.name}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"推广员\"}},[_c('el-radio-group',{model:{value:(_vm.ruleForm.isPromoter),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"isPromoter\", $$v)},expression:\"ruleForm.isPromoter\"}},[_c('el-radio',{attrs:{\"label\":true}},[_vm._v(\"开启\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":false}},[_vm._v(\"关闭\")])],1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"状态\"}},[_c('el-radio-group',{model:{value:(_vm.ruleForm.status),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"status\", $$v)},expression:\"ruleForm.status\"}},[_c('el-radio',{attrs:{\"label\":true}},[_vm._v(\"开启\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":false}},[_vm._v(\"关闭\")])],1)],1),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:user:update']),expression:\"['admin:user:update']\"}],attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitForm('ruleForm')}}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('el-button',{on:{\"click\":function($event){return _vm.resetForm('ruleForm')}}},[_vm._v(\"取消\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}