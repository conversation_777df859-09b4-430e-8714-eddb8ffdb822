{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue?vue&type=template&id=9d985202&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\keyword\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" :inline=\"true\">\n          <el-form-item label=\"回复类型：\">\n            <el-select v-model=\"tableFrom.type\" placeholder=\"请选择类型\" @change=\"seachList\" class=\"selWidth\" clearable>\n              <el-option label=\"文本消息\" value=\"text\"></el-option>\n              <el-option label=\"图片消息\" value=\"image\"></el-option>\n              <el-option label=\"图文消息\" value=\"news\"></el-option>\n              <el-option label=\"音频消息\" value=\"voice\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"关键字：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入关键字\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"seachList\" v-hasPermi=\"['admin:wechat:keywords:reply:info:keywords']\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n        <router-link :to=\"{path: '/appSetting/publicAccount/wxReply/keyword/save'}\">\n          <el-button size=\"small\" type=\"primary\" v-hasPermi=\"['admin:wechat:keywords:reply:save']\">添加关键字</el-button>\n        </router-link>\n      </div>\n    </div>  \n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"small\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"60\"\n      />\n      <el-table-column\n        prop=\"keywords\"\n        label=\"关键字\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        label=\"回复类型\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.type | keywordStatusFilter}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"status\"\n        label=\"是否显示\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:wechat:keywords:reply:status'])\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"显示\"\n            inactive-text=\"隐藏\"\n            @change=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"100\">\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{path: '/appSetting/publicAccount/wxReply/keyword/save/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\" \n            v-if=\"scope.row.keywords !=='subscribe' && scope.row.keywords !=='default'\" \n            v-hasPermi=\"['admin:wechat:keywords:reply:info']\">编辑</el-button>\n          </router-link>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:wechat:keywords:reply:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}