import request from '@/utils/request'

export function brandLstApi(params) {
  return request({
    url: '/admin/brand/list',
    method: 'GET',
    params
  })
}

export function updateBrandInfo(data) {
	return request({
		url: '/admin/brand/update',
		method: 'POST',
		data
	})
}
export function addBrand(data) {
	return request({
		url: '/admin/brand/add',
		method: 'POST',
		data
	})
}
export function batchUpdateBrandInfo(data) {
	return request({
		url: '/admin/brand/batchUpdate',
		method: 'POST',
		data
	})
}
export function industryLstApi(gid) {
	return request({
		url: '/admin/system/group/data/list?gid=' + gid,
		method: 'GET'
	})
}
export function productListApi(params) {
	return request({
		url: '/admin/store/product/list',
		method: 'GET',
		params
	})
}
export function fetchProductApi(form,url) {
	return request({
		url: '/admin/store/product/importProduct?form=' +  form + '&url=' + url,
		method: 'POST'
	})
}
export function batchPutOn(data){
	return request({
		url: '/admin/store/product/batch/putOnShell',
		method: 'POST',
		data
	})
}
export function batchPutoff(data) {
	return request({
		url: '/admin/store/product/batch/offShell',
		method: 'POST',
		data
	})
}
export function batchDelete(data) {
	return request({
		url: '/admin/store/product/batch/delete',
		method: 'POST',
		data
	});
}
export function updateProductInfo(data) {
	return request({
		url: '/admin/store/product/update',
		method: 'POST',
		data
	})
}
export function newProduct(data) {
	return request({
		url:'/admin/store/product/save',
		method: 'POST',
		data
	})
}