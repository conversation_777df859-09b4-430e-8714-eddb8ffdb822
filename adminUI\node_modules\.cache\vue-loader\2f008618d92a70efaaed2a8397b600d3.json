{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue?vue&type=template&id=5ab71352", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\level.vue", "mtime": 1754269254568}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-form',{ref:\"ruleForm\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.ruleForm,\"label-width\":\"100px\"}},[_c('el-form-item',[_c('el-alert',{attrs:{\"title\":_vm.$t('user.levelUpgrade.changeWarning'),\"type\":\"warning\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.center.userLevel'),\"label-width\":\"100px\"}},[_c('el-select',{attrs:{\"clearable\":\"\",\"placeholder\":_vm.$t('common.pleaseSelect')},on:{\"change\":_vm.currentSel},model:{value:(_vm.ruleForm.levelId),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"levelId\", $$v)},expression:\"ruleForm.levelId\"}},_vm._l((_vm.levelList),function(item){return _c('el-option',{key:item.grade,attrs:{\"label\":item.name,\"value\":item.id}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.name))]),_vm._v(\" \"),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.getUpgradeTypeText(item.upgradeType))+\"\\n          \"),(item.upgradeType === 1)?_c('span',[_vm._v(\" - Rp \"+_vm._s(item.upgradePrice))]):_vm._e()])])}),1)],1),_vm._v(\" \"),(_vm.grade =='' ? false : _vm.grade < _vm.levelInfo.gradeLevel)?_c('el-form-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.deductExperience'),\"label-width\":\"100px\"}},[_c('el-switch',{model:{value:(_vm.ruleForm.isSub),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"isSub\", $$v)},expression:\"ruleForm.isSub\"}})],1):_vm._e(),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{on:{\"click\":function($event){return _vm.resetForm('ruleForm')}}},[_vm._v(_vm._s(_vm.$t('common.cancel')))]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitForm('ruleForm')}}},[_vm._v(_vm._s(_vm.$t('common.confirm')))])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}