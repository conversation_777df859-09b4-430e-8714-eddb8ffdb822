{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\mpvue-calendar\\src\\mpvue-calendar.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\mpvue-calendar\\src\\mpvue-calendar.vue", "mtime": 1754138275631}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _readOnlyError(r) { throw new TypeError('\"' + r + '\" is read-only'); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport calendar, { defaultLunar, defaultGregorian, todayString, isBrowser } from './calendarinit.js';\nimport './icon.css';\nexport default {\n  props: {\n    multi: {\n      type: Boolean,\n      default: false\n    },\n    arrowLeft: {\n      type: String,\n      default: ''\n    },\n    arrowRight: {\n      type: String,\n      default: ''\n    },\n    clean: {\n      type: Boolean,\n      default: false\n    },\n    now: {\n      type: [String, Boolean],\n      default: true\n    },\n    range: {\n      type: Boolean,\n      default: false\n    },\n    completion: {\n      type: Boolean,\n      default: false\n    },\n    value: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    begin: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    end: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    zero: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    almanacs: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    tileContent: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    lunar: {\n      type: Boolean,\n      default: false\n    },\n    monFirst: {\n      type: Boolean,\n      default: false\n    },\n    weeks: {\n      type: Array,\n      default: function _default() {\n        return this.monFirst ? ['一', '二', '三', '四', '五', '六', '日'] : ['日', '一', '二', '三', '四', '五', '六'];\n      }\n    },\n    months: {\n      type: Array,\n      default: function _default() {\n        return ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];\n      }\n    },\n    events: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    weekSwitch: {\n      type: Boolean,\n      default: false\n    },\n    monthRange: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    responsive: {\n      type: Boolean,\n      default: false\n    },\n    rangeMonthFormat: {\n      type: String,\n      default: ''\n    }\n  },\n  data: function data() {\n    return {\n      years: [],\n      yearsShow: false,\n      year: 0,\n      month: 0,\n      monthPosition: 0,\n      day: 0,\n      days: [],\n      multiDays: [],\n      today: [],\n      handleMultiDay: [],\n      firstRender: true,\n      isIos: true,\n      showToday: {},\n      monthText: '',\n      festival: {\n        lunar: defaultLunar,\n        gregorian: defaultGregorian\n      },\n      rangeBegin: [],\n      rangeEnd: [],\n      multiDaysData: [],\n      monthsLoop: [],\n      itemWidth: 50,\n      unit: isBrowser ? 'px' : 'rpx',\n      positionH: isBrowser ? -24 : -40,\n      monthIndex: 0,\n      oversliding: false,\n      rangeBgHide: false,\n      monthRangeDays: [],\n      rangeOfMonths: [],\n      monthDays: [],\n      weekIndex: 0,\n      startWeekIndex: 0,\n      positionWeek: true,\n      isMonthRange: false\n    };\n  },\n  computed: {\n    itemStyle: function itemStyle() {\n      return {\n        width: \"\".concat(this.itemWidth, \"px\"),\n        height: \"\".concat(this.itemWidth, \"px\"),\n        fontSize: \"\".concat(this.itemWidth / 4, \"px\"),\n        lineHeight: this.lunar ? \"\".concat(this.itemWidth / 1.5, \"px\") : \"\".concat(this.itemWidth, \"px\")\n      };\n    }\n  },\n  watch: {\n    events: function events() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'events');\n    },\n    disabled: function disabled() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'disabled');\n    },\n    value: function value() {\n      if (this.isRendeRangeMode('_WATCHRENDERVALUE_')) return;\n      var value = this.value;\n      var year = value[0] || this.year;\n      var month = value[1] - 1 || this.month;\n      var day;\n      if (this.multi) {\n        if (this.isUserSelect) {\n          year = this.year;\n          month = this.month;\n          this.isUserSelect = false;\n        } else {\n          year = (value[value.length - 1] || [])[0] || this.year;\n          month = (value[value.length - 1] || [])[1] - 1 || this.month;\n        }\n      } else if (this.range) {\n        if (this.isUserSelect) {\n          year = this.year;\n          month = this.month;\n          this.isUserSelect = false;\n        } else {\n          if (value.length) {\n            year = value[0][0];\n            month = value[0][1] - 1;\n            day = value[0][2];\n          }\n          return this.render(year, month, '_WATCHRENDERVALUE_', [year, month, day]);\n        }\n      }\n      this.render(year, month, '_WATCHRENDERVALUE_');\n    },\n    tileContent: function tileContent() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'tileContent');\n    },\n    almanacs: function almanacs() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'almanacs');\n    },\n    monthRange: function monthRange() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'almanacs');\n    },\n    responsive: function responsive() {\n      if (this.responsive) this.addResponsiveListener();\n    },\n    weekSwitch: function weekSwitch() {\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month, '_WATCHRENDER_', 'almanacs');\n    }\n  },\n  created: function created() {\n    this.isMonthRange = !!this.monthRange.length;\n    var loopArray = this.months.concat();\n    loopArray.unshift(this.months[this.months.length - 1]);\n    loopArray.push(this.months[0]);\n    this.monthsLoop = loopArray;\n    this.monthsLoopCopy = this.monthsLoop.concat();\n  },\n  mounted: function mounted() {\n    var self = this;\n    this.resize();\n    if (!isBrowser) {\n      wx.getSystemInfo({\n        success: function success(res) {\n          self.isIos = (res.system.split(' ') || [])[0] === 'iOS';\n        }\n      });\n    } else if (this.responsive) {\n      this.addResponsiveListener();\n    }\n    this.oversliding = true;\n    this.initRender = true;\n    this.init();\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (isBrowser) {\n      window.removeEventListener('resize', this.resize);\n    }\n  },\n  methods: {\n    init: function init() {\n      var now = new Date();\n      this.year = now.getFullYear();\n      this.month = now.getMonth();\n      this.day = now.getDate();\n      this.monthIndex = this.month + 1;\n      if (this.value.length || this.multi) {\n        if (this.range) {\n          this.year = Number(this.value[0][0]);\n          this.month = this.value[0][1] - 1;\n          this.day = Number(this.value[0][2]);\n          var yearEnd = Number(this.value[1][0]);\n          var monthEnd = this.value[1][1] - 1;\n          var dayEnd = this.value[1][2];\n          this.rangeBegin = [this.year, this.month, this.day];\n          this.rangeEnd = [yearEnd, monthEnd, dayEnd];\n        } else if (this.multi) {\n          this.multiDays = this.value;\n          var handleMultiDay = this.handleMultiDay;\n          if (this.firstRender) {\n            this.firstRender = false;\n            var thatYear = (this.value[0] || [])[0];\n            var thatMonth = (this.value[0] || [])[1];\n            if (isFinite(thatYear) && isFinite(thatMonth)) {\n              this.month = parseInt(thatMonth, 10) - 1;\n              this.year = parseInt(thatYear, 10);\n            }\n          } else if (this.handleMultiDay.length) {\n            this.month = parseInt(handleMultiDay[handleMultiDay.length - 1][1], 10) - 1;\n            this.year = parseInt(handleMultiDay[handleMultiDay.length - 1][0], 10);\n            this.handleMultiDay = [];\n          } else {\n            this.month = parseInt(this.value[this.value.length - 1][1], 10) - 1;\n            this.year = parseInt(this.value[this.value.length - 1][0], 10);\n          }\n          this.day = parseInt((this.value[0] || [])[2], 10);\n        } else {\n          this.year = parseInt(this.value[0], 10);\n          this.month = parseInt(this.value[1], 10) - 1;\n          this.day = parseInt(this.value[2], 10);\n        }\n      }\n      this.updateHeadMonth();\n      if (this.isRendeRangeMode()) return;\n      this.render(this.year, this.month);\n    },\n    renderOption: function renderOption(year, month, i, playload) {\n      var weekSwitch = this.monthRange.length ? false : this.weekSwitch;\n      var selectSplit = this.value;\n      var isMonthModeCurrentMonth = !weekSwitch && !playload;\n      var disabledFilter = function disabledFilter(disabled) {\n        return disabled.find(function (v) {\n          var dayArr = v.split('-');\n          return year === Number(dayArr[0]) && month === dayArr[1] - 1 && i === Number(dayArr[2]);\n        });\n      };\n      if (this.range) {\n        var lastDay = new Date(year, month + 1, 0).getDate() === i ? {\n          lastDay: true\n        } : null;\n        var options = Object.assign({\n          day: i\n        }, this.getLunarInfo(year, month + 1, i), this.getEvents(year, month + 1, i), lastDay);\n        var date = options.date,\n          day = options.day;\n        var copyRangeBegin = this.rangeBegin.concat();\n        var copyRangeEnd = this.rangeEnd.concat();\n        copyRangeBegin[1] += 1;\n        copyRangeEnd[1] += 1;\n        if (weekSwitch || isMonthModeCurrentMonth) {\n          copyRangeEnd.join('-') === date && (options.rangeClassName = 'mc-range-end');\n          copyRangeBegin.join('-') === date && (options.rangeClassName = 'mc-range-begin');\n        }\n        if (year === copyRangeEnd[0] && month + 1 === copyRangeEnd[1] && day === copyRangeEnd[2] - 1) {\n          options.rangeClassName = options.rangeClassName ? ['mc-range-begin', 'mc-range-second-to-last'] : 'mc-range-second-to-last';\n        }\n        if (this.rangeBegin.length) {\n          var beginTime = +new Date(this.rangeBegin[0], this.rangeBegin[1], this.rangeBegin[2]);\n          var endTime = +new Date(this.rangeEnd[0], this.rangeEnd[1], this.rangeEnd[2]);\n          var stepTime = +new Date(year, month, i);\n          if (beginTime <= stepTime && endTime >= stepTime) {\n            options.selected = true;\n          }\n        }\n        if (this.begin.length) {\n          var _beginTime = +new Date(parseInt(this.begin[0], 10), parseInt(this.begin[1], 10) - 1, parseInt(this.begin[2], 10));\n          if (_beginTime > +new Date(year, month, i)) {\n            options.disabled = true;\n          }\n        }\n        if (this.end.length) {\n          var _endTime = Number(new Date(parseInt(this.end[0], 10), parseInt(this.end[1], 10) - 1, parseInt(this.end[2], 10)));\n          if (_endTime < Number(new Date(year, month, i))) {\n            options.disabled = true;\n          }\n        }\n        if (playload && !weekSwitch) {\n          options.disabled = true;\n        } else if (this.disabled.length && disabledFilter(this.disabled)) {\n          options.disabled = true;\n        }\n        var monthFirstDay = \"\".concat(year, \"-\").concat(month + 1, \"-1\");\n        var monthLastDay = \"\".concat(year, \"-\").concat(month + 1, \"-\").concat(new Date(year, month + 1, 0).getDate());\n        monthFirstDay === date && options.selected && !options.rangeClassName && (options.rangeClassName = 'mc-range-month-first');\n        monthLastDay === date && options.selected && !options.rangeClassName && (options.rangeClassName = 'mc-range-month-last');\n        this.isCurrentMonthToday(options) && (options.isToday = true);\n        !weekSwitch && playload && (options.selected = false);\n        return options;\n      }\n      if (this.multi) {\n        var _options;\n        if (this.value.find(function (v) {\n          return year === v[0] && month === v[1] - 1 && i === v[2];\n        })) {\n          _options = Object.assign({\n            day: i,\n            selected: true\n          }, this.getLunarInfo(year, month + 1, i), this.getEvents(year, month + 1, i));\n        } else {\n          _options = Object.assign({\n            day: i,\n            selected: false\n          }, this.getLunarInfo(year, month + 1, i), this.getEvents(year, month + 1, i));\n          if (this.begin.length) {\n            var _beginTime2 = +new Date(parseInt(this.begin[0], 10), parseInt(this.begin[1], 10) - 1, parseInt(this.begin[2], 10));\n            if (_beginTime2 > +new Date(year, month, i)) {\n              _options.disabled = true;\n            }\n          }\n          if (this.end.length) {\n            var _endTime2 = +new Date(parseInt(this.end[0], 10), parseInt(this.end[1], 10) - 1, parseInt(this.end[2], 10));\n            if (_endTime2 < +new Date(year, month, i)) {\n              _options.disabled = true;\n            }\n          }\n          if (this.disabled.length && disabledFilter(this.disabled)) {\n            _options.disabled = true;\n          }\n        }\n        this.isCurrentMonthToday(_options) && (_options.isToday = true);\n        if (playload && !weekSwitch) {\n          _options.disabled = true;\n          _options.selected = false;\n        }\n        return _options;\n      } else {\n        var _options2 = {};\n        var monthHuman = month + 1;\n        if (selectSplit[0] === year && selectSplit[1] === monthHuman && selectSplit[2] === i) {\n          Object.assign(_options2, {\n            day: i,\n            selected: true\n          }, this.getLunarInfo(year, monthHuman, i), this.getEvents(year, monthHuman, i));\n        } else {\n          Object.assign(_options2, {\n            day: i,\n            selected: false\n          }, this.getLunarInfo(year, monthHuman, i), this.getEvents(year, monthHuman, i));\n          if (this.begin.length) {\n            var _beginTime3 = +new Date(parseInt(this.begin[0], 10), parseInt(this.begin[1], 10) - 1, parseInt(this.begin[2], 10));\n            if (_beginTime3 > Number(new Date(year, month, i))) {\n              _options2.disabled = true;\n            }\n          }\n          if (this.end.length) {\n            var _endTime3 = +new Date(parseInt(this.end[0], 10), parseInt(this.end[1], 10) - 1, parseInt(this.end[2], 10));\n            if (_endTime3 < +new Date(year, month, i)) {\n              _options2.disabled = true;\n            }\n          }\n          if (this.disabled.length && disabledFilter(this.disabled)) {\n            _options2.disabled = true;\n          }\n        }\n        this.isCurrentMonthToday(_options2) && (_options2.isToday = true);\n        if (playload && !weekSwitch) {\n          _options2.disabled = true;\n          _options2.selected = false;\n        }\n        return _options2;\n      }\n    },\n    isCurrentMonthToday: function isCurrentMonthToday(options) {\n      var isToday = todayString === options.date;\n      if (!isToday) return false;\n      return this.weekSwitch ? isToday : Number(todayString.split('-')[1]) === this.month + 1;\n    },\n    watchRender: function watchRender(type) {\n      var _this = this;\n      var weekSwitch = this.weekSwitch;\n      var daysDeepCopy = JSON.parse(JSON.stringify(this.monthDays));\n      if (type === 'events') {\n        var events = this.events || {};\n        Object.keys(events).forEach(function (value) {\n          daysDeepCopy.some(function (v) {\n            return v.some(function (vv) {\n              if (vv.date === value) {\n                vv.eventName = events[value];\n                return true;\n              }\n            });\n          });\n        });\n      } else if (type === 'disabled') {\n        var disabled = this.disabled || [];\n        disabled.forEach(function (value) {\n          daysDeepCopy.some(function (v) {\n            return v.some(function (vv) {\n              if (vv.date === value) {\n                vv.disabled = true;\n                return true;\n              }\n            });\n          });\n        });\n      } else if (type === 'almanacs') {\n        var almanacs = this.almanacs || {};\n        Object.keys(almanacs).forEach(function (value) {\n          daysDeepCopy.some(function (v) {\n            return v.some(function (vv) {\n              if (vv.date.slice(5, 20) === value) {\n                var _vv$date$split = vv.date.split('-'),\n                  _vv$date$split2 = _slicedToArray(_vv$date$split, 3),\n                  y = _vv$date$split2[0],\n                  m = _vv$date$split2[1],\n                  d = _vv$date$split2[2];\n                Object.assign(vv, _this.getLunarInfo(y, m, d));\n                return true;\n              }\n            });\n          });\n        });\n      } else if (type === 'tileContent') {\n        var tileContent = this.tileContent || [];\n        tileContent.forEach(function (value) {\n          daysDeepCopy.some(function (v) {\n            return v.some(function (vv) {\n              if (vv.date === value.date) {\n                vv.className = value.className;\n                vv.content = value.content;\n                return true;\n              }\n            });\n          });\n        });\n      }\n      this.monthDays = daysDeepCopy;\n      if (weekSwitch) {\n        this.days = [daysDeepCopy[this.weekIndex]];\n        this.monthRangeDays = [this.days];\n      } else {\n        this.days = daysDeepCopy;\n        this.monthRangeDays = [this.days];\n      }\n    },\n    render: function render(y, m, renderer, payload) {\n      var _this2 = this;\n      var weekSwitch = this.weekSwitch;\n      var isCustomRender = renderer === 'CUSTOMRENDER';\n      var isWatchRenderValue = renderer === '_WATCHRENDERVALUE_';\n      this.year = y;\n      this.month = m;\n      if (renderer === '_WATCHRENDER_') return this.watchRender(payload);\n      if (this.range && isWatchRenderValue) {\n        if (!Array.isArray((this.value || [])[0])) {\n          this.rangeBegin = [];\n          this.rangeEnd = [];\n        } else {\n          this.rangeBegin = [this.value[0][0], this.value[0][1] - 1, this.value[0][2]];\n          this.rangeEnd = [this.value[1][0], this.value[1][1] - 1, this.value[1][2]];\n        }\n      }\n      if (isWatchRenderValue && weekSwitch) {\n        this.positionWeek = true;\n      }\n      if (isCustomRender) {\n        this.year = y;\n        this.month = m;\n        this.positionWeek = true;\n        if (weekSwitch && !payload) {\n          this.startWeekIndex = 0;\n          this.weekIndex = 0;\n        }\n        this.updateHeadMonth();\n      }\n      var firstDayOfMonth = new Date(y, m, 1).getDay();\n      var lastDateOfMonth = new Date(y, m + 1, 0).getDate();\n      var lastDayOfLastMonth = new Date(y, m, 0).getDate();\n      this.year = y;\n      var i = 1;\n      var line = 0;\n      var nextMonthPushDays = 1;\n      var temp = [];\n      for (i; i <= lastDateOfMonth; i++) {\n        var day = new Date(y, m, i).getDay();\n        var k = void 0;\n        if (day === 0) {\n          temp[line] = [];\n        } else if (i === 1) {\n          temp[line] = [];\n          k = lastDayOfLastMonth - firstDayOfMonth + 1;\n          for (var j = 0; j < firstDayOfMonth; j++) {\n            //generate prev month surplus option\n            temp[line].push(Object.assign(this.renderOption(this.computedPrevYear(y, m), this.computedPrevMonth(false, m), k, 'prevMonth'), {\n              lastMonth: true\n            }));\n            k++;\n          }\n        }\n        temp[line].push(this.renderOption(y, m, i)); //generate current month option\n\n        if (day === 6 && i < lastDateOfMonth) {\n          line++;\n        } else if (i === lastDateOfMonth) {\n          var nextDay = 1;\n          var lastDateOfMonthLength = this.monFirst ? 7 : 6;\n          for (var d = day; d < lastDateOfMonthLength; d++) {\n            //generate next month surplus option\n            temp[line].push(Object.assign(this.renderOption(this.computedNextYear(y, m), this.computedNextMonth(false, m), nextDay, 'nextMonth'), {\n              nextMonth: true\n            }));\n            nextDay++;\n          }\n          nextMonthPushDays = nextDay;\n        }\n      }\n      var completion = this.completion;\n      if (this.monFirst) {\n        if (!firstDayOfMonth) {\n          var lastMonthDay = lastDayOfLastMonth;\n          var LastMonthItems = [];\n          for (var _d = 1; _d <= 7; _d++) {\n            LastMonthItems.unshift(Object.assign(this.renderOption(this.computedPrevYear(y, m), this.computedPrevMonth(false, m), lastMonthDay, 'prevMonth'), {\n              lastMonth: true\n            }));\n            lastMonthDay--;\n          }\n          temp.unshift(LastMonthItems);\n        }\n        temp.forEach(function (item, index) {\n          if (!index) {\n            return item.splice(0, 1);\n          }\n          temp[index - 1].length < 7 && temp[index - 1].push(item.splice(0, 1)[0]);\n        });\n        if (this.isMonthRange && temp[temp.length - 1][0].nextMonth) {\n          temp.splice(temp.length - 1, 1); //if the first day of last line is nextMonth, delete this line\n        }\n        if (!completion && !weekSwitch) {\n          var lastIndex = temp.length - 1;\n          var secondToLastIndex = lastIndex - 1;\n          var differentMonth = temp[lastIndex][0].date.split('-')[1] !== temp[secondToLastIndex][6].date.split('-')[1];\n          differentMonth && temp.splice(lastIndex, 1);\n        }\n      }\n      if (completion && !weekSwitch && temp.length <= 5 && nextMonthPushDays > 0) {\n        for (var completionIndex = temp.length; completionIndex <= 5; completionIndex++) {\n          temp[completionIndex] = [];\n          var start = nextMonthPushDays + (completionIndex - line - 1) * 7;\n          for (var _d2 = start; _d2 <= start + 6; _d2++) {\n            temp[completionIndex].push(Object.assign({\n              day: _d2,\n              disabled: true,\n              nextMonth: true\n            }, this.getLunarInfo(this.computedNextYear(y, m), this.computedNextMonth(true, m), _d2), this.getEvents(this.computedNextYear(y, m), this.computedNextMonth(true, m), _d2)));\n          }\n        }\n      }\n      if (this.tileContent.length) {\n        temp.forEach(function (item, index) {\n          item.forEach(function (v) {\n            var contents = _this2.tileContent.find(function (val) {\n              return val.date === v.date;\n            });\n            if (contents) {\n              var _ref = contents || {},\n                className = _ref.className,\n                content = _ref.content;\n              v.className = className;\n              v.content = content;\n            }\n          });\n        });\n      }\n      if (weekSwitch) {\n        var tempLength = temp.length;\n        var lastLineMonth = temp[tempLength - 1][0].date.split('-')[1]; // last line month\n        var secondLastMonth = temp[tempLength - 2][0].date.split('-')[1]; // second-to-last line month\n        lastLineMonth !== secondLastMonth && temp.splice(tempLength - 1, 1);\n      }\n      this.monthDays = temp;\n      if (weekSwitch && !this.isMonthRange) {\n        if (this.positionWeek) {\n          var payloadDay = '';\n          var searchIndex = true;\n          if (Array.isArray(payload)) {\n            //range\n            payloadDay = [payload[0], payload[1] + 1, payload[2]].join('-');\n          } else if (this.multi || isWatchRenderValue) {\n            if (this.thisTimeSelect) {\n              payloadDay = this.thisTimeSelect;\n            } else {\n              payloadDay = this.multi ? this.value[this.value.length - 1].join('-') : this.value.join('-');\n            }\n          }\n          if (payload === 'SETTODAY') {\n            payloadDay = todayString;\n          } else if (isCustomRender) {\n            if (typeof payload === 'string') {\n              payloadDay = [y, Number(m) + 1, payload].join('-');\n              searchIndex = true;\n            } else if (typeof payload === 'number') {\n              var setIndex = payload > temp.length ? temp.length - 1 : payload;\n              this.startWeekIndex = setIndex;\n              this.weekIndex = setIndex;\n              this.positionWeek = false;\n              searchIndex = false;\n            }\n          }\n          var positionDay = payloadDay || todayString;\n          if (searchIndex) {\n            temp.some(function (v, index) {\n              var isWeekNow = v.find(function (vv) {\n                return vv.date === positionDay;\n              });\n              if (isWeekNow) {\n                _this2.startWeekIndex = index;\n                _this2.weekIndex = index;\n                return true;\n              }\n            });\n          }\n          this.positionWeek = false;\n        }\n        this.days = [temp[this.startWeekIndex]];\n        if (this.initRender) {\n          this.setMonthRangeofWeekSwitch();\n          this.initRender = false;\n        }\n      } else {\n        this.days = temp;\n      }\n      var todayText = '今';\n      if (typeof this.now === 'boolean' && !this.now) {\n        this.showToday = {\n          show: false\n        };\n      } else if (typeof this.now === 'string') {\n        this.showToday = {\n          show: true,\n          text: this.now || todayText\n        };\n      } else {\n        this.showToday = {\n          show: true,\n          text: todayText\n        };\n      }\n      this.monthRangeDays = [this.days];\n      isWatchRenderValue && this.updateHeadMonth();\n      return this.days;\n    },\n    rendeRange: function rendeRange(renderer) {\n      var _this3 = this;\n      var range = [];\n      var self = this;\n      var monthRange = this.monthRange;\n      function formatDateText(fYear, fMonth) {\n        var reg = /([y]+)(.*?)([M]+)(.*?)$/i;\n        var rangeMonthFormat = self.rangeMonthFormat || 'yyyy-MM';\n        reg.exec(rangeMonthFormat);\n        return String(fYear).substring(4 - RegExp.$1.length) + RegExp.$2 + String(fMonth).substring(2 - RegExp.$3.length) + RegExp.$4;\n      }\n      if (monthRange[0] === monthRange[1]) {\n        var _monthRange$0$split = monthRange[0].split('-'),\n          _monthRange$0$split2 = _slicedToArray(_monthRange$0$split, 2),\n          y = _monthRange$0$split2[0],\n          m = _monthRange$0$split2[1];\n        range.push([Number(y), Number(m), formatDateText(y, m)]);\n      } else {\n        var monthRangeOfStart = monthRange[0].split('-');\n        var monthRangeOfEnd = monthRange[1].split('-');\n        var startYear = +monthRangeOfStart[0];\n        var startMonth = +monthRangeOfStart[1];\n        var endYear = +monthRangeOfEnd[0];\n        var endtMonth = +monthRangeOfEnd[1] > 12 ? 12 : +monthRangeOfEnd[1];\n        while (startYear < endYear || startMonth <= endtMonth) {\n          range.push([startYear, startMonth, formatDateText(startYear, startMonth)]);\n          if (startMonth === 12 && startYear !== endYear) {\n            startYear++;\n            startMonth = 0;\n          }\n          startMonth++;\n        }\n      }\n      this.rangeOfMonths = range;\n      var monthsRange = range.map(function (item) {\n        var _item = _slicedToArray(item, 2),\n          yearParam = _item[0],\n          monthParam = _item[1];\n        return _this3.render(yearParam, monthParam - 1, renderer);\n      });\n      this.monthRangeDays = monthsRange;\n    },\n    isRendeRangeMode: function isRendeRangeMode(renderer) {\n      this.isMonthRange = !!this.monthRange.length;\n      if (this.isMonthRange) {\n        this.rendeRange(renderer);\n        return true;\n      }\n    },\n    renderer: function renderer(y, m, w) {\n      var renderY = y || this.year;\n      var renderM = typeof parseInt(m, 10) === 'number' ? m - 1 : this.month;\n      this.initRender = true;\n      this.render(renderY, renderM, 'CUSTOMRENDER', w);\n      !this.weekSwitch && (this.monthsLoop = this.monthsLoopCopy.concat());\n    },\n    computedPrevYear: function computedPrevYear(year, month) {\n      var value = year;\n      if (month - 1 < 0) {\n        value--;\n      }\n      return value;\n    },\n    computedPrevMonth: function computedPrevMonth(isString, month) {\n      var value = month;\n      if (month - 1 < 0) {\n        value = 11;\n      } else {\n        value--;\n      }\n      if (isString) {\n        return value + 1;\n      }\n      return value;\n    },\n    computedNextYear: function computedNextYear(year, month) {\n      var value = year;\n      if (month + 1 > 11) {\n        value++;\n      }\n      return value;\n    },\n    computedNextMonth: function computedNextMonth(isString, month) {\n      var value = month;\n      if (month + 1 > 11) {\n        value = 0;\n      } else {\n        value++;\n      }\n      if (isString) {\n        return value + 1;\n      }\n      return value;\n    },\n    getLunarInfo: function getLunarInfo(y, m, d) {\n      var lunarInfo = calendar.solar2lunar(y, m, d);\n      var _ref2 = lunarInfo || {},\n        Term = _ref2.Term,\n        lMonth = _ref2.lMonth,\n        lDay = _ref2.lDay,\n        lYear = _ref2.lYear;\n      var yearEve = '';\n      if (lMonth === 12 && lDay === calendar.monthDays(lYear, 12)) {\n        yearEve = '除夕';\n      }\n      var lunarValue = lunarInfo.IDayCn;\n      var isLunarFestival = false;\n      var isGregorianFestival = false;\n      if (this.festival.lunar[\"\".concat(lunarInfo.lMonth, \"-\").concat(lunarInfo.lDay)]) {\n        lunarValue = this.festival.lunar[\"\".concat(lunarInfo.lMonth, \"-\").concat(lunarInfo.lDay)];\n        isLunarFestival = true;\n      } else if (this.festival.gregorian[\"\".concat(m, \"-\").concat(d)]) {\n        lunarValue = this.festival.gregorian[\"\".concat(m, \"-\").concat(d)];\n        isGregorianFestival = true;\n      }\n      var lunarInfoObj = {\n        date: \"\".concat(y, \"-\").concat(m, \"-\").concat(d),\n        lunar: yearEve || Term || lunarValue,\n        isLunarFestival: isLunarFestival,\n        isGregorianFestival: isGregorianFestival,\n        isTerm: !!yearEve || lunarInfo.isTerm\n      };\n      if (Object.keys(this.almanacs).length) {\n        Object.assign(lunarInfoObj, {\n          almanac: this.almanacs[\"\".concat(m, \"-\").concat(d)] || '',\n          isAlmanac: !!this.almanacs[\"\".concat(m, \"-\").concat(d)]\n        });\n      }\n      return lunarInfoObj;\n    },\n    getEvents: function getEvents(y, m, d) {\n      if (!Object.keys(this.events).length) return;\n      var eventName = this.events[\"\".concat(y, \"-\").concat(m, \"-\").concat(d)];\n      var data = {};\n      if (eventName) {\n        data.eventName = eventName;\n      }\n      return data;\n    },\n    prev: function prev(e) {\n      var _this4 = this;\n      e && e.stopPropagation();\n      if (this.isMonthRange) return;\n      var weekSwitch = this.weekSwitch;\n      var changeMonth = function changeMonth(changed) {\n        if (_this4.monthIndex === 1) {\n          _this4.oversliding = false;\n          _this4.month = 11;\n          _this4.year = parseInt(_this4.year, 10) - 1;\n          _this4.monthIndex = _this4.monthIndex - 1;\n        } else if (_this4.monthIndex === 0) {\n          _this4.oversliding = true;\n          _this4.monthIndex = 12;\n          setTimeout(function () {\n            return _this4.prev(e);\n          }, 50);\n          return _this4.updateHeadMonth('custom');\n        } else if (_this4.monthIndex === 13) {\n          _this4.month = 11;\n          _this4.year = parseInt(_this4.year, 10) - 1;\n          _this4.monthIndex = _this4.monthIndex - 1;\n        } else {\n          _this4.oversliding = false;\n          _this4.month = parseInt(_this4.month, 10) - 1;\n          _this4.monthIndex = _this4.monthIndex - 1;\n        }\n        _this4.updateHeadMonth('custom');\n        _this4.render(_this4.year, _this4.month);\n        typeof changed === 'function' && changed();\n        var weekIndex = weekSwitch ? _this4.weekIndex : undefined;\n        _this4.$emit('prev', _this4.year, _this4.month + 1, weekIndex);\n      };\n      if (!this.weekSwitch) return changeMonth();\n      var changeWeek = function changeWeek() {\n        _this4.weekIndex = _this4.weekIndex - 1;\n        _this4.days = [_this4.monthDays[_this4.weekIndex]];\n        _this4.monthRangeDays = [_this4.days];\n        _this4.setMonthRangeofWeekSwitch();\n        _this4.$emit('prev', _this4.year, _this4.month + 1, _this4.weekIndex);\n      };\n      var currentWeek = (this.days[0] || [])[0] || {};\n      if (currentWeek.lastMonth || currentWeek.day === 1) {\n        var monthChenged = function monthChenged() {\n          var lastMonthLength = _this4.monthDays.length;\n          var startWeekIndex = currentWeek.lastMonth ? lastMonthLength - 1 : lastMonthLength;\n          _this4.startWeekIndex = startWeekIndex;\n          _this4.weekIndex = startWeekIndex;\n          changeWeek();\n        };\n        changeMonth(monthChenged);\n      } else {\n        changeWeek();\n      }\n    },\n    next: function next(e) {\n      var _this5 = this;\n      e && e.stopPropagation();\n      if (this.isMonthRange) return;\n      var weekSwitch = this.weekSwitch;\n      var changeMonth = function changeMonth() {\n        if (_this5.monthIndex === 12) {\n          _this5.oversliding = false;\n          _this5.month = 0;\n          _this5.year = parseInt(_this5.year, 10) + 1;\n          _this5.monthIndex = _this5.monthIndex + 1;\n        } else if (_this5.monthIndex === 0 && _this5.month === 11) {\n          _this5.oversliding = false;\n          _this5.month = 0;\n          _this5.year = parseInt(_this5.year, 10) + 1;\n          _this5.monthIndex = _this5.monthIndex + 1;\n        } else if (_this5.monthIndex === 13) {\n          _this5.oversliding = true;\n          _this5.monthIndex = 1;\n          setTimeout(function () {\n            return _this5.next(e);\n          }, 50);\n          return _this5.updateHeadMonth('custom');\n        } else {\n          _this5.oversliding = false;\n          _this5.month = parseInt(_this5.month, 10) + 1;\n          _this5.monthIndex = _this5.monthIndex + 1;\n        }\n        _this5.updateHeadMonth('custom');\n        _this5.render(_this5.year, _this5.month);\n        var weekIndex = weekSwitch ? _this5.weekIndex : undefined;\n        _this5.$emit('next', _this5.year, _this5.month + 1, weekIndex);\n      };\n      if (!this.weekSwitch) return changeMonth();\n      var changeWeek = function changeWeek() {\n        _this5.weekIndex = _this5.weekIndex + 1;\n        _this5.days = [_this5.monthDays[_this5.weekIndex]];\n        _this5.monthRangeDays = [_this5.days];\n        _this5.setMonthRangeofWeekSwitch();\n        _this5.$emit('next', _this5.year, _this5.month + 1, _this5.weekIndex);\n      };\n      var currentWeek = (this.days[0] || [])[6] || {};\n      if (currentWeek.nextMonth || currentWeek.day === new Date(this.year, this.month + 1, 0).getDate()) {\n        var startWeekIndex = currentWeek.nextMonth ? 1 : 0;\n        this.startWeekIndex = startWeekIndex;\n        this.weekIndex = startWeekIndex;\n        changeMonth();\n      } else {\n        changeWeek();\n      }\n    },\n    select: function select(k1, k2, data, e, monthIndex) {\n      var _this6 = this;\n      e && e.stopPropagation();\n      var weekSwitch = this.weekSwitch;\n      if (data.lastMonth && !weekSwitch) {\n        return this.prev(e);\n      }\n      if (data.nextMonth && !weekSwitch) {\n        return this.next(e);\n      }\n      if (data.disabled) return;\n      (data || {}).event = (this.events || {})[data.date] || '';\n      var selected = data.selected,\n        day = data.day,\n        date = data.date;\n      var selectedDates = date.split('-');\n      var selectYear = Number(selectedDates[0]);\n      var selectMonth = selectedDates[1] - 1;\n      var selectMonthHuman = Number(selectedDates[1]);\n      var selectDay = Number(selectedDates[2]);\n      if (this.range) {\n        this.isUserSelect = true;\n        var rangeDate = function rangeDate(dateArray) {\n          return dateArray.map(function (v, k) {\n            var value = k === 1 ? v + 1 : v;\n            return _this6.zero ? _this6.zeroPad(value) : value;\n          });\n        };\n        if (this.rangeBegin.length === 0 || this.rangeEndTemp !== 0) {\n          this.rangeBegin = [selectYear, selectMonth, selectDay];\n          this.rangeBeginTemp = this.rangeBegin;\n          this.rangeEnd = [selectYear, selectMonth, selectDay];\n          this.thisTimeSelect = this.rangeEnd;\n          this.rangeEndTemp = 0;\n          this.$emit('select', rangeDate(this.rangeBegin), undefined);\n        } else {\n          this.rangeEnd = [selectYear, selectMonth, selectDay];\n          this.thisTimeSelect = [selectYear, selectMonth, selectDay];\n          if (this.rangeBegin.join('-') === this.rangeEnd.join('-')) {\n            return this.rangeEndTemp = 0;\n          }\n          this.rangeEndTemp = 1;\n          if (+new Date(this.rangeEnd[0], this.rangeEnd[1], this.rangeEnd[2]) < +new Date(this.rangeBegin[0], this.rangeBegin[1], this.rangeBegin[2])) {\n            this.rangeBegin = this.rangeEnd;\n            this.rangeEnd = this.rangeBeginTemp;\n          }\n          var begin = rangeDate(this.rangeBegin);\n          var end = rangeDate(this.rangeEnd);\n          this.value.splice(0, 1, begin);\n          this.value.splice(1, 1, end);\n          this.$emit('select', begin, end);\n        }\n        this.rangeBgHide = !this.rangeEndTemp || this.rangeBegin.join('-') === this.rangeEnd.join('-');\n        this.positionWeek = true;\n        if (this.isMonthRange) {\n          this.rendeRange();\n        } else {\n          this.render(this.year, this.month, undefined, this.thisTimeSelect);\n        }\n      } else if (this.multi) {\n        this.isUserSelect = true;\n        var filterDayIndex = this.value.findIndex(function (v) {\n          return v.join('-') === date;\n        });\n        if (~filterDayIndex) {\n          this.handleMultiDay = this.value.splice(filterDayIndex, 1);\n        } else {\n          this.value.push([Number(Number(selectedDates[0])), Number(selectedDates[1]), day]);\n        }\n        this.monthRangeDays[monthIndex][k1][k2].selected = !selected;\n        this.multiDaysData = this.value.map(function (dateItem) {\n          var _dateItem = _slicedToArray(dateItem, 3),\n            year = _dateItem[0],\n            month = _dateItem[1],\n            d = _dateItem[2];\n          return Object.assign({\n            day: d,\n            selected: true\n          }, _this6.getLunarInfo(year, month, d), _this6.getEvents(year, month, d));\n        });\n        this.thisTimeSelect = date;\n        this.$emit('select', this.value, this.multiDaysData);\n      } else {\n        var valueClone = this.value.splice();\n        var currentSelected = valueClone.join('-');\n        this.monthRangeDays.some(function (value) {\n          return value.some(function (v) {\n            return !!v.find(function (vv) {\n              if (vv.date === currentSelected) {\n                vv.selected = false;\n                return true;\n              }\n            });\n          });\n        });\n        this.monthRangeDays[monthIndex][k1][k2].selected = true;\n        this.day = day;\n        var selectDate = [selectYear, selectMonthHuman, selectDay];\n        this.value[0] = selectYear;\n        this.value[1] = selectMonthHuman;\n        this.value[2] = selectDay;\n        this.today = [k1, k2];\n        this.$emit('select', selectDate, data);\n      }\n    },\n    changeYear: function changeYear() {\n      if (this.yearsShow) {\n        this.yearsShow = false;\n        return false;\n      }\n      this.yearsShow = true;\n      this.years = [];\n      for (var i = this.year - 5; i < this.year + 7; i++) {\n        this.years.push(i);\n      }\n    },\n    changeMonth: function changeMonth(value) {\n      this.oversliding && (this.oversliding = false);\n      this.yearsShow = false;\n      this.month = value;\n      this.render(this.year, this.month, 'CUSTOMRENDER', 0);\n      this.updateHeadMonth();\n      this.weekSwitch && this.setMonthRangeofWeekSwitch();\n      this.$emit('selectMonth', this.month + 1, this.year);\n    },\n    selectYear: function selectYear(value) {\n      this.yearsShow = false;\n      this.year = value;\n      this.render(this.year, this.month);\n      this.$emit('selectYear', value);\n    },\n    setToday: function setToday() {\n      var now = new Date();\n      this.year = now.getFullYear();\n      this.month = now.getMonth();\n      this.day = now.getDate();\n      this.positionWeek = true;\n      this.render(this.year, this.month, undefined, 'SETTODAY');\n      this.updateHeadMonth();\n    },\n    setMonthRangeofWeekSwitch: function setMonthRangeofWeekSwitch() {\n      var _this7 = this;\n      this.monthsLoop = this.monthsLoopCopy.concat();\n      this.days[0].reduce(function (prev, current) {\n        if (!prev) return;\n        var prveDate = ((prev || {}).date || '').split('-');\n        var prevYear = prveDate[0];\n        var prevMonth = prveDate[1];\n        var currentMonth = ((current || {}).date || '').split('-')[1];\n        if (prevMonth === currentMonth) {\n          return current;\n        }\n        var prevMonthText = _this7.months[prevMonth - 1];\n        var currentMonthText = _this7.months[currentMonth - 1];\n        _this7.monthsLoop[_this7.monthIndex] = \"\".concat(prevMonthText, \"~\").concat(currentMonthText);\n      });\n    },\n    dateInfo: function dateInfo(y, m, d) {\n      return calendar.solar2lunar(y, m, d);\n    },\n    zeroPad: function zeroPad(n) {\n      return String(n < 10 ? \"0\".concat(n) : n);\n    },\n    updateHeadMonth: function updateHeadMonth(type) {\n      if (!type) this.monthIndex = this.month + 1;\n      this.monthPosition = this.monthIndex * this.positionH;\n      this.monthText = this.months[this.month];\n    },\n    addResponsiveListener: function addResponsiveListener() {\n      window.addEventListener('resize', this.resize);\n    },\n    resize: function resize() {\n      var calendarRef = this.$refs.calendar;\n      this.itemWidth = (calendarRef.clientWidth / 7 - 4).toFixed(5);\n    }\n  }\n};", null]}