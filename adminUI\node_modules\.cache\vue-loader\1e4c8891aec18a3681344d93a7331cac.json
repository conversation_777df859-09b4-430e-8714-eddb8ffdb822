{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue?vue&type=template&id=368e8bb7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <div class=\"header clearfix\">\n    <div class=\"container\">\n      <el-form inline size=\"small\">\n        <el-form-item label=\"优惠卷名称：\">\n          <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入优惠券名称\" class=\"selWidth\" size=\"small\">\n            <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n          </el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n  </div>\n  <el-table\n    ref=\"table\"\n    v-loading=\"listLoading\"\n    :data=\"tableData.data\"\n    style=\"width: 100%\"\n    size=\"mini\"\n    max-height=\"400\"\n    tooltip-effect=\"dark\"\n    highlight-current-row\n    @selection-change=\"handleSelectionChange\"\n  >\n    <el-table-column\n      v-if=\"handle==='wu'\"\n      type=\"selection\"\n      width=\"55\"\n    />\n    <el-table-column\n      prop=\"id\"\n      label=\"ID\"\n      min-width=\"50\"\n    />\n    <el-table-column\n      prop=\"name\"\n      label=\"优惠券名称\"\n      min-width=\"90\"\n    />\n    <el-table-column\n      prop=\"money\"\n      label=\"优惠券面值\"\n      min-width=\"90\"\n    />\n    <el-table-column\n      prop=\"minPrice\"\n      label=\"最低消费额\"\n      min-width=\"90\"\n    >\n      <template slot-scope=\"scope\">\n        <span>{{ scope.row.minPrice===0?'不限制':scope.row.minPrice }}</span>\n      </template>\n    </el-table-column>\n    <el-table-column\n      label=\"有效期限\"\n      min-width=\"190\"\n    >\n      <template slot-scope=\"scope\">\n        <span>{{ scope.row.isFixedTime===1?scope.row.useStartTime+' 一 '+scope.row.useEndTime:'不限制' }}</span>\n      </template>\n    </el-table-column>\n    <el-table-column\n      label=\"剩余数量\"\n      min-width=\"90\"\n    >\n      <template slot-scope=\"scope\">\n        <span>{{ !scope.row.isLimited ? '不限量' : scope.row.lastTotal }}</span>\n      </template>\n    </el-table-column>\n    <el-table-column v-if=\"handle==='send'\" label=\"操作\" min-width=\"120\" fixed=\"right\" align=\"center\">\n      <template slot-scope=\"scope\">\n        <el-button type=\"text\" size=\"small\" class=\"mr10\" @click=\"sendGrant(scope.row.id)\" v-hasPermi=\"['admin:coupon:user:receive']\">发送</el-button>\n      </template>\n    </el-table-column>\n  </el-table>\n  <div class=\"block mb20\">\n    <el-pagination\n      :page-sizes=\"[10, 20, 30, 40]\"\n      :page-size=\"tableFrom.limit\"\n      :current-page=\"tableFrom.page\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"tableData.total\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"pageChange\"\n    />\n  </div>\n  <div  v-if=\"handle==='wu'\">\n    <el-button size=\"small\" type=\"primary\" class=\"fr\" @click=\"ok\">确定</el-button>\n  </div>\n</div>\n", null]}