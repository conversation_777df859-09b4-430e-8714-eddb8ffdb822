{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue?vue&type=template&id=dae61528&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div>\n  <div class=\"priceChange\" :class=\"change === true ? 'on' : ''\">\n    <div class=\"priceTitle\">\n      {{\n        status === 0 || status === 2 ? orderInfo.refundStatus === 1 ? \"立即退款\" : \"一键改价\" :  status === 1?\"订单备注\":\"拒绝原因\"\n      }}\n      <i class=\"el-icon-circle-close iconfonts\" @click=\"close\"></i>\n      <!--<span class=\"iconfont icon-guanbi\" @click=\"close\"></span>-->\n    </div>\n    <div class=\"listChange\" v-if=\"status === 0 || status === 2\">\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderInfo.refundStatus === 0\"\n      >\n        <div>商品总价(¥)</div>\n        <div class=\"money\">\n          {{ orderInfo.totalPrice }}<span class=\"iconfont icon-suozi\"></span>\n        </div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderInfo.refundStatus === 0\"\n      >\n        <div>原始邮费(¥)</div>\n        <div class=\"money\">\n          {{ orderInfo.payPostage }}<span class=\"iconfont icon-suozi\"></span>\n        </div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderInfo.refundStatus === 0\"\n      >\n        <div>实际支付(¥)</div>\n        <div class=\"money\">\n          <input\n            type=\"text\"\n            v-model=\"price\"\n            :class=\"focus === true ? 'on' : ''\"\n            @focus=\"priceChange\"\n          />\n        </div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderInfo.refundStatus === 1\"\n      >\n        <div>实际支付(¥)</div>\n        <div class=\"money\">\n          {{ orderInfo.payPrice }}<span class=\"iconfont icon-suozi\"></span>\n        </div>\n      </div>\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-if=\"orderInfo.refundStatus === 1\"\n      >\n        <div>退款金额(¥)</div>\n        <div class=\"money\">\n          <input\n            type=\"text\"\n            v-model=\"refundPrice\"\n            :class=\"focus === true ? 'on' : ''\"\n            @focus=\"priceChange\"\n          />\n        </div>\n      </div>\n    </div>\n    <div class=\"listChange\" v-else-if=\"status === 3\">\n      <textarea\n        placeholder=\"请填写退款原因\"\n        v-model=\"reason\" maxlength=\"100\"\n      ></textarea>\n    </div>\n    <div class=\"listChange\" v-else>\n      <textarea\n        :placeholder=\"\n          orderInfo.remark ? orderInfo.remark : '请填写备注信息...'\n        \"\n        v-model=\"remark\" maxlength=\"100\"\n      ></textarea>\n    </div>\n    <div class=\"modify\" @click=\"save\">\n      {{ orderInfo.refundStatus === 0 || status === 1 || status === 3 ? \"立即提交\" : \"确认退款\" }}\n    </div>\n    <!--<div class=\"modify1\" @click=\"refuse\" v-if=\"orderInfo.refundStatus === 1 && status === 2\">-->\n      <!--拒绝退款-->\n    <!--</div>-->\n  </div>\n  <div class=\"maskModel\" @touchmove.prevent v-show=\"change === true\"></div>\n</div>\n", null]}