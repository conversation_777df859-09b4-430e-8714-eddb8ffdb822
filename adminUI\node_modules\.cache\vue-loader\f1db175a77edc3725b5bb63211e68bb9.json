{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\group\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\group\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { tagListApi, tagDeleteApi, tagSaveApi, tagInfoApi, tagUpdateApi, groupListApi, groupDeleteApi, groupSaveApi, groupUpdateApi } from '@/api/user'\nexport default {\n  name: 'UserGroup',\n  data() {\n    return {\n      tableFrom: {\n        page: 1,\n        limit: 20\n      },\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    info(){\n\n    },\n    onAdd(row) {\n      this.$prompt(this.$route.path.indexOf('group') !== -1?'分组名称':'标签名称', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: this.$route.path.indexOf('group') !== -1?'请输入分组名称':'请输入标签名称',\n        inputType: 'text',\n        closeOnClickModal: false,\n        inputValue: row ? (this.$route.path.indexOf('group') !== -1?row.groupName:row.name): '',\n        inputPlaceholder: this.$route.path.indexOf('group') !== -1?'请输入分组名称':'请输入标签名称',\n        inputValidator: (value) => { if(!value) return '输入不能为空'}\n      }).then(({value}) => {\n        if(this.$route.path.indexOf('group') !== -1){\n          row?groupUpdateApi({id: row.id},{groupName: value}).then(() => {\n            this.$message.success('编辑成功')\n            this.getList();\n          }):groupSaveApi({ groupName: value}).then(() => {\n            this.$message.success('新增成功')\n            this.getList();\n          })\n        }else{\n          row?tagUpdateApi({id: row.id}, {name: value}).then(() => {\n            this.$message.success('编辑成功')\n            this.getList();\n          }):tagSaveApi({ name: value}).then(() => {\n            this.$message.success('新增成功')\n            this.getList();\n          })\n        }\n      }).catch(() => {\n        this.$message.info('取消输入')\n      })\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      this.$route.path.indexOf('group') !== -1 ? groupListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(res => {\n        this.listLoading = false\n      }) : tagListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(res => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure('删除吗？所有用户已经关联的数据都会清除').then(() => {\n        this.$route.path.indexOf('group') !== -1 ? groupDeleteApi({id:id}).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        }) : tagDeleteApi({id:id}).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    }\n  }\n}\n", null]}