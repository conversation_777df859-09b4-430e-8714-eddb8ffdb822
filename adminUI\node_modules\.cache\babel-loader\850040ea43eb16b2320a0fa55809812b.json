{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1754275430524}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { levelSaveApi, levelInfoApi, levelUpdateApi } from '@/api/user';\nimport { Debounce } from '@/utils/validate';\nvar obj = {\n  name: '',\n  grade: 1,\n  discount: '',\n  icon: '',\n  image: '',\n  id: null\n};\nexport default {\n  name: \"CreatGrade\",\n  props: {\n    'user': Object\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      formValidate: Object.assign({}, obj),\n      loading: false,\n      rules: {\n        name: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.levelNameRequired'),\n          trigger: 'blur'\n        }],\n        grade: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.gradeRequired'),\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          message: this.$t('user.grade.form.validation.gradeNumber')\n        }],\n        discount: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.discountRequired'),\n          trigger: 'blur'\n        }],\n        experience: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.experienceRequired'),\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          message: this.$t('user.grade.form.validation.experienceNumber')\n        }],\n        icon: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.iconRequired'),\n          trigger: 'change'\n        }],\n        image: [{\n          required: true,\n          message: this.$t('user.grade.form.validation.imageRequired'),\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  methods: {\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num) {\n      var _this = this;\n      this.$modalUpload(function (img) {\n        tit === '1' && num === 'icon' ? _this.formValidate.icon = img[0].sattDir : _this.formValidate.image = img[0].sattDir;\n        this.$set(_this.user, 'icon', _this.formValidate.icon);\n        this.$set(_this.user, 'isShow', false);\n      }, tit, 'user');\n    },\n    info: function info(id) {\n      var _this2 = this;\n      this.loading = true;\n      levelInfoApi({\n        id: id\n      }).then(function (res) {\n        _this2.formValidate = res;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    handleClose: function handleClose() {\n      var _this3 = this;\n      this.$nextTick(function () {\n        _this3.$refs.user.resetFields();\n      });\n      this.dialogVisible = false;\n      // this.user = Object.assign({}, '')\n    },\n    submitForm: Debounce(function (formName) {\n      var _this4 = this;\n      this.$refs.user.validate(function (valid) {\n        if (valid) {\n          _this4.loading = true;\n          var data = {\n            discount: _this4.user.discount,\n            experience: _this4.user.experience,\n            grade: _this4.user.grade,\n            icon: _this4.user.icon,\n            id: _this4.user.id,\n            isShow: _this4.user.isShow,\n            name: _this4.user.name\n          };\n          _this4.user.id ? levelUpdateApi(_this4.user.id, data).then(function (res) {\n            _this4.$message.success(_this4.$t('user.grade.form.editSuccess'));\n            _this4.loading = false;\n            _this4.handleClose();\n            _this4.formValidate = Object.assign({}, obj);\n            _this4.$parent.getList();\n          }).catch(function () {\n            _this4.loading = false;\n          }) : levelSaveApi(_this4.user).then(function (res) {\n            _this4.$message.success(_this4.$t('user.grade.form.addSuccess'));\n            _this4.loading = false;\n            _this4.handleClose();\n            _this4.formValidate = Object.assign({}, obj);\n            _this4.$parent.getList();\n          }).catch(function () {\n            _this4.loading = false;\n            _this4.formValidate = Object.assign({}, obj);\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm: function resetForm(formName) {\n      var _this5 = this;\n      // this[formName] = {};\n      this.$nextTick(function () {\n        _this5.$refs.user.resetFields();\n      });\n      this.dialogVisible = false;\n    }\n  }\n};", null]}