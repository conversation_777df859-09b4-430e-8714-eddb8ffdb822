{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue?vue&type=template&id=2b1933c4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue", "mtime": 1754301662333}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('el-tabs',{staticClass:\"mb20\",on:{\"tab-click\":_vm.onChangeType},model:{value:(_vm.form.type),callback:function ($$v) {_vm.$set(_vm.form, \"type\", $$v)},expression:\"form.type\"}},[_c('el-tab-pane',{attrs:{\"label\":_vm.$t('product.isHot'),\"name\":\"1\"}}),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":_vm.$t('product.isBenefit'),\"name\":\"2\"}}),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"label\":_vm.$t('product.isTikTok'),\"name\":\"3\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.form),callback:function ($$v) {_vm.form=$$v},expression:\"form\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('product.productName')}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('product.enterProductName'),\"clearable\":\"\"},model:{value:(_vm.form.keywords),callback:function ($$v) {_vm.$set(_vm.form, \"keywords\", $$v)},expression:\"form.keywords\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('product.status')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('product.pleaseSelect')},model:{value:(_vm.form.isShow),callback:function ($$v) {_vm.$set(_vm.form, \"isShow\", $$v)},expression:\"form.isShow\"}},_vm._l((_vm.statusOptions),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":_vm.$t('product.' + item.label),\"value\":item.value}})}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.getList}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"product.query\"))+\"\\n    \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":function($event){return _vm.resetForm()}}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"product.reset\"))+\"\\n    \")])],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"label\":_vm.$t('common.serialNumber'),\"type\":\"index\",\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productImage'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.image,\"preview-src-list\":[scope.row.image]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productName'),\"prop\":\"storeName\",\"min-width\":\"300\",\"show-overflow-tooltip\":true}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.productPrice'),\"min-width\":\"90\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatAmount(scope.row.price)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.cashbackRate'),\"min-width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatRate(scope.row.cashBackRate)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.addTime'),\"min-width\":\"120\",\"align\":\"center\",\"prop\":\"addTime\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatTime(scope.row.addTime)))]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('product.action'),\"min-width\":\"60\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handleDelete(scope.row)}}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t(\"product.offline\"))+\"\\n          \")])]}}])})],1),_vm._v(\" \"),_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.form.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.form.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.form.total},on:{\"size-change\":_vm.sizeChange,\"current-change\":_vm.pageChange}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}