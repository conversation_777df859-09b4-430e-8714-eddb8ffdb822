{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\wxMenus.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { wechatMenuApi, wechatMenuAddApi } from '@/api/wxApi';\nimport { Debounce } from '@/utils/validate';\nexport default {\n  name: 'WechatMenus',\n  data: function data() {\n    return {\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 8,\n        sm: 8,\n        xs: 24\n      },\n      grid2: {\n        xl: 16,\n        lg: 16,\n        md: 16,\n        sm: 16,\n        xs: 24\n      },\n      modal2: false,\n      formValidate: {\n        name: '',\n        type: 'click',\n        appid: '',\n        url: '',\n        key: '',\n        pagepath: '',\n        id: 0\n      },\n      ruleValidate: {\n        name: [{\n          required: true,\n          message: '请填写菜单名称',\n          trigger: 'blur'\n        }],\n        key: [{\n          required: true,\n          message: '请填写关键字',\n          trigger: 'blur'\n        }],\n        appid: [{\n          required: true,\n          message: '请填写appid',\n          trigger: 'blur'\n        }],\n        pagepath: [{\n          required: true,\n          message: '请填写小程序路径',\n          trigger: 'blur'\n        }],\n        url: [{\n          required: true,\n          message: '请填写跳转地址',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '请选择规则状态',\n          trigger: 'change'\n        }]\n      },\n      parentMenuId: null,\n      list: [],\n      checkedMenuId: null,\n      isTrue: false\n    };\n  },\n  mounted: function mounted() {\n    this.getMenus();\n    if (this.list.length) {\n      this.formValidate = this.list[this.activeClass];\n    } else {\n      return this.formValidate;\n    }\n  },\n  methods: {\n    // 添加一级字段函数\n    defaultMenusData: function defaultMenusData() {\n      return {\n        type: 'click',\n        name: '',\n        sub_button: []\n      };\n    },\n    // 添加二级字段函数\n    defaultChildData: function defaultChildData() {\n      return {\n        type: 'click',\n        name: ''\n      };\n    },\n    // 获取 菜单\n    getMenus: function getMenus() {\n      var _this = this;\n      wechatMenuApi().then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          var data;\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                data = res.menu;\n                _this.list = data.button;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 点击保存提交\n    submenus: Debounce(function (name) {\n      var _this2 = this;\n      if (this.isTrue && !this.checkedMenuId && this.checkedMenuId !== 0) {\n        this.putData();\n      } else {\n        this.$refs[name].validate(function (valid) {\n          if (valid) {\n            _this2.putData();\n          } else {\n            if (!_this2.check()) return false;\n          }\n        });\n      }\n    }),\n    // 新增data\n    putData: function putData() {\n      var _this3 = this;\n      var data = {\n        button: this.list\n      };\n      wechatMenuAddApi(data).then(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                _this3.$message.success('提交成功');\n                _this3.checkedMenuId = null;\n                _this3.formValidate = {};\n                _this3.isTrue = false;\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    },\n    // 点击元素\n    gettem: function gettem(item, index, pid) {\n      this.checkedMenuId = index;\n      this.formValidate = item;\n      this.parentMenuId = pid;\n      this.isTrue = true;\n    },\n    // 增加二级\n    add: function add(item, index) {\n      if (!this.check()) return false;\n      if (item.sub_button.length < 5) {\n        var data = this.defaultChildData();\n        var id = item.sub_button.length;\n        item.sub_button.push(data);\n        this.formValidate = data;\n        this.checkedMenuId = id;\n        this.parentMenuId = index;\n        this.isTrue = true;\n      }\n    },\n    // 增加一级\n    addtext: function addtext() {\n      if (!this.check()) return false;\n      var data = this.defaultMenusData();\n      var id = this.list.length;\n      this.list.push(data);\n      this.formValidate = data;\n      this.checkedMenuId = id;\n      this.parentMenuId = null;\n      this.isTrue = true;\n    },\n    // 判断函数\n    check: function check() {\n      var reg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?/;\n      if (this.checkedMenuId === null) return true;\n      if (!this.isTrue) return true;\n      if (!this.formValidate.name) {\n        this.$message.warning('请输入按钮名称!');\n        return false;\n      }\n      if (this.formValidate.type === 'click' && !this.formValidate.key) {\n        this.$message.warning('请输入关键字!');\n        return false;\n      }\n      if (this.formValidate.type === 'view' && !reg.test(this.formValidate.url)) {\n        this.$message.warning('请输入正确的跳转地址!');\n        return false;\n      }\n      if (this.formValidate.type === 'miniprogram' && (!this.formValidate.appid || !this.formValidate.pagepath || !this.formValidate.url)) {\n        this.$message.warning('请填写完整小程序配置!');\n        return false;\n      }\n      return true;\n    },\n    // 删除\n    deltMenus: function deltMenus() {\n      var _this4 = this;\n      if (this.isTrue) {\n        this.$modalSure().then(function () {\n          _this4.del();\n        });\n      } else {\n        this.$message.warning('请选择菜单!');\n      }\n    },\n    // 确认删除\n    del: function del() {\n      this.parentMenuId === null ? this.list.splice(this.checkedMenuId, 1) : this.list[this.parentMenuId].sub_button.splice(this.checkedMenuId, 1);\n      this.parentMenuId = null;\n      this.formValidate = {\n        name: '',\n        type: 'click',\n        appid: '',\n        url: '',\n        key: '',\n        pagepath: '',\n        id: 0\n      };\n      this.isTrue = false;\n      this.modal2 = false;\n      this.checkedMenuId = null;\n      this.$refs['formValidate'].resetFields();\n      this.submenus('formValidate');\n    }\n  }\n};", null]}