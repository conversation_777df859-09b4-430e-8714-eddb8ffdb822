(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1a38e50f"],{"0bce":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"divBox"},[i("div",[i("el-card",{staticClass:"box-card",attrs:{bordered:!1}},[i("div",[i("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.currentTab,callback:function(e){t.currentTab=e},expression:"currentTab"}},t._l(t.headerList,(function(t,e){return i("el-tab-pane",{key:e+"-only",attrs:{label:t.label,name:t.value.toString()}})})),1)],1),t._v(" "),i("el-row",{staticClass:"mb20 mt-1",attrs:{type:"flex"}},[i("el-col",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:wechat:routine:sync"],expression:"['admin:wechat:routine:sync']"}],attrs:{type:"primary",icon:"el-icon-document"},on:{click:function(e){return t.syncRoutine()}}},[t._v("同步小程序订阅消息")]),t._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:wechat:whcbqhn:sync"],expression:"['admin:wechat:whcbqhn:sync']"}],attrs:{type:"primary",icon:"el-icon-document"},on:{click:function(e){return t.syncWechat()}}},[t._v("同步微信模版消息")])],1)],1),t._v(" "),i("div",{staticClass:"description"},[i("p",[i("span",{staticClass:"iconfont iconxiaochengxu"}),t._v(" 小程序经营类目：生活服务 > 百货/超市/便利店")]),t._v(" "),i("p",[i("span",{staticClass:"iconfont icongongzhonghao"}),t._v(" 公众号经营类目：IT科技/互联网|电子商务，IT科技/IT软件与服务")])]),t._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingList,expression:"loadingList"}],ref:"table",staticClass:"mt25",attrs:{data:t.levelLists,size:"small","header-cell-style":{fontWeight:"bold"}}},[i("el-table-column",{attrs:{label:"ID",prop:"id",width:"80"}}),t._v(" "),i("el-table-column",{attrs:{label:"通知类型",prop:"type"}}),t._v(" "),i("el-table-column",{attrs:{label:"通知场景说明",prop:"description"}}),t._v(" "),i("el-table-column",{attrs:{label:"标识",prop:"mark"}}),t._v(" "),"1"==t.currentTab?i("el-table-column",{attrs:{label:"公众号模板",prop:"isWechat"},scopedSlots:t._u([{key:"default",fn:function(e){return 0!==e.row.isWechat?[i("el-switch",{attrs:{"active-value":1,"inactive-value":2,"active-text":"启用","inactive-text":"禁用"},on:{change:function(i){return t.changeWechat(e.row)}},model:{value:e.row.isWechat,callback:function(i){t.$set(e.row,"isWechat",i)},expression:"scope.row.isWechat"}})]:void 0}}],null,!0)}):t._e(),t._v(" "),"1"==t.currentTab?i("el-table-column",{attrs:{label:"小程序订阅",prop:"isRoutine"},scopedSlots:t._u([{key:"default",fn:function(e){return 0!==e.row.isRoutine?[i("el-switch",{attrs:{"active-value":1,"inactive-value":2,"active-text":"启用","inactive-text":"禁用"},on:{change:function(i){return t.changeRoutine(e.row)}},model:{value:e.row.isRoutine,callback:function(i){t.$set(e.row,"isRoutine",i)},expression:"scope.row.isRoutine"}})]:void 0}}],null,!0)}):t._e(),t._v(" "),i("el-table-column",{attrs:{label:"发送短信",prop:"isSms"},scopedSlots:t._u([{key:"default",fn:function(e){return 0!==e.row.isSms?[i("el-switch",{attrs:{"active-value":1,"inactive-value":2,"active-text":"启用","inactive-text":"禁用"},on:{change:function(i){return t.changeSms(e.row)}},model:{value:e.row.isSms,callback:function(i){t.$set(e.row,"isSms",i)},expression:"scope.row.isSms"}})]:void 0}}],null,!0)}),t._v(" "),i("el-table-column",{attrs:{label:"设置",prop:"id"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:notification:detail"],expression:"['admin:system:notification:detail']"}],attrs:{type:"text"},on:{click:function(i){return t.setting(e.row)}}},[t._v("详情")])]}}])})],1)],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"通知详情",visible:t.centerDialogVisible,width:"50%"},on:{"update:visible":function(e){t.centerDialogVisible=e}}},[i("el-tabs",{attrs:{value:t.infoTab},on:{"tab-click":t.changeInfo}},[t._l("1"==t.currentTab?t.infoList:t.infoList1,(function(t,e){return i("el-tab-pane",{key:e,attrs:{label:t.label,name:t.value.toString()}})})),t._v(" "),i("el-form",{ref:"form",attrs:{model:t.form,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"ID"}},[i("el-input",{attrs:{disabled:""},model:{value:t.form.id,callback:function(e){t.$set(t.form,"id",e)},expression:"form.id"}})],1),t._v(" "),t.form.name?i("el-form-item",{attrs:{label:"模板名"}},[i("el-input",{attrs:{disabled:""},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1):t._e(),t._v(" "),t.form.tempId?i("el-form-item",{attrs:{label:"模板ID"}},[i("el-input",{model:{value:t.form.tempId,callback:function(e){t.$set(t.form,"tempId",e)},expression:"form.tempId"}})],1):t._e(),t._v(" "),t.form.tempKey?i("el-form-item",{attrs:{label:"模板编号"}},[i("el-input",{attrs:{disabled:""},model:{value:t.form.tempKey,callback:function(e){t.$set(t.form,"tempKey",e)},expression:"form.tempKey"}})],1):t._e(),t._v(" "),t.form.title?i("el-form-item",{attrs:{label:"模板说明"}},[i("el-input",{attrs:{disabled:""},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1):t._e(),t._v(" "),t.form.content?i("el-form-item",{attrs:{label:"模板内容"}},[i("el-input",{attrs:{disabled:""},model:{value:t.form.content,callback:function(e){t.$set(t.form,"content",e)},expression:"form.content"}})],1):t._e(),t._v(" "),i("el-form-item",{attrs:{label:"状态"}},[i("el-radio-group",{model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},[i("el-radio",{attrs:{label:"1"}},[t._v("开启")]),t._v(" "),i("el-radio",{attrs:{label:"2"}},[t._v("关闭")])],1)],1)],1)],2),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.centerDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submit()}}},[t._v("确 定")])],1)],1)],1)},a=[],o=i("92c6"),s=i("ffd2"),l=i("61f7"),r={data:function(){return{modalTitle:"",notificationModal:!1,headerList:[{label:"通知会员",value:"1"},{label:"通知平台",value:"2"}],id:0,levelLists:[],currentTab:"1",loading:!1,formData:{},industry:null,loadingList:!1,centerDialogVisible:!1,infoList:[],infoList1:[{label:"短信",value:"sms"}],form:{content:"",name:"",id:"",status:null,tempId:"",tempKey:"",title:""},detailType:"",infoTab:""}},created:function(){this.getNotificationList(Number(this.currentTab))},methods:{changeTab:function(t){this.getNotificationList(t.name)},getNotificationList:function(t){var e=this;this.loadingList=!0,Object(o["f"])({sendType:t}).then((function(t){e.loadingList=!1,e.levelLists=t})).catch((function(t){e.loadingList=!1}))},changeWechat:function(t){var e=this;Object(o["j"])(t.id).then((function(t){e.$modal.msgSuccess("修改成功")}))},changeRoutine:function(t){var e=this;Object(o["g"])(t.id).then((function(t){e.$modal.msgSuccess("修改成功")}))},changeSms:function(t){var e=this;Object(o["h"])(t.id).then((function(t){e.$modal.msgSuccess("修改成功")}))},changeInfo:function(t){this.getNotificationDetail(t)},getNotificationDetail:function(t){var e=this,i={id:this.id,type:t.name};this.$set(this,"detailType",i.type),Object(o["e"])(i).then((function(t){e.form=t,e.$set(e.form,"status",t.status.toString())}))},setting:function(t){this.infoList=[],this.id=t.id,this.centerDialogVisible=!0,0!==t.isWechat&&this.infoList.push({label:"公众号模板消息",value:"wechat"}),0!==t.isRoutine&&this.infoList.push({label:"小程序订阅消息",value:"routine"}),0!==t.isSms&&this.infoList.push({label:"短信",value:"sms"}),this.infoTab=this.infoList[0].value,this.getNotificationDetail({name:this.infoTab})},submit:Object(l["a"])((function(){var t=this,e={id:this.id,status:Number(this.form.status),tempId:this.form.tempId,type:this.detailType};Object(o["i"])(e).then((function(e){t.$modal.msgSuccess("修改成功"),t.centerDialogVisible=!1,t.getNotificationList()}))})),syncWechat:function(){var t=this;Object(s["j"])().then((function(e){t.$message.success("同步成功")}))},syncRoutine:function(){var t=this;Object(s["i"])().then((function(e){t.$message.success("同步成功")}))}}},c=r,u=(i("e59a"),i("2877")),m=Object(u["a"])(c,n,a,!1,null,"3790ec8a",null);e["default"]=m.exports},"92c6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"d",(function(){return s})),i.d(e,"a",(function(){return l})),i.d(e,"f",(function(){return r})),i.d(e,"g",(function(){return c})),i.d(e,"j",(function(){return u})),i.d(e,"h",(function(){return m})),i.d(e,"e",(function(){return f})),i.d(e,"i",(function(){return d}));var n=i("b775");function a(t){var e={id:t.id};return Object(n["a"])({url:"/admin/system/form/temp/info",method:"GET",params:e})}function o(t){var e={keywords:t.keywords,page:t.page,limit:t.limit};return Object(n["a"])({url:"/admin/system/form/temp/list",method:"GET",params:e})}function s(t){var e={content:t.content,info:t.info,name:t.name};return Object(n["a"])({url:"/admin/system/form/temp/save",method:"POST",data:e})}function l(t){var e={id:t.id},i={content:t.content,info:t.info,name:t.name};return Object(n["a"])({url:"/admin/system/form/temp/update",method:"POST",params:e,data:i})}function r(t){var e={sendType:t.sendType};return Object(n["a"])({url:"/admin/system/notification/list",method:"GET",params:e})}function c(t){return Object(n["a"])({url:"/admin/system/notification/routine/switch/".concat(t),method:"post"})}function u(t){return Object(n["a"])({url:"/admin/system/notification/wechat/switch/".concat(t),method:"post"})}function m(t){return Object(n["a"])({url:"/admin/system/notification/sms/switch/".concat(t),method:"post"})}function f(t){var e={detailType:t.type,id:t.id};return Object(n["a"])({url:"/admin/system/notification/detail",method:"get",params:e})}function d(t){var e={detailType:t.type,id:t.id,status:t.status,tempId:t.tempId};return Object(n["a"])({url:"/admin/system/notification/update",method:"post",data:e})}},a9a95:function(t,e,i){},e59a:function(t,e,i){"use strict";i("a9a95")}}]);