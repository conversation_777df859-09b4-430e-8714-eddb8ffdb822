package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;
import com.genco.common.utils.BrandCodeUtils;
import com.genco.common.utils.RedisUtil;
import com.genco.service.dao.StoreBrandDao;
import com.genco.service.service.StoreBrandService;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * StoreBrandServiceImpl 接口实现
 */
@Service
public class StoreBrandServiceImpl extends ServiceImpl<StoreBrandDao, StoreBrand> implements StoreBrandService {

    @Resource
    private StoreBrandDao dao;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取品牌列表
     *
     * @param status 状态类型（-1：全部；0：待上架；1：已上架；2：已下架）
     * @param keywords 关键词搜索
     * @param pageRequest 分页参数
     * @return 列表
     */
    @Override
    public List<StoreBrand> getBrandList(Integer status, String keywords, PageParamRequest pageRequest) {
        LambdaQueryWrapper<StoreBrand> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        // 根据状态筛选品牌（使用状态常量）
        if (status != null && status != -1) {
            // 具体状态筛选：0=待上架，1=已上架，2=已下架
            lambdaQueryWrapper.eq(StoreBrand::getStatus, status.toString());
        } else {
            // status=-1表示查询所有状态，但排除已删除的品牌
            lambdaQueryWrapper.ne(StoreBrand::getStatus, StoreBrand.STATUS_DELETED);
        }

        // 关键词搜索
        if (StringUtils.isNotEmpty(keywords)) {
            lambdaQueryWrapper.apply("LOWER(name) LIKE {0}", "%" + keywords.toLowerCase() + "%");
        }

        lambdaQueryWrapper.orderByDesc(StoreBrand::getPriority);
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据品牌编码获取详情
     *
     * @param code 品牌编码
     * @return StoreBrand
     */
    @Override
    public StoreBrand getByBrandCode(String code) {
        LambdaQueryWrapper<StoreBrand> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreBrand::getCode, code);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    @Override
    public Boolean updateBrand(StoreBrandUpdateRequest request) {
        StoreBrand brand = getById(request.getId());
        if (brand == null) {
            throw new RuntimeException("品牌不存在");
        }
        // 逐字段更新
        if (request.getCode() != null) brand.setCode(request.getCode());
        if (request.getName() != null) brand.setName(request.getName());
        if (request.getLogoUrl() != null) brand.setLogoUrl(request.getLogoUrl());
        if (request.getDescription() != null) brand.setDescription(request.getDescription());
        if (request.getIndustry() != null) brand.setIndustry(request.getIndustry());
        if (request.getStatus() != null) brand.setStatus(request.getStatus());
        if (request.getPlatform() != null) brand.setPlatform(request.getPlatform());
        if (request.getProductCount() != null) brand.setProductCount(request.getProductCount());
        if (request.getProductSoldCount() != null) brand.setProductSoldCount(request.getProductSoldCount());
        if (request.getProductSoldAmount() != null) brand.setProductSoldAmount(request.getProductSoldAmount());
        if (request.getProductCashbackAmount() != null)
            brand.setProductCashbackAmount(request.getProductCashbackAmount());
        if (request.getProductShareCount() != null) brand.setProductShareCount(request.getProductShareCount());
        if (request.getCreator() != null) brand.setCreator(request.getCreator());
        if (request.getIsHot() != null) brand.setIsHot(request.getIsHot());
        if (request.getIsHighCashback() != null) brand.setIsHighCashback(request.getIsHighCashback());
        if (request.getContactPerson() != null) brand.setContactPerson(request.getContactPerson());
        if (request.getContactPhone() != null) brand.setContactPhone(request.getContactPhone());
        return updateById(brand);
    }

    @Override
    public Integer addBrand(StoreBrandUpdateRequest request) {
        StoreBrand brand = new StoreBrand();

        // 自动生成code，如果request中没有提供code或者code为空
        if (StringUtils.isEmpty(request.getCode()) && StringUtils.isNotEmpty(request.getName())) {
            String generatedCode = BrandCodeUtils.generateBrandCode(request.getName());
            brand.setCode(generatedCode);
        } else if (request.getCode() != null) {
            brand.setCode(request.getCode());
        }
        // 设置状态，如果前端传递了status则使用前端的值，否则默认为待上架状态
        if (request.getStatus() != null) {
            brand.setStatus(request.getStatus());
        } else {
            brand.setStatus(StoreBrand.STATUS_OFFLINE); // 新增品牌默认为待上架状态
        }
        if (request.getName() != null) brand.setName(request.getName());
        if (request.getLogoUrl() != null) brand.setLogoUrl(request.getLogoUrl());
        if (request.getDescription() != null) brand.setDescription(request.getDescription());
        if (request.getIndustry() != null) brand.setIndustry(request.getIndustry());
        if (request.getPlatform() != null) brand.setPlatform(request.getPlatform());
        if (request.getProductCount() != null) brand.setProductCount(request.getProductCount());
        if (request.getProductSoldCount() != null) brand.setProductSoldCount(request.getProductSoldCount());
        if (request.getProductSoldAmount() != null) brand.setProductSoldAmount(request.getProductSoldAmount());
        if (request.getProductCashbackAmount() != null)
            brand.setProductCashbackAmount(request.getProductCashbackAmount());
        if (request.getProductShareCount() != null) brand.setProductShareCount(request.getProductShareCount());
        if (request.getCreator() != null) brand.setCreator(request.getCreator());
        if (request.getIsHot() != null) brand.setIsHot(request.getIsHot());
        if (request.getIsHighCashback() != null) brand.setIsHighCashback(request.getIsHighCashback());
        if (request.getContactPerson() != null) brand.setContactPerson(request.getContactPerson());
        if (request.getContactPhone() != null) brand.setContactPhone(request.getContactPhone());
        dao.insert(brand);
        return brand.getId();
    }

    /**
     * 逻辑删除品牌
     * 状态流转：任何状态 → "-1"(已删除)
     */
    @Override
    public Boolean deleteBrand(Integer id) {
        StoreBrand brand = getById(id);
        if (brand == null) {
            throw new RuntimeException("品牌不存在");
        }
        brand.setStatus(StoreBrand.STATUS_DELETED); // 逻辑删除
        return updateById(brand);
    }

    /**
     * 验证状态流转是否合法
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否合法
     */
    private boolean isValidStatusTransition(String fromStatus, String toStatus) {
        // 已删除状态不能流转到其他状态
        if (StoreBrand.STATUS_DELETED.equals(fromStatus)) {
            return false;
        }

        // 任何状态都可以流转到已删除状态
        if (StoreBrand.STATUS_DELETED.equals(toStatus)) {
            return true;
        }

        // 其他状态流转规则
        switch (fromStatus) {
            case StoreBrand.STATUS_OFFLINE: // 待上架
                return StoreBrand.STATUS_ONLINE.equals(toStatus); // 只能到已上架
            case StoreBrand.STATUS_ONLINE: // 已上架
                return StoreBrand.STATUS_SUSPENDED.equals(toStatus); // 只能到已下架
            case StoreBrand.STATUS_SUSPENDED: // 已下架
                return StoreBrand.STATUS_ONLINE.equals(toStatus); // 只能到已上架
            default:
                return false;
        }
    }
}

