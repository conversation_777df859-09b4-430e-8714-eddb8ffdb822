package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 品牌更新请求对象
 * 用于品牌信息的更新操作
 */
@Data
@ApiModel(value = "StoreBrandUpdateRequest", description = "品牌更新请求对象")
public class StoreBrandUpdateRequest implements Serializable {

    @ApiModelProperty(value = "品牌ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    @NotNull(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "logo地址")
    @NotNull(message = "品牌图片不能为空")
    private String logoUrl;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "所属行业")
    private String industry;

    @ApiModelProperty(value = "入驻电商平台")
    private String platform;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "商品数量")
    private Integer productCount;

    @ApiModelProperty(value = "商品已售数量")
    private Integer productSoldCount;

    @ApiModelProperty(value = "商品已售金额")
    private BigDecimal productSoldAmount;

    @ApiModelProperty(value = "商品已返现金额")
    private BigDecimal productCashbackAmount;

    @ApiModelProperty(value = "商品分享数量")
    private Integer productShareCount;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "是否热卖品牌")
    private Boolean isHot;

    @ApiModelProperty(value = "是否高返现品牌")
    private Boolean isHighCashback;
} 