{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderCancellation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderCancellation\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WriteOff from \"../components/WriteOff\";\nimport { wechatEvevt } from \"@/libs/wechat\";\n// import { orderVerific } from \"@api/order\";\nimport { writeUpdateApi, writeConfirmApi } from '@/api/order';\nvar NAME = \"OrderCancellation\";\nexport default {\n  name: NAME,\n  components: {\n    WriteOff: WriteOff\n  },\n  props: {},\n  data: function data() {\n    return {\n      isWeixin: this.$wechat.isWeixin(),\n      iShidden: true,\n      orderInfo: null,\n      verify_code: \"\"\n    };\n  },\n  created: function created() {\n    import('@/assets/js/media_750');\n  },\n  methods: {\n    cancel: function cancel(res) {\n      this.iShidden = res;\n    },\n    confirm: function confirm() {\n      var _this = this;\n      writeUpdateApi(this.verify_code).then(function (res) {\n        _this.iShidden = true;\n        _this.verify_code = \"\";\n        _this.$dialog.success(res.msg);\n      }).catch(function (res) {\n        _this.$dialog.error(res.msg);\n      });\n    },\n    storeCancellation: function storeCancellation() {\n      var _this2 = this;\n      var ref = /[0-9]{10}/;\n      if (!this.verify_code) return this.$dialog.error(\"请输入核销码\");\n      if (!ref.test(this.verify_code)) return this.$dialog.error(\"请输入正确的核销码\");\n      this.$dialog.loading.open(\"查询中\");\n      writeConfirmApi(this.verify_code).then(function (res) {\n        _this2.$dialog.loading.close();\n        _this2.orderInfo = res;\n        _this2.iShidden = false;\n      }).catch(function (res) {\n        _this2.$dialog.loading.close();\n        _this2.verify_code = \"\";\n        return _this2.$dialog.error(res.message);\n      });\n    },\n    openQRCode: function openQRCode() {\n      var that = this;\n      wechatEvevt(\"scanQRCode\", {\n        needResult: 1,\n        scanType: [\"qrCode\", \"barCode\"]\n      }).then(function (res) {\n        if (res.resultStr) {\n          that.verify_code = res.resultStr;\n          that.storeCancellation();\n        } else that.$dialog.error(\"没有扫描到什么！\");\n      }).catch(function (res) {\n        if (res.is_ready) {\n          res.wx.scanQRCode({\n            needResult: 1,\n            scanType: [\"qrCode\", \"barCode\"],\n            success: function success(res) {\n              that.verify_code = res.resultStr;\n              that.storeCancellation();\n            },\n            fail: function fail(res) {\n              if (res.errMsg == \"scanQRCode:permission denied\") {\n                that.$dialog.error(\"没有权限\");\n              }\n            }\n          });\n        }\n      });\n    }\n  }\n};", null]}