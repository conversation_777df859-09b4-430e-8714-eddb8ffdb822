{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\index.vue?vue&type=template&id=3807d465&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\list\\index.vue", "mtime": 1754269254567}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-tabs v-model=\"loginType\" @tab-click=\"getList(1)\">\n        <el-tab-pane :label=\"item.name\" :name=\"item.type.toString()\" v-for=\"(item,index) in headeNum\" :key=\"index\"/>\n      </el-tabs>\n      <div class=\"container\">\n        <el-form inline size=\"small\" :model=\"userFrom\" ref=\"userFrom\" :label-position=\"labelPosition\"  label-width=\"100px\">\n          <el-row>\n            <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n              <el-col v-bind=\"grid\">\n                <el-form-item label=\"用户搜索：\">\n                  <el-input v-model=\"userFrom.keywords\" placeholder=\"请输入姓名或手机号\" clearable  class=\"selWidth\"/>\n                </el-form-item>\n              </el-col>\n            </el-col>\n            <template v-if=\"collapse\">\n              <el-col  :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n                <el-col v-bind=\"grid\">\n                  <el-form-item :label=\"$t('user.center.userLevel') + '：'\">\n                    <el-select v-model=\"levelData\" :placeholder=\"$t('common.pleaseSelect')\"  class=\"selWidth\" clearable filterable multiple>\n                      <el-option :value=\"item.id\" v-for=\"(item, index) in levelList\" :key=\"index\" :label=\"item.name\">\n                        <span style=\"float: left\">{{ item.name }}</span>\n                        <span style=\"float: right; color: #8492a6; font-size: 13px\">\n                          {{ getUpgradeTypeText(item.upgradeType) }}\n                          <span v-if=\"item.upgradeType === 1\"> - Rp {{ item.upgradePrice }}</span>\n                        </span>\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"用户分组：\">\n                    <el-select v-model=\"groupData\" placeholder=\"请选择\"  class=\"selWidth\" clearable filterable multiple>\n                      <el-option :value=\"item.id\" v-for=\"(item, index) in groupList\" :key=\"index\" :label=\"item.groupName\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"用户标签：\">\n                    <el-select v-model=\"labelData\" placeholder=\"请选择\"  class=\"selWidth\" clearable filterable multiple>\n                      <el-option :value=\"item.id\" v-for=\"(item, index) in labelLists\" :key=\"index\" :label=\"item.name\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </el-col>\n              <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"国家：\">\n                    <el-select v-model=\"userFrom.country\" placeholder=\"请选择\"  class=\"selWidth\" clearable @on-change=\"changeCountry\">\n                      <el-option value=\"\" label=\"全部\"></el-option>\n                      <el-option value=\"CN\" label=\"中国\"></el-option>\n                      <el-option value=\"OTHER\" label=\"国外\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\" v-if=\"userFrom.country ==='CN'\">\n                  <el-form-item label=\"省份：\">\n                    <el-cascader :options=\"addresData\" :props=\"propsCity\" filterable v-model=\"address\" @change=\"handleChange\" clearable  class=\"selWidth\"></el-cascader>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"消费情况：\">\n                    <el-select v-model=\"userFrom.payCount\" placeholder=\"请选择\"  class=\"selWidth\" clearable>\n                      <el-option value=\"\" label=\"全部\"></el-option>\n                      <el-option value=\"0\" label=\"0\"></el-option>\n                      <el-option value=\"1\" label=\"1+\"></el-option>\n                      <el-option value=\"2\" label=\"2+\"></el-option>\n                      <el-option value=\"3\" label=\"3+\"></el-option>\n                      <el-option value=\"4\" label=\"4+\"></el-option>\n                      <el-option value=\"5\" label=\"5+\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"时间选择：\" class=\"timeBox\">\n                    <el-date-picker\n                      v-model=\"timeVal\"\n                      align=\"right\"\n                      unlink-panels\n                      value-format=\"yyyy-MM-dd\"\n                      format=\"yyyy-MM-dd\"\n                      size=\"small\"\n                      type=\"daterange\"\n                      placement=\"bottom-end\"\n                      placeholder=\"自定义时间\"\n                      class=\"selWidth\"\n                      :picker-options=\"pickerOptions\" \n                      @change=\"onchangeTime\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-col>\n              <!-- <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n                \n              </el-col> -->\n              <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"访问情况：\">\n                    <el-select v-model=\"userFrom.accessType\" placeholder=\"请选择\"  class=\"selWidth\" clearable>\n                      <el-option :value=\"0\" label=\"全部\"></el-option>\n                      <el-option :value=\"1\" label=\"首次访问\"></el-option>\n                      <el-option :value=\"2\" label=\"时间段访问过\"></el-option>\n                      <el-option :value=\"3\" label=\"时间段未访问\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"性别：\">\n                    <el-radio-group v-model=\"userFrom.sex\" type=\"button\"  class=\"selWidth\">\n                      <el-radio-button label=\"\">\n                        <span>全部</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"0\">\n                        <span>未知</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"1\">\n                        <span>男</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"2\">\n                        <span>女</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"3\">\n                        <span>保密</span>\n                      </el-radio-button>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n                <el-col v-bind=\"grid\">\n                  <el-form-item label=\"身份：\">\n                    <el-radio-group v-model=\"userFrom.isPromoter\" type=\"button\"  class=\"selWidth\">\n                      <el-radio-button label=\"\">\n                        <span>全部</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"1\">\n                        <span>推广员</span>\n                      </el-radio-button>\n                      <el-radio-button label=\"0\">\n                        <span>普通用户</span>\n                      </el-radio-button>\n                    </el-radio-group>\n                  </el-form-item>\n                </el-col>\n              </el-col>\n              <!-- <el-col :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"18\" :xl=\"18\">\n                \n              </el-col> -->\n            </template>\n            <el-col  :xs=\"24\" :sm=\"24\" :md=\"24\" :lg=\"6\" :xl=\"6\" class=\"text-right userFrom\">\n              <el-form-item>\n                <el-button type=\"primary\" icon=\"ios-search\" label=\"default\" class=\"mr15\" size=\"small\"   @click=\"userSearchs\">搜索</el-button>\n                <el-button class=\"ResetSearch mr10\" @click=\"reset('userFrom')\"  size=\"small\">重置</el-button>\n                <a class=\"ivu-ml-8\" @click=\"collapse = !collapse\">\n                  <template v-if=\"!collapse\">\n                    展开 <i class=\"el-icon-arrow-down\"></i>\n                  </template>\n                  <template v-else>\n                    收起 <i class=\"el-icon-arrow-up\"></i>\n                  </template>\n                </a>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n      <div class=\"btn_bt\">\n        <el-button class=\"mr10\" size=\"small\" @click=\"onSend\" type=\"primary\" v-hasPermi=\"['admin:coupon:user:receive']\">发送优惠券</el-button>\n        <el-button class=\"mr10\" size=\"small\" @click=\"setBatch('group')\">批量设置分组</el-button>\n        <el-button class=\"mr10\" size=\"small\" @click=\"setBatch('label')\">批量设置标签</el-button>\n      </div>\n    </div>\n    <el-table\n      ref=\"table\"\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      @selection-change=\"onSelectTab\"\n      highlight-current-row\n      >\n      <el-table-column type=\"expand\">\n        <template slot-scope=\"props\">\n          <el-form label-position=\"left\" inline class=\"demo-table-expand\">\n            <el-form-item label=\"身份：\">\n              <span>{{ props.row.isPromoter | filterIsPromoter }}</span>\n            </el-form-item>\n            <el-form-item label=\"首次访问：\">\n              <span>{{ props.row.createTime | filterEmpty }}</span>\n            </el-form-item>\n            <el-form-item label=\"近次访问：\">\n              <span>{{ props.row.lastLoginTime | filterEmpty }}</span>\n            </el-form-item>\n            <el-form-item label=\"手机号：\">\n              <span>{{ props.row.phone | filterEmpty }}</span>\n            </el-form-item>\n            <el-form-item label=\"标签：\">\n              <span>{{ props.row.tagName | filterEmpty }}</span>\n            </el-form-item>\n            <el-form-item label=\"地址：\">\n              <span>{{ props.row.addres | filterEmpty }}</span>\n            </el-form-item>\n            <el-form-item label=\"备注：\" style=\"width: 100%;display: flex;margin-right: 10px;\">\n              <span>{{ props.row.mark  | filterEmpty}}</span>\n            </el-form-item>\n          </el-form>\n        </template>\n      </el-table-column>\n      <el-table-column\n        type=\"selection\"\n        width=\"55\">\n      </el-table-column>\n      <el-table-column\n        prop=\"uid\"\n        label=\"ID\"\n        min-width=\"80\"\n      />\n      <el-table-column \n      label=\"头像\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"姓名\"\n        min-width=\"160\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.nickname | filterEmpty  }} | {{scope.row.sex | sexFilter}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.center.userLevel')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.level | levelFilter | filterEmpty}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"groupName\"\n        label=\"分组\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"spreadNickname\"\n        label=\"推荐人\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        label=\"手机号\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.phone | filterEmpty}}</span>\n        </template>\n      </el-table-column>\n      <!--<el-table-column-->\n        <!--label=\"用户类型\"-->\n        <!--min-width=\"100\"-->\n      <!--&gt;-->\n        <!--<template slot-scope=\"scope\">-->\n          <!--<span>{{scope.row.userType | typeFilter}}</span>-->\n        <!--</template>-->\n      <!--</el-table-column>-->\n      <el-table-column\n        prop=\"nowMoney\"\n        label=\"余额\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"integral\"\n        label=\"积分\"\n        min-width=\"100\"\n      />\n      <el-table-column label=\"操作\" min-width=\"130\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" @click=\"editUser(scope.row.uid)\" size=\"small\" v-hasPermi=\"['admin:user:infobycondition']\">编辑</el-button>\n          <el-dropdown trigger=\"click\">\n            <span class=\"el-dropdown-link\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\" />\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item @click.native=\"onDetails(scope.row.uid)\" v-if=\"checkPermi(['admin:user:topdetail'])\">账户详情</el-dropdown-item>\n              <el-dropdown-item @click.native=\"editPoint(scope.row.uid)\" v-if=\"checkPermi(['admin:user:operate:founds'])\">积分余额</el-dropdown-item>\n              <el-dropdown-item @click.native=\"setBatch('group',scope.row)\" v-if=\"checkPermi(['admin:user:group'])\">设置分组</el-dropdown-item>\n              <el-dropdown-item @click.native=\"setBatch('label',scope.row)\" v-if=\"checkPermi(['admin:user:tag'])\">设置标签</el-dropdown-item>\n              <el-dropdown-item @click.native=\"setPhone(scope.row)\" v-if=\"checkPermi(['admin:user:update:phone'])\">修改手机号</el-dropdown-item>\n              <el-dropdown-item @click.native=\"onLevel(scope.row.uid,scope.row.level)\" v-if=\"checkPermi(['admin:user:update:level'])\">{{ $t('user.center.userLevel') }}</el-dropdown-item>\n              <el-dropdown-item @click.native=\"setExtension(scope.row)\"  v-if=\"checkPermi(['admin:user:update:spread'])\">修改上级推广人</el-dropdown-item>\n              <el-dropdown-item @click.native=\"clearSpread(scope.row)\" v-if=\"scope.row.spreadUid && scope.row.spreadUid>0 && checkPermi(['admin:retail:spread:clean'])\">清除上级推广人</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n      </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[15, 30, 45, 60]\"\n        :page-size=\"userFrom.limit\"\n        :current-page=\"userFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n  <!--修改推广人-->\n  <el-dialog\n    title=\"修改推广人\"\n    :visible.sync=\"extensionVisible\"\n    width=\"500px\"\n    :before-close=\"handleCloseExtension\">\n    <el-form class=\"formExtension mt20\" ref=\"formExtension\" :model=\"formExtension\" :rules=\"ruleInline\" label-width=\"120px\"\n             @submit.native.prevent v-loading=\"loading\">\n      <el-form-item label=\"用户头像：\" prop=\"image\">\n        <div class=\"upLoadPicBox\" @click=\"modalPicTap\">\n          <div v-if=\"formExtension.image\" class=\"pictrue\"><img :src=\"formExtension.image\"></div>\n          <div v-else class=\"upLoad\">\n            <i class=\"el-icon-camera cameraIconfont\"/>\n          </div>\n        </div>\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"onSubExtension('formExtension')\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!--用户列表-->\n  <el-dialog\n    title=\"用户列表\"\n    :visible.sync=\"userVisible\"\n    width=\"900px\">\n    <user-list v-if=\"userVisible\" @getTemplateRow=\"getTemplateRow\"></user-list>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"userVisible = false\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"userVisible = false\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!--批量设置-->\n  <el-dialog\n    title=\"设置\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n    <el-form :model=\"dynamicValidateForm\" ref=\"dynamicValidateForm\" label-width=\"100px\" class=\"demo-dynamic\" v-loading=\"loading\">\n      <el-form-item\n        prop=\"groupId\"\n        label=\"用户分组\"\n        :rules=\"[{ required: true, message: '请选择用户分组', trigger: 'change' }]\"\n        v-if=\"batchName ==='group'\"\n        key=\"1\"\n      >\n        <el-select v-model=\"dynamicValidateForm.groupId\" placeholder=\"请选择分组\" style=\"width: 80%\" filterable>\n          <el-option :value=\"item.id\" v-for=\"(item, index) in groupList\" :key=\"index\" :label=\"item.groupName\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item\n        prop=\"groupId\"\n        label=\"用户标签\"\n        :rules=\"[{ required: true, message: '请选择用户标签', trigger: 'change' }]\"\n        v-else\n      >\n        <el-select v-model=\"dynamicValidateForm.groupId\" placeholder=\"请选择标签\" style=\"width: 80%\" filterable>\n          <el-option :value=\"item.id\" v-for=\"(item, index) in labelLists\" :key=\"index\" :label=\"item.name\"></el-option>\n        </el-select>\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"submitForm('dynamicValidateForm')\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!--编辑-->\n  <el-dialog\n    title=\"编辑\"\n    :visible.sync=\"visible\"\n    width=\"600px\"\n  >\n    <edit-from v-if=\"visible\" :uid=\"uid\" @resetForm=\"resetForm\"></edit-from>\n  </el-dialog>\n  <!--积分余额-->\n  <el-dialog\n    title=\"积分余额\"\n    :visible.sync=\"VisiblePoint\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n    :before-close=\"handlePointClose\">\n    <el-form :model=\"PointValidateForm\" ref=\"PointValidateForm\" label-width=\"100px\" class=\"demo-dynamic\" v-loading=\"loadingPoint\">\n      <el-form-item\n        label=\"修改余额\"\n        required\n      >\n          <el-radio-group v-model=\"PointValidateForm.moneyType\">\n            <el-radio :label=\"1\">增加</el-radio>\n            <el-radio :label=\"2\">减少</el-radio>\n          </el-radio-group>\n      </el-form-item>\n      <el-form-item\n        label=\"余额\"\n        required\n      >\n        <el-input-number type=\"text\" v-model=\"PointValidateForm.moneyValue\" :precision=\"2\" :step=\"0.1\" :min=\"0\" :max=\"999999\"></el-input-number>\n      </el-form-item>\n      <el-form-item\n        label=\"修改积分\"\n        required\n      >\n        <el-radio-group v-model=\"PointValidateForm.integralType\">\n          <el-radio :label=\"1\">增加</el-radio>\n          <el-radio :label=\"2\">减少</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item\n        label=\"积分\"\n        required\n      >\n        <el-input-number type=\"text\" step-strictly v-model=\"PointValidateForm.integralValue\" :min=\"0\" :max=\"999999\"></el-input-number>\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handlePointClose\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"loadingBtn\" @click=\"submitPointForm('PointValidateForm')\">确 定</el-button>\n    </span>\n  </el-dialog>\n  <!--账户详情-->\n  <el-dialog\n    title=\"用户详情\"\n    :visible.sync=\"Visible\"\n    width=\"1100px\"\n    v-if=\"uid\"\n    :before-close=\"Close\">\n    <user-details ref=\"userDetails\" :uid=\"uid\" v-if=\"Visible\"></user-details>\n  </el-dialog>\n  <!-- 用户等级 -->\n  <el-dialog\n    title=\"设置\"\n    :visible.sync=\"levelVisible\"\n    width=\"600px\"\n    :before-close=\"Close\">\n    <level-edit :levelInfo=\"levelInfo\" :levelList=\"levelList\"></level-edit>\n  </el-dialog>\n</div>\n", null]}