{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue?vue&type=template&id=a9ba4704&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\WriteOff.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div v-show=\"iShidden === false\">\n  <div class=\"WriteOff\">\n    <div class=\"pictrue\"><img :src=\"orderInfo.storeOrderInfoVos[0].info.image\" /></div>\n    <div class=\"num acea-row row-center-wrapper\">\n      {{ orderInfo.orderId }}\n      <div class=\"views\" @click=\"toDetail(orderInfo)\">\n        查看<span class=\"iconfont icon-jiantou views-jian\"></span>\n      </div>\n    </div>\n    <div class=\"tip\">确定要核销此订单吗？</div>\n    <div class=\"sure\" @click=\"confirm\">确定核销</div>\n    <div class=\"sure cancel\" @click=\"cancel\">取消</div>\n  </div>\n  <div class=\"maskModel\" @touchmove.prevent></div>\n</div>\n", null]}