package com.genco.common.enums;

import com.alibaba.druid.util.StringUtils;
import lombok.Getter;

@Getter
public enum ProductChannelEnum {

    /**
     * tiktok
     */
    TIKTOK("TikTok", "0"),

    /**
     * shopee
     */
    SHOPEE("shopee", "1"),

    /**
     * unknow
     */
    UNKNOWN("unknow", "-1");

    private final String code;

    private final String value;

    ProductChannelEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public ProductChannelEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProductChannelEnum productChannelEnum : ProductChannelEnum.values()) {
            if (StringUtils.equals(productChannelEnum.getCode(), code)) {
                return productChannelEnum;
            }
        }
        return null;
    }
}
