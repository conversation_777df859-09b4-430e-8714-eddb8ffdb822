{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\i18n.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\i18n.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["import Vue from 'vue';\nimport VueI18n from 'vue-i18n';\nimport zhCN from './lang/zh-CN';\nimport en from './lang/en';\nimport id from './lang/id';\nVue.use(VueI18n);\nvar messages = {\n  'zh-CN': zhCN,\n  en: en,\n  id: id\n};\nvar i18n = new VueI18n({\n  locale: localStorage.getItem('locale') || 'zh-CN',\n  fallbackLocale: 'zh-CN',\n  messages: messages\n});\nexport default i18n;", null]}