{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {required, num} from \"@/utils/validate\";\nimport {validatorDefaultCatch} from \"@/libs/dialog\";\nimport { orderMarkApi, updatePriceApi, orderRefund<PERSON>pi } from '@/api/order';\nexport default {\n  name: \"PriceChange\",\n  components: {},\n  props: {\n    change: Boolean,\n    orderInfo: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: Number,\n      default: 0\n    }\n  },\n  data: function () {\n    return {\n      focus: false,\n      price: 0,\n      refundPrice: 0,\n      remark: \"\",\n      reason: ''\n    };\n  },\n  watch: {\n    orderInfo: function () {\n      this.price = this.orderInfo.payPrice;\n      this.refundPrice = this.orderInfo.payPrice;\n      this.remark = this.orderInfo.remark;\n    }\n  },\n  created() {\n    import('@/assets/js/media_750')\n  },\n  methods: {\n    priceChange: function () {\n      this.focus = true;\n    },\n    close: function () {\n      this.price = this.orderInfo.payPrice;\n      this.$emit(\"closechange\", false);\n    },\n    save() {\n      if(this.status === 3) {\n        this.refuse();\n      }else{\n        this.savePrice({\n          price: this.price,\n          refundPrice: this.refundPrice,\n          type: 1,\n          remark: this.remark,\n          orderId: this.orderInfo.orderId\n        })\n      }\n    },\n    async savePrice(opt) {\n      let that = this,\n        data = {},\n        price = opt.price,\n        refundPrice = opt.refundPrice,\n        refundStatus = that.orderInfo.refundStatus,\n        remark = opt.remark;\n      if (that.status == 0 && refundStatus === 0) {\n        try {\n          await this.$validator({\n            price: [\n              required(required.message(\"金额\")),\n              num(num.message(\"金额\"))\n            ]\n          }).validate({price});\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.payPrice = price;\n        data.orderNo = opt.orderId;\n        updatePriceApi(data).then(() => {\n          // that.change = false;\n          this.$emit(\"closechange\", false);\n          that.$dialog.success(\"改价成功\");\n          // that.$emit('init');\n          // that.init();\n        }).catch((error) => {\n          that.$dialog.error(error.message);\n        });\n      } else if (that.status == 2 && refundStatus === 1) {\n        try {\n          await this.$validator({\n            refundPrice: [\n              required(required.message(\"金额\")),\n              num(num.message(\"金额\"))\n            ]\n          }).validate({refundPrice});\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.amount = refundPrice;\n        data.type = opt.type;\n        data.orderNo = opt.orderId;\n        orderRefundApi(data).then(\n          res => {\n            this.$emit(\"closechange\", false);\n            // that.change = false;\n            that.$dialog.success('退款成功');\n            // that.init();\n            // that.$emit('init');\n          },\n          err => {\n            this.$emit(\"closechange\", false);\n            that.$dialog.error(err.message);\n          }\n        );\n      } else {\n        try {\n          await this.$validator({\n            remark: [required(required.message(\"备注\"))]\n          }).validate({remark});\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.mark = remark;\n        data.orderNo = opt.orderId;\n        orderMarkApi(data).then(\n          res => {\n            this.$emit(\"closechange\", false);\n            // that.change = false;\n            that.$dialog.success('提交成功');\n            // that.$emit('init');\n            // that.init();\n          },\n          err => {\n            this.$emit(\"closechange\", false);\n            // that.change = false;\n            that.$dialog.error(err.message);\n          }\n        );\n      }\n    },\n    async refuse() {\n      let reason= this.reason;\n      try {\n        await this.$validator({\n          reason: [required(required.message(\"备注\"))]\n        }).validate({reason});\n      } catch (e) {\n        return validatorDefaultCatch(e);\n      }\n      this.$emit(\"getRefuse\", this.orderInfo.orderId, reason);\n    }\n  }\n};\n", null]}