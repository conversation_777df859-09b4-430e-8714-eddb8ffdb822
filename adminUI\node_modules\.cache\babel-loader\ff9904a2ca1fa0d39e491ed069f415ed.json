{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import { getAdminOrderDelivery, setAdminOrderDelivery } from \"../../api/admin\";\nimport { orderSendApi, orderDetailApi } from '@/api/order';\nimport { expressList } from '@/api/logistics';\nimport { required, num } from \"@/utils/validate\";\nimport { validatorDefaultCatch } from \"@/libs/dialog\";\nexport default {\n  name: \"GoodsDeliver\",\n  components: {},\n  props: {},\n  data: function data() {\n    return {\n      types: [{\n        type: \"1\",\n        title: \"发货\"\n      }, {\n        type: \"2\",\n        title: \"送货\"\n      }, {\n        type: \"3\",\n        title: \"无需发货\"\n      }],\n      active: 0,\n      orderId: \"\",\n      delivery: {},\n      express: [],\n      type: \"1\",\n      deliveryName: \"\",\n      expressCode: \"\",\n      expressNumber: '',\n      deliveryTel: \"\"\n    };\n  },\n  watch: {\n    \"$route.params.oid\": function $routeParamsOid(newVal) {\n      var that = this;\n      if (newVal != undefined) {\n        that.orderId = newVal;\n        that.getIndex();\n      }\n    }\n  },\n  created: function created() {\n    import('@/assets/js/media_750');\n  },\n  mounted: function mounted() {\n    this.orderId = this.$route.params.oid;\n    this.getIndex();\n    this.getLogistics();\n  },\n  methods: {\n    changeType: function changeType(item, index) {\n      this.active = index;\n      this.type = item.type;\n      this.deliveryName = \"\";\n      this.deliveryTel = \"\";\n      this.expressCode = \"\";\n      this.expressNumber = \"\";\n    },\n    getIndex: function getIndex() {\n      var _this = this;\n      orderDetailApi({\n        orderNo: this.orderId\n      }).then(function (res) {\n        _this.delivery = res;\n      }).catch(function (error) {\n        _this.$dialog.error(error.message);\n      });\n    },\n    getLogistics: function getLogistics() {\n      var _this2 = this;\n      expressList({\n        page: 1,\n        limit: 999,\n        isShow: 1\n      }).then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                _this2.express = res.list;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    saveInfo: function () {\n      var _saveInfo = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var that, deliveryName, deliveryTel, save, _t, _t2;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.p = _context2.n) {\n            case 0:\n              // type: '1',\n              //   expressRecordType: '1',\n              //   expressId: '',\n              //   expressCode: '',\n              //   deliveryName: '',\n              //   deliveryTel: '',\n              //   // expressName: '',\n              //   expressNumber: '',\n              //   expressTempId: '',\n              //   toAddr: '',\n              //   toName: '',\n              //   toTel: '',\n              //   orderNo: ''\n              that = this, deliveryName = that.deliveryName, deliveryTel = that.deliveryTel, save = {};\n              save.type = that.type;\n              save.orderNo = that.orderId;\n              _t = that.type;\n              _context2.n = _t === \"1\" ? 1 : _t === \"2\" ? 4 : _t === \"3\" ? 8 : 9;\n              break;\n            case 1:\n              if (that.expressCode) {\n                _context2.n = 2;\n                break;\n              }\n              return _context2.a(2, that.$dialog.error('请输入快递公司'));\n            case 2:\n              if (that.expressNumber) {\n                _context2.n = 3;\n                break;\n              }\n              return _context2.a(2, that.$dialog.error('请输入快递单号'));\n            case 3:\n              save.expressNumber = that.expressNumber;\n              save.expressRecordType = 1;\n              save.expressCode = that.expressCode;\n              that.setInfo(save);\n              return _context2.a(3, 9);\n            case 4:\n              _context2.p = 4;\n              _context2.n = 5;\n              return this.$validator({\n                deliveryName: [required(required.message(\"发货人姓名\"))],\n                deliveryTel: [required(required.message(\"发货人电话\"))]\n              }).validate({\n                deliveryName: deliveryName,\n                deliveryTel: deliveryTel\n              });\n            case 5:\n              _context2.n = 7;\n              break;\n            case 6:\n              _context2.p = 6;\n              _t2 = _context2.v;\n              return _context2.a(2, validatorDefaultCatch(_t2));\n            case 7:\n              save.deliveryName = deliveryName;\n              save.deliveryTel = deliveryTel;\n              that.setInfo(save);\n              return _context2.a(3, 9);\n            case 8:\n              that.setInfo(save);\n              return _context2.a(3, 9);\n            case 9:\n              return _context2.a(2);\n          }\n        }, _callee2, this, [[4, 6]]);\n      }));\n      function saveInfo() {\n        return _saveInfo.apply(this, arguments);\n      }\n      return saveInfo;\n    }(),\n    setInfo: function setInfo(item) {\n      var that = this;\n      orderSendApi(item).then(function (res) {\n        that.$dialog.success('发送货成功');\n        that.$router.go(-1);\n      }, function (error) {\n        that.$dialog.error(error.message);\n      });\n    }\n  }\n};", null]}