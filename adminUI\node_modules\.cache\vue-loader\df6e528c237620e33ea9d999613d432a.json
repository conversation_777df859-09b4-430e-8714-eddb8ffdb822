{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import { getAdminOrderDelivery, setAdminOrderDelivery } from \"../../api/admin\";\nimport { orderSendApi, orderDetailApi } from '@/api/order';\nimport { expressList } from '@/api/logistics';\nimport { required, num } from \"@/utils/validate\";\nimport { validatorDefaultCatch } from \"@/libs/dialog\";\nexport default {\n  name: \"GoodsDeliver\",\n  components: {},\n  props: {},\n  data: function() {\n    return {\n      types: [\n        {\n          type: \"1\",\n          title: \"发货\"\n        },\n        {\n          type: \"2\",\n          title: \"送货\"\n        },\n        {\n          type: \"3\",\n          title: \"无需发货\"\n        }\n      ],\n      active: 0,\n      orderId: \"\",\n      delivery: {},\n      express: [],\n      type: \"1\",\n      deliveryName: \"\",\n      expressCode: \"\",\n      expressNumber: '',\n      deliveryTel: \"\"\n    };\n  },\n  watch: {\n    \"$route.params.oid\": function(newVal) {\n      let that = this;\n      if (newVal != undefined) {\n        that.orderId = newVal;\n        that.getIndex();\n      }\n    }\n  },\n  created() {\n    import('@/assets/js/media_750')\n  },\n  mounted: function() {\n    this.orderId = this.$route.params.oid;\n    this.getIndex();\n    this.getLogistics();\n  },\n  methods: {\n    changeType(item, index) {\n      this.active = index;\n      this.type = item.type;\n      this.deliveryName = \"\";\n      this.deliveryTel = \"\";\n      this.expressCode = \"\";\n      this.expressNumber = \"\";\n    },\n    getIndex() {\n      orderDetailApi({ orderNo: this.orderId }).then(res => {\n        this.delivery = res\n      }).catch((error)=>{\n        this.$dialog.error(error.message);\n      })\n    },\n    getLogistics() {\n      expressList({ page: 1, limit: 999, isShow:1 }).then(async res => {\n        this.express = res.list\n      })\n    },\n    async saveInfo() {\n      // type: '1',\n      //   expressRecordType: '1',\n      //   expressId: '',\n      //   expressCode: '',\n      //   deliveryName: '',\n      //   deliveryTel: '',\n      //   // expressName: '',\n      //   expressNumber: '',\n      //   expressTempId: '',\n      //   toAddr: '',\n      //   toName: '',\n      //   toTel: '',\n      //   orderNo: ''\n\n\n      let that = this,\n        deliveryName = that.deliveryName,\n        deliveryTel = that.deliveryTel,\n        save = {};\n      save.type = that.type;\n      save.orderNo = that.orderId;\n\n      switch (that.type) {\n        case \"1\":\n          // try {\n          //   await this.$validator({\n          //     expressId: [required(required.message(\"快递公司\"))],\n          //     expressCode: [required(required.message(\"快递单号\"))]\n          //   }).validate({ expressId, expressCode });\n          // } catch (e) {\n          //   return validatorDefaultCatch(e);\n          // }\n          if( !that.expressCode ) return that.$dialog.error('请输入快递公司');\n          if( !that.expressNumber ) return that.$dialog.error('请输入快递单号');\n          save.expressNumber = that.expressNumber;\n          save.expressRecordType = 1;\n          save.expressCode = that.expressCode;\n          that.setInfo(save);\n          break;\n        case \"2\":\n          try {\n            await this.$validator({\n              deliveryName: [required(required.message(\"发货人姓名\"))],\n              deliveryTel: [required(required.message(\"发货人电话\"))]\n            }).validate({ deliveryName, deliveryTel });\n          } catch (e) {\n            return validatorDefaultCatch(e);\n          }\n          save.deliveryName = deliveryName;\n          save.deliveryTel = deliveryTel;\n          that.setInfo(save);\n          break;\n        case \"3\":\n          that.setInfo(save);\n          break;\n      }\n    },\n    setInfo: function(item) {\n      let that = this;\n      orderSendApi(item).then(\n        res => {\n          that.$dialog.success('发送货成功');\n          that.$router.go(-1);\n        },\n        error => {\n          that.$dialog.error(error.message);\n        }\n      );\n    }\n  }\n};\n", null]}