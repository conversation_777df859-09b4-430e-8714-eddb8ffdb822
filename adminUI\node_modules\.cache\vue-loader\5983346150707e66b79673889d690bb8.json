{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue?vue&type=template&id=24873e6a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\record\\charge\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\" inline>\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <el-form-item label=\"用户id：\">\n            <el-input v-model=\"tableFrom.uid\" placeholder=\"用户id\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"订单号：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"订单号\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n  </el-card>\n  <div class=\"mt20\">\n    <cards-data :card-lists=\"cardLists\" v-if=\"checkPermi(['admin:recharge:balance'])\" />\n  </div>\n  <el-card class=\"box-card\">\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"uid\"\n        label=\"UID\"\n        width=\"60\"\n      />\n      <el-table-column\n        label=\"头像\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"nickname\"\n        label=\"用户昵称\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        prop=\"orderId\"\n        label=\"订单号\"\n        min-width=\"180\"\n      />\n      <el-table-column\n        sortable\n        label=\"支付金额\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.price - b.price}\"\n        prop=\"price\"\n      />\n      <el-table-column\n        sortable\n        label=\"赠送金额\"\n        min-width=\"120\"\n        prop=\"givePrice\"\n        :sort-method=\"(a,b)=>{return a.givePrice - b.givePrice}\"\n      />\n      <el-table-column\n        label=\"充值类型\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.rechargeType | rechargeTypeFilter }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"支付时间\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <span class=\"spBlock\">{{ scope.row.payTime || '无' }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n  <!--退款-->\n  <el-dialog\n    title=\"退款\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n    <zb-parser\n      v-if=\"dialogVisible\"\n      :form-id=\"130\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n  </el-dialog>\n</div>\n", null]}