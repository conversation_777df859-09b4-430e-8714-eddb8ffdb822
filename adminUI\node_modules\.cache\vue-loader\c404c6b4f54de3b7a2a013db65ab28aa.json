{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderCancellation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderCancellation\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport WriteOff from \"../components/WriteOff\";\nimport { wechatEvevt } from \"@/libs/wechat\";\n// import { orderVerific } from \"@api/order\";\nimport { writeUpdateApi, writeConfirmApi } from '@/api/order'\nconst NAME = \"OrderCancellation\";\n\nexport default {\n  name: NAME,\n  components: {\n    WriteOff\n  },\n  props: {},\n  data: function() {\n    return {\n      isWeixin: this.$wechat.isWeixin(),\n      iShidden: true,\n      orderInfo: null,\n      verify_code: \"\"\n    };\n  },\n  created() {\n    import('@/assets/js/media_750')\n  },\n  methods: {\n    cancel: function(res) {\n      this.iShidden = res;\n    },\n    confirm: function() {\n      writeUpdateApi(this.verify_code)\n        .then(res => {\n          this.iShidden = true;\n          this.verify_code = \"\";\n          this.$dialog.success(res.msg);\n        })\n        .catch(res => {\n          this.$dialog.error(res.msg);\n        });\n    },\n    storeCancellation: function() {\n      let ref = /[0-9]{10}/;\n      if (!this.verify_code) return this.$dialog.error(\"请输入核销码\");\n      if (!ref.test(this.verify_code))\n      return this.$dialog.error(\"请输入正确的核销码\");\n      this.$dialog.loading.open(\"查询中\");\n      writeConfirmApi(this.verify_code)\n        .then(res => {\n          this.$dialog.loading.close();\n          this.orderInfo = res;\n          this.iShidden = false;\n        })\n        .catch(res => {\n          this.$dialog.loading.close();\n          this.verify_code = \"\";\n          return this.$dialog.error(res.message);\n        });\n    },\n    openQRCode: function() {\n      let that = this;\n      wechatEvevt(\"scanQRCode\", {\n        needResult: 1,\n        scanType: [\"qrCode\", \"barCode\"]\n      })\n        .then(res => {\n          if (res.resultStr) {\n            that.verify_code = res.resultStr;\n            that.storeCancellation();\n          } else that.$dialog.error(\"没有扫描到什么！\");\n        })\n        .catch(res => {\n          if (res.is_ready) {\n            res.wx.scanQRCode({\n              needResult: 1,\n              scanType: [\"qrCode\", \"barCode\"],\n              success: function(res) {\n                that.verify_code = res.resultStr;\n                that.storeCancellation();\n              },\n              fail: function(res) {\n                if (res.errMsg == \"scanQRCode:permission denied\") {\n                  that.$dialog.error(\"没有权限\");\n                }\n              }\n            });\n          }\n        });\n    }\n  }\n};\n", null]}