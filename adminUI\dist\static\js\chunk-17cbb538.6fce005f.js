(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-17cbb538"],{"1c1a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"divBox"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"container"},[a("el-form",{ref:"form",attrs:{inline:"",model:t.artFrom,size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"提货点名称："}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"请选择",clearable:""},on:{change:t.search},model:{value:t.artFrom.storeId,callback:function(e){t.$set(t.artFrom,"storeId",e)},expression:"artFrom.storeId"}},t._l(t.storeSelectList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1)],1),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:staff:save"],expression:"['admin:system:staff:save']"}],attrs:{type:"primary",size:"small"},on:{click:t.add}},[t._v("添加核销员")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[a("el-table-column",{attrs:{prop:"id",label:"ID",sortable:"",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"staffName",label:"核销员名称","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"avatar",label:"账号","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"phone",label:"手机号码","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"systemStore.detailedAddress",label:"所属提货点","min-width":"200"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"添加时间","min-width":"180"}}),t._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;e.index;return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:staff:info"],expression:"['admin:system:staff:info']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.edit(r.id)}}},[t._v("编辑")]),t._v(" "),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:staff:delete"],expression:"['admin:system:staff:delete']"}],attrs:{type:"text",size:"small"},on:{click:function(e){return t.storeDelete(r.id)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("el-pagination",{staticClass:"mt20",attrs:{"current-page":t.artFrom.page,"page-sizes":[20,40,60,100],"page-size":t.artFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.sizeChange,"current-change":t.pageChange}}),t._v(" "),a("add-clerk-list",{ref:"template",attrs:{storeSelectList:t.storeSelectList},on:{tableList:t.tableList}})],1)],1)},n=[],i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.id?"修改核销员":"添加核销员",visible:t.dialogFormVisible,width:"750px"},on:{"update:visible":function(e){t.dialogFormVisible=e},close:t.cancel},model:{value:t.dialogFormVisible,callback:function(e){t.dialogFormVisible=e},expression:"dialogFormVisible"}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"150px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"管理员：",prop:"uid"}},[a("span",{domProps:{textContent:t._s(t.ruleForm.avatar)}}),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.upImg}},[t._v("选择管理员")])],1),t._v(" "),a("el-form-item",{attrs:{label:"所属提货点：",prop:"storeId"}},[a("el-select",{staticStyle:{width:"50%"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.ruleForm.storeId,callback:function(e){t.$set(t.ruleForm,"storeId",e)},expression:"ruleForm.storeId"}},t._l(t.storeSelectList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"核销员名称："}},[a("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入核销员名称"},model:{value:t.ruleForm.staffName,callback:function(e){t.$set(t.ruleForm,"staffName",e)},expression:"ruleForm.staffName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"手机号码："}},[a("el-input",{staticClass:"dialogWidth",attrs:{placeholder:"请输入手机号码"},model:{value:t.ruleForm.phone,callback:function(e){t.$set(t.ruleForm,"phone",e)},expression:"ruleForm.phone"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t._v(" "),t.id?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:staff:update"],expression:"['admin:system:staff:update']"}],attrs:{type:"primary"},on:{click:function(e){return t.editForm("ruleForm")}}},[t._v("修改")]):a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:staff:save"],expression:"['admin:system:staff:save']"}],attrs:{type:"primary"},on:{click:function(e){return t.submitForm("ruleForm")}}},[t._v("提交")])],1),t._v(" "),a("customer-info",{ref:"customer",on:{upImgUid:t.upImgUid}})],1)},o=[],s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:"请选择管理员","append-to-body":"",visible:t.dialogFormVisible,width:"1200px"},on:{"update:visible":function(e){t.dialogFormVisible=e},close:t.cancel},model:{value:t.dialogFormVisible,callback:function(e){t.dialogFormVisible=e},expression:"dialogFormVisible"}},[a("el-form",{ref:"form",attrs:{inline:"",model:t.artFrom}},[a("el-form-item",{attrs:{label:"身份："}},[a("el-select",{staticClass:"selWidth",attrs:{placeholder:"请输入身份",clearable:""},model:{value:t.artFrom.roles,callback:function(e){t.$set(t.artFrom,"roles",e)},expression:"artFrom.roles"}},t._l(t.roleList.list,(function(t){return a("el-option",{key:t.id,attrs:{label:t.roleName,value:t.id}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"姓名："}},[a("el-input",{staticClass:"selWidth",attrs:{size:"small",placeholder:"请输入姓名或者账号"},model:{value:t.artFrom.realName,callback:function(e){t.$set(t.artFrom,"realName",e)},expression:"artFrom.realName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.search},slot:"append"},[t._v("搜索")])],1)],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{"row-style":{height:"50px"},data:t.tableData,"max-height":"400px",size:"mini"}},[a("el-table-column",{attrs:{label:"",width:"55"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;e.index;return[a("el-radio",{attrs:{label:r.uid},nativeOn:{change:function(e){return t.getTemplateRow(r)}},model:{value:t.templateRadio,callback:function(e){t.templateRadio=e},expression:"templateRadio"}},[t._v(" ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID",sortable:"",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{prop:"realName",label:"姓名","min-Width":"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"account",label:"账号","min-Width":"120"}}),t._v(" "),a("el-table-column",{attrs:{label:"身份",prop:"realName","min-width":"230"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.roleNames.split(","),(function(e,r){return a("el-tag",{key:r,staticClass:"mr5",attrs:{size:"small",type:"info"}},[t._v(t._s(e))])}))}}])}),t._v(" "),a("el-table-column",{attrs:{label:"最后登录时间",prop:"lastTime","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.lastTime)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"最后登录IP",prop:"lastIp","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterEmpty")(e.row.lastIp)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterShowOrHide")(e.row.status)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"删除标记",prop:"status","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("filterYesOrNo")(e.row.isDel)))])]}}])})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{staticClass:"mt20",attrs:{"current-page":t.artFrom.page,"page-sizes":[20,40,60,100],"page-size":t.artFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.sizeChange,"current-change":t.pageChange}})],1)],1)},l=[],u=a("2eb3"),c=a("cc5e"),m={name:"index",data:function(){return{constants:this.$constants,loading:!1,templateRadio:"",dialogFormVisible:!1,tableData:[],artFrom:{page:1,limit:20,status:1,realName:"",roles:""},total:0,timeVal:"",roleList:[]}},created:function(){this.handleGetRoleList()},methods:{handleGetRoleList:function(){var t=this,e={page:1,limit:9999};c["d"](e).then((function(e){t.roleList=e}))},getTemplateRow:function(t){this.dialogFormVisible=!1,this.$emit("upImgUid",t)},tableList:function(){var t=this;this.loading=!0,u["c"](this.artFrom).then((function(e){t.tableData=e.list,t.total=e.total,t.loading=!1})).catch((function(){t.loading=!1}))},sizeChange:function(t){this.artFrom.limit=t,this.tableList()},pageChange:function(t){this.artFrom.page=t,this.tableList()},onchangeTime:function(t){this.artFrom.page=1,this.artFrom.data=null!==t?t.join(","):"",this.tableList()},search:function(){this.timeVal="",this.artFrom.page=1,this.tableList()},cancel:function(){this.artFrom={page:1,limit:20,data:"",realName:""},this.timeVal="",this.templateRadio=""}}},d=m,f=(a("7b84"),a("2877")),p=Object(f["a"])(d,s,l,!1,null,null,null),h=p.exports,b=a("6537"),v=a("02df"),g=a("61f7");function F(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,a="function"==typeof Symbol?Symbol:{},r=a.iterator||"@@iterator",n=a.toStringTag||"@@toStringTag";function i(a,r,n,i){var l=r&&r.prototype instanceof s?r:s,u=Object.create(l.prototype);return y(u,"_invoke",function(a,r,n){var i,s,l,u=0,c=n||[],m=!1,d={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,a){return i=e,s=0,l=t,d.n=a,o}};function f(a,r){for(s=a,l=r,e=0;!m&&u&&!n&&e<c.length;e++){var n,i=c[e],f=d.p,p=i[2];a>3?(n=p===r)&&(l=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=t):i[0]<=f&&((n=a<2&&f<i[1])?(s=0,d.v=r,d.n=i[1]):f<p&&(n=a<3||i[0]>r||r>p)&&(i[4]=a,i[5]=r,d.n=p,s=0))}if(n||a>1)return o;throw m=!0,r}return function(n,c,p){if(u>1)throw TypeError("Generator is already running");for(m&&1===c&&f(c,p),s=c,l=p;(e=s<2?t:l)||!m;){i||(s?s<3?(s>1&&(d.n=-1),f(s,l)):d.n=l:d.v=l);try{if(u=2,i){if(s||(n="next"),e=i[n]){if(!(e=e.call(i,l)))throw TypeError("iterator result is not an object");if(!e.done)return e;l=e.value,s<2&&(s=0)}else 1===s&&(e=i.return)&&e.call(i),s<2&&(l=TypeError("The iterator does not provide a '"+n+"' method"),s=1);i=t}else if((e=(m=d.n<0)?l:a.call(r,d))!==o)break}catch(e){i=t,s=1,l=e}finally{u=1}}return{value:e,done:m}}}(a,n,i),!0),u}var o={};function s(){}function l(){}function u(){}e=Object.getPrototypeOf;var c=[][r]?e(e([][r]())):(y(e={},r,(function(){return this})),e),m=u.prototype=s.prototype=Object.create(c);function d(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,u):(t.__proto__=u,y(t,n,"GeneratorFunction")),t.prototype=Object.create(m),t}return l.prototype=u,y(m,"constructor",u),y(u,"constructor",l),l.displayName="GeneratorFunction",y(u,n,"GeneratorFunction"),y(m),y(m,n,"Generator"),y(m,r,(function(){return this})),y(m,"toString",(function(){return"[object Generator]"})),(F=function(){return{w:i,m:d}})()}function y(t,e,a,r){var n=Object.defineProperty;try{n({},"",{})}catch(t){n=0}y=function(t,e,a,r){function i(e,a){y(t,e,(function(t){return this._invoke(e,a,t)}))}e?n?n(t,e,{value:a,enumerable:!r,configurable:!r,writable:!r}):t[e]=a:(i("next",0),i("throw",1),i("return",2))},y(t,e,a,r)}function _(t,e,a,r,n,i,o){try{var s=t[i](o),l=s.value}catch(t){return void a(t)}s.done?e(l):Promise.resolve(l).then(r,n)}function w(t){return function(){var e=this,a=arguments;return new Promise((function(r,n){var i=t.apply(e,a);function o(t){_(i,r,n,o,s,"next",t)}function s(t){_(i,r,n,o,s,"throw",t)}o(void 0)}))}}var O={name:"addClerk",components:{customerInfo:h},props:{storeSelectList:Array},data:function(){return{loading:!1,dialogFormVisible:!1,id:0,ruleForm:{phone:"",storeId:"",uid:"",avatar:""},name:"",rules:{uid:[{required:!0,message:"请选择管理员",trigger:"change"}],storeId:[{required:!0,message:"请选择提货点地址",trigger:"change"}]}}},created:function(){},mounted:function(){},methods:{upImgUid:function(t){this.ruleForm.avatar=t.account,this.ruleForm.uid=t.id},upImg:function(){this.$refs.customer.dialogFormVisible=!0,this.$refs.customer.tableList()},getInfo:function(t){var e=this;this.id=t,this.loading=!0,Object(b["j"])({id:t}).then((function(t){e.ruleForm=t,e.loading=!1})).catch((function(t){e.loading=!1}))},cancel:function(){this.dialogFormVisible=!1,this.clearFrom(),this.resetForm("ruleForm"),this.ruleForm.avatar="",this.id=0},clearFrom:function(){this.ruleForm.phone="",this.ruleForm.staffName=""},resetForm:function(t){this.$refs[t].resetFields()},submitForm:Object(g["a"])((function(t){var e=this;this.$refs[t].validate((function(a){if(!a)return!1;var r=e.ruleForm.phone;if(r&&!/^1[3456789]\d{9}$/.test(r))return e.$message.error("手机号格式不正确");Object(b["l"])(e.ruleForm).then(w(F().m((function a(){return F().w((function(a){while(1)switch(a.n){case 0:e.$message.success("提交成功"),e.dialogFormVisible=!1,e.$emit("tableList"),e.clearFrom(),e.resetForm(t),e.id=0,Object(v["b"])();case 1:return a.a(2)}}),a)}))))}))})),editForm:Object(g["a"])((function(t){var e=this;this.$refs[t].validate((function(a){if(!a)return!1;var r=e.ruleForm.phone;if(r&&!/^1[3456789]\d{9}$/.test(r))return e.$message.error("手机号格式不正确");Object(b["m"])(e.ruleForm).then(w(F().m((function a(){return F().w((function(a){while(1)switch(a.n){case 0:e.$message.success("编辑成功"),e.dialogFormVisible=!1,e.$emit("tableList"),e.clearFrom(),e.resetForm(t),e.id=0,Object(v["b"])();case 1:return a.a(2)}}),a)}))))}))}))}},j=O,k=Object(f["a"])(j,i,o,!1,null,"6fb94bd8",null),x=k.exports,N={name:"clerkList",components:{addClerkList:x},data:function(){return{storeSelectList:[],artFrom:{page:1,limit:20,storeId:""},loading:!1,tableData:[],total:0}},created:function(){this.tableList(),this.storeList()},methods:{onchangeIsShow:function(t,e){var a=this;Object(b["n"])({id:t,status:e}).then((function(){a.$message.success("操作成功"),a.tableList()})).catch((function(){row.isShow=!row.isShow}))},storeList:function(){var t=this,e={page:1,limit:9999,status:"1",keywords:""};Object(b["f"])(e).then((function(e){t.storeSelectList=e.list}))},tableList:function(){var t=this;t.loading=!0,Object(b["k"])(t.artFrom).then((function(e){t.loading=!1,t.tableData=e.list,t.total=e.total})).catch((function(e){t.$message.error(e.message)}))},pageChange:function(t){this.artFrom.page=t,this.tableList()},sizeChange:function(t){this.artFrom.limit=t,this.tableList()},search:function(){this.artFrom.page=1,this.tableList()},storeDelete:function(t){var e=this;e.$modalSure().then((function(){Object(b["i"])({id:t}).then((function(){e.$message.success("删除成功"),e.tableList()}))})).catch((function(t){e.$message.error(t.message)}))},add:function(){this.$refs.template.dialogFormVisible=!0},edit:function(t){this.$refs.template.dialogFormVisible=!0,this.$refs.template.getInfo(t)}}},L=N,S=Object(f["a"])(L,r,n,!1,null,"a84c322a",null);e["default"]=S.exports},"2eb3":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"l",(function(){return l})),a.d(e,"k",(function(){return u})),a.d(e,"i",(function(){return c})),a.d(e,"f",(function(){return m})),a.d(e,"g",(function(){return d})),a.d(e,"h",(function(){return f})),a.d(e,"j",(function(){return p}));var r=a("b775");function n(t){var e={id:t.id};return Object(r["a"])({url:"/admin/system/admin/delete",method:"GET",params:e})}function i(t){return Object(r["a"])({url:"/admin/system/admin/list",method:"GET",params:t})}function o(t){var e={account:t.account,level:t.level,pwd:t.pwd,realName:t.realName,roles:t.roles.join(","),status:t.status,phone:t.phone};return Object(r["a"])({url:"/admin/system/admin/save",method:"POST",data:e})}function s(t){var e={account:t.account,level:t.level,pwd:t.pwd,roles:t.roles,realName:t.realName,status:t.status,id:t.id,isDel:t.isDel};return Object(r["a"])({url:"/admin/system/admin/update",method:"POST",data:e})}function l(t){return Object(r["a"])({url:"/admin/system/admin/updateStatus",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:t})}function c(t){var e={menuType:t.menuType,name:t.name};return Object(r["a"])({url:"/admin/system/menu/list",method:"get",params:e})}function m(t){var e=t;return Object(r["a"])({url:"/admin/system/menu/add",method:"post",data:e})}function d(t){return Object(r["a"])({url:"/admin/system/menu/delete/".concat(t),method:"post"})}function f(t){return Object(r["a"])({url:"/admin/system/menu/info/".concat(t),method:"get"})}function p(t){var e=t;return Object(r["a"])({url:"/admin/system/menu/update",method:"post",data:e})}},"7b84":function(t,e,a){"use strict";a("fac4")},cc5e:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"f",(function(){return l})),a.d(e,"g",(function(){return u})),a.d(e,"e",(function(){return c}));var r=a("b775");function n(t){var e={level:t.level,roleName:t.roleName,status:t.status,rules:t.rules};return Object(r["a"])({url:"/admin/system/role/save",method:"POST",data:e})}function i(t){var e={id:t.id};return Object(r["a"])({url:"/admin/system/role/delete",method:"GET",params:e})}function o(t){return Object(r["a"])({url:"/admin/system/role/info/".concat(t),method:"GET"})}function s(t){var e={createTime:t.createTime,updateTime:t.updateTime,level:t.level,page:t.page,limit:t.limit,roleName:t.roleName,rules:t.rules,status:t.status};return Object(r["a"])({url:"/admin/system/role/list",method:"get",params:e})}function l(t){var e={id:t.id,roleName:t.roleName,rules:t.rules,status:t.status};return Object(r["a"])({url:"/admin/system/role/update",method:"post",params:{id:t.id},data:e})}function u(t){return Object(r["a"])({url:"/admin/system/role/updateStatus",method:"get",params:{id:t.id,status:t.status}})}function c(t){return Object(r["a"])({url:"/admin/system/menu/cache/tree",method:"get"})}},fac4:function(t,e,a){}}]);