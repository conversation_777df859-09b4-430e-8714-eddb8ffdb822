{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=style&index=0&id=37212a44&prod&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754138267094}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754138276757}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754138271537}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754138265892}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.icon-ul {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  li {\n    list-style-type: none;\n    text-align: center;\n    font-size: 14px;\n    display: inline-block;\n    width: 16.66%;\n    box-sizing: border-box;\n    height: 108px;\n    padding: 15px 6px 6px 6px;\n    cursor: pointer;\n    overflow: hidden;\n    &:hover {\n      background: #f2f2f2;\n    }\n    &.active-item{\n      background: #e1f3fb;\n      color: #7a6df0\n    }\n    > i {\n      font-size: 30px;\n      line-height: 50px;\n    }\n  }\n}\n.icon-dialog {\n  ::v-deep .el-dialog {\n    border-radius: 8px;\n    margin-bottom: 0;\n    margin-top: 4vh !important;\n    display: flex;\n    flex-direction: column;\n    max-height: 92vh;\n    overflow: hidden;\n    box-sizing: border-box;\n    .el-dialog__header {\n      padding-top: 14px;\n    }\n    .el-dialog__body {\n      margin: 0 20px 20px 20px;\n      padding: 0;\n      overflow: auto;\n    }\n  }\n}\n", null]}