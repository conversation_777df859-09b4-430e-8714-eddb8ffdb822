{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getToken } from '@/utils/auth';\nimport { replySaveApi, replyEditApi, replyInfoApi, replyListApi, keywordsInfoApi, replyUpdateApi } from '@/api/wxApi';\nimport { wechatUploadApi } from '@/api/systemSetting';\nimport { Debounce } from '@/utils/validate';\nexport default {\n  name: 'Index',\n  components: {},\n  data: function data() {\n    var _this2 = this;\n    var validateContent = function validateContent(rule, value, callback) {\n      if (_this2.formValidate.type === 'text') {\n        if (_this2.formValidate.contents.content === '') {\n          callback(new Error('请填写规则内容'));\n        } else {\n          callback();\n        }\n      }\n    };\n    var validateSrc = function validateSrc(rule, value, callback) {\n      if (_this2.formValidate.type === 'image' && _this2.formValidate.contents.mediaId === '') {\n        callback(new Error('请上传'));\n      } else {\n        callback();\n      }\n    };\n    var validateVal = function validateVal(rule, value, callback) {\n      if (_this2.labelarr.length === 0) {\n        callback(new Error('请输入后回车'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      loading: false,\n      visible: false,\n      grid: {\n        xl: 7,\n        lg: 12,\n        md: 10,\n        sm: 24,\n        xs: 24\n      },\n      delfromData: {},\n      isShow: false,\n      maxCols: 3,\n      scrollerHeight: '600',\n      contentTop: '130',\n      contentWidth: '98%',\n      modals: false,\n      val: '',\n      formatImg: ['jpg', 'jpeg', 'png', 'bmp', 'gif'],\n      formatVoice: ['mp3', 'wma', 'wav', 'amr'],\n      header: {},\n      formValidate: {\n        status: true,\n        type: '',\n        keywords: '',\n        contents: {\n          content: '',\n          articleData: {},\n          mediaId: '',\n          srcUrl: '',\n          articleId: null\n        },\n        id: null\n      },\n      ruleValidate: {\n        val: [{\n          required: true,\n          validator: validateVal,\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '请选择消息类型',\n          trigger: 'change'\n        }],\n        content: [{\n          required: true,\n          validator: validateContent,\n          trigger: 'blur'\n        }],\n        mediaId: [{\n          required: true,\n          validator: validateSrc,\n          trigger: 'change'\n        }]\n      },\n      labelarr: [],\n      myHeaders: {\n        'X-Token': getToken()\n      }\n    };\n  },\n  computed: {\n    fileUrl: function fileUrl() {\n      return https + \"/wechat/reply/upload/image\";\n    },\n    voiceUrl: function voiceUrl() {\n      return https + \"/wechat/reply/upload/voice\";\n    },\n    httpsURL: function httpsURL() {\n      return process.env.VUE_APP_BASE_API.replace('api/', '');\n    }\n  },\n  watch: {\n    $route: function $route(to, from) {\n      if (this.$route.params.id) {\n        // this.formValidate.keywords = this.$route.params.key\n        this.details();\n      } else {\n        // this.labelarr = []\n        // this.$refs['formValidate'].resetFields()\n      }\n    }\n  },\n  mounted: function mounted() {\n    if (this.$route.params.id) {\n      this.details();\n    }\n    if (this.$route.path.indexOf('keyword') === -1) {\n      this.followDetails();\n    }\n  },\n  methods: {\n    change: function change(e) {\n      this.$forceUpdate();\n    },\n    // 上传\n    handleUploadForm: function handleUploadForm(param) {\n      var _this3 = this;\n      var formData = new FormData();\n      formData.append('media', param.file);\n      var loading = this.$loading({\n        lock: true,\n        text: '上传中，请稍候...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      });\n      wechatUploadApi(formData, {\n        type: this.formValidate.type === 'image' ? 'image' : 'voice'\n      }).then(function (res) {\n        loading.close();\n        _this3.formValidate.contents.mediaId = res.mediaId;\n        _this3.formValidate.contents.srcUrl = res.url;\n        _this3.$message.success('上传成功');\n      }).catch(function () {\n        loading.close();\n      });\n    },\n    changePic: function changePic() {\n      var _this = this;\n      this.$modalArticle(function (row) {\n        _this.formValidate.contents.articleData = {\n          title: row.title,\n          imageInput: row.imageInput\n        };\n        _this.formValidate.contents.articleId = row.id;\n      });\n    },\n    handleClosePic: function handleClosePic() {\n      this.visible = false;\n    },\n    // 详情\n    details: function details() {\n      var _this4 = this;\n      this.loading = true;\n      replyInfoApi({\n        id: this.$route.params.id\n      }).then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          var info;\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                info = res || null;\n                _this4.formValidate = {\n                  status: info.status,\n                  type: info.type,\n                  keywords: info.keywords,\n                  id: info.id,\n                  contents: {\n                    content: JSON.parse(info.data).content,\n                    mediaId: JSON.parse(info.data).mediaId,\n                    srcUrl: JSON.parse(info.data).srcUrl,\n                    articleData: JSON.parse(info.data).articleData\n                  }\n                };\n                _this4.labelarr = info.keywords.split(',') || [];\n                _this4.loading = false;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function () {\n        _this4.loading = false;\n      });\n    },\n    // 关注回复，无效关键词详情\n    followDetails: function followDetails() {\n      var _this5 = this;\n      this.loading = true;\n      keywordsInfoApi({\n        keywords: this.$route.path.indexOf('follow') !== -1 ? 'subscribe' : 'default'\n      }).then(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          var info;\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                info = res || null;\n                _this5.formValidate = {\n                  status: info.status,\n                  type: info.type,\n                  keywords: info.keywords,\n                  data: '',\n                  id: info.id,\n                  contents: {\n                    content: JSON.parse(info.data).content || '',\n                    mediaId: JSON.parse(info.data).mediaId || '',\n                    srcUrl: JSON.parse(info.data).srcUrl || '',\n                    articleData: JSON.parse(info.data).articleData || {}\n                  }\n                };\n                _this5.loading = false;\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function () {\n        _this5.loading = false;\n        // if (res.message === '数据不存在') return\n        // this.$message.error(res.message)\n      });\n    },\n    // 下拉选择\n    RuleFactor: function RuleFactor(type) {\n      switch (type) {\n        case 'text':\n          this.formValidate.contents.mediaId = '';\n          this.formValidate.contents.srcUrl = '';\n          this.formValidate.contents.articleData = {};\n          break;\n        case 'news':\n          this.formValidate.contents.mediaId = '';\n          this.formValidate.contents.content = '';\n          this.formValidate.contents.srcUrl = '';\n          this.formValidate.contents.articleData = {};\n          break;\n        default:\n          this.formValidate.contents.content = '';\n          this.formValidate.contents.mediaId = '';\n          this.formValidate.contents.articleData = {};\n      }\n      // this.$refs['formValidate'].resetFields();\n    },\n    handleClose: function handleClose(tag) {\n      var index = this.labelarr.indexOf(tag);\n      this.labelarr.splice(index, 1);\n    },\n    addlabel: function addlabel() {\n      var count = this.labelarr.indexOf(this.val);\n      if (count === -1) {\n        this.labelarr.push(this.val);\n      }\n      this.val = '';\n    },\n    // 保存\n    submenus: Debounce(function (name) {\n      var _this6 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this6.formValidate.keywords = _this6.labelarr.join(',');\n          _this6.formValidate.data = JSON.stringify(_this6.formValidate.contents);\n          if (_this6.$route.path.indexOf('keyword') !== -1) {\n            _this6.$route.params.id ? replyUpdateApi({\n              id: _this6.$route.params.id\n            }, _this6.formValidate).then(/*#__PURE__*/function () {\n              var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n                return _regenerator().w(function (_context3) {\n                  while (1) switch (_context3.n) {\n                    case 0:\n                      _this6.operation();\n                    case 1:\n                      return _context3.a(2);\n                  }\n                }, _callee3);\n              }));\n              return function (_x3) {\n                return _ref3.apply(this, arguments);\n              };\n            }()).catch(function (res) {\n              _this6.$message.error(res.message);\n            }) : replySaveApi(_this6.formValidate).then(/*#__PURE__*/function () {\n              var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(res) {\n                return _regenerator().w(function (_context4) {\n                  while (1) switch (_context4.n) {\n                    case 0:\n                      _this6.operation();\n                    case 1:\n                      return _context4.a(2);\n                  }\n                }, _callee4);\n              }));\n              return function (_x4) {\n                return _ref4.apply(this, arguments);\n              };\n            }()).catch(function (res) {\n              _this6.$message.error(res.message);\n            });\n          } else {\n            _this6.$route.path.indexOf('follow') !== -1 ? _this6.formValidate.keywords = 'subscribe' : _this6.formValidate.keywords = 'default';\n            _this6.formValidate.id !== null ? replyUpdateApi({\n              id: _this6.formValidate.id\n            }, _this6.formValidate).then(/*#__PURE__*/function () {\n              var _ref5 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(res) {\n                return _regenerator().w(function (_context5) {\n                  while (1) switch (_context5.n) {\n                    case 0:\n                      _this6.$message.success('操作成功');\n                    case 1:\n                      return _context5.a(2);\n                  }\n                }, _callee5);\n              }));\n              return function (_x5) {\n                return _ref5.apply(this, arguments);\n              };\n            }()) : replySaveApi(_this6.formValidate).then(/*#__PURE__*/function () {\n              var _ref6 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(res) {\n                return _regenerator().w(function (_context6) {\n                  while (1) switch (_context6.n) {\n                    case 0:\n                      _this6.operation();\n                    case 1:\n                      return _context6.a(2);\n                  }\n                }, _callee6);\n              }));\n              return function (_x6) {\n                return _ref6.apply(this, arguments);\n              };\n            }()).catch(function (res) {\n              _this6.$message.error(res.message);\n            });\n          }\n        } else {\n          return false;\n        }\n      });\n    }),\n    // 保存成功操作\n    operation: function operation() {\n      var _this7 = this;\n      this.$modalSure('继续添加').then(function () {\n        setTimeout(function () {\n          _this7.labelarr = [];\n          _this7.val = '';\n          _this7.$refs['formValidate'].resetFields();\n          _this7.formValidate.contents.mediaId = '';\n        }, 1000);\n      }).catch(function () {\n        setTimeout(function () {\n          _this7.$router.push({\n            path: \"/appSetting/publicAccount/wxReply/keyword\"\n          });\n        }, 500);\n      });\n    }\n  }\n};", null]}