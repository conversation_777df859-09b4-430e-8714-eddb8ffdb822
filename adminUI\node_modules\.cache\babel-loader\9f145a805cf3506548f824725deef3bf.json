{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { applyListApi, extractBankApi } from \"@/api/financial\";\nexport default {\n  name: \"WithdrawalHistory\",\n  data: function data() {\n    return {\n      loading: false,\n      tableData: [],\n      searchForm: {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: \"wallet\",\n        status: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      timeList: [],\n      dialogFormVisible: false,\n      artFrom: {\n        payType: \"\",\n        file: \"\",\n        remark: \"\"\n      },\n      statusList: [{\n        label: \"unapproved\",\n        value: \"-1\"\n      }, {\n        label: \"underReview\",\n        value: \"0\"\n      }, {\n        label: \"reviewed\",\n        value: \"1\"\n      }, {\n        label: \"paid\",\n        value: \"2\"\n      }],\n      walletList: [{\n        label: \"ShopeePay\",\n        value: \"ShopeePay\"\n      }, {\n        label: \"DANA\",\n        value: \"DANA\"\n      }, {\n        label: \"OVO\",\n        value: \"OVO\"\n      }, {\n        label: \"Gopay\",\n        value: \"Gopay\"\n      }],\n      bankList: []\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n    this.getBankList();\n  },\n  methods: {\n    // 获取银行列表\n    getBankList: function getBankList() {\n      var _this = this;\n      extractBankApi().then(function (res) {\n        _this.bankList = res;\n      }).catch(function () {});\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.loading = true;\n      this.searchForm.page = num ? num : this.searchForm.page;\n      this.searchForm.dateLimit = this.timeList.length ? this.timeList.join(\",\") : \"\";\n      applyListApi(this.searchForm).then(function (res) {\n        _this2.tableData = res.list;\n        _this2.searchForm.total = res.total;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    resetForm: function resetForm() {\n      this.searchForm = {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        status: \"\",\n        extractType: this.searchForm.extractType,\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.timeList = [];\n      this.getList();\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.searchForm.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.searchForm.limit = index;\n      this.getList();\n    },\n    handleUpload: function handleUpload() {},\n    onChangeType: function onChangeType(tab) {\n      this.resetForm();\n      this.getList();\n    }\n  }\n};", null]}