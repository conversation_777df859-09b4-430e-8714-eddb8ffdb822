{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue?vue&type=template&id=6a09cc24&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"statistical-page\" ref=\"container\">\n  <div class=\"navs\">\n    <div class=\"list\">\n      <div\n        class=\"item\"\n        :class=\"time == 'today' ? 'on' : ''\"\n        @click=\"setTime('today')\"\n      >\n        今天\n      </div>\n      <div\n        class=\"item\"\n        :class=\"time == 'yesterday' ? 'on' : ''\"\n        @click=\"setTime('yesterday')\"\n      >\n        昨天\n      </div>\n      <div\n        class=\"item\"\n        :class=\"time == 'lately7' ? 'on' : ''\"\n        @click=\"setTime('lately7')\"\n      >\n        最近7天\n      </div>\n      <div\n        class=\"item\"\n        :class=\"time == 'month' ? 'on' : ''\"\n        @click=\"setTime('month')\"\n      >\n        本月\n      </div>\n      <div\n        class=\"item\"\n        :class=\"time == 'date' ? 'on' : ''\"\n        @click=\"dateTitle\"\n      >\n        <!-- <span class=\"iconfont icon-xiangxia\"></span>\n        <span v-for=\"(value, index) in renderValues\" :key=\"index\">\n          {{ value }}</span\n        > -->\n        自定义\n      </div>\n    </div>\n  </div>\n  <div class=\"wrapper\">\n    <div class=\"title\">\n      {{ title }}{{ this.where.type == 1 ? \"营业额（元）\" : \"订单量（份）\" }}\n    </div>\n    <div class=\"money\">{{ time_price }}</div>\n    <div class=\"increase acea-row row-between-wrapper\">\n      <div>\n        {{ title }}增长率：<span\n          :class=\"increase_time_status === 1 ? 'red' : 'green'\"\n          >{{ increase_time_status === 1 ? \"\" : \"-\" }}{{ growth_rate }}%\n          <span\n            class=\"iconfont\"\n            :class=\"\n              increase_time_status === 1\n                ? 'icon-xiangshang1'\n                : 'icon-xiangxia2'\n            \"\n          ></span\n        ></span>\n      </div>\n      <div>\n        {{ title }}增长：<span\n          :class=\"increase_time_status === 1 ? 'red' : 'green'\"\n          >{{ Number(increase_time).toFixed(2) }}\n          <span\n            class=\"iconfont\"\n            :class=\"\n              increase_time_status === 1\n                ? 'icon-xiangshang1'\n                : 'icon-xiangxia2'\n            \"\n          ></span\n        ></span>\n      </div>\n    </div>\n  </div>\n  <div class=\"chart\">\n    <div class=\"company\">\n      {{ where.type === 1 ? \"单位（元）\" : \"单位（份）\" }}\n    </div>\n    <ECharts :options=\"polar\"></ECharts>\n  </div>\n  <!--<div class=\"public-wrapper\">-->\n    <!--<div class=\"title\">-->\n      <!--<span class=\"iconfont icon-xiangxishuju\"></span>详细数据-->\n    <!--</div>-->\n    <!--<div class=\"nav acea-row row-between-wrapper\">-->\n      <!--<div class=\"data\">日期</div>-->\n      <!--<div class=\"browse\">订单量</div>-->\n      <!--<div class=\"turnover\">成交额</div>-->\n    <!--</div>-->\n    <!--<div class=\"conter\">-->\n      <!--<div-->\n        <!--class=\"item acea-row row-between-wrapper\"-->\n        <!--v-for=\"(item, index) in list\"-->\n        <!--:key=\"index\"-->\n      <!--&gt;-->\n        <!--<div class=\"data\">{{ item.time }}</div>-->\n        <!--<div class=\"browse\">{{ item.count }}</div>-->\n        <!--<div class=\"turnover\">{{ item.price }}</div>-->\n      <!--</div>-->\n    <!--</div>-->\n  <!--</div>-->\n  <statistics-data :list=\"list\"></statistics-data>\n  <div class=\"calendar-wrapper\" :class=\"current === true ? 'on' : ''\">\n    <div class=\"calendar\">\n      <Calendar\n        :clean=\"clean\"\n        :lunar=\"lunar\"\n        ref=\"calendar\"\n        :range=\"isrange\"\n        :multi=\"ismulti\"\n        @select=\"select\"\n        @next=\"next\"\n        @prev=\"prev\"\n        :value=\"value\"\n        :weekSwitch=\"weekSwitch\"\n        :monthRange=\"monthRange\"\n        rangeMonthFormat=\"yyyy-mm-dd\"\n        monFirst\n        responsive\n        :begin=\"[1992, 5, 20]\"\n        :end=\"[2049, 5, 20]\"\n      />\n    </div>\n  </div>\n  <div\n    class=\"maskModel\"\n    @touchmove.prevent\n    v-show=\"current === true\"\n    @click=\"close\"\n  ></div>\n  <Loading :loaded=\"loaded\" :loading=\"loading\"></Loading>\n</div>\n", null]}