{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\request\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { applyListApi, extractBankApi, financeApplyDealApi, uploadImage } from \"@/api/financial\";\nimport { getToken } from \"@/utils/auth\";\nexport default {\n  name: \"WithdrawalRequest\",\n  components: {},\n  data: function data() {\n    return {\n      loading: false,\n      tableData: [],\n      realName: \"\",\n      pid: \"\",\n      myHeaders: {\n        \"X-Token\": getToken()\n      },\n      isMore: \"\",\n      modelName: \"\",\n      searchForm: {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: \"wallet\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      timeList: [],\n      dialogFormVisible: false,\n      artFrom: {\n        id: null,\n        voucherImage: \"\",\n        remark: \"\"\n      },\n      walletList: [{\n        label: \"ShopeePay\",\n        value: \"ShopeePay\"\n      }, {\n        label: \"DANA\",\n        value: \"DANA\"\n      }, {\n        label: \"OVO\",\n        value: \"OVO\"\n      }, {\n        label: \"Gopay\",\n        value: \"Gopay\"\n      }],\n      bankList: [],\n      rules: {\n        voucherImage: [{\n          required: true,\n          message: \"请选择\",\n          trigger: \"blur\"\n        }]\n      }\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n    this.getBankList();\n  },\n  methods: {\n    // 获取银行列表\n    getBankList: function getBankList() {\n      var _this = this;\n      extractBankApi().then(function (res) {\n        _this.bankList = res;\n      }).catch(function () {});\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.loading = true;\n      this.searchForm.page = num ? num : this.searchForm.page;\n      this.searchForm.dateLimit = this.timeList.length ? this.timeList.join(\",\") : \"\";\n      applyListApi(this.searchForm).then(function (res) {\n        _this2.tableData = res.list;\n        _this2.searchForm.total = res.total;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    resetForm: function resetForm() {\n      this.searchForm = {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: this.searchForm.extractType,\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.timeList = [];\n      this.getList();\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.searchForm.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.searchForm.limit = index;\n      this.getList();\n    },\n    handleUpload: function handleUpload() {},\n    onChangeType: function onChangeType(tab) {\n      this.getList();\n    },\n    handelConfirm: function handelConfirm() {\n      var _this3 = this;\n      this.$refs.elForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(valid) {\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                if (valid) {\n                  _context.n = 1;\n                  break;\n                }\n                return _context.a(2);\n              case 1:\n                financeApplyDealApi(_this3.artFrom).then(function (res) {\n                  _this3.$message.success(\"操作成功\");\n                  _this3.handleCancle();\n                  getList(1);\n                });\n              case 2:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    handleFinish: function handleFinish(row) {\n      this.dialogFormVisible = true;\n      this.artFrom.id = row.id;\n      this.pid = row.id;\n      this.realName = row.realName;\n    },\n    handleCancle: function handleCancle() {\n      this.dialogFormVisible = false;\n      this.artFrom = {\n        id: null,\n        voucherImage: \"\",\n        remark: \"\"\n      };\n    },\n    // 选取图片后自动回调，里面可以获取到文件\n    imgSaveToUrl: function imgSaveToUrl(event) {\n      // 也可以用file\n      this.localFile = event.raw; // 或者 this.localFile=file.raw\n\n      // 转换操作可以不放到这个函数里面，\n      // 因为这个函数会被多次触发，上传时触发，上传成功也触发\n      var reader = new FileReader();\n      reader.readAsDataURL(this.localFile); // 这里也可以直接写参数event.raw\n\n      // 转换成功后的操作，reader.result即为转换后的DataURL ，\n      // 它不需要自己定义，你可以console.integralLog(reader.result)看一下\n      reader.onload = function () {\n        // console.integralLog(reader.result)\n      };\n\n      /* 另外一种本地预览方法 */\n      var URL = window.URL || window.webkitURL;\n      this.localImg = URL.createObjectURL(event.raw);\n      // 转换后的地址为 blob:http://xxx/7bf54338-74bb-47b9-9a7f-7a7093c716b5\n    },\n    beforeAvatarUpload: function beforeAvatarUpload(rawFile) {\n      if (rawFile.type === \"image/jpeg\" || rawFile.type === \"image/png\") {\n        return true;\n      } else {\n        this.$message.error(\"Avatar picture must be JPG format!\");\n        return false;\n      }\n    },\n    // 上传\n    handleUploadForm: function handleUploadForm(param) {\n      var _this4 = this;\n      var formData = new FormData();\n      var data = {\n        model: this.realName,\n        pid: this.pid\n      };\n      formData.append(\"multipart\", param.file);\n      var loading = this.$loading({\n        lock: true,\n        text: \"上传中，请稍候...\",\n        spinner: \"el-icon-loading\",\n        background: \"rgba(0, 0, 0, 0.7)\"\n      });\n      uploadImage(formData, data).then(function (res) {\n        loading.close();\n        _this4.$message.success(\"上传成功\");\n        _this4.artFrom.voucherImage = res.url;\n      }).catch(function (res) {\n        loading.close();\n      });\n    }\n  }\n};", null]}