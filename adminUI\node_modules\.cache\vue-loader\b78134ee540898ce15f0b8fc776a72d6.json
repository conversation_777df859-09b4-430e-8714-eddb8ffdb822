{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1754275430524}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./creatGrade.vue?vue&type=template&id=64cb5309&scoped=true\"\nimport script from \"./creatGrade.vue?vue&type=script&lang=js\"\nexport * from \"./creatGrade.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64cb5309\",\n  null\n  \n)\n\nexport default component.exports"]}