{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\PriceChange.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./PriceChange.vue?vue&type=template&id=dae61528&scoped=true\"\nimport script from \"./PriceChange.vue?vue&type=script&lang=js\"\nexport * from \"./PriceChange.vue?vue&type=script&lang=js\"\nimport style0 from \"./PriceChange.vue?vue&type=style&index=0&id=dae61528&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dae61528\",\n  null\n  \n)\n\nexport default component.exports"]}