{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\element-ui\\lib\\transitions\\collapse-transition.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\element-ui\\lib\\transitions\\collapse-transition.js", "mtime": 1754138286561}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["'use strict';\n\nexports.__esModule = true;\nvar _dom = require('element-ui/lib/utils/dom');\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar Transition = function () {\n  function Transition() {\n    _classCallCheck(this, Transition);\n  }\n  Transition.prototype.beforeEnter = function beforeEnter(el) {\n    (0, _dom.addClass)(el, 'collapse-transition');\n    if (!el.dataset) el.dataset = {};\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n    el.style.height = '0';\n    el.style.paddingTop = 0;\n    el.style.paddingBottom = 0;\n  };\n  Transition.prototype.enter = function enter(el) {\n    el.dataset.oldOverflow = el.style.overflow;\n    if (el.scrollHeight !== 0) {\n      el.style.height = el.scrollHeight + 'px';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    } else {\n      el.style.height = '';\n      el.style.paddingTop = el.dataset.oldPaddingTop;\n      el.style.paddingBottom = el.dataset.oldPaddingBottom;\n    }\n    el.style.overflow = 'hidden';\n  };\n  Transition.prototype.afterEnter = function afterEnter(el) {\n    // for safari: remove class then reset height is necessary\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n  };\n  Transition.prototype.beforeLeave = function beforeLeave(el) {\n    if (!el.dataset) el.dataset = {};\n    el.dataset.oldPaddingTop = el.style.paddingTop;\n    el.dataset.oldPaddingBottom = el.style.paddingBottom;\n    el.dataset.oldOverflow = el.style.overflow;\n    el.style.height = el.scrollHeight + 'px';\n    el.style.overflow = 'hidden';\n  };\n  Transition.prototype.leave = function leave(el) {\n    if (el.scrollHeight !== 0) {\n      // for safari: add class after set height, or it will jump to zero height suddenly, weired\n      (0, _dom.addClass)(el, 'collapse-transition');\n      el.style.height = 0;\n      el.style.paddingTop = 0;\n      el.style.paddingBottom = 0;\n    }\n  };\n  Transition.prototype.afterLeave = function afterLeave(el) {\n    (0, _dom.removeClass)(el, 'collapse-transition');\n    el.style.height = '';\n    el.style.overflow = el.dataset.oldOverflow;\n    el.style.paddingTop = el.dataset.oldPaddingTop;\n    el.style.paddingBottom = el.dataset.oldPaddingBottom;\n  };\n  return Transition;\n}();\nexports.default = {\n  name: 'ElCollapseTransition',\n  functional: true,\n  render: function render(h, _ref) {\n    var children = _ref.children;\n    var data = {\n      on: new Transition()\n    };\n    return h('transition', data, children);\n  }\n};", null]}