{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\history\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { applyListApi, extractBankApi } from \"@/api/financial\";\nexport default {\n  name: \"WithdrawalHistory\",\n  data() {\n    return {\n      loading: false,\n      tableData: [],\n\n      searchForm: {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        extractType: \"wallet\",\n        status: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      timeList: [],\n      dialogFormVisible: false,\n      artFrom: {\n        payType: \"\",\n        file: \"\",\n        remark: \"\"\n      },\n      statusList: [\n        { label: \"unapproved\", value: \"-1\" },\n        { label: \"underReview\", value: \"0\" },\n        { label: \"reviewed\", value: \"1\" },\n        { label: \"paid\", value: \"2\" }\n      ],\n      walletList: [\n        { label: \"ShopeePay\", value: \"ShopeePay\" },\n        { label: \"DANA\", value: \"DANA\" },\n        { label: \"OVO\", value: \"OVO\" },\n        { label: \"Gopay\", value: \"Gopay\" }\n      ],\n      bankList: []\n    };\n  },\n  created() {},\n  mounted() {\n    this.getList();\n    this.getBankList();\n  },\n  methods: {\n    // 获取银行列表\n    getBankList() {\n      extractBankApi()\n        .then(res => {\n          this.bankList = res;\n        })\n        .catch(() => {});\n    },\n    // 列表\n    getList(num) {\n      this.loading = true;\n      this.searchForm.page = num ? num : this.searchForm.page;\n      this.searchForm.dateLimit = this.timeList.length\n        ? this.timeList.join(\",\")\n        : \"\";\n      applyListApi(this.searchForm)\n        .then(res => {\n          this.tableData = res.list;\n          this.searchForm.total = res.total;\n          this.loading = false;\n        })\n        .catch(() => {\n          this.loading = false;\n        });\n    },\n    resetForm() {\n      this.searchForm = {\n        keywords: \"\",\n        dateLimit: \"\",\n        bankName: \"\",\n        walletCode: \"\",\n        status: \"\",\n        extractType: this.searchForm.extractType,\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.timeList = [];\n      this.getList();\n    },\n    //切换页数\n    pageChange(index) {\n      this.searchForm.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange(index) {\n      this.searchForm.limit = index;\n      this.getList();\n    },\n    handleUpload() {},\n    onChangeType(tab) {\n      this.resetForm();\n      this.getList();\n    }\n  }\n};\n", null]}