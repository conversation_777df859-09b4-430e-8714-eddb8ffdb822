{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\center\\index.vue?vue&type=template&id=b060d7fe&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\center\\index.vue", "mtime": 1754382949124}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.userFrom),callback:function ($$v) {_vm.userFrom=$$v},expression:\"userFrom\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('user.center.nickname') + '：'}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('user.center.nickname'),\"clearable\":\"\"},model:{value:(_vm.userFrom.keywords),callback:function ($$v) {_vm.$set(_vm.userFrom, \"keywords\", $$v)},expression:\"userFrom.keywords\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.center.phone') + '：'}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('user.center.phone'),\"clearable\":\"\"},model:{value:(_vm.userFrom.phone),callback:function ($$v) {_vm.$set(_vm.userFrom, \"phone\", $$v)},expression:\"userFrom.phone\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.center.userLevel') + '：'}},[_c('el-select',{staticClass:\"selWidth\",attrs:{\"placeholder\":_vm.$t('common.pleaseSelect'),\"clearable\":\"\",\"filterable\":\"\",\"multiple\":\"\"},model:{value:(_vm.levelData),callback:function ($$v) {_vm.levelData=$$v},expression:\"levelData\"}},_vm._l((_vm.levelList),function(item,index){return _c('el-option',{key:index,attrs:{\"value\":item.id,\"label\":item.name}},[_c('span',{staticStyle:{\"float\":\"left\"}},[_vm._v(_vm._s(item.name))]),_vm._v(\" \"),_c('span',{staticStyle:{\"float\":\"right\",\"color\":\"#8492a6\",\"font-size\":\"13px\"}},[_vm._v(\"\\n                \"+_vm._s(_vm.getUpgradeTypeText(item.upgradeType))+\"\\n                \"),(item.upgradeType === 1)?_c('span',[_vm._v(\" - Rp \"+_vm._s(item.upgradePrice))]):_vm._e()])])}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.getList(1)}}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"common.query\"))+\"\\n    \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":function($event){return _vm.resetForm()}}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"common.reset\"))+\"\\n    \")])],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"label\":_vm.$t('common.serialNumber'),\"type\":\"index\",\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.avatar'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticStyle:{\"width\":\"36px\",\"height\":\"36px\"},attrs:{\"src\":scope.row.avatar,\"preview-src-list\":[scope.row.avatar]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.nickname'),\"min-width\":\"150\",\"prop\":\"nickname\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.nickname)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.tiktokId'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.openId)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.phone'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.phone)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.whatsApp'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.whatsAppAccount)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.registerTime'),\"min-width\":\"150\",\"prop\":\"createTime\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.lastLoginTime'),\"min-width\":\"150\",\"prop\":\"lastLoginTime\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.orderCount'),\"min-width\":\"80\",\"prop\":\"payCount\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.orderFinishCount'),\"min-width\":\"80\",\"prop\":\"spreadCount\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.isAgent'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.isAgent)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.isPartner'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.isPartner)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.userLevelLabel'),\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(_vm.handlelevelFilter(scope.row.level))))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.center.inviter'),\"min-width\":\"80\",\"prop\":\"spreadNickname\"}})],1),_vm._v(\" \"),_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.userFrom.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.userFrom.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.userFrom.total},on:{\"size-change\":_vm.sizeChange,\"current-change\":_vm.pageChange}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}