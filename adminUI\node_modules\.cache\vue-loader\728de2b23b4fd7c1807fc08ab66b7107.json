{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderVideoSend.vue?vue&type=template&id=d2116198&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderVideoSend.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-dialog',{staticClass:\"order_box\",attrs:{\"visible\":_vm.modals,\"title\":\"发送货\",\"before-close\":_vm.handleClose,\"width\":\"600px\"},on:{\"update:visible\":function($event){_vm.modals=$event}}},[_c('el-form',{ref:\"formItem\",attrs:{\"model\":_vm.formItem,\"label-width\":\"110px\",\"rules\":_vm.rules},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',{attrs:{\"label\":\"快递公司：\",\"prop\":\"expressCode\"}},[_c('el-select',{staticStyle:{\"width\":\"80%\"},attrs:{\"filterable\":\"\"},model:{value:(_vm.formItem.deliveryId),callback:function ($$v) {_vm.$set(_vm.formItem, \"deliveryId\", $$v)},expression:\"formItem.deliveryId\"}},_vm._l((_vm.express),function(item,i){return _c('el-option',{key:i,attrs:{\"value\":item.deliveryId,\"label\":item.deliveryName}})}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"快递单号：\",\"prop\":\"waybillId\"}},[_c('el-input',{staticStyle:{\"width\":\"80%\"},attrs:{\"placeholder\":\"请输入快递单号\"},model:{value:(_vm.formItem.waybillId),callback:function ($$v) {_vm.$set(_vm.formItem, \"waybillId\", $$v)},expression:\"formItem.waybillId\"}})],1)],1),_vm._v(\" \"),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:order:video:send']),expression:\"['admin:order:video:send']\"}],attrs:{\"size\":\"mini\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.putSend('formItem')}}},[_vm._v(\"提交\")]),_vm._v(\" \"),_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":function($event){return _vm.cancel('formItem')}}},[_vm._v(\"取消\")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}