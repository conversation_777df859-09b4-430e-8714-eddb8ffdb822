{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\codegen\\codegenList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\codegen\\codegenList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getCodegenList } from '@/api/codegen'\nimport SettingMer from \"@/utils/settingMer\";\n\nexport default {\n  name: \"codegenList\",\n  data(){\n    return{\n      constants: this.$constants,\n      codeListData:{\n        pram:{\n          page: 1,\n          limit: 10,\n          tableName: ''\n        },\n        data:{\n          list:[],\n          totalCount:0\n        },\n        selectedTables:[]\n      }\n    }\n  },\n  created(){\n\n  },\n  mounted(){\n\n  },\n  methods: {\n    handlerSearch(){\n      this.codeListData.pram.limit = 10;\n      this.codeListData.pram.page = 1;\n      this.getList(this.codeListData.pram);\n    },\n    getList(pram){\n      getCodegenList(pram).then(res => {\n        this.codeListData.data = res;\n      })\n    },\n    handleSizeChange(val) {\n      this.codeListData.pram.limit = val\n      this.getList(this.codeListData.pram)\n    },\n    handleCurrentChange(val) {\n      this.codeListData.pram.page = val\n      this.getList(this.codeListData.pram)\n    },\n    handleSelectionChange(selectedRows){\n      this.codeListData.selectedTables = [];\n      selectedRows.forEach(row => {\n        this.codeListData.selectedTables.push(row.tableName);\n      });\n    },\n    handleGenCode(){\n      window.open(`${SettingMer.apiBaseURL}codegen/code?tables=${this.codeListData.selectedTables.join(\",\")}`);\n\n    }\n  }\n}\n", null]}