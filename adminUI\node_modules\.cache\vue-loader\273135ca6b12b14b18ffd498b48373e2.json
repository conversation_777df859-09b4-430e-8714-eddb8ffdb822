{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport iconList from '../utils/icon.json'\n\nconst originList = iconList.map(name => `el-icon-${name}`)\n\nexport default {\n  inheritAttrs: false,\n  props: ['current'],\n  data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    }\n  },\n  watch: {\n    key(val) {\n      if (val) {\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\n      } else {\n        this.iconList = originList\n      }\n    }\n  },\n  methods: {\n    onOpen() {\n      this.active = this.current\n      this.key = ''\n    },\n    onClose() {},\n    onSelect(icon) {\n      this.active = icon\n      this.$emit('select', icon)\n      this.$emit('update:visible', false)\n    }\n  }\n}\n", null]}