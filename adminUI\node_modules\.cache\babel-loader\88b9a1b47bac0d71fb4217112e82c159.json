{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\resize-detector\\esm\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\resize-detector\\esm\\index.js", "mtime": 1754138272931}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["var raf = null;\nfunction requestAnimationFrame(callback) {\n  if (!raf) {\n    raf = (window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function (callback) {\n      return setTimeout(callback, 16);\n    }).bind(window);\n  }\n  return raf(callback);\n}\nvar caf = null;\nfunction cancelAnimationFrame(id) {\n  if (!caf) {\n    caf = (window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || function (id) {\n      clearTimeout(id);\n    }).bind(window);\n  }\n  caf(id);\n}\nfunction createStyles(styleText) {\n  var style = document.createElement('style');\n  style.type = 'text/css';\n  if (style.styleSheet) {\n    style.styleSheet.cssText = styleText;\n  } else {\n    style.appendChild(document.createTextNode(styleText));\n  }\n  (document.querySelector('head') || document.body).appendChild(style);\n  return style;\n}\nfunction createElement(tagName) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var elem = document.createElement(tagName);\n  Object.keys(props).forEach(function (key) {\n    elem[key] = props[key];\n  });\n  return elem;\n}\nfunction getComputedStyle(elem, prop, pseudo) {\n  // for older versions of Firefox, `getComputedStyle` required\n  // the second argument and may return `null` for some elements\n  // when `display: none`\n  var computedStyle = window.getComputedStyle(elem, pseudo || null) || {\n    display: 'none'\n  };\n  return computedStyle[prop];\n}\nfunction getRenderInfo(elem) {\n  if (!document.documentElement.contains(elem)) {\n    return {\n      detached: true,\n      rendered: false\n    };\n  }\n  var current = elem;\n  while (current !== document) {\n    if (getComputedStyle(current, 'display') === 'none') {\n      return {\n        detached: false,\n        rendered: false\n      };\n    }\n    current = current.parentNode;\n  }\n  return {\n    detached: false,\n    rendered: true\n  };\n}\nvar css = \".resize-triggers{visibility:hidden;opacity:0}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:\\\"\\\";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}\";\nvar total = 0;\nvar style = null;\nfunction addListener(elem, callback) {\n  if (!elem.__resize_mutation_handler__) {\n    elem.__resize_mutation_handler__ = handleMutation.bind(elem);\n  }\n  var listeners = elem.__resize_listeners__;\n  if (!listeners) {\n    elem.__resize_listeners__ = [];\n    if (window.ResizeObserver) {\n      var offsetWidth = elem.offsetWidth,\n        offsetHeight = elem.offsetHeight;\n      var ro = new ResizeObserver(function () {\n        if (!elem.__resize_observer_triggered__) {\n          elem.__resize_observer_triggered__ = true;\n          if (elem.offsetWidth === offsetWidth && elem.offsetHeight === offsetHeight) {\n            return;\n          }\n        }\n        runCallbacks(elem);\n      });\n\n      // initially display none won't trigger ResizeObserver callback\n      var _getRenderInfo = getRenderInfo(elem),\n        detached = _getRenderInfo.detached,\n        rendered = _getRenderInfo.rendered;\n      elem.__resize_observer_triggered__ = detached === false && rendered === false;\n      elem.__resize_observer__ = ro;\n      ro.observe(elem);\n    } else if (elem.attachEvent && elem.addEventListener) {\n      // targeting IE9/10\n      elem.__resize_legacy_resize_handler__ = function handleLegacyResize() {\n        runCallbacks(elem);\n      };\n      elem.attachEvent('onresize', elem.__resize_legacy_resize_handler__);\n      document.addEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n    } else {\n      if (!total) {\n        style = createStyles(css);\n      }\n      initTriggers(elem);\n      elem.__resize_rendered__ = getRenderInfo(elem).rendered;\n      if (window.MutationObserver) {\n        var mo = new MutationObserver(elem.__resize_mutation_handler__);\n        mo.observe(document, {\n          attributes: true,\n          childList: true,\n          characterData: true,\n          subtree: true\n        });\n        elem.__resize_mutation_observer__ = mo;\n      }\n    }\n  }\n  elem.__resize_listeners__.push(callback);\n  total++;\n}\nfunction removeListener(elem, callback) {\n  // targeting IE9/10\n  if (elem.detachEvent && elem.removeEventListener) {\n    elem.detachEvent('onresize', elem.__resize_legacy_resize_handler__);\n    document.removeEventListener('DOMSubtreeModified', elem.__resize_mutation_handler__);\n    return;\n  }\n  var listeners = elem.__resize_listeners__;\n  if (!listeners) {\n    return;\n  }\n  listeners.splice(listeners.indexOf(callback), 1);\n  if (!listeners.length) {\n    if (elem.__resize_observer__) {\n      elem.__resize_observer__.unobserve(elem);\n      elem.__resize_observer__.disconnect();\n      elem.__resize_observer__ = null;\n    } else {\n      if (elem.__resize_mutation_observer__) {\n        elem.__resize_mutation_observer__.disconnect();\n        elem.__resize_mutation_observer__ = null;\n      }\n      elem.removeEventListener('scroll', handleScroll);\n      elem.removeChild(elem.__resize_triggers__.triggers);\n      elem.__resize_triggers__ = null;\n    }\n    elem.__resize_listeners__ = null;\n  }\n  if (! --total && style) {\n    style.parentNode.removeChild(style);\n  }\n}\nfunction getUpdatedSize(elem) {\n  var _elem$__resize_last__ = elem.__resize_last__,\n    width = _elem$__resize_last__.width,\n    height = _elem$__resize_last__.height;\n  var offsetWidth = elem.offsetWidth,\n    offsetHeight = elem.offsetHeight;\n  if (offsetWidth !== width || offsetHeight !== height) {\n    return {\n      width: offsetWidth,\n      height: offsetHeight\n    };\n  }\n  return null;\n}\nfunction handleMutation() {\n  // `this` denotes the scrolling element\n  var _getRenderInfo2 = getRenderInfo(this),\n    rendered = _getRenderInfo2.rendered,\n    detached = _getRenderInfo2.detached;\n  if (rendered !== this.__resize_rendered__) {\n    if (!detached && this.__resize_triggers__) {\n      resetTriggers(this);\n      this.addEventListener('scroll', handleScroll, true);\n    }\n    this.__resize_rendered__ = rendered;\n    runCallbacks(this);\n  }\n}\nfunction handleScroll() {\n  var _this = this;\n  // `this` denotes the scrolling element\n  resetTriggers(this);\n  if (this.__resize_raf__) {\n    cancelAnimationFrame(this.__resize_raf__);\n  }\n  this.__resize_raf__ = requestAnimationFrame(function () {\n    var updated = getUpdatedSize(_this);\n    if (updated) {\n      _this.__resize_last__ = updated;\n      runCallbacks(_this);\n    }\n  });\n}\nfunction runCallbacks(elem) {\n  if (!elem || !elem.__resize_listeners__) {\n    return;\n  }\n  elem.__resize_listeners__.forEach(function (callback) {\n    callback.call(elem);\n  });\n}\nfunction initTriggers(elem) {\n  var position = getComputedStyle(elem, 'position');\n  if (!position || position === 'static') {\n    elem.style.position = 'relative';\n  }\n  elem.__resize_old_position__ = position;\n  elem.__resize_last__ = {};\n  var triggers = createElement('div', {\n    className: 'resize-triggers'\n  });\n  var expand = createElement('div', {\n    className: 'resize-expand-trigger'\n  });\n  var expandChild = createElement('div');\n  var contract = createElement('div', {\n    className: 'resize-contract-trigger'\n  });\n  expand.appendChild(expandChild);\n  triggers.appendChild(expand);\n  triggers.appendChild(contract);\n  elem.appendChild(triggers);\n  elem.__resize_triggers__ = {\n    triggers: triggers,\n    expand: expand,\n    expandChild: expandChild,\n    contract: contract\n  };\n  resetTriggers(elem);\n  elem.addEventListener('scroll', handleScroll, true);\n  elem.__resize_last__ = {\n    width: elem.offsetWidth,\n    height: elem.offsetHeight\n  };\n}\nfunction resetTriggers(elem) {\n  var _elem$__resize_trigge = elem.__resize_triggers__,\n    expand = _elem$__resize_trigge.expand,\n    expandChild = _elem$__resize_trigge.expandChild,\n    contract = _elem$__resize_trigge.contract;\n\n  // batch read\n  var csw = contract.scrollWidth,\n    csh = contract.scrollHeight;\n  var eow = expand.offsetWidth,\n    eoh = expand.offsetHeight,\n    esw = expand.scrollWidth,\n    esh = expand.scrollHeight;\n\n  // batch write\n  contract.scrollLeft = csw;\n  contract.scrollTop = csh;\n  expandChild.style.width = eow + 1 + 'px';\n  expandChild.style.height = eoh + 1 + 'px';\n  expand.scrollLeft = esw;\n  expand.scrollTop = esh;\n}\nexport { addListener, removeListener };", null]}