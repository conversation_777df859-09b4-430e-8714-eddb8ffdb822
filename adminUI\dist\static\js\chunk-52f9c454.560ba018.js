(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52f9c454"],{"21d2":function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return s})),r.d(e,"c",(function(){return n})),r.d(e,"e",(function(){return l})),r.d(e,"f",(function(){return i})),r.d(e,"d",(function(){return c}));var o=r("b775");function a(){return Object(o["a"])({url:"/admin/store/retail/spread/manage/get",method:"get"})}function s(t){return Object(o["a"])({url:"/admin/store/retail/spread/manage/set",method:"post",data:t})}function n(t){return Object(o["a"])({url:"/admin/store/retail/list",method:"get",params:t})}function l(t,e){return Object(o["a"])({url:"/admin/store/retail/spread/userlist",method:"post",params:t,data:e})}function i(t,e){return Object(o["a"])({url:"/admin/store/retail/spread/orderlist",method:"post",params:t,data:e})}function c(t){return Object(o["a"])({url:"/admin/store/retail/spread/clean/".concat(t),method:"get"})}},"64b8":function(t,e,r){"use strict";r("7c60")},"7c60":function(t,e,r){},9026:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-card",{staticClass:"box-card"},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"promoterForm",staticClass:"demo-promoterForm",attrs:{model:t.promoterForm,rules:t.rules,"label-width":"200px"}},[r("el-form-item",{attrs:{prop:"brokerageFuncStatus"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销启用：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"商城分销功能开启关闭",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.brokerageFuncStatus,callback:function(e){t.$set(t.promoterForm,"brokerageFuncStatus",e)},expression:"promoterForm.brokerageFuncStatus"}},[r("el-radio",{attrs:{label:"1"}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:"0"}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"storeBrokerageQuota"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("满额分销最低金额：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"满额分销满足金额开通分销权限",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{placeholder:"满额分销满足金额开通分销权限",min:-1,step:1},nativeOn:{keydown:function(e){return t.channelInputLimit(e)}},model:{value:t.promoterForm.storeBrokerageQuota,callback:function(e){t.$set(t.promoterForm,"storeBrokerageQuota",e)},expression:"promoterForm.storeBrokerageQuota"}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"brokerageBindind"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销关系绑定：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"所有用户”指所有没有上级推广人的用户，“新用户”指新注册的用户",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.brokerageBindind,callback:function(e){t.$set(t.promoterForm,"brokerageBindind",e)},expression:"promoterForm.brokerageBindind"}},[r("el-radio",{attrs:{label:"0"}},[t._v("所有用户")]),t._v(" "),r("el-radio",{attrs:{label:"1"}},[t._v("新用户")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"storeBrokerageIsBubble"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("分销气泡：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"基础商品详情页分销气泡功能开启关闭",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-radio-group",{model:{value:t.promoterForm.storeBrokerageIsBubble,callback:function(e){t.$set(t.promoterForm,"storeBrokerageIsBubble",e)},expression:"promoterForm.storeBrokerageIsBubble"}},[r("el-radio",{attrs:{label:"1"}},[t._v("开启")]),t._v(" "),r("el-radio",{attrs:{label:"0"}},[t._v("关闭")])],1)],1),t._v(" "),r("el-form-item",{attrs:{prop:"storeBrokerageRatio"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("一级返佣比例：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{"step-strictly":"",min:0,max:100,placeholder:"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%"},model:{value:t.promoterForm.storeBrokerageRatio,callback:function(e){t.$set(t.promoterForm,"storeBrokerageRatio",e)},expression:"promoterForm.storeBrokerageRatio"}}),t._v(" "),r("span",[t._v("%")])],1),t._v(" "),r("el-form-item",{attrs:{prop:"storeBrokerageTwo"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("二级返佣比例：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{"step-strictly":"",min:0,max:100,placeholder:"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%"},model:{value:t.promoterForm.storeBrokerageTwo,callback:function(e){t.$set(t.promoterForm,"storeBrokerageTwo",e)},expression:"promoterForm.storeBrokerageTwo"}}),t._v(" "),r("span",[t._v("%")])],1),t._v(" "),r("el-form-item",{attrs:{prop:"userExtractMinPrice"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("提现最低金额：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"用户提现最低金额",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{min:0,step:1,placeholder:"用户提现最低金额"},model:{value:t.promoterForm.userExtractMinPrice,callback:function(e){t.$set(t.promoterForm,"userExtractMinPrice",e)},expression:"promoterForm.userExtractMinPrice"}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"userExtractBank"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("提现银行卡：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"提现银行卡，每个银行换行",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input",{attrs:{type:"textarea",rows:4,placeholder:"提现银行卡，每个银行换行"},model:{value:t.promoterForm.userExtractBank,callback:function(e){t.$set(t.promoterForm,"userExtractBank",e)},expression:"promoterForm.userExtractBank"}})],1),t._v(" "),r("el-form-item",{attrs:{prop:"extractTime"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",[t._v("冻结时间：")]),t._v(" "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"佣金冻结时间(天)",placement:"top-start"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1),t._v(" "),r("el-input-number",{staticClass:"selWidth",attrs:{min:0,placeholder:"佣金冻结时间(天)"},model:{value:t.promoterForm.extractTime,callback:function(e){t.$set(t.promoterForm,"extractTime",e)},expression:"promoterForm.extractTime"}})],1),t._v(" "),r("el-form-item",[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:retail:spread:manage:set"],expression:"['admin:retail:spread:manage:set']"}],attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.submitForm("promoterForm")}}},[t._v("提交")])],1)],1)],1)],1)},a=[],s=r("21d2"),n=r("fca7"),l=r("e350"),i=r("61f7"),c={name:"Index",data:function(){return{promoterForm:{},loading:!0,rules:{brokerageFuncStatus:[{required:!0,message:"请选择是否启用分销",trigger:"change"}],storeBrokerageRatio:[{required:!0,message:"请输入一级返佣比例",trigger:"blur"}],storeBrokerageTwo:[{required:!0,message:"请输入二级返佣比例",trigger:"blur"}]}}},mounted:function(){this.getDetal()},methods:{checkPermi:l["a"],channelInputLimit:function(t){var e=t.key;return"e"!==e&&"."!==e||(t.returnValue=!1,!1)},getDetal:function(){var t=this;this.loading=!0,Object(s["a"])().then((function(e){t.loading=!1,t.promoterForm=e,t.promoterForm.storeBrokerageIsBubble=e.storeBrokerageIsBubble.toString(),t.promoterForm.brokerageFuncStatus=e.brokerageFuncStatus.toString(),t.promoterForm.brokerageBindind=e.brokerageBindind.toString()})).catch((function(e){t.$message.error(e.message)}))},submitForm:Object(i["a"])((function(t){var e=this;this.$refs[t].validate((function(t){return!!t&&(n["Add"](e.promoterForm.storeBrokerageRatio,e.promoterForm.storeBrokerageTwo)>100?e.$message.warning("返佣比例相加不能超过100%"):(e.loading=!0,void Object(s["b"])(e.promoterForm).then((function(t){e.loading=!1,e.$message.success("提交成功")})).catch((function(t){e.loading=!1}))))}))}))}},m=c,p=(r("64b8"),r("2877")),u=Object(p["a"])(m,o,a,!1,null,"36aadf0b",null);e["default"]=u.exports}}]);