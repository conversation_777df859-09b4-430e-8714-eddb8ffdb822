{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\statisticsData.vue?vue&type=template&id=b6e74016&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\statisticsData.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div ref=\"container\">\n  <div class=\"public-wrapper\">\n    <div class=\"title\">\n      <span class=\"iconfont icon-xiangxishuju\"></span>详细数据\n    </div>\n    <div class=\"nav acea-row row-between-wrapper\">\n      <div class=\"data\">日期</div>\n      <div class=\"browse\">订单数</div>\n      <div class=\"turnover\">成交额</div>\n    </div>\n    <div class=\"conter\">\n      <div\n        class=\"item acea-row row-between-wrapper\"\n        v-for=\"(item, index) in list\"\n        :key=\"index\"\n      >\n        <div class=\"data\">{{ item.time }}</div>\n        <div class=\"browse\">{{ item.count }}</div>\n        <div class=\"turnover\">{{ item.price }}</div>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}