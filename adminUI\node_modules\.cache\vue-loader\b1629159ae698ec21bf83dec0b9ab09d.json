{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\Category\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\Category\\list.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport * as categoryApi from '@/api/categoryApi.js'\nimport info from './info'\nimport edit from './edit'\nimport * as selfUtil from '@/utils/ZBKJIutil.js'\nimport { checkPermi, checkRole } from \"@/utils/permission\";\nexport default {\n  // name: \"list\"\n  components: { info, edit },\n  props: {\n    biztype: { // 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类\n      type: Object,\n      default: { value: -1 },\n      validator: (obj) => {\n        return obj.value > 0\n      }\n    },\n    pid: {\n      type: Number,\n      default: 0,\n      validator: (value) => {\n        return value >= 0\n      }\n    },\n    selectModel: { // 是否选择模式\n      type: Boolean,\n      default: false\n    },\n    selectModelKeys: {\n      type: Array\n    },\n    rowSelect: {}\n  },\n  data() {\n    return {\n      selectModelKeysNew: this.selectModelKeys,\n      loading: false,\n      constants: this.$constants,\n      treeProps: {\n        label: 'name',\n        children: 'child',\n        // expandTrigger: 'hover',\n        // checkStrictly: false,\n        // emitPath: false\n      },\n      // treeCheckedKeys:[],// 选择模式下的属性结构默认选中\n      multipleSelection: [],\n      editDialogConfig: {\n        visible: false,\n        isCreate: 0, // 0=创建，1=编辑\n        prent: {}, // 父级对象\n        data: {},\n        biztype: this.biztype // 统一主业务中的目录类型\n      },\n      dataList: [],\n      treeList: [],\n      listPram: {\n        pid: this.pid,\n        type: this.biztype.value,\n        status: -1,\n        name: '',\n        page: this.$constants.page.page,\n        limit: this.$constants.page.limit[0]\n      },\n      viewInfoConfig: {\n        data: null,\n        visible: false\n      },\n      defaultImg:require('@/assets/imgs/moren.jpg')\n    }\n  },\n  mounted() {\n   /* if(this.biztype.value === 3){\n      this.listPram.pageSize = constants.page.pageSize[4]\n      this.handlerGetList()\n    }else{*/\n    this.handlerGetTreeList()\n    // }\n  },\n  methods: {\n    checkPermi, //权限控制\n    onchangeIsShow(row){\n      categoryApi.categroyUpdateStatus( row.id ).then(() => {\n        this.$message.success('修改成功')\n        this.handlerGetTreeList()\n      }).catch(()=>{\n        row.status = !row.status\n      })\n    },\n    handleEditMenu(rowData) {\n      this.editDialogConfig.isCreate = 1\n      this.editDialogConfig.data = rowData\n      this.editDialogConfig.prent = rowData\n      this.editDialogConfig.visible = true\n    },\n    handleAddMenu(rowData) {\n      this.editDialogConfig.isCreate = 0\n      this.editDialogConfig.prent = rowData\n      this.editDialogConfig.data = {}\n      this.editDialogConfig.biztype = this.biztype\n      this.editDialogConfig.visible = true\n    },\n    getCurrentNode(data) {\n      let node = this.$refs.tree.getNode(data);\n      this.childNodes(node);\n      // this.parentNodes(node);\n      //是否编辑的表示\n      // this.ruleForm.isEditorFlag = true;\n      //编辑时候使用\n      this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys());\n      // this.selectModelKeys = this.$refs.tree.getCheckedKeys();\n      //无论编辑和新增点击了就传到后台这个值\n      // this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys()));\n      // this.ruleForm.menuIdsisEditor = this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys());\n    },\n    //具体方法可以看element官网api\n    childNodes(node){\n      let len = node.childNodes.length;\n      for(let i = 0; i < len; i++){\n        node.childNodes[i].checked = node.checked;\n        this.childNodes(node.childNodes[i]);\n      }\n    },\n    parentNodes(node){\n      if(node.parent){\n        for(let key in node){\n          if(key == \"parent\"){\n            node[key].checked = true;\n            this.parentNodes(node[key]);\n          }\n        }\n      }\n    },\n    handleDelMenu(rowData) {\n      this.$confirm('确定删除当前数据?').then(() => {\n        categoryApi.deleteCategroy(rowData).then(data => {\n          this.handlerGetTreeList()\n          this.$message.success('删除成功')\n        })\n      })\n    },\n    handlerGetList() {\n      this.handlerGetTreeList();\n      // categoryApi.listCategroy({status:this.listPram.status, type: 1 }).then(data => {\n      //   this.treeList = data.list\n      // })\n    },\n    handlerGetTreeList() {\n      // this.biztype.value === 5 && !this.selectModel) ?  -1 : 1\n      // const _pram = { type: this.biztype.value, status: !this.selectModel ? -1 : (this.biztype.value === 5 ? -1 : 1) }\n      const _pram = { type: this.biztype.value, status: this.listPram.status, name: this.listPram.name  }\n      this.loading = true\n      this.biztype.value!==3 ? categoryApi.treeCategroy(_pram).then(data => {\n        this.treeList = this.handleAddArrt(data)\n        this.loading = false\n      }).catch(()=>{\n        this.loading = false\n      }) : categoryApi.listCategroy({ type: 3, status: this.listPram.status, pid: this.listPram.pid, name: this.listPram.name}).then(data => {\n        this.treeList = data.list\n      })\n    },\n    handlerGetInfo(id) {\n      this.viewInfoConfig.data = id\n      this.viewInfoConfig.visible = true\n    },\n    handleNodeClick(data) {\n      console.log('data:', data)\n    },\n    handleAddArrt(treeData) {\n      const _result = selfUtil.addTreeListLabel(treeData)\n      return _result\n    },\n    hideEditDialog() {\n      setTimeout(() => {\n        this.editDialogConfig.prent = {}\n        this.editDialogConfig.type = 0\n        this.editDialogConfig.visible = false\n        this.handlerGetTreeList()\n      }, 200)\n    },\n    handleSelectionChange(d1, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }) {\n      // this.multipleSelection =  checkedKeys.concat(halfCheckedKeys)\n      this.multipleSelection = checkedKeys\n      this.$emit('rulesSelect', this.multipleSelection)\n    },\n  }\n}\n", null]}