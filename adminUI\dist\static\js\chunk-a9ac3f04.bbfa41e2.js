(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a9ac3f04"],{"0bf0":function(e,t,n){e.exports=n.p+"static/img/lbg.c7afc734.png"},"1fe6":function(e,t,n){},"7daa":function(e,t){!function(){function e(e,t,n){return e.getAttribute(t)||n}function t(e){return document.getElementsByTagName(e)}function n(){var n=t("script"),o=n.length,i=n[o-1];return{l:o,z:e(i,"zIndex",-2),o:e(i,"opacity",.8),c:e(i,"color","255,255,255"),n:e(i,"count",240)}}function o(){a=s.width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,c=s.height=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight}function i(){if(d+=1,d<5)m(i);else{d=0,u.clearRect(0,0,a,c);var e,t,n,o,s,l,g=[h].concat(f);f.forEach((function(i){for(i.x+=i.xa,i.y+=i.ya,i.xa*=i.x>a||i.x<0?-1:1,i.ya*=i.y>c||i.y<0?-1:1,u.fillRect(i.x-.5,i.y-.5,2,2),u.fillStyle="#FFFFFF",t=0;t<g.length;t++)e=g[t],i!==e&&null!==e.x&&null!==e.y&&(o=i.x-e.x,s=i.y-e.y,l=o*o+s*s,l<e.max&&(e===h&&l>=e.max/2&&(i.x-=.03*o,i.y-=.03*s),n=(e.max-l)/e.max,u.beginPath(),u.lineWidth=n/2,u.strokeStyle="rgba("+r.c+","+(n+.2)+")",u.moveTo(i.x,i.y),u.lineTo(e.x,e.y),u.stroke()));g.splice(g.indexOf(i),1)})),m(i)}}var a,c,s=document.createElement("canvas"),r=n(),l="c_n"+r.l,u=s.getContext("2d"),d=0,m=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/45)},g=Math.random,h={x:null,y:null,max:2e4};s.id=l,s.style.cssText="position:fixed;top:0;left:0;z-index:"+r.z+";opacity:"+r.o,t("body")[0].appendChild(s),o(),window.onresize=o,window.onmousemove=function(e){e=e||window.event,h.x=e.clientX,h.y=e.clientY},window.onmouseout=function(){h.x=null,h.y=null};for(var f=[],p=0;r.n>p;p++){var y=g()*a,w=g()*c,b=2*g()-1,v=2*g()-1;f.push({x:y,y:w,xa:b,ya:v,max:6e3})}setTimeout((function(){i()}),100)}()},"9ed6":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"page-account",style:e.backgroundImages?{backgroundImage:"url("+e.backgroundImages+")"}:{backgroundImage:"url("+e.backgroundImageMo+")"}},[n("div",{staticClass:"container",class:[e.fullWidth>768?"containerSamll":"containerBig"]},[e.fullWidth>768?[n("div",{staticClass:"swiperPross"},[n("img",{staticStyle:{width:"100%"},attrs:{src:e.backgroundImageMo}})])]:e._e(),e._v(" "),n("div",{staticClass:"index_from page-account-container"},[n("div",{staticClass:"page-account-top ",staticStyle:{"font-size":"16px","font-weight":"bold"}},[e._v("\n          GENCO 管理登录\n        ")]),e._v(" "),n("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,autocomplete:"on","label-position":"left"},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}}},[n("el-form-item",{attrs:{prop:"account"}},[n("el-input",{ref:"account",attrs:{"prefix-icon":"el-icon-user",placeholder:"用户名",name:"username",type:"text",tabindex:"1",autocomplete:"on"},model:{value:e.loginForm.account,callback:function(t){e.$set(e.loginForm,"account",t)},expression:"loginForm.account"}})],1),e._v(" "),n("el-form-item",{attrs:{prop:"pwd"}},[n("el-input",{key:e.passwordType,ref:"pwd",attrs:{"prefix-icon":"el-icon-lock",type:e.passwordType,placeholder:"密码",name:"pwd",tabindex:"2","auto-complete":"on"},model:{value:e.loginForm.pwd,callback:function(t){e.$set(e.loginForm,"pwd",t)},expression:"loginForm.pwd"}}),e._v(" "),n("span",{staticClass:"show-pwd",on:{click:e.showPwd}},[n("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1),e._v(" "),n("el-form-item",{staticClass:"captcha",attrs:{prop:"code"}},[n("div",{staticClass:"captcha"},[n("el-input",{ref:"username",staticStyle:{width:"218px"},attrs:{"prefix-icon":"el-icon-message",placeholder:"验证码",name:"username",type:"text",tabindex:"3",autocomplete:"on"},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}}),e._v(" "),n("div",{staticClass:"imgs",on:{click:function(t){return e.getCaptcha()}}},[n("img",{attrs:{src:e.captchatImg}})])],1)]),e._v(" "),n("div",{staticClass:"acea-row"},[n("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v("登录\n          ")])],1)],1)],1)],2)])},i=[],a=(n("61f7"),n("7daa"),n("c24f")),c=n("02df"),s=n("74f9"),r=(n("ffd2"),n("5f87"),n("a78e"),{name:"Login",data:function(){return{captchatImg:"",swiperList:[],loginLogo:"",lglogo:n("1c3d"),backgroundImages:"",backgroundImageMo:n("0bf0"),fullWidth:document.body.clientWidth,swiperOption:{pagination:{el:".pagination"},autoplay:{enabled:!0,disableOnInteraction:!1,delay:3e3}},loginForm:{account:"admin",pwd:"BzX36YjvNR8o",key:"",code:"",wxCode:""},loginRules:{account:[{required:!0,trigger:"blur",message:"请输入用户名"}],pwd:[{required:!0,trigger:"blur",message:"请输入密码"}],code:[{required:!0,message:"请输入正确的验证码",trigger:"blur"}]},passwordType:"password",capsTooltip:!1,loading:!1,showDialog:!1,redirect:void 0,otherQuery:{}}},watch:{fullWidth:function(e){if(!this.timer){this.screenWidth=e,this.timer=!0;var t=this;setTimeout((function(){t.timer=!1}),400)}},$route:{handler:function(e){var t=e.query;t&&(this.redirect=t.redirect,this.otherQuery=this.getOtherQuery(t))},immediate:!0}},created:function(){var e=this;document.onkeydown=function(t){if(-1!==e.$route.path.indexOf("login")){var n=window.event.keyCode;13===n&&e.handleLogin()}},window.addEventListener("resize",this.handleResize)},mounted:function(){var e=this;this.getInfo(),this.$nextTick((function(){e.screenWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"})),""===this.loginForm.account?this.$refs.account.focus():""===this.loginForm.pwd&&this.$refs.pwd.focus(),this.getCaptcha(),this.agentWeiXinLogin()},beforeCreate:function(){this.fullWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"},destroyed:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize),document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg")},methods:{agentWeiXinLogin:function(){var e=this,t=this.$wechat.isWeixin();if(t){var n=this.$route.query.code,o=this.$route.query.state,i=location.origin+"/login";null==n&&Object(s["getWXCodeByUrl"])(i,"step1"),"step1"===o?Object(s["loginByWxCode"])(n).then((function(t){sessionStorage.setItem("token",t.token),e.$router.push({path:e.redirect||"/",query:e.otherQuery})})).catch((function(e){Object(s["getWXCodeByUrl"])(i,"step2")})):"step2"===o&&(this.loginForm.wxCode=n)}},onWechat:function(){var e=this.$route.query.redirect?this.$route.query.redirect:"/dashboard";this.$wechat.oAuth(e,"login")},handleResize:function(e){this.fullWidth=document.body.clientWidth,this.fullWidth<768?document.getElementsByTagName("canvas")[0].removeAttribute("class","index_bg"):document.getElementsByTagName("canvas")[0].className="index_bg"},getInfo:function(){var e=this;Object(a["e"])().then((function(t){e.swiperList=t.banner,e.loginLogo=t.loginLogo,e.backgroundImages=t.backgroundImage}))},checkCapslock:function(e){var t=e.key;this.capsTooltip=t&&1===t.length&&t>="A"&&t<="Z"},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.pwd.focus()}))},handleLogin:function(){var e=this,t=this.$route.query.code;this.$refs.loginForm.validate((function(n){if(!n)return!1;e.loading=!0,e.$wechat.isWeixin()&&(e.loginForm.wxCode=t),e.$store.dispatch("user/login",e.loginForm).then((function(){e.$router.push({path:e.redirect||"/",query:e.otherQuery}),Object(c["b"])(),e.loading=!1})).catch((function(t){e.loading=!1,e.$wechat.isPhone()&&e.$dialog.error(t.message),e.getCaptcha()}))}))},getCaptcha:function(){var e=this;Object(a["b"])().then((function(t){e.captchatImg=t.code,e.loginForm.key=t.key})).catch((function(t){var n=t.message;e.$message.error(n)}))},getOtherQuery:function(e){return Object.keys(e).reduce((function(t,n){return"redirect"!==n&&(t[n]=e[n]),t}),{})}}}),l=r,u=(n("a6a4"),n("b677"),n("2877")),d=Object(u["a"])(l,o,i,!1,null,"58c17e32",null);t["default"]=d.exports},a6a4:function(e,t,n){"use strict";n("c888")},b677:function(e,t,n){"use strict";n("1fe6")},c888:function(e,t,n){}}]);