(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d68c2024"],{"0018":function(t,e,r){"use strict";r("0e5d")},"025b":function(t,e,r){},"0e5d":function(t,e,r){},1849:function(t,e,r){"use strict";r("e2fe")},9406:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[t.checkPermi(["admin:statistics:home:index"])?r("base-info",{ref:"baseInfo"}):t._e(),t._v(" "),r("grid-menu",{staticClass:"mb20"}),t._v(" "),r("visit-chart",{ref:"visitChart"}),t._v(" "),t.checkPermi(["admin:statistics:home:chart:user"])?r("user-chart",{ref:"userChart",staticClass:"mb20"}):t._e()],1)},a=[],n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox",staticStyle:{"padding-bottom":"0"}},[r("el-row",{staticClass:"baseInfo",attrs:{gutter:20}},[r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("销售额")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.sales))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdaySales)+" 元")])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("用户访问量")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.pageviews))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayPageviews))])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("订单量")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.orderNum))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayOrderNum)+"单")])])],1):t._e()])],1),t._v(" "),r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1,"dis-hover":"",padding:12}},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row align-center"},[r("span",{staticClass:"main_tit"},[t._v("新增用户")])]),t._v(" "),r("el-tag",{attrs:{type:"primary"}},[t._v("今日")])],1),t._v(" "),t.viewData?r("div",{staticClass:"content"},[r("span",{staticClass:"content-number spBlock my15"},[t._v(t._s(t.viewData.newUserNum))]),t._v(" "),r("el-divider"),t._v(" "),r("div",{staticClass:"acea-row row-between-wrapper"},[r("span",{staticClass:"content-time"},[t._v("昨日数据")]),t._v(" "),r("span",{staticClass:"content-time"},[t._v(t._s(t.viewData.yesterdayNewUserNum)+" 人")])])],1):t._e()])],1)],1)],1)},s=[],o=r("b775");function c(){return Object(o["a"])({url:"/admin/statistics/home/<USER>",method:"GET"})}function l(){return Object(o["a"])({url:"/admin/statistics/home/<USER>/user",method:"get"})}function u(){return Object(o["a"])({url:"/admin/statistics/home/<USER>/order",method:"get"})}function d(){return Object(o["a"])({url:"/admin/statistics/home/<USER>/order/month",method:"get"})}function h(){return Object(o["a"])({url:"/admin/statistics/home/<USER>/order/week",method:"get"})}function f(){return Object(o["a"])({url:"/admin/statistics/home/<USER>/order/year",method:"get"})}function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function n(r,i,a,n){var c=i&&i.prototype instanceof o?i:o,l=Object.create(c.prototype);return p(l,"_invoke",function(r,i,a){var n,o,c,l=0,u=a||[],d=!1,h={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,r){return n=e,o=0,c=t,h.n=r,s}};function f(r,i){for(o=r,c=i,e=0;!d&&l&&!a&&e<u.length;e++){var a,n=u[e],f=h.p,v=n[2];r>3?(a=v===i)&&(c=n[(o=n[4])?5:(o=3,3)],n[4]=n[5]=t):n[0]<=f&&((a=r<2&&f<n[1])?(o=0,h.v=i,h.n=n[1]):f<v&&(a=r<3||n[0]>i||i>v)&&(n[4]=r,n[5]=i,h.n=v,o=0))}if(a||r>1)return s;throw d=!0,i}return function(a,u,v){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,v),o=u,c=v;(e=o<2?t:c)||!d;){n||(o?o<3?(o>1&&(h.n=-1),f(o,c)):h.n=c:h.v=c);try{if(l=2,n){if(o||(a="next"),e=n[a]){if(!(e=e.call(n,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,o<2&&(o=0)}else 1===o&&(e=n.return)&&e.call(n),o<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),o=1);n=t}else if((e=(d=h.n<0)?c:r.call(i,h))!==s)break}catch(e){n=t,o=1,c=e}finally{l=1}}return{value:e,done:d}}}(r,a,n),!0),l}var s={};function o(){}function c(){}function l(){}e=Object.getPrototypeOf;var u=[][i]?e(e([][i]())):(p(e={},i,(function(){return this})),e),d=l.prototype=o.prototype=Object.create(u);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,p(t,a,"GeneratorFunction")),t.prototype=Object.create(d),t}return c.prototype=l,p(d,"constructor",l),p(l,"constructor",c),c.displayName="GeneratorFunction",p(l,a,"GeneratorFunction"),p(d),p(d,a,"Generator"),p(d,i,(function(){return this})),p(d,"toString",(function(){return"[object Generator]"})),(v=function(){return{w:n,m:h}})()}function p(t,e,r,i){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}p=function(t,e,r,i){function n(e,r){p(t,e,(function(t){return this._invoke(e,r,t)}))}e?a?a(t,e,{value:r,enumerable:!i,configurable:!i,writable:!i}):t[e]=r:(n("next",0),n("throw",1),n("return",2))},p(t,e,r,i)}function m(t,e,r,i,a,n,s){try{var o=t[n](s),c=o.value}catch(t){return void r(t)}o.done?e(c):Promise.resolve(c).then(i,a)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function s(t){m(n,i,a,s,o,"next",t)}function o(t){m(n,i,a,s,o,"throw",t)}s(void 0)}))}}var b={data:function(){return{grid:{xl:6,lg:6,md:12,sm:12,xs:24},viewData:{}}},methods:{statisticsOrder:function(){var t=this;c().then(function(){var e=y(v().m((function e(r){return v().w((function(e){while(1)switch(e.n){case 0:t.viewData=r;case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())}},mounted:function(){this.statisticsOrder()}},_=b,w=(r("0018"),r("2877")),g=Object(w["a"])(_,n,s,!1,null,"45acb3d2",null),C=g.exports,x=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-row",{staticClass:"dashboard-console-grid",attrs:{gutter:24}},[t.checkPermi(["admin:user:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/user/index"}}},[r("i",{staticClass:"el-icon-user",staticStyle:{color:"#69c0ff"}}),t._v(" "),r("p",[t._v("会员管理")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:system:config:info"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/operation/setting"}}},[r("i",{staticClass:"el-icon-setting",staticStyle:{color:"#95de64"}}),t._v(" "),r("p",[t._v("系统设置")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:product:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/store/index"}}},[r("i",{staticClass:"el-icon-goods",staticStyle:{color:"#ff9c6e"}}),t._v(" "),r("p",[t._v("商品")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:order:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/order/index"}}},[r("i",{staticClass:"el-icon-s-order",staticStyle:{color:"#b37feb"}}),t._v(" "),r("p",[t._v("订单管理")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:pass:login"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/operation/systemSms/config"}}},[r("i",{staticClass:"el-icon-message",staticStyle:{color:"#ffd666"}}),t._v(" "),r("p",[t._v("短信配置")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:article:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/content/articleManager"}}},[r("i",{staticClass:"el-icon-notebook-1",staticStyle:{color:"#5cdbd3"}}),t._v(" "),r("p",[t._v("文章管理")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:retail:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/distribution/index"}}},[r("i",{staticClass:"el-icon-s-finance",staticStyle:{color:"#ff85c0"}}),t._v(" "),r("p",[t._v("分销管理")])])],1)],1):t._e(),t._v(" "),t.checkPermi(["admin:coupon:list"])?r("el-col",t._b({staticClass:"ivu-mb"},"el-col",t.grid,!1),[r("el-card",{attrs:{bordered:!1}},[r("router-link",{attrs:{to:{path:"/marketing/coupon/list"}}},[r("i",{staticClass:"el-icon-s-ticket",staticStyle:{color:"#ffc069"}}),t._v(" "),r("p",[t._v("优惠券")])])],1)],1):t._e()],1)],1)},D=[],S=r("e350"),k={data:function(){return{grid:{xl:3,lg:3,md:6,sm:8,xs:8}}},methods:{checkPermi:S["a"]}},A=k,O=(r("c5ec"),Object(w["a"])(A,x,D,!1,null,"1403bf1a",null)),F=O.exports,j=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[t.checkPermi(["admin:statistics:home:chart:order","admin:statistics:home:chart:order:week","admin:statistics:home:chart:order:month","admin:statistics:home:chart:order:year"])?r("el-row",{attrs:{gutter:24}},[r("el-col",{attrs:{san:"24"}},[r("el-card",{staticClass:"dashboard-console-visit",attrs:{bordered:!1}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"acea-row row-between-wrapper"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"header_title"},[t._v("订单统计")])]),t._v(" "),r("div",{staticClass:"checkTime"},[r("el-radio-group",{staticClass:"ivu-mr-8",on:{change:t.radioChange},model:{value:t.visitDate,callback:function(e){t.visitDate=e},expression:"visitDate"}},[r("el-radio-button",{attrs:{label:"last30"}},[t._v("30天")]),t._v(" "),r("el-radio-button",{attrs:{label:"week"}},[t._v("周")]),t._v(" "),r("el-radio-button",{attrs:{label:"month"}},[t._v("月")]),t._v(" "),r("el-radio-button",{attrs:{label:"year"}},[t._v("年")])],1)],1)])]),t._v(" "),r("h4",[t._v("订单量趋势")]),t._v(" "),t.info?r("echarts-from",{ref:"visitChart",attrs:{yAxisData:t.yAxisData,seriesData:t.series,xAxis:t.xAxis,legendData:t.legendData}}):t._e()],1)],1)],1):t._e()],1)},P=[],T=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{style:t.styles,attrs:{id:t.echarts}})])},E=[],L=r("313e"),B=r.n(L),N={name:"index",props:{seriesData:{type:Array,default:function(){return[]}},xAxis:{type:Array,default:function(){return[]}},echartsTitle:{type:String,default:""},yAxisData:{type:Array,default:function(){return[]}},legendData:{type:Array,default:function(){return[]}}},data:function(){return{styles:"height:300px",infoLists:this.infoList,seriesArray:this.seriesData}},watch:{seriesData:{handler:function(t,e){this.seriesArray=t,this.handleSetVisitChart()},deep:!0}},computed:{echarts:function(){return"echarts"+Math.ceil(100*Math.random())}},mounted:function(){var t=this,e=this;e.$nextTick((function(){e.handleSetVisitChart(),window.addEventListener("resize",t.wsFunc)}))},methods:{wsFunc:function(){this.myChart.resize()},handleSetVisitChart:function(){this.myChart=B.a.init(document.getElementById(this.echarts));var t=null;t="circle"===this.echartsTitle?{tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right",data:this.legendData||[]},series:[{name:"访问来源",type:"pie",radius:"70%",center:["50%","60%"],data:this.seriesArray||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}:{tooltip:{trigger:"axis"},toolbox:{},legend:{data:this.legendData||[]},color:["#1495EB","#00CC66","#F9D249","#ff9900","#9860DF"],grid:{left:16,right:25,bottom:10,top:40,containLabel:!0},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#D7DDE4"}},axisTick:{alignWithLabel:!0,lineStyle:{color:"#D7DDE4"}},splitLine:{show:!1,lineStyle:{color:"#F5F7F9"}},axisLabel:{interval:0,rotate:40,textStyle:{color:"#7F8B9C"}},data:this.xAxis}],yAxis:this.yAxisData.length?this.yAxisData:{axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}},type:"value"},series:this.seriesArray},this.myChart.setOption(t,!0)},handleResize:function(){this.myChart.resize()}},beforeDestroy:function(){window.removeEventListener("resize",this.wsFunc),this.myChart&&(this.myChart.dispose(),this.myChart=null)}},G=N,I=Object(w["a"])(G,T,E,!1,null,"1fe6eea3",null),z=I.exports;function $(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function n(r,i,a,n){var c=i&&i.prototype instanceof o?i:o,l=Object.create(c.prototype);return q(l,"_invoke",function(r,i,a){var n,o,c,l=0,u=a||[],d=!1,h={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,r){return n=e,o=0,c=t,h.n=r,s}};function f(r,i){for(o=r,c=i,e=0;!d&&l&&!a&&e<u.length;e++){var a,n=u[e],f=h.p,v=n[2];r>3?(a=v===i)&&(c=n[(o=n[4])?5:(o=3,3)],n[4]=n[5]=t):n[0]<=f&&((a=r<2&&f<n[1])?(o=0,h.v=i,h.n=n[1]):f<v&&(a=r<3||n[0]>i||i>v)&&(n[4]=r,n[5]=i,h.n=v,o=0))}if(a||r>1)return s;throw d=!0,i}return function(a,u,v){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,v),o=u,c=v;(e=o<2?t:c)||!d;){n||(o?o<3?(o>1&&(h.n=-1),f(o,c)):h.n=c:h.v=c);try{if(l=2,n){if(o||(a="next"),e=n[a]){if(!(e=e.call(n,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,o<2&&(o=0)}else 1===o&&(e=n.return)&&e.call(n),o<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),o=1);n=t}else if((e=(d=h.n<0)?c:r.call(i,h))!==s)break}catch(e){n=t,o=1,c=e}finally{l=1}}return{value:e,done:d}}}(r,a,n),!0),l}var s={};function o(){}function c(){}function l(){}e=Object.getPrototypeOf;var u=[][i]?e(e([][i]())):(q(e={},i,(function(){return this})),e),d=l.prototype=o.prototype=Object.create(u);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,q(t,a,"GeneratorFunction")),t.prototype=Object.create(d),t}return c.prototype=l,q(d,"constructor",l),q(l,"constructor",c),c.displayName="GeneratorFunction",q(l,a,"GeneratorFunction"),q(d),q(d,a,"Generator"),q(d,i,(function(){return this})),q(d,"toString",(function(){return"[object Generator]"})),($=function(){return{w:n,m:h}})()}function q(t,e,r,i){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}q=function(t,e,r,i){function n(e,r){q(t,e,(function(t){return this._invoke(e,r,t)}))}e?a?a(t,e,{value:r,enumerable:!i,configurable:!i,writable:!i}):t[e]=r:(n("next",0),n("throw",1),n("return",2))},q(t,e,r,i)}function Q(t,e,r,i,a,n,s){try{var o=t[n](s),c=o.value}catch(t){return void r(t)}o.done?e(c):Promise.resolve(c).then(i,a)}function M(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function s(t){Q(n,i,a,s,o,"next",t)}function o(t){Q(n,i,a,s,o,"throw",t)}s(void 0)}))}}var R={components:{echartsFrom:z},data:function(){return{infoList:null,visitDate:"last30",series:[],xAxis:[],info:{},legendData:[],yAxisData:[]}},mounted:function(){this.yAxisData=[{type:"value",name:"金额",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"数量",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}]},methods:{checkPermi:S["a"],radioChange:function(t){switch(t){case"week":this.handleChangeWeek();break;case"month":this.handleChangeMonth();break;case"year":this.handleChangeYear();break;default:this.handleChangeVisitType();break}},handleChangeVisitType:function(){var t=this;this.xAxis=[],this.legendData=[],u().then(function(){var e=M($().m((function e(r){var i,a,n,s;return $().w((function(e){while(1)switch(e.n){case 0:for(n in t.info=r,i=[],a=[],r.price)i.push(Number(r.price[n])),t.xAxis.push(n);for(s in r.quality)a.push(Number(r.quality[s]));t.legendData=["订单金额","订单数"],t.series=[{name:"订单金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:i},{name:"订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#4BCAD5"}},yAxisIndex:1,data:a}];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeWeek:function(){var t=this;this.xAxis=[],this.legendData=[],h().then(function(){var e=M($().m((function e(r){var i,a,n,s,o,c,l,u;return $().w((function(e){while(1)switch(e.n){case 0:for(o in t.info=r,t.legendData=["上周金额","本周金额","上周订单数","本周订单数"],i=[],a=[],n=[],s=[],r.prePrice)i.push(Number(r.prePrice[o])),t.xAxis.push(o);for(c in r.price)a.push(Number(r.price[c]));for(l in r.preQuality)s.push(Number(r.preQuality[l]));for(u in r.quality)n.push(Number(r.quality[u]));t.series=[{name:"上周金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:i},{name:"本周金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:a},{name:"上周订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:s},{name:"本周订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:n}];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeMonth:function(){var t=this;this.xAxis=[],this.legendData=[],d().then(function(){var e=M($().m((function e(r){var i,a,n,s,o,c,l,u;return $().w((function(e){while(1)switch(e.n){case 0:for(o in t.info=r,t.legendData=["上月金额","本月金额","上月订单数","本月订单数"],i=[],a=[],n=[],s=[],r.prePrice)i.push(Number(r.prePrice[o])),t.xAxis.push(o);for(c in r.price)a.push(Number(r.price[c]));for(l in r.preQuality)s.push(Number(r.preQuality[l]));for(u in r.quality)n.push(Number(r.quality[u]));t.series=[{name:"上月金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:i},{name:"本月金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:a},{name:"上月订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:s},{name:"本月订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:n}];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleChangeYear:function(){var t=this;this.xAxis=[],this.legendData=[],f().then(function(){var e=M($().m((function e(r){var i,a,n,s,o,c,l,u;return $().w((function(e){while(1)switch(e.n){case 0:for(o in t.info=r,t.legendData=["去年金额","今年金额","去年订单数","今年订单数"],i=[],a=[],n=[],s=[],r.prePrice)i.push(Number(r.prePrice[o])),t.xAxis.push(o);for(c in r.price)a.push(Number(r.price[c]));for(l in r.preQuality)s.push(Number(r.preQuality[l]));for(u in r.quality)n.push(Number(r.quality[u]));t.series=[{name:"去年金额",type:"bar",itemStyle:{normal:{color:"#5B8FF9"}},data:i},{name:"今年金额",type:"bar",itemStyle:{normal:{color:"#4BCAD5"}},data:a},{name:"去年订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#E6A23C"}},yAxisIndex:1,data:s},{name:"今年订单数",type:"line",smooth:!0,itemStyle:{normal:{color:"#768A9C"}},yAxisIndex:1,data:n}];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleResize:function(){this.infoList&&this.$refs.visitChart.handleResize()}},created:function(){this.handleChangeVisitType()}},V=R,U=(r("1849"),Object(w["a"])(V,j,P,!1,null,"79ef721c",null)),W=U.exports,J=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"divBox"},[r("el-row",{attrs:{gutter:24}},[r("el-col",{staticClass:"ivu-mb mb10 dashboard-console-visit"},[r("el-card",{attrs:{bordered:!1,"dis-hover":""}},[r("div",{attrs:{slot:"header"},slot:"header"},[r("div",{staticClass:"acea-row row-middle"},[r("div",{staticClass:"header_title"},[t._v("用户统计")])])]),t._v(" "),t.infoList?r("echarts-from",{ref:"userChart",attrs:{echartsTitle:t.line,xAxis:t.xAxis,seriesData:t.series}}):t._e()],1)],1)],1)],1)},Y=[];function H(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function n(r,i,a,n){var c=i&&i.prototype instanceof o?i:o,l=Object.create(c.prototype);return X(l,"_invoke",function(r,i,a){var n,o,c,l=0,u=a||[],d=!1,h={p:0,n:0,v:t,a:f,f:f.bind(t,4),d:function(e,r){return n=e,o=0,c=t,h.n=r,s}};function f(r,i){for(o=r,c=i,e=0;!d&&l&&!a&&e<u.length;e++){var a,n=u[e],f=h.p,v=n[2];r>3?(a=v===i)&&(c=n[(o=n[4])?5:(o=3,3)],n[4]=n[5]=t):n[0]<=f&&((a=r<2&&f<n[1])?(o=0,h.v=i,h.n=n[1]):f<v&&(a=r<3||n[0]>i||i>v)&&(n[4]=r,n[5]=i,h.n=v,o=0))}if(a||r>1)return s;throw d=!0,i}return function(a,u,v){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,v),o=u,c=v;(e=o<2?t:c)||!d;){n||(o?o<3?(o>1&&(h.n=-1),f(o,c)):h.n=c:h.v=c);try{if(l=2,n){if(o||(a="next"),e=n[a]){if(!(e=e.call(n,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,o<2&&(o=0)}else 1===o&&(e=n.return)&&e.call(n),o<2&&(c=TypeError("The iterator does not provide a '"+a+"' method"),o=1);n=t}else if((e=(d=h.n<0)?c:r.call(i,h))!==s)break}catch(e){n=t,o=1,c=e}finally{l=1}}return{value:e,done:d}}}(r,a,n),!0),l}var s={};function o(){}function c(){}function l(){}e=Object.getPrototypeOf;var u=[][i]?e(e([][i]())):(X(e={},i,(function(){return this})),e),d=l.prototype=o.prototype=Object.create(u);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,X(t,a,"GeneratorFunction")),t.prototype=Object.create(d),t}return c.prototype=l,X(d,"constructor",l),X(l,"constructor",c),c.displayName="GeneratorFunction",X(l,a,"GeneratorFunction"),X(d),X(d,a,"Generator"),X(d,i,(function(){return this})),X(d,"toString",(function(){return"[object Generator]"})),(H=function(){return{w:n,m:h}})()}function X(t,e,r,i){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}X=function(t,e,r,i){function n(e,r){X(t,e,(function(t){return this._invoke(e,r,t)}))}e?a?a(t,e,{value:r,enumerable:!i,configurable:!i,writable:!i}):t[e]=r:(n("next",0),n("throw",1),n("return",2))},X(t,e,r,i)}function K(t,e,r,i,a,n,s){try{var o=t[n](s),c=o.value}catch(t){return void r(t)}o.done?e(c):Promise.resolve(c).then(i,a)}function Z(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function s(t){K(n,i,a,s,o,"next",t)}function o(t){K(n,i,a,s,o,"throw",t)}s(void 0)}))}}var tt={name:"user-chart",components:{echartsFrom:z},data:function(){return{line:"line",circle:"circle",xAxis:[],infoList:{},series:[],xData:[],y1Data:[],y2Data:[],lists:[],bing_data:[],bing_xdata:[],legendData:[],seriesUser:[],chartBuy:{}}},methods:{getStatistics:function(){var t=this;l().then(function(){var e=Z(H().m((function e(r){var i,a;return H().w((function(e){while(1)switch(e.n){case 0:for(a in t.infoList=r,i=[],r)i.push(r[a]),t.xAxis.push(a);t.series=[{data:i,name:"人数（人）",type:"line",tooltip:!0,smooth:!0,symbol:"none",areaStyle:{normal:{opacity:.2}}}];case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleResize:function(){this.infoList&&0!==this.series.length&&this.$refs.userChart.handleResize(),this.infoList&&this.$refs.visitChart.handleResize()}},mounted:function(){this.getStatistics()},beforeDestroy:function(){this.visitChart&&(this.visitChart.dispose(),this.visitChart=null)}},et=tt,rt=(r("9fb8"),Object(w["a"])(et,J,Y,!1,null,"4e448304",null)),it=rt.exports,at={name:"Dashboard",components:{baseInfo:C,gridMenu:F,visitChart:W,userChart:it},data:function(){return{authStatus:null,authHost:"",authQueryStatus:!1}},methods:{checkPermi:S["a"]}},nt=at,st=Object(w["a"])(nt,i,a,!1,null,null,null);e["default"]=st.exports},"9c8f":function(t,e,r){},"9fb8":function(t,e,r){"use strict";r("025b")},c5ec:function(t,e,r){"use strict";r("9c8f")},e2fe:function(t,e,r){}}]);