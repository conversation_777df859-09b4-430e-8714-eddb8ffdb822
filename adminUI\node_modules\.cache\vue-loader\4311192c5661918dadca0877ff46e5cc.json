{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=template&id=1907ea3f&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754382949112}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n <div class=\"divBox relative\">\n   <el-card class=\"box-card\">\n     <div slot=\"header\" class=\"clearfix\">\n       <div class=\"container mt-1\">\n         <el-form inline size=\"small\">\n           <el-form-item :label=\"$t('product.search')\">\n             <el-input\n               v-model=\"form.keywords\"\n               :placeholder=\"$t('product.enterProductName')\"\n               class=\"selWidth\"\n               size=\"small\"\n               clearable\n             />\n           </el-form-item>\n           <el-form-item :label=\"$t('product.status')\">\n             <el-select\n               v-model=\"form.isShow\"\n               :placeholder=\"$t('product.pleaseSelect')\"\n             >\n               <el-option\n                 v-for=\"item in statusOptions\"\n                 :key=\"item.value\"\n                 :label=\"$t('product.' + item.label)\"\n                 :value=\"item.value\"\n               />\n             </el-select>\n           </el-form-item>\n\n           <el-form-item :label=\"$t('brand.brandName')\">\n             <el-select\n               v-model=\"form.brand\"\n               :placeholder=\"$t('product.pleaseSelect')\"\n               clearable\n             >\n               <el-option\n                 v-for=\"item in brandOptions\"\n                 :key=\"item.value\"\n                 :label=\"item.label\"\n                 :value=\"item.value\"\n               />\n             </el-select>\n           </el-form-item>\n         </el-form>\n       </div>\n       <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"onSearch\">\n         {{ $t(\"product.query\") }}\n       </el-button>\n       <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"onReset\">\n         {{ $t(\"product.reset\") }}\n       </el-button>\n\n       <div class=\"acea-row padtop-10\">\n         <el-button size=\"small\" type=\"success\" @click=\"onAdd\">\n           {{ $t(\"product.addProduct\") }}\n         </el-button>\n         <el-button size=\"small\" @click=\"batchHandle('online')\">\n           {{ $t(\"product.batchOnline\") }}\n         </el-button>\n         <el-button size=\"small\" @click=\"batchHandle('outline')\">\n           {{ $t(\"product.batchOffline\") }}\n         </el-button>\n         <el-button size=\"small\" @click=\"batchHandle('delete')\">\n           {{ $t(\"product.batchDelete\") }}\n         </el-button>\n       </div>\n     </div>\n\n     <el-table\n       v-loading=\"listLoading\"\n       :data=\"tableData.data\"\n       style=\"width: 100%\"\n       size=\"mini\"\n       :highlight-current-row=\"true\"\n       :header-cell-style=\"{ fontWeight: 'bold' }\"\n       @selection-change=\"handleSelection\"\n     >\n       <el-table-column type=\"selection\" width=\"55\" />\n       <el-table-column :label=\"$t('product.productImage')\" min-width=\"80\">\n         <template slot-scope=\"scope\">\n           <div class=\"demo-image__preview\">\n             <el-image\n               style=\"width: 36px; height: 36px\"\n               :src=\"scope.row.image || ''\"\n               :preview-src-list=\"[scope.row.image || '']\"\n             />\n           </div>\n         </template>\n       </el-table-column>\n       <el-table-column\n         :label=\"$t('product.productName')\"\n         min-width=\"160\"\n         :show-overflow-tooltip=\"true\"\n         prop=\"storeName\"\n       />\n       <el-table-column\n         :label=\"$t('product.productPrice')\"\n         min-width=\"90\"\n         align=\"center\"\n       >\n       <template slot-scope=\"scope\">\n           {{formatAmount(scope.row.price)}}\n       </template>\n       \n       </el-table-column>\n       <el-table-column\n         :label=\"$t('product.cashbackRate')\"\n         min-width=\"100\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">{{\n           formatRate(scope.row.cashBackRate)\n         }}</template>\n       </el-table-column>\n\n\n       <el-table-column\n         :label=\"$t('product.isHot')\"\n         min-width=\"80\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">\n           <el-switch\n             v-model=\"scope.row.isHot\"\n             :active-value=\"true\"\n             :inactive-value=\"false\"\n             @change=\"isShowChange(scope.row, scope.row.isHot, 'isHot')\"\n           />\n         </template>\n       </el-table-column>\n\n       <el-table-column\n         :label=\"$t('product.isBenefit')\"\n         min-width=\"80\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">\n           <el-switch\n             v-model=\"scope.row.isBenefit\"\n             :active-value=\"true\"\n             :inactive-value=\"false\"\n             @change=\"\n               isShowChange(scope.row, scope.row.isBenefit, 'isBenefit')\n             \"\n           />\n         </template>\n       </el-table-column>\n\n       <el-table-column\n         :label=\"$t('product.isTikTok')\"\n         min-width=\"80\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">\n           <el-switch\n             v-model=\"scope.row.isBest\"\n             :active-value=\"true\"\n             :inactive-value=\"false\"\n             @change=\"isShowChange(scope.row, scope.row.isBest, 'isBest')\"\n           />\n         </template>\n       </el-table-column>\n\n      \n\n       <el-table-column\n         :label=\"$t('product.addTime')\"\n         min-width=\"120\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">{{\n           formatTime(scope.row.addTime)\n         }}</template>\n       </el-table-column>\n\n\n        <el-table-column\n         :label=\"$t('product.online')\"\n         min-width=\"80\"\n         align=\"center\"\n       >\n           <template slot-scope=\"scope\">\n               <el-switch\n                 v-model=\"scope.row.isShow\"\n                 :active-value=\"true\"\n                 :inactive-value=\"false\"\n                 @change=\"handleUpdate(scope.row)\"\n               />\n           </template>\n       </el-table-column>\n\n       <el-table-column\n         :label=\"$t('product.action')\"\n         min-width=\"100\"\n         fixed=\"right\"\n         align=\"center\"\n       >\n         <template slot-scope=\"scope\">\n           <!-- <el-button\n             type=\"text\"\n             size=\"small\"\n             class=\"mr10\"\n             @click=\"handleUpdate(scope.row, scope.$index)\"\n           >\n             <span v-if=\"scope.row.isShow\">{{ $t(\"product.offline\") }}</span>\n             <span v-else>{{ $t(\"product.online\") }}</span>\n           </el-button>\n-->\n           <el-button\n             type=\"text\"\n             size=\"small\"\n             class=\"mr10\"\n             @click=\"editProduct(scope.row, scope.$index)\"\n           >\n             {{ $t(\"product.edit\") }}\n           </el-button>\n           <el-button\n             type=\"text\"\n             size=\"small\"\n             @click=\"handleDelete(scope.row, scope.$index)\"\n           >\n             {{ $t(\"product.delete\") }}\n           </el-button>\n         </template>\n       </el-table-column>\n     </el-table>\n\n     <div class=\"block\">\n       <el-pagination\n         :page-sizes=\"[20, 40, 60, 80]\"\n         :page-size=\"form.limit\"\n         :current-page=\"form.page\"\n         layout=\"total, sizes, prev, pager, next, jumper\"\n         :total=\"tableData.total\"\n         @size-change=\"handleSizeChange\"\n         @current-change=\"pageChange\"\n       />\n     </div>\n   </el-card>\n\n   <el-dialog\n     :title=\"isEditMode ? $t('product.editDialogTitle') : $t('product.addDialogTitle')\"\n     :visible.sync=\"productDialogVisible\"\n     width=\"540px\"\n     :before-close=\"handleCloseProductDialog\"\n   >\n     <el-form\n       class=\"mt24\"\n       ref=\"dform\"\n       :model=\"dform\"\n       label-width=\"160px\"\n       @submit.native.prevent\n       v-loading=\"loading\"\n     >\n       <el-form-item :label=\"$t('product.enterProductLink')\">\n         <el-input\n           v-model=\"url\"\n           placeholder=\"please input link\"\n           class=\"selWidth width200\"\n           size=\"small\"\n           clearable\n         />\n         <el-button size=\"small\" @click=\"fetchProduct\">\n           {{ $t(\"product.fetchProductInfo\") }}\n         </el-button>\n       </el-form-item>\n\n       <el-form-item :label=\"$t('brand.brandName')\">\n         <div style=\"display: flex; align-items: center; gap: 8px;\">\n           <el-select\n             v-model=\"brandName\"\n             :placeholder=\"$t('brand.pleaseSelect')\"\n             :loading=\"brandLoading\"\n             filterable\n             clearable\n             style=\"flex: 1;\"\n           >\n             <el-option\n               v-for=\"item in brandOptions\"\n               :key=\"item.value\"\n               :label=\"item.label\"\n               :value=\"item.value\"\n             />\n           </el-select>\n           <el-button\n             size=\"small\"\n             icon=\"el-icon-refresh\"\n             :loading=\"brandLoading\"\n             @click=\"refreshBrands\"\n             :title=\"$t('brand.refreshBrands') || '刷新品牌数据'\"\n           />\n         </div>\n       </el-form-item>\n       <el-form-item :label=\"$t('product.productName')\">\n         <el-input\n           readonly\n           v-model=\"dform.storeName\"\n           :placeholder=\"$t('product.enterProductName')\"\n           class=\"selWidth readonly-input\"\n           size=\"small\"\n           clearable\n         />\n       </el-form-item>\n       <el-form-item :label=\"$t('product.productImage')\">\n         <template slot-scope=\"scope\">\n           <div class=\"demo-image__preview\">\n             <el-image\n               style=\"width: 36px; height: 36px\"\n               :src=\"dform.image\"\n               :preview-src-list=\"[dform.image]\"\n             />\n           </div>\n         </template>\n       </el-form-item>\n       <el-form-item :label=\"$t('product.productPrice')\">\n         <el-input\n           readonly\n           v-model=\"dform.price\"\n           :placeholder=\"$t('product.enterProductPrice')\"\n           class=\"selWidth readonly-input\"\n           size=\"small\"\n           clearable\n         />\n       </el-form-item>\n       <el-form-item :label=\"$t('product.cashbackRate')\">\n         <el-input\n           readonly\n           v-model=\"dform.forMatCashBackRate\"\n           :placeholder=\"$t('product.enterCashbackRate')\"\n           class=\"selWidth readonly-input\"\n           size=\"small\"\n           clearable\n         />\n       </el-form-item>\n\n       <el-form-item :label=\"$t('product.isOnline')\">\n         <el-select v-model=\"status\" :placeholder=\"$t('product.pleaseSelect')\">\n           <el-option\n             v-for=\"item in typeOptions\"\n             :key=\"item.value\"\n             :label=\"$t('product.' + item.label)\"\n             :value=\"item.value\"\n           />\n         </el-select>\n       </el-form-item>\n     </el-form>\n     <span slot=\"footer\" class=\"dialog-footer\">\n       <el-button type=\"primary\" @click=\"onSubProduct\">{{\n         $t(\"product.confirm\")\n       }}</el-button>\n       <el-button @click=\"handleCloseProductDialog\">{{\n         $t(\"product.cancel\")\n       }}</el-button>\n     </span>\n   </el-dialog>\n </div>\n", null]}