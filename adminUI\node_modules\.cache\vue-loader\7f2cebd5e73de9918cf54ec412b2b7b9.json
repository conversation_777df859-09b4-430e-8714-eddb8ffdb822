{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue?vue&type=template&id=********&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\appSetting\\wxAccount\\reply\\follow\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <router-link v-show=\"$route.path.indexOf('keyword') !== -1\" :to=\"{path: '/appSetting/publicAccount/wxReply/keyword'}\">\n      <el-button size=\"mini\" class=\"mr20 mb20\" icon=\"el-icon-back\">返回</el-button>\n    </router-link>\n    <el-row :gutter=\"30\"  v-loading=\"loading\">\n      <el-col v-bind=\"grid\" class=\"acea-row\">\n        <div class=\"left mb15 ml40\">\n          <img class=\"top\" src=\"@/assets/imgs/mobilehead.png\">\n          <img class=\"bottom\" src=\"@/assets/imgs/mobilefoot.png\">\n          <div class=\"centent\">\n            <div class=\"time-wrapper\"><span class=\"time\">9:36</span></div>\n            <div v-if=\"formValidate.type !== 'news'\" class=\"view-item text-box clearfix\">\n              <div class=\"avatar fl\"><img src=\"@/assets/imgs/head.gif\"></div>\n              <div class=\"box-content fl\">\n                <span\n                  v-if=\"formValidate.type === 'text'\"\n                  v-text=\"formValidate.contents.content\"\n                />\n                <div v-if=\"formValidate.contents.mediaId\" class=\"box-content_pic\">\n                  <img v-if=\"formValidate.type === 'image'\" :src=\"formValidate.contents.srcUrl\">\n                  <i class=\"el-icon-service\" v-else></i>\n                </div>\n              </div>\n            </div>\n            <div v-if=\"formValidate.type === 'news'\">\n              <div class=\"newsBox\">\n                <!--<div class=\"news_pic mb15\" style=\"backgroundImage: url('@/assets/imgs/mobilefoot.png');backgroundSize:'100% 100%'}\" />-->\n               <div class=\"news_pic mb15\" :style=\"{backgroundImage: 'url(' + (formValidate.contents.articleData.imageInput?formValidate.contents.articleData.imageInput:'') + ')',backgroundSize:'100% 100%'}\" />\n                <span class=\"news_sp\">{{ formValidate.contents.articleData.title }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-col>\n      <el-col :xl=\"11\" :lg=\"12\" :md=\"14\" :sm=\"22\" :xs=\"22\">\n        <div class=\"box-card right ml50\">\n          <el-form\n            ref=\"formValidate\"\n            :model=\"formValidate\"\n            :rules=\"ruleValidate\"\n            label-width=\"100px\"\n            class=\"mt20\"\n            @submit.native.prevent\n          >\n            <el-form-item v-if=\"$route.path.indexOf('keyword') !== -1\" label=\"关键字：\" prop=\"val\">\n              <div class=\"arrbox\">\n                <el-tag\n                  v-for=\"(item, index) in labelarr\"\n                  :key=\"index\"\n                  type=\"success\"\n                  closable\n                  class=\"mr5\"\n                  :disable-transitions=\"false\"\n                  @close=\"handleClose(item)\"\n                >{{ item }}\n                </el-tag>\n                <el-input\n                  v-model=\"val\"\n                  size=\"mini\"\n                  class=\"arrbox_ip\"\n                  placeholder=\"输入后回车\"\n                  style=\"width: 90%;\"\n                  @change=\"addlabel\"\n                />\n              </div>\n            </el-form-item>\n            <el-form-item label=\"规则状态：\">\n              <el-radio-group v-model=\"formValidate.status\">\n                <el-radio :label=\"true\">启用</el-radio>\n                <el-radio :label=\"false\">禁用</el-radio>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item label=\"消息类型：\" prop=\"type\">\n              <el-select\n                v-model=\"formValidate.type\"\n                placeholder=\"请选择规则状态\"\n                style=\"width: 90%;\"\n                @change=\"RuleFactor(formValidate.type)\"\n              >\n                <el-option label=\"文字消息\" value=\"text\">文字消息</el-option>\n                <el-option label=\"图片消息\" value=\"image\">图片消息</el-option>\n                <el-option label=\"图文消息\" value=\"news\">图文消息</el-option>\n                <el-option label=\"声音消息\" value=\"voice\">声音消息</el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item v-if=\"formValidate.type === 'text'\" label=\"规则内容：\" prop=\"content\">\n              <el-input\n                v-model=\"formValidate.contents.content\"\n                placeholder=\"请填写规则内容\"\n                style=\"width: 90%;\"\n                @input=\"change($event)\"\n              />\n            </el-form-item>\n            <el-form-item v-if=\"formValidate.type === 'news'\" label=\"选取图文：\">\n              <el-button size=\"mini\" type=\"primary\" @click=\"changePic\">选择图文消息</el-button>\n            </el-form-item>\n            <el-form-item\n              v-if=\"formValidate.type === 'image' || formValidate.type === 'voice'\"\n              :label=\"formValidate.type === 'image'?'图片地址：':'语音地址：'\"\n              prop=\"mediaId\"\n            >\n              <div class=\"acea-row row-middle\">\n                <el-input\n                  v-model=\"formValidate.contents.mediaId\"\n                  readonly=\"readonly\"\n                  placeholder=\"default size\"\n                  style=\"width: 75%;\"\n                  class=\"mr10\"\n                />\n                <el-upload\n                  class=\"upload-demo mr10\"\n                  action\n                  :http-request=\"handleUploadForm\"\n                  :headers=\"myHeaders\"\n                  :show-file-list=\"false\"\n                  multiple\n                >\n                  <el-button size=\"mini\" type=\"primary\">点击上传</el-button>\n                </el-upload>\n              </div>\n              <span v-show=\"formValidate.type === 'image'\">文件最大2Mb，支持bmp/png/jpeg/jpg/gif格式</span>\n              <span v-show=\"formValidate.type === 'voice'\">文件最大2Mb，支持mp3/wma/wav/amr格式,播放长度不超过60s</span>\n            </el-form-item>\n          </el-form>\n        </div>\n        <el-col :span=\"24\">\n          <div class=\"acea-row row-center\">\n            <el-button\n              type=\"primary\"\n              class=\"ml50\"\n              @click=\"submenus('formValidate')\"\n              v-hasPermi=\"['admin:wechat:keywords:reply:update']\"\n            >保存并发布\n            </el-button>\n          </div>\n        </el-col>\n      </el-col>\n    </el-row>\n  </el-card>\n</div>\n", null]}