{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { applyListApi, applyBalanceApi, applyUpdateApi, applyStatusApi } from '@/api/financial';\nimport cardsData from '@/components/cards/index';\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport { Debounce } from '@/utils/validate';\nexport default {\n  name: 'AccountsExtract',\n  components: {\n    cardsData: cardsData,\n    zbParser: zbParser\n  },\n  data: function data() {\n    return {\n      editData: {},\n      isCreate: 1,\n      dialogVisible: false,\n      timeVal: [],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        extractType: '',\n        status: '',\n        dateLimit: '',\n        keywords: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      cardLists: [],\n      applyId: null,\n      extractType: ''\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n    this.getBalance();\n  },\n  methods: {\n    checkPermi: checkPermi,\n    resetForm: function resetForm() {\n      this.dialogVisible = false;\n    },\n    handleEdit: function handleEdit(row) {\n      this.extractType = row.extractType;\n      this.applyId = row.id;\n      this.dialogVisible = true;\n      this.isCreate = 1;\n      this.editData = JSON.parse(JSON.stringify(row));\n    },\n    handlerSubmit: Debounce(function (formValue) {\n      var _this = this;\n      formValue.id = this.applyId;\n      formValue.extractType = this.extractType;\n      applyUpdateApi(formValue).then(function (data) {\n        _this.$message.success('编辑成功');\n        _this.dialogVisible = false;\n        _this.getList();\n      });\n    }),\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.editData = {};\n    },\n    onExamine: function onExamine(id) {\n      var _this2 = this;\n      this.$prompt('未通过', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入原因',\n        inputType: 'textarea',\n        inputValue: '输入信息不完整或有误!',\n        inputPlaceholder: '请输入原因',\n        inputValidator: function inputValidator(value) {\n          if (!value) {\n            return '请输入原因';\n          }\n        }\n      }).then(function (_ref) {\n        var value = _ref.value;\n        applyStatusApi({\n          id: id,\n          status: -1,\n          backMessage: value\n        }).then(function (res) {\n          _this2.$message({\n            type: 'success',\n            message: '提交成功'\n          });\n          _this2.getList();\n        });\n      }).catch(function () {\n        _this2.$message({\n          type: 'info',\n          message: '取消输入'\n        });\n      });\n    },\n    ok: function ok(id) {\n      var _this3 = this;\n      this.$modalSure('审核通过吗').then(function () {\n        applyStatusApi({\n          id: id,\n          status: 1\n        }).then(function () {\n          _this3.$message.success('操作成功');\n          _this3.getList();\n        });\n      });\n    },\n    // 金额\n    getBalance: function getBalance() {\n      var _this4 = this;\n      applyBalanceApi({\n        dateLimit: this.tableFrom.dateLimit\n      }).then(function (res) {\n        _this4.cardLists = [{\n          name: '待提现金额',\n          count: res.toBeWithdrawn,\n          color: '#1890FF',\n          class: 'one',\n          icon: 'iconzhichujine1'\n        }, {\n          name: '佣金总金额',\n          count: res.commissionTotal,\n          color: '#A277FF',\n          class: 'two',\n          icon: 'iconzhifuyongjinjine1'\n        }, {\n          name: '已提现金额',\n          count: res.withdrawn,\n          color: '#EF9C20',\n          class: 'three',\n          icon: 'iconyingyee1'\n        }, {\n          name: '未提现金额',\n          count: res.unDrawn,\n          color: '#1BBE6B',\n          class: 'four',\n          icon: 'iconyuezhifujine2'\n        }];\n      });\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.timeVal = [];\n      this.tableFrom.dateLimit = tab;\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getBalance();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getBalance();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this5 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      applyListApi(this.tableFrom).then(function (res) {\n        _this5.tableData.data = res.list;\n        _this5.tableData.total = res.total;\n        _this5.listLoading = false;\n      }).catch(function () {\n        _this5.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}