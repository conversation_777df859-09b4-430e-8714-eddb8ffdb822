(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6f43"],{"756e":function(e,n){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}(function(e,n){var i=n.documentElement||n.body,o=e.devicePixelRatio||1;function d(){n.body||n.addEventListener("DOMContentLoaded",d)}function r(){var e=i.clientWidth/7.5;i.style.fontSize=e+"px"}if(d(),r(),e.addEventListener("resize",r),e.addEventListener("pageshow",(function(e){e.persisted&&r()})),o>=2){var a=n.createElement("body"),c=n.createElement("div");c.style.border=".5px solid transparent",a.appendChild(c),i.appendChild(a),1===c.offsetHeight&&i.classList.add("hairlines"),i.removeChild(a)}function f(){WeixinJSBridge.invoke("setFontSizeCallback",{fontSize:0}),WeixinJSBridge.on("menu:setfont",(function(){WeixinJSBridge.invoke("setFontSizeCallback",{fontSize:0})}))}"object"==("undefined"===typeof WeixinJSBridge?"undefined":t(WeixinJSBridge))&&"function"==typeof WeixinJSBridge.invoke?f():n.addEventListener?n.addEventListener("WeixinJSBridgeReady",f,!1):n.attachEvent&&(n.attachEvent("WeixinJSBridgeReady",f),n.attachEvent("onWeixinJSBridgeReady",f))})(window,document)}}]);