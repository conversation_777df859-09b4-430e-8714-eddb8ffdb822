{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue?vue&type=template&id=7a8e852e&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderDelivery.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"deliver-goods\">\n  <header>\n    <div class=\"order-num acea-row row-between-wrapper\">\n      <div class=\"num line1\">订单号：{{ orderId }}</div>\n      <div class=\"name line1\">\n        <span class=\"iconfont iconios-contact\"></span>{{ delivery.nikeName }}\n      </div>\n    </div>\n    <div class=\"address\">\n      <div class=\"name\">\n        {{ delivery.realName\n        }}<span class=\"phone\">{{ delivery.userPhone }}</span>\n      </div>\n      <div>{{ delivery.userAddress }}</div>\n    </div>\n    <div class=\"line\"><img src=\"../../../assets/imgs/line.jpg\" /></div>\n  </header>\n  <div class=\"wrapper\">\n    <div class=\"item acea-row row-between-wrapper\">\n      <div>发货方式</div>\n      <div class=\"mode acea-row row-middle row-right\">\n        <div\n          class=\"goods\"\n          :class=\"active === index ? 'on' : ''\"\n          v-for=\"(item, index) in types\"\n          :key=\"index\"\n          @click=\"changeType(item, index)\"\n        >\n          {{ item.title }}<span class=\"iconfont icon-xuanzhong2\"></span>\n        </div>\n      </div>\n    </div>\n    <div class=\"list\" v-show=\"active === 0\">\n      <div class=\"item acea-row row-between-wrapper\">\n        <div>发货方式</div>\n        <select class=\"mode\" v-model=\"expressCode\">\n          <option value=\"\">选择快递公司</option>\n          <option\n            :value=\"item.code\"\n            v-for=\"(item, index) in express\"\n            :key=\"index\"\n            >{{ item.name }}</option\n          >\n        </select>\n        <span class=\"iconfont icon-up\"></span>\n      </div>\n      <div class=\"item acea-row row-between-wrapper\">\n        <div>快递单号</div>\n        <input\n          type=\"text\"\n          placeholder=\"填写快递单号\"\n          v-model=\"expressNumber\"\n          class=\"mode\"\n        />\n      </div>\n    </div>\n    <div class=\"list\" v-show=\"active === 1\">\n      <div class=\"item acea-row row-between-wrapper\">\n        <div>送货人</div>\n        <input\n          type=\"text\"\n          placeholder=\"填写送货人\"\n          v-model=\"deliveryName\"\n          class=\"mode\"\n        />\n      </div>\n      <div class=\"item acea-row row-between-wrapper\">\n        <div>送货电话</div>\n        <input\n          type=\"text\"\n          placeholder=\"填写送货电话\"\n          v-model=\"deliveryTel\"\n          class=\"mode\"\n        />\n      </div>\n    </div>\n  </div>\n  <div style=\"height:1.2rem;\"></div>\n  <div class=\"confirm\" @click=\"saveInfo\">确认提交</div>\n</div>\n", null]}