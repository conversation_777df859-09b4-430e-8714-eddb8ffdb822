{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue?vue&type=template&id=64cb5309&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1754275430524}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.dialogVisible)?_c('el-dialog',{attrs:{\"title\":_vm.$t('user.grade.form.dialogTitle'),\"visible\":_vm.dialogVisible,\"width\":\"500px\",\"before-close\":_vm.handleClose},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-form',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"user\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.user,\"rules\":_vm.rules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('user.grade.form.levelNameLabel'),\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('user.grade.form.levelNamePlaceholder')},model:{value:(_vm.user.name),callback:function ($$v) {_vm.$set(_vm.user, \"name\", $$v)},expression:\"user.name\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.grade.form.gradeLabel'),\"prop\":\"grade\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('user.grade.form.gradePlaceholder')},model:{value:(_vm.user.grade),callback:function ($$v) {_vm.$set(_vm.user, \"grade\", _vm._n($$v))},expression:\"user.grade\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.grade.form.discountLabel'),\"prop\":\"discount\"}},[_c('el-input-number',{attrs:{\"min\":0,\"max\":100,\"step-strictly\":\"\",\"placeholder\":_vm.$t('user.grade.form.discountPlaceholder')},model:{value:(_vm.user.discount),callback:function ($$v) {_vm.$set(_vm.user, \"discount\", $$v)},expression:\"user.discount\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.grade.form.experienceLabel'),\"prop\":\"experience\"}},[_c('el-input-number',{attrs:{\"placeholder\":_vm.$t('user.grade.form.experiencePlaceholder'),\"min\":0,\"step-strictly\":\"\"},model:{value:(_vm.user.experience),callback:function ($$v) {_vm.$set(_vm.user, \"experience\", _vm._n($$v))},expression:\"user.experience\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.grade.form.iconLabel'),\"prop\":\"icon\"}},[_c('div',{staticClass:\"upLoadPicBox\",on:{\"click\":function($event){return _vm.modalPicTap('1', 'icon')}}},[(_vm.user.icon)?_c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":_vm.user.icon}})]):(_vm.formValidate.icon)?_c('div',{staticClass:\"pictrue\"},[_c('img',{attrs:{\"src\":_vm.formValidate.icon}})]):_c('div',{staticClass:\"upLoad\"},[_c('i',{staticClass:\"el-icon-camera cameraIconfont\"})])])])],1),_vm._v(\" \"),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){return _vm.resetForm('user')}}},[_vm._v(_vm._s(_vm.$t('user.grade.form.cancel')))]),_vm._v(\" \"),_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:system:user:level:update','admin:system:user:level:save']),expression:\"['admin:system:user:level:update','admin:system:user:level:save']\"}],attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitForm('formValidate')}}},[_vm._v(_vm._s(_vm.$t('user.grade.form.confirm')))])],1)],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}