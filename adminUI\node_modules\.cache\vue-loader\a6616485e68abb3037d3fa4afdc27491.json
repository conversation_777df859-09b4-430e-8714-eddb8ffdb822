{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue?vue&type=template&id=5de361b9&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('el-dialog',_vm._g(_vm._b({attrs:{\"title\":\"外部资源引用\",\"width\":\"600px\",\"close-on-click-modal\":false},on:{\"open\":_vm.onOpen,\"close\":_vm.onClose}},'el-dialog',_vm.$attrs,false),_vm.$listeners),[_vm._l((_vm.resources),function(item,index){return _c('el-input',{key:index,staticClass:\"url-item\",attrs:{\"placeholder\":\"请输入 css 或 js 资源路径\",\"prefix-icon\":\"el-icon-link\",\"clearable\":\"\"},model:{value:(_vm.resources[index]),callback:function ($$v) {_vm.$set(_vm.resources, index, $$v)},expression:\"resources[index]\"}},[_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteOne(index)}},slot:\"append\"})],1)}),_vm._v(\" \"),_c('el-button-group',{staticClass:\"add-item\"},[_c('el-button',{attrs:{\"plain\":\"\"},on:{\"click\":function($event){return _vm.addOne('https://cdn.bootcss.com/jquery/1.8.3/jquery.min.js')}}},[_vm._v(\"\\n        jQuery1.8.3\\n      \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"plain\":\"\"},on:{\"click\":function($event){return _vm.addOne('https://unpkg.com/http-vue-loader')}}},[_vm._v(\"\\n        http-vue-loader\\n      \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"icon\":\"el-icon-circle-plus-outline\",\"plain\":\"\"},on:{\"click\":function($event){return _vm.addOne('')}}},[_vm._v(\"\\n        添加其他\\n      \")])],1),_vm._v(\" \"),_c('div',{attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":_vm.close}},[_vm._v(\"\\n        取消\\n      \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handelConfirm}},[_vm._v(\"\\n        确定\\n      \")])],1)],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}