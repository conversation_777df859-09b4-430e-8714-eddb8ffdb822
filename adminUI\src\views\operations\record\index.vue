<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="searchForm.extractType"
        @tab-click="onChangeType"
        class="mb20"
      >
        <el-tab-pane
          :label="$t('operations.withdrawal.walletWithdrawal')"
          name="wallet"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('operations.withdrawal.bankWithdrawal')"
          name="bank"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('operations.withdrawal.applicant') + '：'">
            <el-input
              v-model="searchForm.keywords"
              size="small"
              :placeholder="$t('common.enter')"
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.applicationTime') + '：'"
          >
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.electronicWallet') + '：'"
            v-if="searchForm.extractType == 'wallet'"
          >
            <el-select
              v-model="searchForm.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="$t('operations.withdrawal.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.bankName') + '：'"
            v-if="searchForm.extractType == 'bank'"
          >
            <el-select
              v-model="searchForm.bankName"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.withdrawalStatus') + '：'"
          >
            <el-select
              v-model="searchForm.status"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="$t('operations.withdrawal.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-button size="small" type="primary" class="mr10" @click="getList(1)">
        {{ $t("common.query") }}
      </el-button>
      <el-button size="small" type="" class="mr10" @click="resetForm">
        {{ $t("common.reset") }}
      </el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <el-button
          type="primary"
          size="small"
          @click="handleUpload"
          v-hasPermi="['admin:financialCenter:request:upload']"
        >
          {{ $t("operations.withdrawal.exportExcel") }}
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="$t('common.serialNumber')"  width="110">
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationId')"
          min-width="80"
          prop="uid"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicantName')"
          min-width="80"
          prop="realName"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.withdrawalAmount')"
          min-width="80"
          prop="extractPrice"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.serviceFee')"
          min-width="80"
          prop="serviceFee"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.actualAmount')"
          min-width="100"
          prop="actualAmount"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationTime')"
          min-width="80"
          prop="createTime"
        ></el-table-column>

        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletCode')"
          min-width="80"
          prop="walletCode"
        ></el-table-column>

        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletAccount')"
          min-width="80"
          prop="walletAccount"
        ></el-table-column>

        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankName')"
          min-width="80"
          prop="bankName"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bankName | filterEmpty }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankCardNumber')"
          min-width="80"
        ></el-table-column>

        <el-table-column
          :label="$t('operations.withdrawal.name')"
          min-width="80"
          prop="nickName"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.phoneNumber')"
          min-width="80"
          prop="phone"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.transferTime')"
          min-width="80"
          prop="transferTime"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.transferResult')"
          min-width="80"
          prop="transferResult"
        ></el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.remark')"
          min-width="80"
          prop="mark"
        ></el-table-column>

        <el-table-column
          :label="$t('operations.withdrawal.attachment')"
          min-width="80"
          v-if="searchForm.extractType === 'wallet'"
        >
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.voucherImage"
                :preview-src-list="[scope.row.voucherImage]"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('operations.withdrawal.operator')"
          min-width="80"
          prop="operator"
        ></el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="searchForm.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchForm.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { applyListApi, extractBankApi } from "@/api/financial";
import { resetForm } from "@/utils/parsing";
export default {
  name: "WithdrawalHistory",
  data() {
    return {
      loading: false,
      tableData: [],

      searchForm: {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: "wallet",
        status: "",
        page: 1,
        limit: 20,
        total: 0
      },
      timeList: [],
      dialogFormVisible: false,
      artFrom: {
        payType: "",
        file: "",
        remark: ""
      },
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      statusList: [
        { label: "unapproved", value: "-1" },
        { label: "underReview", value: "0" },
        { label: "reviewed", value: "1" },
        { label: "paid", value: "2" }
      ],
      bankList: []
    };
  },
  created() {},
  mounted() {
    this.getList();
    this.getBankList();
  },
  methods: {
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.searchForm.page = num ? num : this.searchForm.page;
      this.searchForm.dateLimit = this.timeList.length
        ? this.timeList.join(",")
        : "";
      applyListApi(this.searchForm)
        .then(res => {
          this.tableData = res.list;
          this.searchForm.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetForm() {
      this.searchForm = {
        keywords: "",
        dateLimit: "",
        bankName: "",
        status: "",
        walletCode: "",
        extractType: this.searchForm.extractType,
        page: 1,
        limit: 20,
        total: 0
      };
      this.timeList = [];
      this.getList();
    },
    //切换页数
    pageChange(index) {
      this.searchForm.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.searchForm.limit = index;
      this.getList();
    },
    handleUpload() {},
    onChangeType() {
      this.resetForm();
      this.getList();
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
