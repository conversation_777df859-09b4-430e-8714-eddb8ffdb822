{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=template&id=476cf91d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('el-drawer',_vm._g(_vm._b({on:{\"opened\":_vm.onOpen,\"close\":_vm.onClose}},'el-drawer',_vm.$attrs,false),_vm.$listeners),[_c('div',{staticStyle:{\"height\":\"100%\"}},[_c('el-row',{staticStyle:{\"height\":\"100%\",\"overflow\":\"auto\"}},[_c('el-col',{staticClass:\"left-editor\",attrs:{\"md\":24,\"lg\":12}},[_c('div',{staticClass:\"setting\",attrs:{\"title\":\"资源引用\"},on:{\"click\":_vm.showResource}},[_c('el-badge',{staticClass:\"item\",attrs:{\"is-dot\":!!_vm.resources.length}},[_c('i',{staticClass:\"el-icon-setting\"})])],1),_vm._v(\" \"),_c('el-tabs',{staticClass:\"editor-tabs\",attrs:{\"type\":\"card\"},model:{value:(_vm.activeTab),callback:function ($$v) {_vm.activeTab=$$v},expression:\"activeTab\"}},[_c('el-tab-pane',{attrs:{\"name\":\"html\"}},[_c('span',{attrs:{\"slot\":\"label\"},slot:\"label\"},[(_vm.activeTab==='html')?_c('i',{staticClass:\"el-icon-edit\"}):_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\"\\n                template\\n              \")])]),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"name\":\"js\"}},[_c('span',{attrs:{\"slot\":\"label\"},slot:\"label\"},[(_vm.activeTab==='js')?_c('i',{staticClass:\"el-icon-edit\"}):_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\"\\n                script\\n              \")])]),_vm._v(\" \"),_c('el-tab-pane',{attrs:{\"name\":\"css\"}},[_c('span',{attrs:{\"slot\":\"label\"},slot:\"label\"},[(_vm.activeTab==='css')?_c('i',{staticClass:\"el-icon-edit\"}):_c('i',{staticClass:\"el-icon-document\"}),_vm._v(\"\\n                css\\n              \")])])],1),_vm._v(\" \"),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activeTab==='html'),expression:\"activeTab==='html'\"}],staticClass:\"tab-editor\",attrs:{\"id\":\"editorHtml\"}}),_vm._v(\" \"),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activeTab==='js'),expression:\"activeTab==='js'\"}],staticClass:\"tab-editor\",attrs:{\"id\":\"editorJs\"}}),_vm._v(\" \"),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.activeTab==='css'),expression:\"activeTab==='css'\"}],staticClass:\"tab-editor\",attrs:{\"id\":\"editorCss\"}})],1),_vm._v(\" \"),_c('el-col',{staticClass:\"right-preview\",attrs:{\"md\":24,\"lg\":12}},[_c('div',{staticClass:\"action-bar\",style:({'text-align': 'left'})},[_c('span',{staticClass:\"bar-btn\",on:{\"click\":_vm.runCode}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\"\\n              刷新\\n            \")]),_vm._v(\" \"),_c('span',{staticClass:\"bar-btn\",on:{\"click\":_vm.exportFile}},[_c('i',{staticClass:\"el-icon-download\"}),_vm._v(\"\\n              导出vue文件\\n            \")]),_vm._v(\" \"),_c('span',{ref:\"copyBtn\",staticClass:\"bar-btn copy-btn\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_vm._v(\"\\n              复制代码\\n            \")]),_vm._v(\" \"),_c('span',{staticClass:\"bar-btn delete-btn\",on:{\"click\":function($event){return _vm.$emit('update:visible', false)}}},[_c('i',{staticClass:\"el-icon-circle-close\"}),_vm._v(\"\\n              关闭\\n            \")])]),_vm._v(\" \"),_c('iframe',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isIframeLoaded),expression:\"isIframeLoaded\"}],ref:\"previewPage\",staticClass:\"result-wrapper\",attrs:{\"frameborder\":\"0\",\"src\":\"preview.html\"},on:{\"load\":_vm.iframeLoad}}),_vm._v(\" \"),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.isIframeLoaded),expression:\"!isIframeLoaded\"},{name:\"loading\",rawName:\"v-loading\",value:(true),expression:\"true\"}],staticClass:\"result-wrapper\"})])],1)],1)]),_vm._v(\" \"),_c('resource-dialog',{attrs:{\"visible\":_vm.resourceVisible,\"origin-resource\":_vm.resources},on:{\"update:visible\":function($event){_vm.resourceVisible=$event},\"save\":_vm.setResource}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}