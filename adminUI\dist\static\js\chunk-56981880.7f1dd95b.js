(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56981880"],{"2eb3":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return s})),n.d(e,"d",(function(){return l})),n.d(e,"l",(function(){return o})),n.d(e,"k",(function(){return m})),n.d(e,"i",(function(){return u})),n.d(e,"f",(function(){return d})),n.d(e,"g",(function(){return c})),n.d(e,"h",(function(){return p})),n.d(e,"j",(function(){return f}));var a=n("b775");function i(t){var e={id:t.id};return Object(a["a"])({url:"/admin/system/admin/delete",method:"GET",params:e})}function r(t){return Object(a["a"])({url:"/admin/system/admin/list",method:"GET",params:t})}function s(t){var e={account:t.account,level:t.level,pwd:t.pwd,realName:t.realName,roles:t.roles.join(","),status:t.status,phone:t.phone};return Object(a["a"])({url:"/admin/system/admin/save",method:"POST",data:e})}function l(t){var e={account:t.account,level:t.level,pwd:t.pwd,roles:t.roles,realName:t.realName,status:t.status,id:t.id,isDel:t.isDel};return Object(a["a"])({url:"/admin/system/admin/update",method:"POST",data:e})}function o(t){return Object(a["a"])({url:"/admin/system/admin/updateStatus",method:"get",params:t})}function m(t){return Object(a["a"])({url:"/admin/system/admin/update/isSms",method:"get",params:t})}function u(t){var e={menuType:t.menuType,name:t.name};return Object(a["a"])({url:"/admin/system/menu/list",method:"get",params:e})}function d(t){var e=t;return Object(a["a"])({url:"/admin/system/menu/add",method:"post",data:e})}function c(t){return Object(a["a"])({url:"/admin/system/menu/delete/".concat(t),method:"post"})}function p(t){return Object(a["a"])({url:"/admin/system/menu/info/".concat(t),method:"get"})}function f(t){var e=t;return Object(a["a"])({url:"/admin/system/menu/update",method:"post",data:e})}},a391:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"divBox"},[n("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-form-item",[n("el-select",{staticClass:"selWidth",attrs:{placeholder:t.$t("admin.system.admin.role"),clearable:""},model:{value:t.listPram.roles,callback:function(e){t.$set(t.listPram,"roles",e)},expression:"listPram.roles"}},t._l(t.roleList.list,(function(t){return n("el-option",{key:t.id,attrs:{label:t.roleName,value:t.id}})})),1)],1),t._v(" "),n("el-form-item",[n("el-select",{staticClass:"selWidth",attrs:{placeholder:t.$t("admin.system.admin.status"),clearable:""},model:{value:t.listPram.status,callback:function(e){t.$set(t.listPram,"status",e)},expression:"listPram.status"}},t._l(t.constants.roleListStatus,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),n("el-form-item",[n("el-input",{staticClass:"selWidth",attrs:{placeholder:t.$t("admin.system.admin.realName"),clearable:""},model:{value:t.listPram.realName,callback:function(e){t.$set(t.listPram,"realName",e)},expression:"listPram.realName"}})],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.handleSearch}},[t._v(t._s(t.$t("common.query")))])],1)],1),t._v(" "),n("el-form",{attrs:{inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-form-item",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:save"],expression:"['admin:system:admin:save']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handlerOpenEdit(0)}}},[t._v("\n        "+t._s(t.$t("admin.system.admin.addAdmin"))+"\n      ")])],1)],1),t._v(" "),n("el-table",{attrs:{data:t.listData.list,size:"mini","header-cell-style":{fontWeight:"bold"}}},[n("el-table-column",{attrs:{prop:"id",label:t.$t("admin.system.admin.id"),width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.realName"),prop:"realName","min-width":"120"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.account"),prop:"account","min-width":"120"}}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.phone"),prop:"lastTime","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("filterEmpty")(e.row.phone)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.role"),prop:"realName","min-width":"230"},scopedSlots:t._u([{key:"default",fn:function(e){return e.row.roleNames?t._l(e.row.roleNames.split(","),(function(e,a){return n("el-tag",{key:a,staticClass:"mr5",attrs:{size:"small",type:"info"}},[t._v(t._s(e))])})):void 0}}],null,!0)}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.lastTime"),prop:"lastTime","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("filterEmpty")(e.row.lastTime)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.lastIp"),prop:"lastIp","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("filterEmpty")(e.row.lastIp)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.status"),"min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["admin:system:admin:update:status"])?[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":t.$t("common.activeTextOn"),"inactive-text":t.$t("common.activeTextOff")},on:{change:function(n){return t.onchangeIsShow(e.row)}},model:{value:e.row.status,callback:function(n){t.$set(e.row,"status",n)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.isSms"),"min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return t.checkPermi(["admin:system:admin:update:sms"])?[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1,"active-text":t.$t("common.activeTextOn"),"inactive-text":t.$t("common.activeTextOff"),disabled:!e.row.phone},nativeOn:{click:function(n){return t.onchangeIsSms(e.row)}},model:{value:e.row.isSms,callback:function(n){t.$set(e.row,"isSms",n)},expression:"scope.row.isSms"}})]:void 0}}],null,!0)}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.isDel"),prop:"status","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("filterYesOrNo")(e.row.isDel)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:t.$t("admin.system.admin.operation"),"min-width":"130",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isDel?[n("span",[t._v("-")])]:[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:info"],expression:"['admin:system:admin:info']"}],attrs:{type:"text",size:"mini"},on:{click:function(n){return t.handlerOpenEdit(1,e.row)}}},[t._v("\n            "+t._s(t.$t("admin.system.admin.edit"))+"\n          ")]),t._v(" "),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:delete"],expression:"['admin:system:admin:delete']"}],attrs:{type:"text",size:"mini"},on:{click:function(n){return t.handlerOpenDel(e.row)}}},[t._v("\n            "+t._s(t.$t("admin.system.admin.delete"))+"\n          ")])]]}}])})],1),t._v(" "),n("el-dialog",{attrs:{visible:t.editDialogConfig.visible,title:0===t.editDialogConfig.isCreate?t.$t("admin.system.admin.createIdentity"):t.$t("admin.system.admin.editIdentity"),"destroy-on-close":"","close-on-click-modal":!1,width:"700px"},on:{"update:visible":function(e){return t.$set(t.editDialogConfig,"visible",e)}}},[t.editDialogConfig.visible?n("edit",{attrs:{"is-create":t.editDialogConfig.isCreate,"edit-data":t.editDialogConfig.editData},on:{hideEditDialog:t.hideEditDialog}}):t._e()],1)],1)},i=[],r=n("2eb3"),s=n("cc5e"),l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"pram",attrs:{model:t.pram,rules:t.rules,"label-width":"100px"},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-form-item",{attrs:{label:t.$t("admin.system.admin.account"),prop:"account"}},[n("el-input",{attrs:{placeholder:t.$t("admin.system.admin.account")},model:{value:t.pram.account,callback:function(e){t.$set(t.pram,"account",e)},expression:"pram.account"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("admin.system.admin.pwd"),prop:"pwd"}},[n("el-input",{attrs:{placeholder:t.$t("admin.system.admin.pwd"),clearable:""},on:{input:t.handlerPwdInput,clear:t.handlerPwdInput},model:{value:t.pram.pwd,callback:function(e){t.$set(t.pram,"pwd",e)},expression:"pram.pwd"}})],1),t._v(" "),t.pram.pwd?n("el-form-item",{attrs:{label:t.$t("admin.system.admin.repwd"),prop:"repwd"}},[n("el-input",{attrs:{placeholder:t.$t("admin.system.admin.repwd"),clearable:""},model:{value:t.pram.repwd,callback:function(e){t.$set(t.pram,"repwd",e)},expression:"pram.repwd"}})],1):t._e(),t._v(" "),n("el-form-item",{attrs:{label:t.$t("admin.system.admin.realName"),prop:"realName"}},[n("el-input",{attrs:{placeholder:t.$t("admin.system.admin.realName")},model:{value:t.pram.realName,callback:function(e){t.$set(t.pram,"realName",e)},expression:"pram.realName"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("admin.system.admin.roles"),prop:"roles"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:t.$t("admin.system.admin.roles"),clearable:"",multiple:""},model:{value:t.pram.roles,callback:function(e){t.$set(t.pram,"roles",e)},expression:"pram.roles"}},t._l(t.roleList.list,(function(t,e){return n("el-option",{key:e,attrs:{label:t.roleName,value:t.id}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("admin.system.admin.phone"),prop:"phone"}},[n("el-input",{attrs:{type:"text",prefix:"ios-contact-outline",placeholder:t.$t("admin.system.admin.phone"),size:"large"},model:{value:t.pram.phone,callback:function(e){t.$set(t.pram,"phone",e)},expression:"pram.phone"}})],1),t._v(" "),n("el-form-item",{attrs:{label:t.$t("common.status")}},[n("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:t.pram.status,callback:function(e){t.$set(t.pram,"status",e)},expression:"pram.status"}})],1),t._v(" "),n("el-form-item",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:admin:update","admin:system:admin:save"],expression:"['admin:system:admin:update', 'admin:system:admin:save']"}],attrs:{type:"primary"},on:{click:function(e){return t.handlerSubmit("pram")}}},[t._v(t._s(0===t.isCreate?t.$t("common.confirm"):t.$t("common.update")))]),t._v(" "),n("el-button",{on:{click:t.close}},[t._v(t._s(t.$t("common.cancel")))])],1)],1)],1)},o=[],m=n("61f7");function u(t){return f(t)||p(t)||c(t)||d()}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"==typeof t)return h(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(t,e):void 0}}function p(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function f(t){if(Array.isArray(t))return h(t)}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}var v={components:{},props:{isCreate:{type:Number,required:!0},editData:{type:Object,default:function(){return{rules:[]}}}},data:function(){var t=this,e=function(e,n,a){""===n?a(new Error(t.$t("admin.system.admin.validatePass.required"))):n!==t.pram.pwd?a(new Error(t.$t("admin.system.admin.validatePass.notMatch"))):a()};return{constants:this.$constants,pram:{account:null,level:null,pwd:null,repwd:null,realName:null,roles:[],status:null,id:null,phone:null},roleList:[],rules:{account:[{required:!0,message:this.$t("admin.system.admin.validateAccount.required"),trigger:["blur","change"]}],pwd:[{required:!0,message:this.$t("admin.system.admin.validatePassword.required"),trigger:["blur","change"]}],repwd:[{required:!0,message:this.$t("admin.system.admin.validateConfirmPassword.required"),validator:e,trigger:["blur","change"]}],realName:[{required:!0,message:this.$t("admin.system.admin.validateRealName.required"),trigger:["blur","change"]}],roles:[{required:!0,message:this.$t("admin.system.admin.validateRoles.required"),trigger:["blur","change"]}],phone:[{required:!0,message:this.$t("admin.system.admin.validatePhone.required"),trigger:["blur","change"]}]}}},mounted:function(){this.initEditData(),this.handleGetRoleList()},methods:{close:function(){this.$emit("hideEditDialog")},handleGetRoleList:function(){var t=this,e={page:1,limit:this.constants.page.limit[4],status:1};s["d"](e).then((function(e){t.roleList=e;var n=[];e.list.forEach((function(t){n.push(t.id)})),n.includes(Number.parseInt(t.pram.roles))||t.$set(t.pram,"roles",[])}))},initEditData:function(){if(1===this.isCreate){var t=this.editData,e=t.account,n=t.realName,a=t.roles,i=(t.level,t.status),r=t.id,s=t.phone;this.pram.account=e,this.pram.realName=n;var l=[];a.length>0&&!a.includes(",")?l.push(Number.parseInt(a)):l.push.apply(l,u(a.split(",").map((function(t){return Number.parseInt(t)})))),this.pram.roles=l,this.pram.status=i,this.pram.id=r,this.pram.phone=s,this.rules.pwd=[],this.rules.repwd=[]}},handlerSubmit:Object(m["a"])((function(t){var e=this;this.$refs[t].validate((function(t){t&&(0===e.isCreate?e.handlerSave():e.handlerEdit())}))})),handlerSave:function(){var t=this;r["a"](this.pram).then((function(e){t.$message.success(t.$t("admin.system.admin.message.createSuccess")),t.$emit("hideEditDialog")}))},handlerEdit:function(){var t=this;this.pram.roles=this.pram.roles.join(","),r["d"](this.pram).then((function(e){t.$message.success(t.$t("admin.system.admin.message.updateSuccess")),t.$emit("hideEditDialog")}))},rulesSelect:function(t){this.pram.rules=t},handlerPwdInput:function(t){var e=this;if(!t)return this.rules.pwd=[],void(this.rules.repwd=[]);this.rules.pwd=[{required:!0,message:this.$t("admin.system.admin.validatePassword.required"),trigger:["blur","change"]},{min:6,max:20,message:this.$t("admin.system.admin.validatePassword.lengthError"),trigger:["blur","change"]}],this.rules.repwd=[{required:!0,message:this.$t("admin.system.admin.validateConfirmPassword.required"),validator:function(t,n,a){""===n||n!==e.pram.pwd?a(new Error(e.$t("admin.system.admin.validatePass.notMatch"))):a()},trigger:["blur","change"]}]}}},y=v,b=n("2877"),g=Object(b["a"])(y,l,o,!1,null,"4a1c684c",null),w=g.exports,$=n("e350");function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var t,e,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.toStringTag||"@@toStringTag";function r(n,a,i,r){var o=a&&a.prototype instanceof l?a:l,m=Object.create(o.prototype);return O(m,"_invoke",function(n,a,i){var r,l,o,m=0,u=i||[],d=!1,c={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,n){return r=e,l=0,o=t,c.n=n,s}};function p(n,a){for(l=n,o=a,e=0;!d&&m&&!i&&e<u.length;e++){var i,r=u[e],p=c.p,f=r[2];n>3?(i=f===a)&&(o=r[(l=r[4])?5:(l=3,3)],r[4]=r[5]=t):r[0]<=p&&((i=n<2&&p<r[1])?(l=0,c.v=a,c.n=r[1]):p<f&&(i=n<3||r[0]>a||a>f)&&(r[4]=n,r[5]=a,c.n=f,l=0))}if(i||n>1)return s;throw d=!0,a}return function(i,u,f){if(m>1)throw TypeError("Generator is already running");for(d&&1===u&&p(u,f),l=u,o=f;(e=l<2?t:o)||!d;){r||(l?l<3?(l>1&&(c.n=-1),p(l,o)):c.n=o:c.v=o);try{if(m=2,r){if(l||(i="next"),e=r[i]){if(!(e=e.call(r,o)))throw TypeError("iterator result is not an object");if(!e.done)return e;o=e.value,l<2&&(l=0)}else 1===l&&(e=r.return)&&e.call(r),l<2&&(o=TypeError("The iterator does not provide a '"+i+"' method"),l=1);r=t}else if((e=(d=c.n<0)?o:n.call(a,c))!==s)break}catch(e){r=t,l=1,o=e}finally{m=1}}return{value:e,done:d}}}(n,i,r),!0),m}var s={};function l(){}function o(){}function m(){}e=Object.getPrototypeOf;var u=[][a]?e(e([][a]())):(O(e={},a,(function(){return this})),e),d=m.prototype=l.prototype=Object.create(u);function c(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,O(t,i,"GeneratorFunction")),t.prototype=Object.create(d),t}return o.prototype=m,O(d,"constructor",m),O(m,"constructor",o),o.displayName="GeneratorFunction",O(m,i,"GeneratorFunction"),O(d),O(d,i,"Generator"),O(d,a,(function(){return this})),O(d,"toString",(function(){return"[object Generator]"})),(_=function(){return{w:r,m:c}})()}function O(t,e,n,a){var i=Object.defineProperty;try{i({},"",{})}catch(t){i=0}O=function(t,e,n,a){function r(e,n){O(t,e,(function(t){return this._invoke(e,n,t)}))}e?i?i(t,e,{value:n,enumerable:!a,configurable:!a,writable:!a}):t[e]=n:(r("next",0),r("throw",1),r("return",2))},O(t,e,n,a)}function P(t,e,n,a,i,r,s){try{var l=t[r](s),o=l.value}catch(t){return void n(t)}l.done?e(o):Promise.resolve(o).then(a,i)}function S(t){return function(){var e=this,n=arguments;return new Promise((function(a,i){var r=t.apply(e,n);function s(t){P(r,a,i,s,l,"next",t)}function l(t){P(r,a,i,s,l,"throw",t)}s(void 0)}))}}var k={components:{edit:w},data:function(){return{constants:this.$constants,listData:{list:[]},listPram:{account:null,addTime:null,lastIp:null,lastTime:null,level:null,loginCount:null,realName:null,roles:null,status:null,page:1,limit:this.$constants.page.limit[0]},roleList:[],menuList:[],editDialogConfig:{visible:!1,isCreate:0,editData:{}}}},mounted:function(){this.handleGetAdminList(),this.handleGetRoleList()},methods:{checkPermi:$["a"],onchangeIsShow:function(t){var e=this;r["l"]({id:t.id,status:t.status}).then(S(_().m((function t(){return _().w((function(t){while(1)switch(t.n){case 0:e.$message.success(e.$t("common.operationSuccess")),e.handleGetAdminList();case 1:return t.a(2)}}),t)})))).catch((function(){t.status=!t.status}))},onchangeIsSms:function(t){var e=this;if(!t.phone)return this.$message({message:this.$t("admin.system.admin.pleaseAddPhone"),type:"warning"});r["k"]({id:t.id}).then(S(_().m((function t(){return _().w((function(t){while(1)switch(t.n){case 0:e.$message.success(e.$t("common.operationSuccess")),e.handleGetAdminList();case 1:return t.a(2)}}),t)})))).catch((function(){t.isSms=!t.isSms}))},handleSearch:function(){this.listPram.page=1,this.handleGetAdminList()},handleSizeChange:function(t){this.listPram.limit=t,this.handleGetAdminList(),this.handleGetRoleList(this.listPram)},handleCurrentChange:function(t){this.listPram.page=t,this.handleGetAdminList(),this.handleGetRoleList(this.listPram)},handleGetRoleList:function(){var t=this,e={page:1,limit:this.constants.page.limit[4]};s["d"](e).then((function(e){t.roleList=e}))},handlerOpenDel:function(t){var e=this;this.$confirm(this.$t("admin.system.admin.confirmDelete")).then((function(){var n={id:t.id};r["b"](n).then((function(t){e.$message.success(e.$t("common.prompt")),e.handleGetAdminList()}))}))},handleGetAdminList:function(){var t=this;r["c"](this.listPram).then((function(e){t.listData=e}))},handlerOpenEdit:function(t,e){this.editDialogConfig.editData=e,this.editDialogConfig.isCreate=t,this.editDialogConfig.visible=!0},handlerGetMenuList:function(){var t=this;r["listCategroy"]({page:1,limit:999,type:5}).then((function(e){t.menuList=e.list,t.listData.list.forEach((function(e){var n=[],a=e.rules.split(",");a.map((function(e){t.menuList.filter((function(t){t.id==e&&n.push(t.name)}))})),e.rulesView=n.join(","),t.$set(e,"rulesViews",e.rulesView)}))}))},hideEditDialog:function(){this.editDialogConfig.visible=!1,this.handleGetAdminList()}}},N=k,D=Object(b["a"])(N,a,i,!1,null,"c0090934",null);e["default"]=D.exports},cc5e:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return s})),n.d(e,"d",(function(){return l})),n.d(e,"f",(function(){return o})),n.d(e,"g",(function(){return m})),n.d(e,"e",(function(){return u}));var a=n("b775");function i(t){var e={level:t.level,roleName:t.roleName,status:t.status,rules:t.rules};return Object(a["a"])({url:"/admin/system/role/save",method:"POST",data:e})}function r(t){var e={id:t.id};return Object(a["a"])({url:"/admin/system/role/delete",method:"GET",params:e})}function s(t){return Object(a["a"])({url:"/admin/system/role/info/".concat(t),method:"GET"})}function l(t){var e={createTime:t.createTime,updateTime:t.updateTime,level:t.level,page:t.page,limit:t.limit,roleName:t.roleName,rules:t.rules,status:t.status};return Object(a["a"])({url:"/admin/system/role/list",method:"get",params:e})}function o(t){var e={id:t.id,roleName:t.roleName,rules:t.rules,status:t.status};return Object(a["a"])({url:"/admin/system/role/update",method:"post",params:{id:t.id},data:e})}function m(t){return Object(a["a"])({url:"/admin/system/role/updateStatus",method:"get",params:{id:t.id,status:t.status}})}function u(t){return Object(a["a"])({url:"/admin/system/menu/cache/tree",method:"get"})}}}]);