{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue?vue&type=template&id=0a5856ef&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\grade\\index.vue", "mtime": 1754275430525}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column :label=\"$t('user.grade.levelIcon')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.icon\"\n              :preview-src-list=\"[scope.row.icon]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"name\"\n        :label=\"$t('user.grade.levelName')\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"experience\"\n        :label=\"$t('user.grade.experience')\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"discount\"\n        :label=\"$t('user.grade.discount') + '(%)'\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"commissionRate\"\n        :label=\"$t('user.grade.commissionRate') + '(%)'\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        prop=\"upgradeType\"\n        :label=\"$t('user.grade.upgradeType')\"\n        min-width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getUpgradeTypeColor(scope.row.upgradeType)\">\n            {{ getUpgradeTypeName(scope.row.upgradeType) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"upgradePrice\"\n        :label=\"$t('user.grade.upgradeFee')\"\n        min-width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.upgradeType === 1\">Rp {{ scope.row.upgradePrice }}</span>\n          <span v-else>{{ $t('user.grade.free') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"isAvailable\"\n        :label=\"$t('user.grade.availableStatus')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.isAvailable ? 'success' : 'danger'\">\n            {{ scope.row.isAvailable ? $t('user.grade.available') : $t('user.grade.unavailable') }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('user.grade.status')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:system:user:level:use'])\">\n          <el-switch\n            v-model=\"scope.row.isShow\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            :active-text=\"$t('user.grade.enable')\"\n            :inactive-text=\"$t('user.grade.disable')\"\n            disabled\n            @click.native=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('user.grade.operation')\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\" class=\"mr10\" v-hasPermi=\"['admin:system:user:level:update']\">{{ $t('user.grade.edit') }}</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:system:user:level:delete']\">{{ $t('user.grade.delete') }}</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n  </el-card>\n  <creat-grade ref=\"grades\" :user=\"userInfo\"></creat-grade>\n</div>\n", null]}