(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ed55421"],{"2f2c":function(e,t,r){"use strict";r.d(t,"b",(function(){return d})),r.d(t,"c",(function(){return l})),r.d(t,"r",(function(){return p})),r.d(t,"d",(function(){return m})),r.d(t,"a",(function(){return f})),r.d(t,"g",(function(){return v})),r.d(t,"h",(function(){return h})),r.d(t,"j",(function(){return b})),r.d(t,"i",(function(){return y})),r.d(t,"e",(function(){return g})),r.d(t,"o",(function(){return O})),r.d(t,"q",(function(){return j})),r.d(t,"l",(function(){return w})),r.d(t,"m",(function(){return _})),r.d(t,"n",(function(){return x})),r.d(t,"p",(function(){return C})),r.d(t,"k",(function(){return N})),r.d(t,"f",(function(){return P}));var n=r("b775");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function s(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){var t=c(e,"string");return"symbol"==o(t)?t:t+""}function c(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function d(e){return Object(n["a"])({url:"/admin/system/city/list",method:"get",params:a({},e)})}function l(){return Object(n["a"])({url:"/admin/system/city/list/tree",method:"get"})}function p(e){return Object(n["a"])({url:"/admin/system/city/update/status",method:"post",params:a({},e)})}function m(e){return Object(n["a"])({url:"/admin/system/city/update",method:"post",params:a({},e)})}function f(e){return Object(n["a"])({url:"/admin/system/city/info",method:"get",params:a({},e)})}function v(e){return Object(n["a"])({url:"/admin/express/list",method:"get",params:a({},e)})}function h(){return Object(n["a"])({url:"/admin/express/sync/express",method:"post"})}function b(e){return Object(n["a"])({url:"/admin/express/update/show",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/admin/express/update",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/admin/express/delete",method:"GET",params:a({},e)})}function O(e){return Object(n["a"])({url:"/admin/express/shipping/templates/list",method:"get",params:a({},e)})}function j(e){return Object(n["a"])({url:"/admin/express/shipping/templates/info",method:"get",params:a({},e)})}function w(e){return Object(n["a"])({url:"/admin/express/shipping/free/list",method:"get",params:a({},e)})}function _(e){return Object(n["a"])({url:"admin/express/shipping/region/list",method:"get",params:a({},e)})}function x(e){return Object(n["a"])({url:"admin/express/shipping/templates/save",method:"post",data:e})}function C(e,t){return Object(n["a"])({url:"admin/express/shipping/templates/update",method:"post",data:e,params:a({},t)})}function N(e){return Object(n["a"])({url:"admin/express/shipping/templates/delete",method:"get",params:e})}function P(e){return Object(n["a"])({url:"admin/express/info",method:"get",params:a({},e)})}},"719d":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"deliver-goods"},[r("header",[r("div",{staticClass:"order-num acea-row row-between-wrapper"},[r("div",{staticClass:"num line1"},[e._v("订单号："+e._s(e.orderId))]),e._v(" "),r("div",{staticClass:"name line1"},[r("span",{staticClass:"iconfont iconios-contact"}),e._v(e._s(e.delivery.nikeName)+"\n      ")])]),e._v(" "),r("div",{staticClass:"address"},[r("div",{staticClass:"name"},[e._v("\n        "+e._s(e.delivery.realName)),r("span",{staticClass:"phone"},[e._v(e._s(e.delivery.userPhone))])]),e._v(" "),r("div",[e._v(e._s(e.delivery.userAddress))])]),e._v(" "),e._m(0)]),e._v(" "),r("div",{staticClass:"wrapper"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[e._v("发货方式")]),e._v(" "),r("div",{staticClass:"mode acea-row row-middle row-right"},e._l(e.types,(function(t,n){return r("div",{key:n,staticClass:"goods",class:e.active===n?"on":"",on:{click:function(r){return e.changeType(t,n)}}},[e._v("\n          "+e._s(t.title)),r("span",{staticClass:"iconfont icon-xuanzhong2"})])})),0)]),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:0===e.active,expression:"active === 0"}],staticClass:"list"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[e._v("发货方式")]),e._v(" "),r("select",{directives:[{name:"model",rawName:"v-model",value:e.expressCode,expression:"expressCode"}],staticClass:"mode",on:{change:function(t){var r=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){var t="_value"in e?e._value:e.value;return t}));e.expressCode=t.target.multiple?r:r[0]}}},[r("option",{attrs:{value:""}},[e._v("选择快递公司")]),e._v(" "),e._l(e.express,(function(t,n){return r("option",{key:n,domProps:{value:t.code}},[e._v(e._s(t.name))])}))],2),e._v(" "),r("span",{staticClass:"iconfont icon-up"})]),e._v(" "),r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[e._v("快递单号")]),e._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:e.expressNumber,expression:"expressNumber"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写快递单号"},domProps:{value:e.expressNumber},on:{input:function(t){t.target.composing||(e.expressNumber=t.target.value)}}})])]),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:1===e.active,expression:"active === 1"}],staticClass:"list"},[r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[e._v("送货人")]),e._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:e.deliveryName,expression:"deliveryName"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写送货人"},domProps:{value:e.deliveryName},on:{input:function(t){t.target.composing||(e.deliveryName=t.target.value)}}})]),e._v(" "),r("div",{staticClass:"item acea-row row-between-wrapper"},[r("div",[e._v("送货电话")]),e._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:e.deliveryTel,expression:"deliveryTel"}],staticClass:"mode",attrs:{type:"text",placeholder:"填写送货电话"},domProps:{value:e.deliveryTel},on:{input:function(t){t.target.composing||(e.deliveryTel=t.target.value)}}})])])]),e._v(" "),r("div",{staticStyle:{height:"1.2rem"}}),e._v(" "),r("div",{staticClass:"confirm",on:{click:e.saveInfo}},[e._v("确认提交")])])},o=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"line"},[n("img",{attrs:{src:r("754f")}})])}],i=r("f8b7"),a=r("2f2c"),s=r("61f7"),u=r("69ae");function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",o=r.toStringTag||"@@toStringTag";function i(r,n,o,i){var u=n&&n.prototype instanceof s?n:s,c=Object.create(u.prototype);return d(c,"_invoke",function(r,n,o){var i,s,u,c=0,d=o||[],l=!1,p={p:0,n:0,v:e,a:m,f:m.bind(e,4),d:function(t,r){return i=t,s=0,u=e,p.n=r,a}};function m(r,n){for(s=r,u=n,t=0;!l&&c&&!o&&t<d.length;t++){var o,i=d[t],m=p.p,f=i[2];r>3?(o=f===n)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=m&&((o=r<2&&m<i[1])?(s=0,p.v=n,p.n=i[1]):m<f&&(o=r<3||i[0]>n||n>f)&&(i[4]=r,i[5]=n,p.n=f,s=0))}if(o||r>1)return a;throw l=!0,n}return function(o,d,f){if(c>1)throw TypeError("Generator is already running");for(l&&1===d&&m(d,f),s=d,u=f;(t=s<2?e:u)||!l;){i||(s?s<3?(s>1&&(p.n=-1),m(s,u)):p.n=u:p.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(l=p.n<0)?u:r.call(n,p))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:l}}}(r,o,i),!0),c}var a={};function s(){}function u(){}function l(){}t=Object.getPrototypeOf;var p=[][n]?t(t([][n]())):(d(t={},n,(function(){return this})),t),m=l.prototype=s.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,d(e,o,"GeneratorFunction")),e.prototype=Object.create(m),e}return u.prototype=l,d(m,"constructor",l),d(l,"constructor",u),u.displayName="GeneratorFunction",d(l,o,"GeneratorFunction"),d(m),d(m,o,"Generator"),d(m,n,(function(){return this})),d(m,"toString",(function(){return"[object Generator]"})),(c=function(){return{w:i,m:f}})()}function d(e,t,r,n){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}d=function(e,t,r,n){function i(t,r){d(e,t,(function(e){return this._invoke(t,r,e)}))}t?o?o(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r:(i("next",0),i("throw",1),i("return",2))},d(e,t,r,n)}function l(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){l(i,n,o,a,s,"next",e)}function s(e){l(i,n,o,a,s,"throw",e)}a(void 0)}))}}var m={name:"GoodsDeliver",components:{},props:{},data:function(){return{types:[{type:"1",title:"发货"},{type:"2",title:"送货"},{type:"3",title:"无需发货"}],active:0,orderId:"",delivery:{},express:[],type:"1",deliveryName:"",expressCode:"",expressNumber:"",deliveryTel:""}},watch:{"$route.params.oid":function(e){var t=this;void 0!=e&&(t.orderId=e,t.getIndex())}},created:function(){r.e("chunk-2d0d6f43").then(r.t.bind(null,"756e",7))},mounted:function(){this.orderId=this.$route.params.oid,this.getIndex(),this.getLogistics()},methods:{changeType:function(e,t){this.active=t,this.type=e.type,this.deliveryName="",this.deliveryTel="",this.expressCode="",this.expressNumber=""},getIndex:function(){var e=this;Object(i["e"])({orderNo:this.orderId}).then((function(t){e.delivery=t})).catch((function(t){e.$dialog.error(t.message)}))},getLogistics:function(){var e=this;Object(a["g"])({page:1,limit:999,isShow:1}).then(function(){var t=p(c().m((function t(r){return c().w((function(t){while(1)switch(t.n){case 0:e.express=r.list;case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},saveInfo:function(){var e=p(c().m((function e(){var t,r,n,o,i,a;return c().w((function(e){while(1)switch(e.p=e.n){case 0:t=this,r=t.deliveryName,n=t.deliveryTel,o={},o.type=t.type,o.orderNo=t.orderId,i=t.type,e.n="1"===i?1:"2"===i?4:"3"===i?8:9;break;case 1:if(t.expressCode){e.n=2;break}return e.a(2,t.$dialog.error("请输入快递公司"));case 2:if(t.expressNumber){e.n=3;break}return e.a(2,t.$dialog.error("请输入快递单号"));case 3:return o.expressNumber=t.expressNumber,o.expressRecordType=1,o.expressCode=t.expressCode,t.setInfo(o),e.a(3,9);case 4:return e.p=4,e.n=5,this.$validator({deliveryName:[Object(s["f"])(s["f"].message("发货人姓名"))],deliveryTel:[Object(s["f"])(s["f"].message("发货人电话"))]}).validate({deliveryName:r,deliveryTel:n});case 5:e.n=7;break;case 6:return e.p=6,a=e.v,e.a(2,Object(u["b"])(a));case 7:return o.deliveryName=r,o.deliveryTel=n,t.setInfo(o),e.a(3,9);case 8:return t.setInfo(o),e.a(3,9);case 9:return e.a(2)}}),e,this,[[4,6]])})));function t(){return e.apply(this,arguments)}return t}(),setInfo:function(e){var t=this;Object(i["m"])(e).then((function(e){t.$dialog.success("发送货成功"),t.$router.go(-1)}),(function(e){t.$dialog.error(e.message)}))}}},f=m,v=(r("ed00"),r("2877")),h=Object(v["a"])(f,n,o,!1,null,"7a8e852e",null);t["default"]=h.exports},"754f":function(e,t,r){e.exports=r.p+"static/img/line.05bf1c84.jpg"},df8c:function(e,t,r){},ed00:function(e,t,r){"use strict";r("df8c")},f8b7:function(e,t,r){"use strict";r.d(t,"f",(function(){return o})),r.d(t,"o",(function(){return i})),r.d(t,"g",(function(){return a})),r.d(t,"d",(function(){return s})),r.d(t,"h",(function(){return u})),r.d(t,"e",(function(){return c})),r.d(t,"i",(function(){return d})),r.d(t,"m",(function(){return l})),r.d(t,"l",(function(){return p})),r.d(t,"k",(function(){return m})),r.d(t,"v",(function(){return f})),r.d(t,"u",(function(){return v})),r.d(t,"n",(function(){return h})),r.d(t,"r",(function(){return b})),r.d(t,"s",(function(){return y})),r.d(t,"p",(function(){return g})),r.d(t,"q",(function(){return O})),r.d(t,"c",(function(){return j})),r.d(t,"a",(function(){return w})),r.d(t,"t",(function(){return _})),r.d(t,"j",(function(){return x}));var n=r("b775");function o(e){return Object(n["a"])({url:"/admin/store/order/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/admin/store/order/status/num",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/admin/store/order/list/data",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/admin/store/order/delete",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/admin/store/order/status/list",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/admin/store/order/info",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/admin/store/order/mark",method:"post",params:e})}function l(e){return Object(n["a"])({url:"/admin/store/order/send",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/admin/store/order/refund/refuse",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/admin/store/order/refund",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/admin/store/order/writeUpdate/".concat(e),method:"get"})}function v(e){return Object(n["a"])({url:"/admin/store/order/writeConfirm/".concat(e),method:"get"})}function h(){return Object(n["a"])({url:"/admin/store/order/statistics",method:"get"})}function b(e){return Object(n["a"])({url:"/admin/store/order/statisticsData",method:"get",params:e})}function y(e){return Object(n["a"])({url:"admin/store/order/update/price",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/admin/store/order/time",method:"get",params:e})}function O(){return Object(n["a"])({url:"/admin/store/order/sheet/info",method:"get"})}function j(e){return Object(n["a"])({url:"/admin/store/order/getLogisticsInfo",method:"get",params:e})}function w(){return Object(n["a"])({url:"/admin/pay/component/delivery/company/get/list",method:"get"})}function _(e){return Object(n["a"])({url:"/admin/store/order/video/send",method:"post",data:e})}function x(e){return Object(n["a"])({url:"/admin/yly/print/".concat(e),method:"get"})}}}]);