-- 清理配置表中已废弃的已兑换数量字段（可选执行）
-- 这些字段已经不再使用，现在从任务兑换记录表统计

-- 将所有配置表中的已兑换数量重置为0
UPDATE eb_user_referral_reward_config 
SET 
    redeemed_referral_count = 0,
    redeemed_first_order_count = 0,
    update_time = NOW()
WHERE 
    (redeemed_referral_count > 0 OR redeemed_first_order_count > 0);

-- 查看清理结果
SELECT 
    COUNT(*) as total_configs,
    SUM(CASE WHEN redeemed_referral_count = 0 THEN 1 ELSE 0 END) as cleaned_referral,
    SUM(CASE WHEN redeemed_first_order_count = 0 THEN 1 ELSE 0 END) as cleaned_first_order
FROM eb_user_referral_reward_config;
