{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue?vue&type=template&id=15282485&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"pos-order-list\" ref=\"container\">\n  <div class=\"nav acea-row row-around row-middle\">\n    <div\n      class=\"item\"\n      :class=\"where.status == 'unPaid' ? 'on' : ''\"\n      @click=\"changeStatus('unPaid')\"\n    >\n      待付款\n    </div>\n    <div\n      class=\"item\"\n      :class=\"where.status == 'notShipped' ? 'on' : ''\"\n      @click=\"changeStatus('notShipped')\"\n    >\n      待发货\n    </div>\n    <div\n      class=\"item\"\n      :class=\"where.status == 'spike' ? 'on' : ''\"\n      @click=\"changeStatus('spike')\"\n    >\n      待收货\n    </div>\n    <div\n      class=\"item\"\n      :class=\"where.status == 'toBeWrittenOff' ? 'on' : ''\"\n      @click=\"changeStatus('toBeWrittenOff')\"\n    >\n      待核销\n    </div>\n    <div\n      class=\"item\"\n      :class=\"where.status == 'complete' ? 'on' : ''\"\n      @click=\"changeStatus('complete')\"\n    >\n      已完成\n    </div>\n    <div\n      class=\"item\"\n      :class=\"where.status == 'refunding' ? 'on' : ''\"\n      @click=\"changeStatus('refunding')\"\n    >\n      退款\n    </div>\n  </div>\n  <div class=\"list\">\n    <template v-if=\"list.length > 0\">\n      <div class=\"item\" v-for=\"(item, index) in list\" :key=\"index\">\n        <div class=\"order-num acea-row row-middle\" @click=\"toDetail(item)\">\n          订单号：{{ item.orderId }}\n          <span class=\"time\">下单时间：{{ item.createTime }}</span>\n        </div>\n        <template if=\"item.productList && item.productList.length\">\n          <div\n            class=\"pos-order-goods\"\n            v-for=\"(val, key) in item.productList\"\n            :key=\"key\"\n          >\n            <div\n              class=\"goods acea-row row-between-wrapper\"\n              @click=\"toDetail(item)\"\n            >\n              <div class=\"picTxt acea-row row-between-wrapper\">\n                <div class=\"pictrue\">\n                  <img :src=\"val.info.image\" />\n                </div>\n                <div class=\"text \">\n                  <div class=\"info line2\">\n                    {{ val.info.productName }}\n                  </div>\n                  <div class=\"attr\" v-if=\"val.info.sku\">\n                    {{ val.info.sku }}\n                  </div>\n                </div>\n              </div>\n              <div class=\"money\">\n                <div class=\"x-money\">￥{{ val.info.price }}</div>\n                <div class=\"num\">x{{ val.info.payNum }}</div>\n                <div class=\"y-money\">\n                  <!--￥{{ val.info.productInfo.attrInfo.otPrice }}-->\n                </div>\n              </div>\n            </div>\n          </div>\n        </template>\n        <div class=\"public-total\">\n          共{{ item.totalNum ? item.totalNum : 1 }}件商品，应支付\n          <span class=\"money\">￥{{ item.payPrice }}</span> ( 邮费 ¥{{item.totalPostage ? item.totalPostage : 0}})\n        </div>\n        <div class=\"operation acea-row row-between-wrapper\">\n          <div class=\"more\">\n            <!--            <div class=\"iconfont icon-gengduo\" @click=\"more(index)\"></div>-->\n            <!--            <div class=\"order\" v-show=\"current === index\">-->\n            <!--              <div class=\"items\">-->\n            <!--                {{ where.status > 0 ? \"删除\" : \"取消\" }}订单-->\n            <!--              </div>-->\n            <!--              <div class=\"arrow\"></div>-->\n            <!--            </div>-->\n          </div>\n          <div class=\"acea-row row-middle\">\n            <div class=\"bnt\" @click=\"modify(item, 0)\" v-if=\"!item.isAlterPrice && item.paid == false\">\n              一键改价\n            </div>\n            <div class=\"bnt\" @click=\"modify(item, 1)\">订单备注</div>\n            <div\n              class=\"bnt\"\n              @click=\"modify(item, 2)\"\n              v-if=\"where.status === 'refunding' && item.refundStatus === 1\"\n            >\n              立即退款\n            </div>\n            <div\n              class=\"bnt\"\n              @click=\"modify(item, 3)\"\n              v-if=\"where.status === 'refunding' && item.refundStatus === 1\"\n            >\n              拒绝退款\n            </div>\n            <!--<div-->\n            <!--class=\"bnt cancel\"-->\n            <!--v-if=\"item.pay_type === 'offline' && item.paid === 0\"-->\n            <!--@click=\"offlinePay(item)\"-->\n            <!--&gt;-->\n            <!--确认付款-->\n            <!--</div>-->\n            <router-link\n              class=\"bnt\"\n              v-if=\"where.status === 'notShipped' && item.shippingType !== 2 && item.refundStatus !==2\"\n              :to=\"'/javaMobile/orderDelivery/' + item.orderId + '/' + item.id\"\n            >去发货\n            </router-link>\n            <router-link\n              class=\"bnt\"\n              v-if=\"where.status === 'toBeWrittenOff' && item.shippingType === 2 && isWriteOff && item.refundStatus === 0 && item.paid == true\"\n              :to=\"'/javaMobile/orderCancellation'\"\n            >去核销\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </template>\n   <template v-if=\"!loading && list.length === 0\">\n     <div style=\"text-align: center;\">暂无数据</div>\n   </template>\n  </div>\n  <Loading :loaded=\"loaded\" :loading=\"loading\"></Loading>\n  <PriceChange\n    v-if=\"orderInfo\"\n    :change=\"change\"\n    :orderInfo=\"orderInfo\"\n    v-on:closechange=\"changeclose($event)\"\n    :status=\"status\"\n    @getRefuse=\"getRefuse\"\n  ></PriceChange>\n</div>\n", null]}