{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport statisticsData from \"../components/statisticsData\";\nimport ECharts from \"vue-echarts\";\nimport \"echarts/lib/chart/line\";\nimport \"echarts/lib/component/polar\";\nimport Calendar from \"mpvue-calendar\";\nimport \"mpvue-calendar/src/browser-style.css\";\nimport { statisticsDataApi, orderTimeApi } from '@/api/order';\nimport { parseTime } from '@/utils';\nimport Loading from \"../components/Loading\";\nconst year = new Date().getFullYear();\nconst month = new Date().getMonth() + 1;\nconst day = new Date().getDate();\nexport default {\n  name: \"Statistics\",\n  components: {\n    ECharts,\n    Calendar,\n    Loading,\n    statisticsData\n  },\n  props: {},\n  data: function() {\n    return {\n      polar: {\n        tooltip: {\n          trigger: \"axis\"\n        },\n        legend: {\n          data: [\"\"]\n        },\n        toolbox: {\n          show: false,\n          feature: {\n            mark: { show: true },\n            dataView: { show: true, readOnly: false },\n            magicType: { show: true, type: [\"line\"] },\n            restore: { show: true },\n            saveAsImage: { show: true }\n          }\n        },\n        calculable: true,\n        xAxis: [\n          {\n            type: \"category\",\n            boundaryGap: false,\n            data: [\"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\", \"周日\"],\n            splitLine: {\n              show: false\n            },\n            axisLine: {\n              lineStyle: {\n                color: \"#999\",\n                width: 1 //这里是为了突出显示加上的\n              }\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: \"value\",\n            splitLine: {\n              show: true,\n              lineStyle: {\n                color: [\"#f5f5f5\"],\n                width: 1,\n                type: \"solid\"\n              }\n            },\n            axisLine: {\n              lineStyle: {\n                color: \"#999\",\n                width: 1 //这里是为了突出显示加上的\n              }\n            }\n          }\n        ],\n        series: [\n          {\n            name: \"邮件营销\",\n            type: \"line\",\n            stack: \"总量\",\n            itemStyle: {\n              normal: {\n                color: \"#2291f8\", //折点颜色\n                lineStyle: {\n                  color: \"#2291f8\" //折线颜色\n                }\n              }\n            },\n            data: [120, 132.5, 101, 134, 90, 150, 30]\n          }\n        ],\n        grid: {\n          x: 30,\n          x2: 10,\n          y: 20,\n          y2: 110,\n          left: 40\n        },\n        animationDuration: 2000\n      },\n      value: [[year, month, day - 1], [year, month, day]],\n      isrange: true,\n      weekSwitch: false,\n      ismulti: false,\n      monFirst: true,\n      clean: true, //简洁模式\n      lunar: false, //显示农历\n      renderValues: [],\n      monthRange: [],\n      current: false,\n      where: {\n        dateLimit : '',\n        type: ''\n      },\n      types: \"\", //类型|order=订单数|price=营业额\n      time: \"\", //时间|today=今天|yesterday=昨天|month=本月\n      title: \"\", //时间|today=今天|yesterday=昨天|month=本月\n      growth_rate: \"\", //增长率\n      increase_time: \"\", //增长率\n      increase_time_status: \"\", //增长率\n      time_price: \"\", //增长率\n      loaded: false,\n      loading: false,\n      filter: {\n        page: 1,\n        limit: 10,\n        dateLimit: \"\"\n      },\n      list: []\n    };\n  },\n  watch: {\n    \"$route.params\": function(newVal) {\n      var that = this;\n      if (newVal != undefined) {\n        that.setType(newVal.type);\n        that.setTime(newVal.time);\n        that.getIndex();\n      }\n    }\n  },\n  mounted: function() {\n    this.handelRenderValues();\n    this.setTime(this.$route.params.time);\n    this.setType(this.$route.params.type);\n    this.getIndex();\n    this.getInfo();\n    this.$scroll(this.$refs.container, () => {\n      !this.loading && this.getInfo();\n    });\n  },\n  computed: {\n    monthRangeText() {\n      return this.monthRange.length ? \"固定\" : \"指定范围\";\n    }\n  },\n  methods: {\n    getIndex: function() {\n      var that = this;\n      orderTimeApi(that.where).then(\n        res => {\n          var _info = res.chart,\n            day = [],\n            num = [];\n          _info.forEach(function(item) {\n            day.push(item.time);\n            num.push(item.num);\n          });\n          that.polar.xAxis[0].data = day;\n          that.polar.series[0].data = num;\n          that.growth_rate = res.growthRate;\n          that.increase_time = res.increaseTime;\n          that.increase_time_status = res.increaseTimeStatus;\n          that.time_price = res.time;\n        },\n        error => {\n          that.$dialog.error(error.msg);\n        }\n      );\n    },\n    setTime: function(time) {\n      this.time = time;\n      this.where.dateLimit = time\n      this.filter.dateLimit = time\n      this.list = [];\n      this.filter.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.getIndex();\n      this.getInfo();\n    },\n    setType: function(type) {\n      switch (type) {\n        case \"price\":\n          this.where.type = 1;\n          break;\n        case \"order\":\n          this.where.type = 2;\n          break;\n      }\n    },\n    handelRenderValues(data) {\n      if (this.ismulti) {\n        this.renderValues = this.value.map(v => v.join(\"-\"));\n      } else if (this.isrange) {\n        const values = [];\n        data || this.value;\n        this.value.forEach((v, i) => {\n          values.push(v.join(\"-\"));\n          // if (!i) {\n          //   values.push(\"~\");\n          // }\n        });\n        this.renderValues = values;\n      } else {\n        this.renderValues = [this.value.join(\"-\")];\n      }\n      this.where.dateLimit = this.renderValues.join(',')\n      // this.where.dateLimit = parseTime(this.renderValues[0], '{y}-{m}-{d}')+','+parseTime(this.renderValues[1], '{y}-{m}-{d}')\n      this.filter.dateLimit = this.where.dateLimit\n    },\n    prev(y, m, w) {\n      console.log(y, m, w);\n    },\n    next(year, month, week) {\n      console.log(year, month, week);\n    },\n    selectYear(year) {\n    },\n    setToday() {\n      this.$refs.calendar.setToday();\n    },\n    dateInfo() {\n      const info = this.$refs.calendar.dateInfo(2018, 8, 23);\n    },\n    renderer() {\n      if (this.monthRange.length) {\n        this.monthRange = [\"2018-08\", \"2018-08\"];\n      }\n      this.$refs.calendar.renderer(2018, 8); //渲染2018年8月份\n    },\n    select(val, val2) {\n      if (this.isrange) {\n        this.handelRenderValues([val, val2]);\n      } else if (this.ismulti) {\n        this.handelRenderValues(val);\n      } else {\n        this.handelRenderValues([val]);\n      }\n      this.list = [];\n      this.filter.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.time = \"date\";\n      this.title = \"\";\n      // this.getIndex();\n      // this.getInfo();\n    },\n    dateTitle: function() {\n      this.current = true;\n    },\n    close: function() {\n      this.current = false;\n      this.getIndex();\n      this.getInfo();\n    },\n    getInfo: function() {\n      var that = this;\n      if (that.loading || that.loaded) return;\n      that.loading = true;\n      statisticsDataApi(that.filter).then(\n        res => {\n          that.loading = false;\n          that.loaded = res.length < that.filter.limit;\n          that.list.push.apply(that.list, res);\n          that.filter.page = that.filter.page + 1;\n        },\n        error => {\n          that.$dialog.message(error.msg);\n        }\n      );\n    }\n  }\n};\n", null]}