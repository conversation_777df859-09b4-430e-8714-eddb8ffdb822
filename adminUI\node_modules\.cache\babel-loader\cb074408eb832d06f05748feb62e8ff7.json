{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\ThemePicker\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar version = require('element-ui/package.json').version; // element-ui version from node_modules\nvar ORIGINAL_THEME = '#409EFF'; // default color\n\nexport default {\n  data: function data() {\n    return {\n      chalk: '',\n      // content of theme-chalk css\n      theme: ''\n    };\n  },\n  computed: {\n    defaultTheme: function defaultTheme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    defaultTheme: {\n      handler: function handler(val, oldVal) {\n        this.theme = val;\n      },\n      immediate: true\n    },\n    theme: function () {\n      var _theme = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(val) {\n        var _this = this;\n        var oldVal, themeCluster, originalCluster, $message, getHandler, url, chalkHandler, styles;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              oldVal = this.chalk ? this.theme : ORIGINAL_THEME;\n              if (!(typeof val !== 'string')) {\n                _context.n = 1;\n                break;\n              }\n              return _context.a(2);\n            case 1:\n              themeCluster = this.getThemeCluster(val.replace('#', ''));\n              originalCluster = this.getThemeCluster(oldVal.replace('#', ''));\n              $message = this.$message({\n                message: '  Compiling the theme',\n                customClass: 'theme-message',\n                type: 'success',\n                duration: 0,\n                iconClass: 'el-icon-loading'\n              });\n              getHandler = function getHandler(variable, id) {\n                return function () {\n                  var originalCluster = _this.getThemeCluster(ORIGINAL_THEME.replace('#', ''));\n                  var newStyle = _this.updateStyle(_this[variable], originalCluster, themeCluster);\n                  var styleTag = document.getElementById(id);\n                  if (!styleTag) {\n                    styleTag = document.createElement('style');\n                    styleTag.setAttribute('id', id);\n                    document.head.appendChild(styleTag);\n                  }\n                  styleTag.innerText = newStyle;\n                };\n              };\n              if (this.chalk) {\n                _context.n = 2;\n                break;\n              }\n              url = \"https://unpkg.com/element-ui@\".concat(version, \"/lib/theme-chalk/index.css\");\n              _context.n = 2;\n              return this.getCSSString(url, 'chalk');\n            case 2:\n              chalkHandler = getHandler('chalk', 'chalk-style');\n              chalkHandler();\n              styles = [].slice.call(document.querySelectorAll('style')).filter(function (style) {\n                var text = style.innerText;\n                return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text);\n              });\n              styles.forEach(function (style) {\n                var innerText = style.innerText;\n                if (typeof innerText !== 'string') return;\n                style.innerText = _this.updateStyle(innerText, originalCluster, themeCluster);\n              });\n              this.$emit('change', val);\n              $message.close();\n            case 3:\n              return _context.a(2);\n          }\n        }, _callee, this);\n      }));\n      function theme(_x) {\n        return _theme.apply(this, arguments);\n      }\n      return theme;\n    }()\n  },\n  methods: {\n    updateStyle: function updateStyle(style, oldCluster, newCluster) {\n      var newStyle = style;\n      oldCluster.forEach(function (color, index) {\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index]);\n      });\n      return newStyle;\n    },\n    getCSSString: function getCSSString(url, variable) {\n      var _this2 = this;\n      return new Promise(function (resolve) {\n        var xhr = new XMLHttpRequest();\n        xhr.onreadystatechange = function () {\n          if (xhr.readyState === 4 && xhr.status === 200) {\n            _this2[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '');\n            resolve();\n          }\n        };\n        xhr.open('GET', url);\n        xhr.send();\n      });\n    },\n    getThemeCluster: function getThemeCluster(theme) {\n      var tintColor = function tintColor(color, tint) {\n        var red = parseInt(color.slice(0, 2), 16);\n        var green = parseInt(color.slice(2, 4), 16);\n        var blue = parseInt(color.slice(4, 6), 16);\n        if (tint === 0) {\n          // when primary color is in its rgb space\n          return [red, green, blue].join(',');\n        } else {\n          red += Math.round(tint * (255 - red));\n          green += Math.round(tint * (255 - green));\n          blue += Math.round(tint * (255 - blue));\n          red = red.toString(16);\n          green = green.toString(16);\n          blue = blue.toString(16);\n          return \"#\".concat(red).concat(green).concat(blue);\n        }\n      };\n      var shadeColor = function shadeColor(color, shade) {\n        var red = parseInt(color.slice(0, 2), 16);\n        var green = parseInt(color.slice(2, 4), 16);\n        var blue = parseInt(color.slice(4, 6), 16);\n        red = Math.round((1 - shade) * red);\n        green = Math.round((1 - shade) * green);\n        blue = Math.round((1 - shade) * blue);\n        red = red.toString(16);\n        green = green.toString(16);\n        blue = blue.toString(16);\n        return \"#\".concat(red).concat(green).concat(blue);\n      };\n      var clusters = [theme];\n      for (var i = 0; i <= 9; i++) {\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))));\n      }\n      clusters.push(shadeColor(theme, 0.1));\n      return clusters;\n    }\n  }\n};", null]}