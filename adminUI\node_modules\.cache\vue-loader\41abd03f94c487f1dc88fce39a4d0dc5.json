{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue?vue&type=template&id=2215a1a0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"order-index\" ref=\"container\">\n  <div class=\"header acea-row\">\n    <router-link class=\"item\" :to=\"'/javaMobile/orderList/unPaid'\">\n      <div class=\"num\">{{ census.unpaidCount }}</div>\n      <div>待付款</div>\n    </router-link>\n    <router-link class=\"item\" :to=\"'/javaMobile/orderList/notShipped'\">\n      <div class=\"num\">{{ census.unshippedCount }}</div>\n      <div>待发货</div>\n    </router-link>\n    <router-link class=\"item\" :to=\"'/javaMobile/orderList/spike'\">\n      <div class=\"num\">{{ census.receivedCount }}</div>\n      <div>待收货</div>\n    </router-link>\n    <router-link class=\"item\" :to=\"'/javaMobile/orderList/toBeWrittenOff'\">\n      <div class=\"num\">{{ census.verificationCount }}</div>\n      <div>待核销</div>\n    </router-link>\n    <router-link class=\"item\" :to=\"'/javaMobile/orderList/refunding'\">\n      <div class=\"num\">{{ census.refundCount }}</div>\n      <div>退款</div>\n    </router-link>\n  </div>\n  <div class=\"wrapper\">\n    <div class=\"title\">\n      <span class=\"iconfont icon-shujutongji\"></span>数据统计\n    </div>\n    <div class=\"list acea-row\">\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/price/today'\">\n        <div class=\"num\">{{ census.todayPrice }}</div>\n        <div>今日成交额</div>\n      </router-link>\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/price/yesterday'\">\n        <div class=\"num\">{{ census.proPrice }}</div>\n        <div>昨日成交额</div>\n      </router-link>\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/price/month'\">\n        <div class=\"num\">{{ census.monthPrice }}</div>\n        <div>本月成交额</div>\n      </router-link>\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/order/today'\">\n        <div class=\"num\">{{ census.todayCount }}</div>\n        <div>今日订单数</div>\n      </router-link>\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/order/yesterday'\">\n        <div class=\"num\">{{ census.proCount }}</div>\n        <div>昨日订单数</div>\n      </router-link>\n      <router-link class=\"item\" :to=\"'/javaMobile/orderStatisticsDetail/order/month'\">\n        <div class=\"num\">{{ census.monthCount }}</div>\n        <div>本月订单数</div>\n      </router-link>\n    </div>\n  </div>\n  <statistics-data :list=\"list\"></statistics-data>\n  <Loading :loaded=\"loaded\" :loading=\"loading\"></Loading>\n</div>\n", null]}