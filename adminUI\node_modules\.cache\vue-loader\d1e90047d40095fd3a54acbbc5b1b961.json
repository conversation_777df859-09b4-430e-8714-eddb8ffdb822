{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue?vue&type=template&id=4c776bef&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('el-drawer',_vm._g(_vm._b({attrs:{\"append-to-body\":\"\"},on:{\"opened\":_vm.onOpen,\"close\":_vm.onClose}},'el-drawer',_vm.$attrs,false),_vm.$listeners),[_c('div',{staticClass:\"action-bar\",style:({'text-align': 'left'})},[_c('span',{staticClass:\"bar-btn\",on:{\"click\":_vm.refresh}},[_c('i',{staticClass:\"el-icon-refresh\"}),_vm._v(\"\\n        刷新\\n      \")]),_vm._v(\" \"),_c('span',{ref:\"copyBtn\",staticClass:\"bar-btn copy-json-btn\"},[_c('i',{staticClass:\"el-icon-document-copy\"}),_vm._v(\"\\n        复制JSON\\n      \")]),_vm._v(\" \"),_c('span',{staticClass:\"bar-btn\",on:{\"click\":_vm.exportJsonFile}},[_c('i',{staticClass:\"el-icon-download\"}),_vm._v(\"\\n        导出JSON文件\\n      \")]),_vm._v(\" \"),_c('span',{staticClass:\"bar-btn delete-btn\",on:{\"click\":function($event){return _vm.$emit('update:visible', false)}}},[_c('i',{staticClass:\"el-icon-circle-close\"}),_vm._v(\"\\n        关闭\\n      \")])]),_vm._v(\" \"),_c('div',{staticClass:\"json-editor\",attrs:{\"id\":\"editorJson\"}})])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}