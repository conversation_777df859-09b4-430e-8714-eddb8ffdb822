{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue?vue&type=template&id=2b1933c4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue", "mtime": 1754301662333}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <el-tabs v-model=\"form.type\" @tab-click=\"onChangeType\" class=\"mb20\">\n      <el-tab-pane :label=\"$t('product.isHot')\" name=\"1\"></el-tab-pane>\n      <el-tab-pane :label=\"$t('product.isBenefit')\" name=\"2\"></el-tab-pane>\n      <el-tab-pane :label=\"$t('product.isTikTok')\" name=\"3\"></el-tab-pane>\n    </el-tabs>\n    <div class=\"container mt-1\">\n      <el-form v-model=\"form\" inline size=\"small\">\n        <el-form-item :label=\"$t('product.productName')\">\n          <el-input\n            v-model=\"form.keywords\"\n            :placeholder=\"$t('product.enterProductName')\"\n            clearable\n          />\n        </el-form-item>\n        <el-form-item :label=\"$t('product.status')\">\n          <el-select\n            v-model=\"form.isShow\"\n            :placeholder=\"$t('product.pleaseSelect')\"\n          >\n            <el-option\n              v-for=\"item in statusOptions\"\n              :key=\"item.value\"\n              :label=\"$t('product.' + item.label)\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"getList\">\n      {{ $t(\"product.query\") }}\n    </el-button>\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm()\">\n      {{ $t(\"product.reset\") }}\n    </el-button>\n  </el-card>\n\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        :label=\"$t('common.serialNumber')\"\n        type=\"index\"\n        width=\"110\"\n      />\n      <el-table-column :label=\"$t('product.productImage')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.image\"\n              :preview-src-list=\"[scope.row.image]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('product.productName')\"\n        prop=\"storeName\"\n        min-width=\"300\"\n        :show-overflow-tooltip=\"true\"\n      />\n      <el-table-column\n        :label=\"$t('product.productPrice')\"\n        min-width=\"90\"\n        align=\"center\"\n      >\n      <template slot-scope=\"scope\">{{\n          formatAmount(scope.row.price)\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('product.cashbackRate')\"\n        min-width=\"80\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">{{\n          formatRate(scope.row.cashBackRate)\n        }}</template>\n      </el-table-column>\n\n      <el-table-column\n        :label=\"$t('product.addTime')\"\n        min-width=\"120\"\n        align=\"center\"\n        prop=\"addTime\"\n      >\n        <template slot-scope=\"scope\">{{\n          formatTime(scope.row.addTime)\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('product.action')\"\n        min-width=\"60\"\n        fixed=\"right\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            @click=\"handleDelete(scope.row)\"\n          >\n            {{ $t(\"product.offline\") }}\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"sizeChange\"\n      @current-change=\"pageChange\"\n      :current-page=\"form.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"form.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"form.total\"\n    />\n  </el-card>\n</div>\n", null]}