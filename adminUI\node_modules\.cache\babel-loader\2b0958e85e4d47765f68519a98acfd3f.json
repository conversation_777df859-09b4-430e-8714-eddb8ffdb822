{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\Statistics.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport statisticsData from \"../components/statisticsData\";\nimport ECharts from \"vue-echarts\";\nimport \"echarts/lib/chart/line\";\nimport \"echarts/lib/component/polar\";\nimport Calendar from \"mpvue-calendar\";\nimport \"mpvue-calendar/src/browser-style.css\";\nimport { statisticsDataApi, orderTimeApi } from '@/api/order';\nimport { parseTime } from '@/utils';\nimport Loading from \"../components/Loading\";\nvar year = new Date().getFullYear();\nvar month = new Date().getMonth() + 1;\nvar day = new Date().getDate();\nexport default {\n  name: \"Statistics\",\n  components: {\n    ECharts: ECharts,\n    Calendar: Calendar,\n    Loading: Loading,\n    statisticsData: statisticsData\n  },\n  props: {},\n  data: function data() {\n    return {\n      polar: {\n        tooltip: {\n          trigger: \"axis\"\n        },\n        legend: {\n          data: [\"\"]\n        },\n        toolbox: {\n          show: false,\n          feature: {\n            mark: {\n              show: true\n            },\n            dataView: {\n              show: true,\n              readOnly: false\n            },\n            magicType: {\n              show: true,\n              type: [\"line\"]\n            },\n            restore: {\n              show: true\n            },\n            saveAsImage: {\n              show: true\n            }\n          }\n        },\n        calculable: true,\n        xAxis: [{\n          type: \"category\",\n          boundaryGap: false,\n          data: [\"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\", \"周日\"],\n          splitLine: {\n            show: false\n          },\n          axisLine: {\n            lineStyle: {\n              color: \"#999\",\n              width: 1 //这里是为了突出显示加上的\n            }\n          }\n        }],\n        yAxis: [{\n          type: \"value\",\n          splitLine: {\n            show: true,\n            lineStyle: {\n              color: [\"#f5f5f5\"],\n              width: 1,\n              type: \"solid\"\n            }\n          },\n          axisLine: {\n            lineStyle: {\n              color: \"#999\",\n              width: 1 //这里是为了突出显示加上的\n            }\n          }\n        }],\n        series: [{\n          name: \"邮件营销\",\n          type: \"line\",\n          stack: \"总量\",\n          itemStyle: {\n            normal: {\n              color: \"#2291f8\",\n              //折点颜色\n              lineStyle: {\n                color: \"#2291f8\" //折线颜色\n              }\n            }\n          },\n          data: [120, 132.5, 101, 134, 90, 150, 30]\n        }],\n        grid: {\n          x: 30,\n          x2: 10,\n          y: 20,\n          y2: 110,\n          left: 40\n        },\n        animationDuration: 2000\n      },\n      value: [[year, month, day - 1], [year, month, day]],\n      isrange: true,\n      weekSwitch: false,\n      ismulti: false,\n      monFirst: true,\n      clean: true,\n      //简洁模式\n      lunar: false,\n      //显示农历\n      renderValues: [],\n      monthRange: [],\n      current: false,\n      where: {\n        dateLimit: '',\n        type: ''\n      },\n      types: \"\",\n      //类型|order=订单数|price=营业额\n      time: \"\",\n      //时间|today=今天|yesterday=昨天|month=本月\n      title: \"\",\n      //时间|today=今天|yesterday=昨天|month=本月\n      growth_rate: \"\",\n      //增长率\n      increase_time: \"\",\n      //增长率\n      increase_time_status: \"\",\n      //增长率\n      time_price: \"\",\n      //增长率\n      loaded: false,\n      loading: false,\n      filter: {\n        page: 1,\n        limit: 10,\n        dateLimit: \"\"\n      },\n      list: []\n    };\n  },\n  watch: {\n    \"$route.params\": function $routeParams(newVal) {\n      var that = this;\n      if (newVal != undefined) {\n        that.setType(newVal.type);\n        that.setTime(newVal.time);\n        that.getIndex();\n      }\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    this.handelRenderValues();\n    this.setTime(this.$route.params.time);\n    this.setType(this.$route.params.type);\n    this.getIndex();\n    this.getInfo();\n    this.$scroll(this.$refs.container, function () {\n      !_this.loading && _this.getInfo();\n    });\n  },\n  computed: {\n    monthRangeText: function monthRangeText() {\n      return this.monthRange.length ? \"固定\" : \"指定范围\";\n    }\n  },\n  methods: {\n    getIndex: function getIndex() {\n      var that = this;\n      orderTimeApi(that.where).then(function (res) {\n        var _info = res.chart,\n          day = [],\n          num = [];\n        _info.forEach(function (item) {\n          day.push(item.time);\n          num.push(item.num);\n        });\n        that.polar.xAxis[0].data = day;\n        that.polar.series[0].data = num;\n        that.growth_rate = res.growthRate;\n        that.increase_time = res.increaseTime;\n        that.increase_time_status = res.increaseTimeStatus;\n        that.time_price = res.time;\n      }, function (error) {\n        that.$dialog.error(error.msg);\n      });\n    },\n    setTime: function setTime(time) {\n      this.time = time;\n      this.where.dateLimit = time;\n      this.filter.dateLimit = time;\n      this.list = [];\n      this.filter.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.getIndex();\n      this.getInfo();\n    },\n    setType: function setType(type) {\n      switch (type) {\n        case \"price\":\n          this.where.type = 1;\n          break;\n        case \"order\":\n          this.where.type = 2;\n          break;\n      }\n    },\n    handelRenderValues: function handelRenderValues(data) {\n      if (this.ismulti) {\n        this.renderValues = this.value.map(function (v) {\n          return v.join(\"-\");\n        });\n      } else if (this.isrange) {\n        var values = [];\n        data || this.value;\n        this.value.forEach(function (v, i) {\n          values.push(v.join(\"-\"));\n          // if (!i) {\n          //   values.push(\"~\");\n          // }\n        });\n        this.renderValues = values;\n      } else {\n        this.renderValues = [this.value.join(\"-\")];\n      }\n      this.where.dateLimit = this.renderValues.join(',');\n      // this.where.dateLimit = parseTime(this.renderValues[0], '{y}-{m}-{d}')+','+parseTime(this.renderValues[1], '{y}-{m}-{d}')\n      this.filter.dateLimit = this.where.dateLimit;\n    },\n    prev: function prev(y, m, w) {\n      console.log(y, m, w);\n    },\n    next: function next(year, month, week) {\n      console.log(year, month, week);\n    },\n    selectYear: function selectYear(year) {},\n    setToday: function setToday() {\n      this.$refs.calendar.setToday();\n    },\n    dateInfo: function dateInfo() {\n      var info = this.$refs.calendar.dateInfo(2018, 8, 23);\n    },\n    renderer: function renderer() {\n      if (this.monthRange.length) {\n        this.monthRange = [\"2018-08\", \"2018-08\"];\n      }\n      this.$refs.calendar.renderer(2018, 8); //渲染2018年8月份\n    },\n    select: function select(val, val2) {\n      if (this.isrange) {\n        this.handelRenderValues([val, val2]);\n      } else if (this.ismulti) {\n        this.handelRenderValues(val);\n      } else {\n        this.handelRenderValues([val]);\n      }\n      this.list = [];\n      this.filter.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.time = \"date\";\n      this.title = \"\";\n      // this.getIndex();\n      // this.getInfo();\n    },\n    dateTitle: function dateTitle() {\n      this.current = true;\n    },\n    close: function close() {\n      this.current = false;\n      this.getIndex();\n      this.getInfo();\n    },\n    getInfo: function getInfo() {\n      var that = this;\n      if (that.loading || that.loaded) return;\n      that.loading = true;\n      statisticsDataApi(that.filter).then(function (res) {\n        that.loading = false;\n        that.loaded = res.length < that.filter.limit;\n        that.list.push.apply(that.list, res);\n        that.filter.page = that.filter.page + 1;\n      }, function (error) {\n        that.$dialog.message(error.msg);\n      });\n    }\n  }\n};", null]}