{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\statisticsData.vue?vue&type=template&id=b6e74016&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\components\\statisticsData.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{ref:\"container\"},[_c('div',{staticClass:\"public-wrapper\"},[_vm._m(0),_vm._v(\" \"),_vm._m(1),_vm._v(\" \"),_c('div',{staticClass:\"conter\"},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"item acea-row row-between-wrapper\"},[_c('div',{staticClass:\"data\"},[_vm._v(_vm._s(item.time))]),_vm._v(\" \"),_c('div',{staticClass:\"browse\"},[_vm._v(_vm._s(item.count))]),_vm._v(\" \"),_c('div',{staticClass:\"turnover\"},[_vm._v(_vm._s(item.price))])])}),0)])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"title\"},[_c('span',{staticClass:\"iconfont icon-xiangxishuju\"}),_vm._v(\"详细数据\\n    \")])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"nav acea-row row-between-wrapper\"},[_c('div',{staticClass:\"data\"},[_vm._v(\"日期\")]),_vm._v(\" \"),_c('div',{staticClass:\"browse\"},[_vm._v(\"订单数\")]),_vm._v(\" \"),_c('div',{staticClass:\"turnover\"},[_vm._v(\"成交额\")])])}]\n\nexport { render, staticRenderFns }"]}