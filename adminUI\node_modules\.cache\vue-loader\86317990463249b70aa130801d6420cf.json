{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\userList\\index.vue?vue&type=template&id=44b1ead6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\userList\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('el-form',{attrs:{\"inline\":\"\"}},[_c('el-form-item',[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":\"请输入用户名称\"},model:{value:(_vm.tableFrom.keywords),callback:function ($$v) {_vm.$set(_vm.tableFrom, \"keywords\", $$v)},expression:\"tableFrom.keywords\"}},[_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-search\"},on:{\"click\":_vm.search},slot:\"append\"})],1)],1)],1)],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tableData.data,\"width\":\"800px\",\"size\":\"small\"}},[_c('el-table-column',{attrs:{\"label\":\"\",\"width\":\"40\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio',{attrs:{\"label\":scope.row.uid},nativeOn:{\"change\":function($event){return _vm.getTemplateRow(scope.$index,scope.row)}},model:{value:(_vm.templateRadio),callback:function ($$v) {_vm.templateRadio=$$v},expression:\"templateRadio\"}},[_vm._v(\" \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"uid\",\"label\":\"ID\",\"min-width\":\"60\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"nickname\",\"label\":\"微信用户名称\",\"min-width\":\"130\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"用户头像\",\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticClass:\"demo-image__preview\"},[_c('el-image',{staticClass:\"tabImage\",attrs:{\"src\":scope.row.avatar,\"preview-src-list\":[scope.row.avatar]}})],1)]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"性别\",\"min-width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"saxFilter\")(scope.row.sex)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"地区\",\"min-width\":\"130\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(scope.row.addres))])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"block\"},[_c('el-pagination',{attrs:{\"page-sizes\":[10, 20, 30, 40],\"page-size\":_vm.tableFrom.limit,\"current-page\":_vm.tableFrom.page,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.tableData.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.pageChange}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}