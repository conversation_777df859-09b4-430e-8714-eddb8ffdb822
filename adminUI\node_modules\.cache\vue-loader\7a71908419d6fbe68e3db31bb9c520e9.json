{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\financial\\commission\\withdrawal\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { applyListApi, applyBalanceApi, applyUpdateApi, applyStatusApi } from '@/api/financial'\nimport cardsData from '@/components/cards/index'\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: 'AccountsExtract',\n  components: {\n    cardsData,\n    zbParser\n  },\n  data() {\n    return {\n      editData: {},\n      isCreate: 1,\n      dialogVisible: false,\n      timeVal: [],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        extractType: '',\n        status: '',\n        dateLimit: '',\n        keywords: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      cardLists: [],\n      applyId: null,\n      extractType: ''\n    }\n  },\n  mounted() {\n    this.getList()\n    this.getBalance()\n  },\n  methods: {\n    checkPermi,\n    resetForm(){\n      this.dialogVisible = false;\n    },\n    handleEdit(row) {\n      this.extractType = row.extractType;\n      this.applyId = row.id;\n      this.dialogVisible = true;\n      this.isCreate = 1;\n      this.editData = JSON.parse(JSON.stringify(row));\n    },\n    handlerSubmit:Debounce(function(formValue) {\n      formValue.id = this.applyId;\n      formValue.extractType = this.extractType;\n      applyUpdateApi(formValue).then(data => {\n        this.$message.success('编辑成功')\n        this.dialogVisible = false\n        this.getList()\n      })\n    }),\n    handleClose() {\n      this.dialogVisible = false\n      this.editData = {}\n    },\n    onExamine(id) {\n      this.$prompt('未通过', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入原因',\n        inputType: 'textarea',\n        inputValue: '输入信息不完整或有误!',\n        inputPlaceholder: '请输入原因',\n        inputValidator: (value) => {\n          if (!value) {\n            return '请输入原因'\n          }\n        }\n      }).then(({ value }) => {\n        applyStatusApi({ id: id, status: -1, backMessage: value }).then(res => {\n          this.$message({\n            type: 'success',\n            message: '提交成功'\n          })\n          this.getList()\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '取消输入'\n        })\n      })\n    },\n    ok(id) {\n      this.$modalSure('审核通过吗').then(() => {\n        applyStatusApi({id: id, status: 1 }).then(() => {\n          this.$message.success('操作成功')\n          this.getList()\n        })\n      })\n    },\n    // 金额\n    getBalance() {\n      applyBalanceApi({dateLimit: this.tableFrom.dateLimit}).then(res => {\n        this.cardLists = [\n          { name: '待提现金额', count: res.toBeWithdrawn,color:'#1890FF',class:'one',icon:'iconzhichujine1' },\n          { name: '佣金总金额', count: res.commissionTotal,color:'#A277FF',class:'two',icon:'iconzhifuyongjinjine1' },\n          { name: '已提现金额', count: res.withdrawn,color:'#EF9C20',class:'three',icon:'iconyingyee1' },\n          { name: '未提现金额', count: res.unDrawn,color:'#1BBE6B',class:'four',icon:'iconyuezhifujine2' }\n        ]\n      })\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.timeVal = []\n      this.tableFrom.dateLimit = tab\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getBalance();\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getBalance();\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      applyListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    }\n  }\n}\n", null]}