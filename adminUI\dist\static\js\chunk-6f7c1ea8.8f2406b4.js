(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f7c1ea8"],{"2f4f":function(e,t,l){"use strict";l("c626")},"414e":function(e,t,l){"use strict";l.r(t);var r=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"divBox relative"},[l("el-card",{staticClass:"box-card"},[l("div",{staticClass:"container mt-1"},[l("el-form",{attrs:{inline:"",size:"small"},model:{value:e.userFrom,callback:function(t){e.userFrom=t},expression:"userFrom"}},[l("el-form-item",{attrs:{label:e.$t("user.center.nickname")+"："}},[l("el-input",{attrs:{placeholder:e.$t("user.center.nickname"),clearable:""},model:{value:e.userFrom.keywords,callback:function(t){e.$set(e.userFrom,"keywords",t)},expression:"userFrom.keywords"}})],1),e._v(" "),l("el-form-item",{attrs:{label:e.$t("user.center.phone")+"："}},[l("el-input",{attrs:{placeholder:e.$t("user.center.phone"),clearable:""},model:{value:e.userFrom.phone,callback:function(t){e.$set(e.userFrom,"phone",t)},expression:"userFrom.phone"}})],1),e._v(" "),l("el-form-item",{attrs:{label:e.$t("user.center.userLevel")+"："}},[l("el-select",{staticClass:"selWidth",attrs:{placeholder:e.$t("common.pleaseSelect"),clearable:"",filterable:"",multiple:""},model:{value:e.levelData,callback:function(t){e.levelData=t},expression:"levelData"}},e._l(e.levelList,(function(t,r){return l("el-option",{key:r,attrs:{value:t.id,label:t.name}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.name))]),e._v(" "),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("\n                "+e._s(e.getUpgradeTypeText(t.upgradeType))+"\n                "),1===t.upgradeType?l("span",[e._v(" - Rp "+e._s(t.upgradePrice))]):e._e()])])})),1)],1)],1)],1),e._v(" "),l("el-button",{staticClass:"mr10",attrs:{size:"small",type:"primary"},on:{click:function(t){return e.getList(1)}}},[e._v("\n      "+e._s(e.$t("common.query"))+"\n    ")]),e._v(" "),l("el-button",{staticClass:"mr10",attrs:{size:"small",type:""},on:{click:function(t){return e.resetForm()}}},[e._v("\n      "+e._s(e.$t("common.reset"))+"\n    ")])],1),e._v(" "),l("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"12px"}},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableData,size:"small","header-cell-style":{fontWeight:"bold"}}},[l("el-table-column",{attrs:{label:e.$t("common.serialNumber"),type:"index",width:"110"}}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.avatar"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(e){return[l("div",{staticClass:"demo-image__preview"},[l("el-image",{staticStyle:{width:"36px",height:"36px"},attrs:{src:e.row.avatar,"preview-src-list":[e.row.avatar]}})],1)]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.nickname"),"min-width":"150",prop:"nickname"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.nickname)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.tiktokId"),"min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.openId)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.phone"),"min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.phone)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.whatsApp"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.whatsAppAccount)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.registerTime"),"min-width":"150",prop:"createTime"}}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.lastLoginTime"),"min-width":"150",prop:"lastLoginTime"}}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.orderCount"),"min-width":"80",prop:"payCount"}}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.orderFinishCount"),"min-width":"80",prop:"spreadCount"}}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.isAgent"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.isAgent)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.isPartner"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(t.row.isPartner)))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.userLevelLabel"),"min-width":"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e._f("filterEmpty")(e.handlelevelFilter(t.row.level))))])]}}])}),e._v(" "),l("el-table-column",{attrs:{label:e.$t("user.center.inviter"),"min-width":"80",prop:"spreadNickname"}})],1),e._v(" "),l("el-pagination",{staticClass:"mt20",attrs:{"current-page":e.userFrom.page,"page-sizes":[20,40,60,100],"page-size":e.userFrom.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.userFrom.total},on:{"size-change":e.sizeChange,"current-change":e.pageChange}})],1)],1)},a=[],n=l("c24f"),s={name:"UserCenter",data:function(){return{userFrom:{keywords:"",phone:"",level:[],page:1,limit:20,total:0},tableData:[],levelList:[],levelData:[],loading:!1}},created:function(){},mounted:function(){this.getLevelList()},methods:{handlelevelFilter:function(e){if(!String(e)&&0!==e)return"";var t=this.levelList.filter((function(t){return e===t.grade}));return t.length?t[0].name:""},getList:function(e){var t=this;this.loading=!0,this.userFrom.page=e||this.userFrom.page,this.userFrom.level=this.levelData.join(","),0==this.loginType&&(this.userFrom.userType=""),Object(n["D"])(this.userFrom).then((function(e){t.tableData=e.list,t.userFrom.total=e.total,t.loading=!1})).catch((function(){t.loading=!1})),this.checkedCities=this.$cache.local.has("user_stroge")?this.$cache.local.getJSON("user_stroge"):this.checkedCities},pageChange:function(e){this.userFrom.page=e,this.getList()},sizeChange:function(e){this.userFrom.limit=e,this.getList()},resetForm:function(){this.userFrom={keywords:"",phone:"",level:[],page:1,limit:20,total:0},this.levelData=[],this.getList()},getLevelList:function(){var e=this;Object(n["n"])().then((function(t){e.levelList=t,localStorage.setItem("levelKey",JSON.stringify(t)),e.getList()}))},getUpgradeTypeText:function(e){return this.$t("user.grade.upgradeTypes.".concat(e))||this.$t("common.unknown")}}},i=s,o=(l("2f4f"),l("2877")),c=Object(o["a"])(i,r,a,!1,null,"b060d7fe",null);t["default"]=c.exports},c626:function(e,t,l){}}]);