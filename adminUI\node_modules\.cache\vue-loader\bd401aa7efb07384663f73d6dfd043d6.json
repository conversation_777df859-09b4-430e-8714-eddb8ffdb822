{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\mobile\\orderStatistics\\orderList.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport PriceChange from \"../components/PriceChange\";\nimport Loading from \"../components/Loading\";\nimport { orderRefuseApi, orderListApi, statisticsDataApi, orderMarkApi, editPriceApi, orderRefundApi } from '@/api/order';\nimport { required, num } from \"@/utils/validate\";\nimport { validatorDefaultCatch } from \"@/libs/dialog\";\nimport { isWriteOff } from \"@/utils\";\nexport default {\n  name: \"AdminOrderList\",\n  components: {\n    PriceChange,\n    Loading\n  },\n  props: {},\n  data: function() {\n    return {\n      isWriteOff: isWriteOff(),\n      current: \"\",\n      change: false,\n      types: 0,\n      where: {\n        page: 1,\n        limit: 10,\n        status: 'unPaid'\n      },\n      list: [],\n      loaded: false,\n      loading: false,\n      orderInfo: {},\n      status: null\n    };\n  },\n  watch: {\n    \"$route.params.types\": function(newVal) {\n      let that = this;\n      if (newVal != undefined) {\n        that.where.status = newVal;\n        that.init();\n      }\n    },\n    types: function() {\n      this.getIndex();\n    }\n  },\n  created() {\n    import('@/assets/js/media_750')\n  },\n  mounted() {\n    this.where.status = this.$route.params.types;\n    this.current = \"\";\n    this.getIndex();\n    this.$scroll(this.$refs.container, () => {\n      !this.loading && this.getIndex();\n    });\n  },\n  methods: {\n    more: function(index) {\n      if (this.current === index) this.current = \"\";\n      else this.current = index;\n    },\n    modify: function(item, status) {\n      this.change = true;\n      this.orderInfo = item;\n      this.status = status;\n    },\n    changeclose: function(msg) {\n      this.change = msg;\n      this.init()\n    },\n    // 拒绝退款\n    getRefuse(id, reason) {\n      orderRefuseApi({ orderNo: id, reason: reason}).then(() =>{\n        this.change = false;\n        this.$dialog.success(\"已拒绝退款\");\n        this.init();\n      }).catch((error) => {\n        this.$dialog.error(error.message);\n      });\n    },\n    async savePrice(opt) {\n      let that = this,\n        data = {},\n        price = opt.price,\n        refundPrice = opt.refundPrice,\n        refundStatus = that.orderInfo.refundStatus,\n        remark = opt.remark;\n      if (that.status == 0 && refundStatus === 0) {\n        try {\n          await this.$validator({\n            price: [\n              required(required.message(\"金额\"))\n            ]\n          }).validate({ price });\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.price = price;\n        data.orderNo  = opt.orderId;\n        editPriceApi(data).then(() =>{\n          that.change = false;\n          that.$dialog.success(\"改价成功\");\n          that.init();\n        }).catch((error) => {\n          that.$dialog.error(error.message);\n        });\n      } else if (that.status == 0 && refundStatus === 1) {\n        try {\n          await this.$validator({\n            refundPrice: [\n              required(required.message(\"金额\")),\n              num(num.message(\"金额\"))\n            ]\n          }).validate({ refundPrice });\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.amount = refundPrice;\n        data.type = opt.type;\n        data.orderNo  = opt.orderId;\n        orderRefundApi(data).then(\n          res => {\n            that.change = false;\n            that.$dialog.success('退款成功');\n            that.init();\n          },\n          err => {\n            that.change = false;\n            that.$dialog.error(err.message);\n          }\n        );\n      } else {\n        try {\n          await this.$validator({\n            remark: [required(required.message(\"备注\"))]\n          }).validate({ remark });\n        } catch (e) {\n          return validatorDefaultCatch(e);\n        }\n        data.mark = remark;\n        data.orderNo = opt.orderId;\n        orderMarkApi(data).then(\n          res => {\n            that.change = false;\n            that.$dialog.success('提交成功');\n            that.init();\n          },\n          err => {\n            that.change = false;\n            that.$dialog.error(err.message);\n          }\n        );\n      }\n    },\n    init: function() {\n      this.list = [];\n      this.where.page = 1;\n      this.loaded = false;\n      this.loading = false;\n      this.getIndex();\n      this.current = \"\";\n    },\n    getIndex() {\n      if (this.loading || this.loaded) return;\n      this.loading = true;\n      orderListApi(this.where).then(\n        res => {\n          this.loading = false;\n          this.loaded = res.list.length < this.where.limit;\n          this.list.push.apply(this.list, res.list);\n          this.where.page = this.where.page + 1;\n        },\n        err => {\n          this.$dialog.error(err.message);\n        }\n      );\n    },\n    changeStatus(val) {\n      if (this.where.status != val) {\n        this.where.status = val;\n        this.init();\n      }\n    },\n    toDetail(item) {\n      this.$router.push({ path: \"/javaMobile/orderDetail/\" + item.orderId });\n    },\n    offlinePay(item) {\n      // setOfflinePay({ order_id: item.order_id }).then(\n      //   res => {\n      //     this.$dialog.success(res.message);\n      //     this.init();\n      //   },\n      //   error => {\n      //     this.$dialog.error(error.message);\n      //   }\n      // );\n    }\n  }\n};\n", null]}