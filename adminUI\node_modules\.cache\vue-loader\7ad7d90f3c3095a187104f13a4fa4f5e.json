{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue?vue&type=template&id=368e8bb7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\couponList\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox\"},[_c('div',{staticClass:\"header clearfix\"},[_c('div',{staticClass:\"container\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"}},[_c('el-form-item',{attrs:{\"label\":\"优惠卷名称：\"}},[_c('el-input',{staticClass:\"selWidth\",attrs:{\"placeholder\":\"请输入优惠券名称\",\"size\":\"small\"},model:{value:(_vm.tableFrom.keywords),callback:function ($$v) {_vm.$set(_vm.tableFrom, \"keywords\", $$v)},expression:\"tableFrom.keywords\"}},[_c('el-button',{attrs:{\"slot\":\"append\",\"icon\":\"el-icon-search\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.getList(1)}},slot:\"append\"})],1)],1)],1)],1)]),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],ref:\"table\",staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData.data,\"size\":\"mini\",\"max-height\":\"400\",\"tooltip-effect\":\"dark\",\"highlight-current-row\":\"\"},on:{\"selection-change\":_vm.handleSelectionChange}},[(_vm.handle==='wu')?_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}):_vm._e(),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"id\",\"label\":\"ID\",\"min-width\":\"50\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":\"优惠券名称\",\"min-width\":\"90\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"money\",\"label\":\"优惠券面值\",\"min-width\":\"90\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"minPrice\",\"label\":\"最低消费额\",\"min-width\":\"90\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(scope.row.minPrice===0?'不限制':scope.row.minPrice))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"有效期限\",\"min-width\":\"190\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(scope.row.isFixedTime===1?scope.row.useStartTime+' 一 '+scope.row.useEndTime:'不限制'))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"剩余数量\",\"min-width\":\"90\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(!scope.row.isLimited ? '不限量' : scope.row.lastTotal))])]}}])}),_vm._v(\" \"),(_vm.handle==='send')?_c('el-table-column',{attrs:{\"label\":\"操作\",\"min-width\":\"120\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:coupon:user:receive']),expression:\"['admin:coupon:user:receive']\"}],staticClass:\"mr10\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.sendGrant(scope.row.id)}}},[_vm._v(\"发送\")])]}}],null,false,458370523)}):_vm._e()],1),_vm._v(\" \"),_c('div',{staticClass:\"block mb20\"},[_c('el-pagination',{attrs:{\"page-sizes\":[10, 20, 30, 40],\"page-size\":_vm.tableFrom.limit,\"current-page\":_vm.tableFrom.page,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.tableData.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.pageChange}})],1),_vm._v(\" \"),(_vm.handle==='wu')?_c('div',[_c('el-button',{staticClass:\"fr\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":_vm.ok}},[_vm._v(\"确定\")])],1):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}