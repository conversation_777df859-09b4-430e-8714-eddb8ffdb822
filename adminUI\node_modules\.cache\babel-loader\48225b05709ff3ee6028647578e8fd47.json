{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\main.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\main.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.array.copy-within\";\nimport \"core-js/modules/es6.array.fill\";\nimport \"core-js/modules/es6.array.find\";\nimport \"core-js/modules/es6.array.find-index\";\nimport \"core-js/modules/es6.array.from\";\nimport \"core-js/modules/es7.array.includes\";\nimport \"core-js/modules/es6.array.iterator\";\nimport \"core-js/modules/es6.array.of\";\nimport \"core-js/modules/es6.array.sort\";\nimport \"core-js/modules/es6.array.species\";\nimport \"core-js/modules/es6.date.to-primitive\";\nimport \"core-js/modules/es6.function.has-instance\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/es6.map\";\nimport \"core-js/modules/es6.math.acosh\";\nimport \"core-js/modules/es6.math.asinh\";\nimport \"core-js/modules/es6.math.atanh\";\nimport \"core-js/modules/es6.math.cbrt\";\nimport \"core-js/modules/es6.math.clz32\";\nimport \"core-js/modules/es6.math.cosh\";\nimport \"core-js/modules/es6.math.expm1\";\nimport \"core-js/modules/es6.math.fround\";\nimport \"core-js/modules/es6.math.hypot\";\nimport \"core-js/modules/es6.math.imul\";\nimport \"core-js/modules/es6.math.log1p\";\nimport \"core-js/modules/es6.math.log10\";\nimport \"core-js/modules/es6.math.log2\";\nimport \"core-js/modules/es6.math.sign\";\nimport \"core-js/modules/es6.math.sinh\";\nimport \"core-js/modules/es6.math.tanh\";\nimport \"core-js/modules/es6.math.trunc\";\nimport \"core-js/modules/es6.number.constructor\";\nimport \"core-js/modules/es6.number.epsilon\";\nimport \"core-js/modules/es6.number.is-finite\";\nimport \"core-js/modules/es6.number.is-integer\";\nimport \"core-js/modules/es6.number.is-nan\";\nimport \"core-js/modules/es6.number.is-safe-integer\";\nimport \"core-js/modules/es6.number.max-safe-integer\";\nimport \"core-js/modules/es6.number.min-safe-integer\";\nimport \"core-js/modules/es6.number.parse-float\";\nimport \"core-js/modules/es6.number.parse-int\";\nimport \"core-js/modules/es6.object.assign\";\nimport \"core-js/modules/es7.object.define-getter\";\nimport \"core-js/modules/es7.object.define-setter\";\nimport \"core-js/modules/es7.object.entries\";\nimport \"core-js/modules/es6.object.freeze\";\nimport \"core-js/modules/es6.object.get-own-property-descriptor\";\nimport \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.object.get-own-property-names\";\nimport \"core-js/modules/es6.object.get-prototype-of\";\nimport \"core-js/modules/es7.object.lookup-getter\";\nimport \"core-js/modules/es7.object.lookup-setter\";\nimport \"core-js/modules/es6.object.prevent-extensions\";\nimport \"core-js/modules/es6.object.is\";\nimport \"core-js/modules/es6.object.is-frozen\";\nimport \"core-js/modules/es6.object.is-sealed\";\nimport \"core-js/modules/es6.object.is-extensible\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/es6.object.seal\";\nimport \"core-js/modules/es6.object.set-prototype-of\";\nimport \"core-js/modules/es7.object.values\";\nimport \"core-js/modules/es6.promise\";\nimport \"core-js/modules/es7.promise.finally\";\nimport \"core-js/modules/es6.reflect.apply\";\nimport \"core-js/modules/es6.reflect.construct\";\nimport \"core-js/modules/es6.reflect.define-property\";\nimport \"core-js/modules/es6.reflect.delete-property\";\nimport \"core-js/modules/es6.reflect.get\";\nimport \"core-js/modules/es6.reflect.get-own-property-descriptor\";\nimport \"core-js/modules/es6.reflect.get-prototype-of\";\nimport \"core-js/modules/es6.reflect.has\";\nimport \"core-js/modules/es6.reflect.is-extensible\";\nimport \"core-js/modules/es6.reflect.own-keys\";\nimport \"core-js/modules/es6.reflect.prevent-extensions\";\nimport \"core-js/modules/es6.reflect.set\";\nimport \"core-js/modules/es6.reflect.set-prototype-of\";\nimport \"core-js/modules/es6.regexp.constructor\";\nimport \"core-js/modules/es6.regexp.flags\";\nimport \"core-js/modules/es6.regexp.match\";\nimport \"core-js/modules/es6.regexp.replace\";\nimport \"core-js/modules/es6.regexp.split\";\nimport \"core-js/modules/es6.regexp.search\";\nimport \"core-js/modules/es6.regexp.to-string\";\nimport \"core-js/modules/es6.set\";\nimport \"core-js/modules/es6.symbol\";\nimport \"core-js/modules/es7.symbol.async-iterator\";\nimport \"core-js/modules/es6.string.anchor\";\nimport \"core-js/modules/es6.string.big\";\nimport \"core-js/modules/es6.string.blink\";\nimport \"core-js/modules/es6.string.bold\";\nimport \"core-js/modules/es6.string.code-point-at\";\nimport \"core-js/modules/es6.string.ends-with\";\nimport \"core-js/modules/es6.string.fixed\";\nimport \"core-js/modules/es6.string.fontcolor\";\nimport \"core-js/modules/es6.string.fontsize\";\nimport \"core-js/modules/es6.string.from-code-point\";\nimport \"core-js/modules/es6.string.includes\";\nimport \"core-js/modules/es6.string.italics\";\nimport \"core-js/modules/es6.string.iterator\";\nimport \"core-js/modules/es6.string.link\";\nimport \"core-js/modules/es7.string.pad-start\";\nimport \"core-js/modules/es7.string.pad-end\";\nimport \"core-js/modules/es6.string.raw\";\nimport \"core-js/modules/es6.string.repeat\";\nimport \"core-js/modules/es6.string.small\";\nimport \"core-js/modules/es6.string.starts-with\";\nimport \"core-js/modules/es6.string.strike\";\nimport \"core-js/modules/es6.string.sub\";\nimport \"core-js/modules/es6.string.sup\";\nimport \"core-js/modules/es6.typed.array-buffer\";\nimport \"core-js/modules/es6.typed.int8-array\";\nimport \"core-js/modules/es6.typed.uint8-array\";\nimport \"core-js/modules/es6.typed.uint8-clamped-array\";\nimport \"core-js/modules/es6.typed.int16-array\";\nimport \"core-js/modules/es6.typed.uint16-array\";\nimport \"core-js/modules/es6.typed.int32-array\";\nimport \"core-js/modules/es6.typed.uint32-array\";\nimport \"core-js/modules/es6.typed.float32-array\";\nimport \"core-js/modules/es6.typed.float64-array\";\nimport \"core-js/modules/es6.weak-map\";\nimport \"core-js/modules/es6.weak-set\";\nimport \"core-js/modules/web.timers\";\nimport \"core-js/modules/web.immediate\";\nimport \"core-js/modules/web.dom.iterable\";\nimport \"regenerator-runtime/runtime\";\nimport Vue from 'vue';\n// import 'babel-polyfill'\nimport Cookies from 'js-cookie';\nimport 'normalize.css/normalize.css'; // a modern alternative to CSS resets\nimport Element from 'element-ui';\nimport './styles/element-variables.scss';\nimport '@/styles/index.scss'; // global css\nimport \"@/assets/iconfont/iconfont\";\nimport \"@/assets/iconfont/iconfont.css\";\nimport VueAwesomeSwiper from 'vue-awesome-swiper';\nimport 'swiper/dist/css/swiper.css';\nimport \"vue-ydui/dist/ydui.base.css\";\nimport { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from \"@/utils/parsing\";\n// 懒加载\nimport VueLazyload from 'vue-lazyload';\nVue.config.devtools = true;\nimport App from './App';\nimport store from './store';\nimport router from './router';\nimport attrFrom from './components/attrFrom';\nimport uploadPicture from './components/uploadPicture/uploadFrom';\nimport goodListFrom from './components/goodList/goodListFrom';\nimport couponFrom from './components/couponList/couponFrom';\nimport articleFrom from './components/articleList/articleFrom';\nimport UploadIndex from '@/components/uploadPicture/index.vue';\nimport UploadFile from '@/components/Upload/uploadFile.vue';\n// import VueUeditorWrap from 'vue-ueditor-wrap'\nimport iconFrom from './components/iconFrom';\nimport TimeSelect from '@/components/TimeSelect';\nimport dialog from \"@/libs/dialog\";\nimport scroll from \"@/libs/loading\";\nimport schema from \"async-validator\";\n// 切勿更改 此组件为表单生成中使用的图片上传组件\nimport SelfUpload from '@/components/uploadPicture/forGenrator/index.vue';\nimport util from '@/utils/utils';\nimport modalAttr from '@/libs/modal-attr';\nimport modalIcon from '@/libs/modal-icon';\nimport { modalSure } from '@/libs/public';\nimport timeOptions from \"@/libs/timeOptions\";\nimport { loadScriptQueue } from '@/components/FormGenerator/utils/loadScript';\nimport './icons'; // icon\nimport './permission'; // permission control\nimport './utils/error-log'; // error integralLog\nimport * as filters from './filters'; // global filters\nimport { parseQuery } from \"@/utils\";\nimport * as Auth from '@/libs/wechat';\nimport * as constants from '@/utils/constants.js';\nimport * as selfUtil from '@/utils/ZBKJIutil.js';\nimport SettingMer from \"@/utils/settingMer\";\nimport plugins from './plugins';\nimport directive from './directive'; //directive\nimport i18n from './i18n';\nVue.use(VueLazyload, {\n  preLoad: 1.3,\n  error: require('./assets/imgs/no.png'),\n  loading: require('./assets/imgs/moren.jpg'),\n  attempt: 1,\n  listenEvents: ['scroll', 'wheel', 'mousewheel', 'resize', 'animationend', 'transitionend', 'touchmove']\n});\nVue.use(uploadPicture);\nVue.use(goodListFrom);\nVue.use(couponFrom);\nVue.use(articleFrom);\nVue.use(VueAwesomeSwiper);\nVue.use(plugins);\nVue.use(directive);\nVue.component('attrFrom', attrFrom);\nVue.component('UploadIndex', UploadIndex);\nVue.component('SelfUpload', SelfUpload);\nVue.component('iconFrom', iconFrom);\nVue.component('uploadFile', UploadFile);\nVue.component('timeSelect', TimeSelect);\nVue.prototype.$modalSure = modalSure;\nVue.prototype.$modalAttr = modalAttr;\nVue.prototype.$modalIcon = modalIcon;\nVue.prototype.$dialog = dialog;\nVue.prototype.$scroll = scroll;\nVue.prototype.$wechat = Auth;\nVue.prototype.$util = util;\nVue.prototype.$constants = constants;\nVue.prototype.$selfUtil = selfUtil;\nVue.prototype.$timeOptions = timeOptions;\nVue.prototype.$validator = function (rule) {\n  return new schema(rule);\n};\nVue.prototype.handleTree = handleTree;\nVue.prototype.parseTime = parseTime;\nVue.prototype.resetForm = resetForm;\nvar cookieName = \"VCONSOLE\";\nvar query = parseQuery();\nvar urlSpread = query[\"spread\"];\nvar vconsole = query[cookieName.toLowerCase()];\nvar md5Crmeb = \"b14d1e9baeced9bb7525ab19ee35f2d2\"; //CRMEB MD5 加密开启vconsole模式\nvar md5UnCrmeb = \"3dca2162c4e101b7656793a1af20295c\"; //UN_CREMB MD5 加密关闭vconsole模式\n\nif (vconsole !== undefined) {\n  if (vconsole === md5UnCrmeb && Cookies.has(cookieName)) Cookies.remove(cookieName);\n} else vconsole = Cookies.get(cookieName);\nif (vconsole !== undefined && vconsole === md5Crmeb) {\n  Cookies.set(cookieName, md5Crmeb, 3600);\n  var module = function module() {\n    return import(\"vconsole\");\n  };\n  module().then(function (Module) {\n    new Module.default();\n  });\n}\n// 自定义实现String 类型的replaceAll方法\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, \"gm\"), s2);\n};\nVue.use(Element, {\n  size: Cookies.get('size') || 'mini' // set element-ui default size\n});\n\n// register global utility filters\nObject.keys(filters).forEach(function (key) {\n  Vue.filter(key, filters[key]);\n});\nVue.config.productionTip = false;\nvar $previewApp = document.getElementById('previewApp');\nvar childAttrs = {\n  file: '',\n  dialog: ' width=\"600px\" class=\"dialog-width\" v-if=\"visible\" :visible.sync=\"visible\" :modal-append-to-body=\"false\" '\n};\nwindow.addEventListener('message', init, false);\nfunction buildLinks(links) {\n  var strs = '';\n  links.forEach(function (url) {\n    strs += \"<link href=\\\"\".concat(url, \"\\\" rel=\\\"stylesheet\\\">\");\n  });\n  return strs;\n}\nfunction init(event) {\n  if (event.data.type === 'refreshFrame') {\n    var code = event.data.data;\n    var attrs = childAttrs[code.generateConf.type];\n    var links = '';\n    if (Array.isArray(code.links) && code.links.length > 0) {\n      links = buildLinks(code.links);\n    }\n    $previewApp.innerHTML = \"\".concat(links, \"<style>\").concat(code.css, \"</style><div id=\\\"app\\\"></div>\");\n    if (Array.isArray(code.scripts) && code.scripts.length > 0) {\n      loadScriptQueue(code.scripts, function () {\n        newVue(attrs, code.js, code.html);\n      });\n    } else {\n      newVue(attrs, code.js, code.html);\n    }\n  }\n}\nfunction newVue(attrs, main, html) {\n  // eslint-disable-next-line no-eval\n  main = eval(\"(\".concat(main, \")\"));\n  main.template = \"<div>\".concat(html, \"</div>\");\n  new Vue({\n    components: {\n      child: main\n    },\n    data: function data() {\n      return {\n        visible: true\n      };\n    },\n    template: \"<div><child \".concat(attrs, \"/></div>\")\n  }).$mount('#app');\n}\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, \"gm\"), s2);\n};\n\n// 添加crmeb chat 统计\nvar __s = document.createElement('script');\n__s.src = \"\".concat(SettingMer.apiBaseURL, \"/public/jsconfig/getcrmebchatconfig\");\ndocument.head.appendChild(__s);\nvar _hmt = _hmt || [];\n(function () {\n  var hm = document.createElement(\"script\");\n  hm.src = \"https://cdn.oss.9gt.net/js/es.js?version=JAVA-KY-v1.3.4\";\n  var s = document.getElementsByTagName(\"script\")[0];\n  s.parentNode.insertBefore(hm, s);\n})();\nnew Vue({\n  el: '#app',\n  router: router,\n  store: store,\n  i18n: i18n,\n  render: function render(h) {\n    return h(App);\n  }\n});", null]}