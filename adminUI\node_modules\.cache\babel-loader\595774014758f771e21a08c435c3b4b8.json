{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\order\\orderSend.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { orderSendApi, sheetInfoApi } from '@/api/order';\nimport { expressAllApi, exportTempApi } from '@/api/sms';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport { Debounce } from '@/utils/validate';\nvar validatePhone = function validatePhone(rule, value, callback) {\n  if (!value) {\n    return callback(new Error('请填写手机号'));\n  } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n    callback(new Error('手机号格式不正确!'));\n  } else {\n    callback();\n  }\n};\nexport default {\n  name: 'orderSend',\n  props: {\n    orderId: String\n  },\n  data: function data() {\n    return {\n      formItem: {\n        type: '1',\n        expressRecordType: '1',\n        expressId: '',\n        expressCode: '',\n        deliveryName: '',\n        deliveryTel: '',\n        // expressName: '',\n        expressNumber: '',\n        expressTempId: '',\n        toAddr: '',\n        toName: '',\n        toTel: '',\n        orderNo: ''\n      },\n      modals: false,\n      express: [],\n      exportTempList: [],\n      tempImg: '',\n      rules: {\n        toName: [{\n          required: true,\n          message: '请输寄件人姓名',\n          trigger: 'blur'\n        }],\n        toTel: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        toAddr: [{\n          required: true,\n          message: '请输入寄件人地址',\n          trigger: 'blur'\n        }],\n        expressCode: [{\n          required: true,\n          message: '请选择快递公司',\n          trigger: 'change'\n        }],\n        expressNumber: [{\n          required: true,\n          message: '请输入快递单号',\n          trigger: 'blur'\n        }],\n        expressTempId: [{\n          required: true,\n          message: '请选择电子面单',\n          trigger: 'change'\n        }],\n        deliveryName: [{\n          required: true,\n          message: '请输入送货人姓名',\n          trigger: 'blur'\n        }],\n        deliveryTel: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }]\n      },\n      expressType: 'normal'\n    };\n  },\n  mounted: function mounted() {},\n  methods: {\n    checkPermi: checkPermi,\n    // 默认信息\n    sheetInfo: function sheetInfo() {\n      var _this = this;\n      sheetInfoApi().then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                _this.formItem.toAddr = res.exportToAddress || '';\n                _this.formItem.toName = res.exportToName || '';\n                _this.formItem.toTel = res.exportToTel || '';\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 快递公司选择\n    onChangeExport: function onChangeExport(val) {\n      this.formItem.expressTempId = '';\n      if (this.formItem.expressRecordType === '2') this.exportTemp(val);\n    },\n    // 电子面单模板\n    exportTemp: function exportTemp(val) {\n      var _this2 = this;\n      exportTempApi({\n        com: val\n      }).then(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                _this2.exportTempList = res.data.data || [];\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    },\n    onChangeImg: function onChangeImg(item) {\n      var _this3 = this;\n      this.exportTempList.map(function (i) {\n        if (i.temp_id === item) _this3.tempImg = i.pic;\n      });\n    },\n    changeRadioType: function changeRadioType() {\n      this.formItem.expressId = '';\n      this.formItem.expressCode = '';\n    },\n    changeRadio: function changeRadio(o) {\n      if (o == 2) {\n        this.expressType = 'elec';\n      } else {\n        this.expressType = 'normal';\n      }\n      this.formItem.expressId = '';\n      this.formItem.expressCode = '';\n      this.getList();\n    },\n    // 物流公司列表\n    getList: function getList() {\n      var _this4 = this;\n      expressAllApi({\n        type: this.expressType\n      }).then(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n          return _regenerator().w(function (_context3) {\n            while (1) switch (_context3.n) {\n              case 0:\n                _this4.express = res;\n              case 1:\n                return _context3.a(2);\n            }\n          }, _callee3);\n        }));\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    // 提交\n    putSend: Debounce(function (name) {\n      var _this5 = this;\n      this.formItem.orderNo = this.orderId;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          orderSendApi(_this5.formItem).then(function (async) {\n            _this5.$message.success('发送货成功');\n            _this5.modals = false;\n            _this5.$refs[name].resetFields();\n            _this5.$emit('submitFail');\n          });\n        } else {\n          _this5.$message.error('请填写信息');\n        }\n      });\n    }),\n    handleClose: function handleClose() {\n      this.cancel('formItem');\n    },\n    cancel: function cancel(name) {\n      this.modals = false;\n      this.$refs[name].resetFields();\n      this.formItem.type = '1';\n      this.formItem.expressRecordType = '1';\n    }\n  }\n};", null]}