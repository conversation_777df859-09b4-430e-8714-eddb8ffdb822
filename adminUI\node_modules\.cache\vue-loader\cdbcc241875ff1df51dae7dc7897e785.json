{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\userList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\components\\userList\\index.vue", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userListApi } from '@/api/user'\nexport default {\n  name: 'UserList',\n  filters: {\n    saxFilter(status) {\n      const statusMap = {\n        0: '未知',\n        1: '男',\n        2: '女'\n      }\n      return statusMap[status]\n    },\n    statusFilter(status) {\n      const statusMap = {\n        'wechat': '微信用户',\n        'routine': '小程序用户'\n      }\n      return statusMap[status]\n    }\n  },\n  data() {\n    return {\n      templateRadio: 0,\n      loading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 10,\n        keywords: ''\n      }\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    getTemplateRow(idx, row) {\n       this.$emit('getTemplateRow', row);\n    },\n    // 列表\n    getList() {\n      this.loading = true\n      userListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.loading = false\n      }).catch(res => {\n        this.$message.error(res.message)\n        this.loading = false\n      })\n    },\n    search(){\n       this.loading = true\n      userListApi({keywords:this.tableFrom.keywords}).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.loading = false\n      }).catch(res => {\n        this.$message.error(res.message)\n        this.loading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    }\n  }\n}\n", null]}