{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue?vue&type=template&id=102b558c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue", "mtime": 1754382949118}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"container mt-1\"},[_c('el-form',{attrs:{\"inline\":\"\",\"size\":\"small\"},model:{value:(_vm.searchFrom),callback:function ($$v) {_vm.searchFrom=$$v},expression:\"searchFrom\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('chainTransferRecord.keyword') + '：'}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('chainTransferRecord.enterProductName'),\"clearable\":\"\"},model:{value:(_vm.searchFrom.keyword),callback:function ($$v) {_vm.$set(_vm.searchFrom, \"keyword\", $$v)},expression:\"searchFrom.keyword\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('chainTransferRecord.brandName') + '：'}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('common.all'),\"clearable\":\"\"},model:{value:(_vm.searchFrom.brandCode),callback:function ($$v) {_vm.$set(_vm.searchFrom, \"brandCode\", $$v)},expression:\"searchFrom.brandCode\"}},_vm._l((_vm.brandList),function(item){return _c('el-option',{key:item.code,attrs:{\"label\":item.name,\"value\":item.code}})}),1)],1)],1)],1),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"primary\"},on:{\"click\":function($event){return _vm.getList(1)}}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"chainTransferRecord.query\"))+\"\\n    \")]),_vm._v(\" \"),_c('el-button',{staticClass:\"mr10\",attrs:{\"size\":\"small\",\"type\":\"\"},on:{\"click\":_vm.resetForm}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\"chainTransferRecord.reset\"))+\"\\n    \")])],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('el-button',{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:(['admin:financialCenter:request:upload']),expression:\"['admin:financialCenter:request:upload']\"}],attrs:{\"type\":\"primary\",\"size\":\"small\"}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t(\"chainTransferRecord.exportExcel\"))+\"\\n      \")])],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.tableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }}},[_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.serialNumber'),\"type\":\"index\",\"width\":\"110\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.nickname')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.userAccount)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.tiktokId')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.tiktokUid)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.originalLink')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.originUrl)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.rebateLink')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.shareUrl)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.operationTime')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.operateTime)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.linkSource')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.channel)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.productId')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.productId)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.productName')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(scope.row.productName)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.productPrice')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm._f(\"filterEmpty\")(_vm.formatAmount(scope.row.productPrice))))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('chainTransferRecord.productCashbackRate')},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(_vm._s(_vm.formatRate(scope.row.productCashbackRate)))]}}])})],1),_vm._v(\" \"),_c('el-pagination',{staticClass:\"mt20\",attrs:{\"current-page\":_vm.searchFrom.page,\"page-sizes\":[20, 40, 60, 100],\"page-size\":_vm.searchFrom.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.searchFrom.total},on:{\"size-change\":_vm.sizeChange,\"current-change\":_vm.pageChange}})],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}