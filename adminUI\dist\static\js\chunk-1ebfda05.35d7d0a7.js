(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ebfda05"],{"0f53":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"divBox"},[i("el-card",{staticClass:"box-card"},[i("el-form",{attrs:{inline:"",size:"small"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",[i("el-input",{staticClass:"selWidth",attrs:{placeholder:e.$t("admin.system.role.roleName"),clearable:""},model:{value:e.listPram.roleName,callback:function(t){e.$set(e.listPram,"roleName",t)},expression:"listPram.roleName"}})],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{size:"mini",type:"primary"},nativeOn:{click:function(t){return e.handleGetRoleList(t)}}},[e._v(e._s(e.$t("common.query")))]),e._v(" "),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("common.reset")))])],1)],1),e._v(" "),i("el-form",{attrs:{inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:role:save","admin:system:menu:cache:tree"],expression:"[\n            'admin:system:role:save',\n            'admin:system:menu:cache:tree'\n          ]"}],attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handlerOpenEdit(0)}}},[e._v(e._s(e.$t("admin.system.role.addRole")))])],1)],1),e._v(" "),i("el-table",{attrs:{data:e.listData.list,size:"mini","header-cell-style":{fontWeight:"bold",background:"#f8f8f9",color:"#515a6e",height:"40px"}}},[i("el-table-column",{attrs:{label:e.$t("admin.system.role.roleId"),prop:"id",width:"120"}}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("admin.system.role.roleName"),prop:"roleName","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("common.status"),prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return e.checkPermi(["admin:system:role:update:status"])?[i("el-switch",{staticStyle:{width:"40px"},attrs:{"active-value":!0,"inactive-value":!1},on:{change:function(i){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(i){e.$set(t.row,"status",i)},expression:"scope.row.status"}})]:void 0}}],null,!0)}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("admin.system.role.createTime"),prop:"createTime","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("admin.system.role.updateTime"),prop:"updateTime","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:e.$t("admin.system.role.operation"),"min-width":"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:role:info"],expression:"['admin:system:role:info']"}],attrs:{size:"small",type:"text"},on:{click:function(i){return e.handlerOpenEdit(1,t.row)}}},[e._v(e._s(e.$t("admin.system.role.editRole")))]),e._v(" "),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:role:delete"],expression:"['admin:system:role:delete']"}],attrs:{size:"small",type:"text"},on:{click:function(i){return e.handlerOpenDel(t.row)}}},[e._v(e._s(e.$t("admin.system.role.deleteRole")))])]}}])})],1),e._v(" "),i("el-pagination",{attrs:{"current-page":e.listPram.page,"page-sizes":e.constants.page.limit,layout:e.constants.page.layout,total:e.listData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),i("el-dialog",{attrs:{visible:e.editDialogConfig.visible,title:0===e.editDialogConfig.isCreate?e.$t("admin.system.role.createIdentity"):e.$t("admin.system.role.editIdentity"),"destroy-on-close":"","close-on-click-modal":!1,width:"500px"},on:{"update:visible":function(t){return e.$set(e.editDialogConfig,"visible",t)}}},[e.editDialogConfig.visible?i("edit",{ref:"editForm",attrs:{"is-create":e.editDialogConfig.isCreate,"edit-data":e.editDialogConfig.editData},on:{hideEditDialog:e.hideEditDialog}}):e._e()],1)],1)},a=[],s=i("cc5e"),l=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"pram",attrs:{model:e.pram,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",{attrs:{label:e.$t("admin.system.role.roleForm.roleNameLabel"),prop:"roleName",rules:[{required:!0,message:e.$t("common.enter")+e.$t("admin.system.role.roleForm.roleNameLabel"),trigger:["blur","change"]}]}},[i("el-input",{attrs:{placeholder:e.$t("admin.system.role.roleForm.roleNamePlaceholder")},model:{value:e.pram.roleName,callback:function(t){e.$set(e.pram,"roleName",t)},expression:"pram.roleName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("admin.system.role.roleForm.statusLabel")}},[i("el-switch",{attrs:{"active-value":!0,"inactive-value":!1},model:{value:e.pram.status,callback:function(t){e.$set(e.pram,"status",t)},expression:"pram.status"}})],1),e._v(" "),i("el-form-item",{attrs:{label:e.$t("admin.system.role.roleForm.menuPermissions")}},[i("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeExpand(t,"menu")}},model:{value:e.menuExpand,callback:function(t){e.menuExpand=t},expression:"menuExpand"}},[e._v(e._s(e.$t("admin.system.role.roleForm.expandCollapse")))]),e._v(" "),i("el-checkbox",{on:{change:function(t){return e.handleCheckedTreeConnect(t,"menu")}},model:{value:e.menuCheckStrictly,callback:function(t){e.menuCheckStrictly=t},expression:"menuCheckStrictly"}},[e._v(e._s(e.$t("admin.system.role.roleForm.parentChildLink")))]),e._v(" "),i("el-tree",{ref:"menu",staticClass:"tree-border",attrs:{data:e.menuOptions,"show-checkbox":"","node-key":"id","check-strictly":!e.menuCheckStrictly,"empty-text":e.$t("common.fetchDataFailed"),props:e.defaultProps}})],1),e._v(" "),i("el-form-item",[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["admin:system:role:update","admin:system:role:save"],expression:"['admin:system:role:update', 'admin:system:role:save']"}],attrs:{type:"primary"},on:{click:function(t){return e.handlerSubmit("pram")}}},[e._v(e._s(0===e.isCreate?e.$t("admin.system.role.roleForm.confirm"):e.$t("admin.system.role.roleForm.update")))]),e._v(" "),i("el-button",{on:{click:e.close}},[e._v(e._s(e.$t("admin.system.role.roleForm.cancel")))])],1)],1)],1)},r=[],o=i("61f7"),m={name:"roleEdit",props:{isCreate:{type:Number,required:!0},editData:{type:Object,default:null}},data:function(){return{pram:{roleName:null,rules:"",status:null,id:null},menuExpand:!1,menuNodeAll:!1,menuOptions:[],menuCheckStrictly:!0,currentNodeId:[],defaultProps:{children:"childList",label:"name"},menuIds:[]}},mounted:function(){this.initEditData(),this.getCacheMenu()},methods:{close:function(){this.$emit("hideEditDialog")},initEditData:function(){var e=this;if(1===this.isCreate){var t=this.editData,i=t.roleName,n=t.status,a=t.id;this.pram.roleName=i,this.pram.status=n,this.pram.id=a;var l=this.$loading({lock:!0,text:"Loading"});s["c"](a).then((function(t){e.menuOptions=t.menuList,e.checkDisabled(e.menuOptions),l.close(),e.getTreeId(t.menuList),e.$nextTick((function(){e.menuIds.forEach((function(t,i){var n=e.$refs.menu.getNode(t);n.isLeaf&&e.$refs.menu.setChecked(n,!0)}))}))}))}},handlerSubmit:Object(o["a"])((function(e){var t=this;this.$refs[e].validate((function(e){if(e){var i=t.getMenuAllCheckedKeys().toString();t.pram.rules=i,0===t.isCreate?t.handlerSave():t.handlerEdit()}}))})),handlerSave:function(){var e=this;s["a"](this.pram).then((function(t){e.$message.success(e.$t("admin.system.role.createIdentity")+e.$t("common.operationSuccess")),e.$emit("hideEditDialog")}))},handlerEdit:function(){var e=this;s["f"](this.pram).then((function(t){e.$message.success(e.$t("admin.system.role.editIdentity")+e.$t("common.operationSuccess")),e.$emit("hideEditDialog")}))},rulesSelect:function(e){this.pram.rules=e},handleCheckedTreeExpand:function(e,t){if("menu"==t)for(var i=this.menuOptions,n=0;n<i.length;n++)this.$refs.menu.store.nodesMap[i[n].id].expanded=e},handleCheckedTreeNodeAll:function(e,t){"menu"==t&&this.$refs.menu.setCheckedNodes(e?this.menuOptions:[])},handleCheckedTreeConnect:function(e,t){"menu"==t&&(this.menuCheckStrictly=!!e)},getMenuAllCheckedKeys:function(){var e=this.$refs.menu.getCheckedKeys(),t=this.$refs.menu.getHalfCheckedKeys();return e.unshift.apply(e,t),e},getCacheMenu:function(){var e=this;if(0===this.isCreate){var t=this.$loading({lock:!0,text:"Loading"});s["e"]().then((function(i){e.menuOptions=i,e.checkDisabled(e.menuOptions),t.close()}))}},getTreeId:function(e){for(var t in e)e[t].checked&&this.menuIds.push(e[t].id),e[t].childList&&this.getTreeId(e[t].childList)},checkDisabled:function(e){var t=this;e.forEach((function(e){280!==e.id&&294!==e.id&&344!==e.id||(e.disabled=!0,e.childList.forEach((function(e){e.disabled=!0,t.$nextTick((function(){var i=t.$refs.menu.getNode(e.id);i.isLeaf&&t.$refs.menu.setChecked(i,!0)}))})))}))}}},u=m,c=i("2877"),d=Object(c["a"])(u,l,r,!1,null,"5c83c94e",null),h=d.exports,f=i("e350"),p={components:{edit:h},data:function(){return{constants:this.$constants,listData:{list:[]},listPram:{createTime:null,updateTime:null,level:null,page:1,limit:this.$constants.page.limit[0],roleName:null,rules:null,status:null},menuList:[],editDialogConfig:{visible:!1,isCreate:0,editData:{}}}},mounted:function(){this.handleGetRoleList()},methods:{checkPermi:f["a"],handlerOpenDel:function(e){var t=this;this.$confirm(this.$t("admin.system.role.confirmDelete")).then((function(){s["b"](e).then((function(e){t.$message.success(t.$t("admin.system.role.deleteSuccess")),t.handleGetRoleList()}))}))},handleGetRoleList:function(){var e=this;s["d"](this.listPram).catch((function(){e.$message.error(e.$t("common.fetchDataFailed"))}))},handlerOpenEdit:function(e,t){this.editDialogConfig.editData=1===e?t:{},this.editDialogConfig.isCreate=e,this.editDialogConfig.visible=!0},hideEditDialog:function(){this.editDialogConfig.visible=!1,this.handleGetRoleList()},handleSizeChange:function(e){this.listPram.limit=e,this.handleGetRoleList(this.listPram)},handleCurrentChange:function(e){this.listPram.page=e,this.handleGetRoleList(this.listPram)},handleStatusChange:function(e){var t=this;s["g"](e).then((function(e){t.$message.success("更新状态成功"),t.handleGetRoleList()}))},resetQuery:function(){this.listPram.roleName="",this.handleGetRoleList()}}},v=p,g=Object(c["a"])(v,n,a,!1,null,"711b24ea",null);t["default"]=g.exports},cc5e:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return l})),i.d(t,"d",(function(){return r})),i.d(t,"f",(function(){return o})),i.d(t,"g",(function(){return m})),i.d(t,"e",(function(){return u}));var n=i("b775");function a(e){var t={level:e.level,roleName:e.roleName,status:e.status,rules:e.rules};return Object(n["a"])({url:"/admin/system/role/save",method:"POST",data:t})}function s(e){var t={id:e.id};return Object(n["a"])({url:"/admin/system/role/delete",method:"GET",params:t})}function l(e){return Object(n["a"])({url:"/admin/system/role/info/".concat(e),method:"GET"})}function r(e){var t={createTime:e.createTime,updateTime:e.updateTime,level:e.level,page:e.page,limit:e.limit,roleName:e.roleName,rules:e.rules,status:e.status};return Object(n["a"])({url:"/admin/system/role/list",method:"get",params:t})}function o(e){var t={id:e.id,roleName:e.roleName,rules:e.rules,status:e.status};return Object(n["a"])({url:"/admin/system/role/update",method:"post",params:{id:e.id},data:t})}function m(e){return Object(n["a"])({url:"/admin/system/role/updateStatus",method:"get",params:{id:e.id,status:e.status}})}function u(e){return Object(n["a"])({url:"/admin/system/menu/cache/tree",method:"get"})}}}]);