{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue?vue&type=template&id=1cbd0e4c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\user\\upgrade\\index.vue", "mtime": 1754269254569}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(_vm._s(_vm.$t('user.levelUpgrade.title')))])]),_vm._v(\" \"),_c('el-form',{staticClass:\"demo-form-inline\",attrs:{\"inline\":true,\"model\":_vm.searchForm}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.orderNo')}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('user.levelUpgrade.enterOrderNo'),\"clearable\":\"\"},model:{value:(_vm.searchForm.orderNo),callback:function ($$v) {_vm.$set(_vm.searchForm, \"orderNo\", $$v)},expression:\"searchForm.orderNo\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.orderStatus')}},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('user.levelUpgrade.selectStatus'),\"clearable\":\"\"},model:{value:(_vm.searchForm.orderStatus),callback:function ($$v) {_vm.$set(_vm.searchForm, \"orderStatus\", $$v)},expression:\"searchForm.orderStatus\"}},[_c('el-option',{attrs:{\"label\":_vm.$t('user.levelUpgrade.pending'),\"value\":0}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('user.levelUpgrade.paid'),\"value\":1}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('user.levelUpgrade.cancelled'),\"value\":2}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('user.levelUpgrade.refunded'),\"value\":3}})],1)],1),_vm._v(\" \"),_c('el-form-item',[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.getList}},[_vm._v(_vm._s(_vm.$t('user.center.query')))]),_vm._v(\" \"),_c('el-button',{on:{\"click\":_vm.resetSearch}},[_vm._v(_vm._s(_vm.$t('user.center.reset')))])],1)],1),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.listLoading),expression:\"listLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData,\"size\":\"mini\"}},[_c('el-table-column',{attrs:{\"prop\":\"orderNo\",\"label\":_vm.$t('user.levelUpgrade.orderNo'),\"min-width\":\"150\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"uid\",\"label\":_vm.$t('user.levelUpgrade.userId'),\"min-width\":\"80\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.levelUpgrade.upgradeInfo'),\"min-width\":\"150\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',[_vm._v(_vm._s(_vm.getLevelName(scope.row.fromLevelId))+\" → \"+_vm._s(_vm.getLevelName(scope.row.toLevelId)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"upgradePrice\",\"label\":_vm.$t('user.levelUpgrade.upgradeFee'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(\"Rp \"+_vm._s(scope.row.upgradePrice))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"paymentMethod\",\"label\":_vm.$t('user.levelUpgrade.paymentMethod'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',[_vm._v(_vm._s(_vm.getPaymentMethodName(scope.row.paymentMethod)))])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"orderStatus\",\"label\":_vm.$t('user.levelUpgrade.orderStatus'),\"min-width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":_vm.getStatusColor(scope.row.orderStatus)}},[_vm._v(\"\\n            \"+_vm._s(_vm.getStatusName(scope.row.orderStatus))+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"createTime\",\"label\":_vm.$t('user.levelUpgrade.createTime'),\"min-width\":\"150\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"payTime\",\"label\":_vm.$t('user.levelUpgrade.payTime'),\"min-width\":\"150\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('user.levelUpgrade.operation'),\"min-width\":\"120\",\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.orderStatus === 0)?_c('el-button',{staticClass:\"mr10\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.cancelOrder(scope.row.orderNo)}}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('user.levelUpgrade.cancelOrder'))+\"\\n          \")]):_vm._e(),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.viewDetail(scope.row)}}},[_vm._v(_vm._s(_vm.$t('user.levelUpgrade.viewDetail')))])]}}])})],1),_vm._v(\" \"),_c('div',{staticClass:\"block\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.searchForm.page,\"page-sizes\":[10, 20, 50, 100],\"page-size\":_vm.searchForm.limit,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":_vm.$t('user.levelUpgrade.orderDetail'),\"visible\":_vm.detailVisible,\"width\":\"600px\"},on:{\"update:visible\":function($event){_vm.detailVisible=$event}}},[(_vm.currentOrder)?_c('div',[_c('el-descriptions',{attrs:{\"column\":2,\"border\":\"\"}},[_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.orderNo')}},[_vm._v(_vm._s(_vm.currentOrder.orderNo))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.userId')}},[_vm._v(_vm._s(_vm.currentOrder.uid))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.fromLevel')}},[_vm._v(_vm._s(_vm.getLevelName(_vm.currentOrder.fromLevelId)))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.toLevel')}},[_vm._v(_vm._s(_vm.getLevelName(_vm.currentOrder.toLevelId)))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.upgradeFee')}},[_vm._v(\"Rp \"+_vm._s(_vm.currentOrder.upgradePrice))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.paymentMethod')}},[_vm._v(_vm._s(_vm.getPaymentMethodName(_vm.currentOrder.paymentMethod)))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.orderStatus')}},[_c('el-tag',{attrs:{\"type\":_vm.getStatusColor(_vm.currentOrder.orderStatus)}},[_vm._v(\"\\n            \"+_vm._s(_vm.getStatusName(_vm.currentOrder.orderStatus))+\"\\n          \")])],1),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.createTime')}},[_vm._v(_vm._s(_vm.currentOrder.createTime))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.payTime')}},[_vm._v(_vm._s(_vm.currentOrder.payTime || _vm.$t('user.levelUpgrade.unpaid')))]),_vm._v(\" \"),_c('el-descriptions-item',{attrs:{\"label\":_vm.$t('user.levelUpgrade.remark')}},[_vm._v(_vm._s(_vm.currentOrder.remark || _vm.$t('user.levelUpgrade.noRemark')))])],1)],1):_vm._e()])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}