import 'package:flutter/material.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';

/// 提现API测试页面
class WithdrawalTestScreen extends StatefulWidget {
  const WithdrawalTestScreen({super.key});

  @override
  State<WithdrawalTestScreen> createState() => _WithdrawalTestScreenState();
}

class _WithdrawalTestScreenState extends State<WithdrawalTestScreen> {
  Map<String, dynamic>? eligibilityResult;
  Map<String, dynamic>? limitInfo;
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("提现API测试"),
        backgroundColor: selectedTabColor,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: isLoading ? null : _checkEligibility,
              child: Text("检查提现资格"),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: isLoading ? null : _getLimitInfo,
              child: Text("获取提现限制信息"),
            ),
            SizedBox(height: 24),
            if (isLoading)
              Center(child: CircularProgressIndicator())
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (eligibilityResult != null) ...[
                        Text(
                          "提现资格检查结果:",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _formatJson(eligibilityResult!),
                            style: TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                        SizedBox(height: 16),
                      ],
                      if (limitInfo != null) ...[
                        Text(
                          "提现限制信息:",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _formatJson(limitInfo!),
                            style: TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkEligibility() async {
    setState(() {
      isLoading = true;
    });

    try {
      final result = await networkApiClient.checkWithdrawEligibility();
      setState(() {
        eligibilityResult = result;
      });
      makeToast("资格检查完成");
    } catch (e) {
      makeToast("检查失败: $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _getLimitInfo() async {
    setState(() {
      isLoading = true;
    });

    try {
      final result = await networkApiClient.getWithdrawLimitInfo();
      setState(() {
        limitInfo = result;
      });
      makeToast("限制信息获取完成");
    } catch (e) {
      makeToast("获取失败: $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  String _formatJson(Map<String, dynamic> json) {
    String result = "";
    json.forEach((key, value) {
      result += "$key: $value\n";
    });
    return result;
  }
}
