(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0604cde0"],{1861:function(e,t,i){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}var s,n,a,o,l;function c(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=y(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,s=function(){};return{s:s,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,a=!0,o=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){o=!0,n=e},f:function(){try{a||null==i.return||i.return()}finally{if(o)throw n}}}}function h(e){return d(e)||p(e)||y(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function d(e){if(Array.isArray(e))return v(e)}function f(e,t){return x(e)||g(e,t)||y(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e,t){if(e){if("string"==typeof e)return v(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=Array(t);i<t;i++)r[i]=e[i];return r}function g(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var r,s,n,a,o=[],l=!0,c=!1;try{if(n=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;l=!1}else for(;!(l=(r=n.call(i)).done)&&(o.push(r.value),o.length!==t);l=!0);}catch(e){c=!0,s=e}finally{try{if(!l&&null!=i.return&&(a=i.return(),Object(a)!==a))return}finally{if(c)throw s}}return o}}function x(e){if(Array.isArray(e))return e}function b(e,t,i){return t=E(t),P(e,T()?Reflect.construct(t,i||[],E(e).constructor):t.apply(e,i))}function P(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return k(e)}function k(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function T(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(T=function(){return!!e})()}function w(e,t,i,r){var s=S(E(1&r?e.prototype:e),t,i);return 2&r&&"function"==typeof s?function(e){return s.apply(i,e)}:s}function S(){return S="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var r=A(e,t);if(r){var s=Object.getOwnPropertyDescriptor(r,t);return s.get?s.get.call(arguments.length<3?e:i):s.value}},S.apply(null,arguments)}function A(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=E(e)););return e}function E(e){return E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},E(e)}function C(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&I(e,t)}function I(e,t){return I=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},I(e,t)}function N(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function O(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,D(r.key),r)}}function L(e,t,i){return t&&O(e.prototype,t),i&&O(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function D(e){var t=M(e,"string");return"symbol"==r(t)?t:t+""}function M(e,t){if("object"!=r(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=r(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function F(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function B(e,t){if(null==e)return{};var i={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;i[r]=e[r]}return i}Object.defineProperty(t,"__esModule",{value:!0});var _=L((function e(t,i,r){F(this,e),this.line=void 0,this.column=void 0,this.index=void 0,this.line=t,this.column=i,this.index=r})),j=L((function e(t,i){F(this,e),this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=t,this.end=i}));function R(e,t){var i=e.line,r=e.column,s=e.index;return new _(i,r+t,s+t)}var U="BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED",V={ImportMetaOutsideModule:{message:"import.meta may appear only with 'sourceType: \"module\"'",code:U},ImportOutsideModule:{message:"'import' and 'export' may appear only with 'sourceType: \"module\"'",code:U}},H={ArrayPattern:"array destructuring pattern",AssignmentExpression:"assignment expression",AssignmentPattern:"assignment expression",ArrowFunctionExpression:"arrow function expression",ConditionalExpression:"conditional expression",CatchClause:"catch clause",ForOfStatement:"for-of statement",ForInStatement:"for-in statement",ForStatement:"for-loop",FormalParameters:"function parameter list",Identifier:"identifier",ImportSpecifier:"import specifier",ImportDefaultSpecifier:"import default specifier",ImportNamespaceSpecifier:"import namespace specifier",ObjectPattern:"object destructuring pattern",ParenthesizedExpression:"parenthesized expression",RestElement:"rest element",UpdateExpression:{true:"prefix operation",false:"postfix operation"},VariableDeclarator:"variable declaration",YieldExpression:"yield expression"},z=function(e){return"UpdateExpression"===e.type?H.UpdateExpression["".concat(e.prefix)]:H[e.type]},q={AccessorIsGenerator:function(e){var t=e.kind;return"A ".concat(t,"ter cannot be a generator.")},ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitUsingNotInAsyncContext:"'await using' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",BadGetterArity:"A 'get' accessor must not have any formal parameters.",BadSetterArity:"A 'set' accessor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accessor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:function(e){var t=e.kind;return"Missing initializer in ".concat(t," declaration.")},DecoratorArgumentsOutsideParentheses:"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.",DecoratorsBeforeAfterExport:"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeferImportRequiresNamespace:'Only `import defer * as x from "./module"` is valid.',DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:function(e){var t=e.exportName;return"`".concat(t,"` has already been exported. Exported identifiers must be unique.")},DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:function(e){var t=e.localName,i=e.exportName;return"A string literal cannot be used as an exported binding without `from`.\n- Did you mean `export { '".concat(t,"' as '").concat(i,"' } from 'some-module'`?")},ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:function(e){var t=e.type;return"'".concat("ForInStatement"===t?"for-in":"for-of","' loop variable declaration may not have an initializer.")},ForInUsing:"For-in loop may not start with 'using' declaration.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:function(e){var t=e.type;return"Unsyntactic ".concat("BreakStatement"===t?"break":"continue",".")},IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportAttributesUseAssert:"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.",ImportBindingIsString:function(e){var t=e.importName;return'A string literal cannot be used as an imported binding.\n- Did you mean `import { "'.concat(t,'" as foo }`?')},ImportCallArity:"`import()` requires exactly one or two arguments.",ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",ImportJSONBindingNotDefault:"A JSON module can only be imported with `default`.",ImportReflectionHasAssertion:"`import module x` cannot have assertions.",ImportReflectionNotBinding:'Only `import module x from "./module"` is valid.',IncompatibleRegExpUVFlags:"The 'u' and 'v' regular expression flags cannot be enabled at the same time.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidCoverDiscardElement:"'void' must be followed by an expression when not used in a binding position.",InvalidCoverInitializedName:"Invalid shorthand property initializer.",InvalidDecimal:"Invalid decimal.",InvalidDigit:function(e){var t=e.radix;return"Expected number in radix ".concat(t,".")},InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:function(e){var t=e.reservedWord;return"Escape sequence in keyword ".concat(t,".")},InvalidIdentifier:function(e){var t=e.identifierName;return"Invalid identifier ".concat(t,".")},InvalidLhs:function(e){var t=e.ancestor;return"Invalid left-hand side in ".concat(z(t),".")},InvalidLhsBinding:function(e){var t=e.ancestor;return"Binding invalid left-hand side in ".concat(z(t),".")},InvalidLhsOptionalChaining:function(e){var t=e.ancestor;return"Invalid optional chaining in the left-hand side of ".concat(z(t),".")},InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:function(e){var t=e.unexpected;return"Unexpected character '".concat(t,"'.")},InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:function(e){var t=e.identifierName;return"Private name #".concat(t," is not defined.")},InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:function(e){var t=e.labelName;return"Label '".concat(t,"' is already declared.")},LetInLexicalBinding:"'let' is disallowed as a lexically bound name.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingPlugin:function(e){var t=e.missingPlugin;return"This experimental syntax requires enabling the parser plugin: ".concat(t.map((function(e){return JSON.stringify(e)})).join(", "),".")},MissingOneOfPlugins:function(e){var t=e.missingPlugin;return"This experimental syntax requires enabling one of the following parser plugin(s): ".concat(t.map((function(e){return JSON.stringify(e)})).join(", "),".")},MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:function(e){var t=e.key;return'Duplicate key "'.concat(t,'" is not allowed in module attributes.')},ModuleExportNameHasLoneSurrogate:function(e){var t=e.surrogateCharCode;return"An export name cannot include a lone surrogate, found '\\u".concat(t.toString(16),"'.")},ModuleExportUndefined:function(e){var t=e.localName;return"Export '".concat(t,"' is not defined.")},MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PrivateInExpectedIn:function(e){var t=e.identifierName;return"Private names are only allowed in property accesses (`obj.#".concat(t,"`) or in `in` expressions (`#").concat(t," in obj`).")},PrivateNameRedeclaration:function(e){var t=e.identifierName;return"Duplicate private name #".concat(t,".")},RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level or inside a block.",SloppyFunctionAnnexB:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",SourcePhaseImportRequiresDefault:'Only `import source x from "./module"` is valid.',StaticPrototype:"Classes may not have static property named prototype.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:function(e){var t=e.keyword;return"Unexpected keyword '".concat(t,"'.")},UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Unexpected private name.",UnexpectedReservedWord:function(e){var t=e.reservedWord;return"Unexpected reserved word '".concat(t,"'.")},UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:function(e){var t=e.expected,i=e.unexpected;return"Unexpected token".concat(i?" '".concat(i,"'."):"").concat(t?', expected "'.concat(t,'"'):"")},UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnexpectedUsingDeclaration:"Using declaration cannot appear in the top level when source type is `script` or in the bare case statement.",UnexpectedVoidPattern:"Unexpected void binding.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:function(e){var t=e.target,i=e.onlyValidPropertyName;return"The only valid meta property for ".concat(t," is ").concat(t,".").concat(i,".")},UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",UsingDeclarationExport:"Using declaration cannot be exported.",UsingDeclarationHasBindingPattern:"Using declaration cannot have destructuring patterns.",VarRedeclaration:function(e){var t=e.identifierName;return"Identifier '".concat(t,"' has already been declared.")},VoidPatternCatchClauseParam:"A void binding can not be the catch clause parameter. Use `try { ... } catch { ... }` if you want to discard the caught error.",VoidPatternInitializer:"A void binding may not have an initializer.",YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",YieldNotInGeneratorFunction:"'yield' is only allowed within generator functions.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},K={StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:function(e){var t=e.referenceName;return"Assigning to '".concat(t,"' in strict mode.")},StrictEvalArgumentsBinding:function(e){var t=e.bindingName;return"Binding '".concat(t,"' in strict mode.")},StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode."},W={ParseExpressionEmptyInput:"Unexpected parseExpression() input: The input is empty or contains only comments.",ParseExpressionExpectsEOF:function(e){var t=e.unexpected;return"Unexpected parseExpression() input: The input should contain exactly one expression, but the first expression is followed by the unexpected character `".concat(String.fromCodePoint(t),"`.")}},X=new Set(["ArrowFunctionExpression","AssignmentExpression","ConditionalExpression","YieldExpression"]),J=Object.assign({PipeBodyIsTighter:"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.',PipeTopicUnbound:"Topic reference is unbound; it must be inside a pipe body.",PipeTopicUnconfiguredToken:function(e){var t=e.token;return"Invalid topic token ".concat(t,". In order to use ").concat(t,' as a topic reference, the pipelineOperator plugin must be configured with { "proposal": "hack", "topicToken": "').concat(t,'" }.')},PipeTopicUnused:"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.",PipeUnparenthesizedBody:function(e){var t=e.type;return"Hack-style pipe body cannot be an unparenthesized ".concat(z({type:t}),"; please wrap it in parentheses.")}},{PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a "proposal": "hack" or "smart" option.'}),Y=["message"];function G(e,t,i){Object.defineProperty(e,t,{enumerable:!1,configurable:!0,value:i})}function $(e){var t=e.toMessage,i=e.code,r=e.reasonCode,s=e.syntaxPlugin,n="MissingPlugin"===r||"MissingOneOfPlugins"===r,a={AccessorCannotDeclareThisParameter:"AccesorCannotDeclareThisParameter",AccessorCannotHaveTypeParameters:"AccesorCannotHaveTypeParameters",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference",SetAccessorCannotHaveOptionalParameter:"SetAccesorCannotHaveOptionalParameter",SetAccessorCannotHaveRestParameter:"SetAccesorCannotHaveRestParameter",SetAccessorCannotHaveReturnType:"SetAccesorCannotHaveReturnType"};return a[r]&&(r=a[r]),function e(a,o){var l=new SyntaxError;return l.code=i,l.reasonCode=r,l.loc=a,l.pos=a.index,l.syntaxPlugin=s,n&&(l.missingPlugin=o.missingPlugin),G(l,"clone",(function(){var t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=null!=(t=i.loc)?t:a,s=r.line,n=r.column,l=r.index;return e(new _(s,n,l),Object.assign({},o,i.details))})),G(l,"details",o),Object.defineProperty(l,"message",{configurable:!0,get:function(){var e="".concat(t(o)," (").concat(a.line,":").concat(a.column,")");return this.message=e,e},set:function(e){Object.defineProperty(this,"message",{value:e,writable:!0})}}),l}}function Q(e,t){if(Array.isArray(e))return function(t){return Q(t,e[0])};for(var i={},r=function(){var r=n[s],a=e[r],o="string"===typeof a?{message:function(){return a}}:"function"===typeof a?{message:a}:a,l=o.message,c=B(o,Y),h="string"===typeof l?function(){return l}:l;i[r]=$(Object.assign({code:"BABEL_PARSER_SYNTAX_ERROR",reasonCode:r,toMessage:h},t?{syntaxPlugin:t}:{},c))},s=0,n=Object.keys(e);s<n.length;s++)r();return i}var Z=Object.assign({},Q(V),Q(q),Q(K),Q(W),Q(s||(s=N(["pipelineOperator"])))(J));function ee(){return{sourceType:"script",sourceFilename:void 0,startIndex:0,startColumn:0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowNewTargetOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,allowYieldOutsideFunction:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createImportExpressions:!1,createParenthesizedExpressions:!1,errorRecovery:!1,attachComment:!0,annexB:!0}}function te(e){var t=ee();if(null==e)return t;if(null!=e.annexB&&!1!==e.annexB)throw new Error("The `annexB` option can only be set to `false`.");for(var i=0,r=Object.keys(t);i<r.length;i++){var s=r[i];null!=e[s]&&(t[s]=e[s])}if(1===t.startLine)null==e.startIndex&&t.startColumn>0?t.startIndex=t.startColumn:null==e.startColumn&&t.startIndex>0&&(t.startColumn=t.startIndex);else if((null==e.startColumn||null==e.startIndex)&&null!=e.startIndex)throw new Error("With a `startLine > 1` you must also specify `startIndex` and `startColumn`.");if("commonjs"===t.sourceType){if(null!=e.allowAwaitOutsideFunction)throw new Error("The `allowAwaitOutsideFunction` option cannot be used with `sourceType: 'commonjs'`.");if(null!=e.allowReturnOutsideFunction)throw new Error("`sourceType: 'commonjs'` implies `allowReturnOutsideFunction: true`, please remove the `allowReturnOutsideFunction` option or use `sourceType: 'script'`.");if(null!=e.allowNewTargetOutsideFunction)throw new Error("`sourceType: 'commonjs'` implies `allowNewTargetOutsideFunction: true`, please remove the `allowNewTargetOutsideFunction` option or use `sourceType: 'script'`.")}return t}var ie=Object.defineProperty,re=function(e,t){e&&ie(e,t,{enumerable:!1,value:e[t]})};function se(e){return re(e.loc.start,"index"),re(e.loc.end,"index"),e}var ne=function(e){return function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"parse",value:function(){var e=se(w(t,"parse",this,3)([]));return 256&this.optionFlags&&(e.tokens=e.tokens.map(se)),e}},{key:"parseRegExpLiteral",value:function(e){var t=e.pattern,i=e.flags,r=null;try{r=new RegExp(t,i)}catch(n){}var s=this.estreeParseLiteral(r);return s.regex={pattern:t,flags:i},s}},{key:"parseBigIntLiteral",value:function(e){var t;try{t=BigInt(e)}catch(r){t=null}var i=this.estreeParseLiteral(t);return i.bigint=String(i.value||e),i}},{key:"parseDecimalLiteral",value:function(e){var t=null,i=this.estreeParseLiteral(t);return i.decimal=String(i.value||e),i}},{key:"estreeParseLiteral",value:function(e){return this.parseLiteral(e,"Literal")}},{key:"parseStringLiteral",value:function(e){return this.estreeParseLiteral(e)}},{key:"parseNumericLiteral",value:function(e){return this.estreeParseLiteral(e)}},{key:"parseNullLiteral",value:function(){return this.estreeParseLiteral(null)}},{key:"parseBooleanLiteral",value:function(e){return this.estreeParseLiteral(e)}},{key:"estreeParseChainExpression",value:function(e,t){var i=this.startNodeAtNode(e);return i.expression=e,this.finishNodeAt(i,"ChainExpression",t)}},{key:"directiveToStmt",value:function(e){var t=e.value;delete e.value,this.castNodeTo(t,"Literal"),t.raw=t.extra.raw,t.value=t.extra.expressionValue;var i=this.castNodeTo(e,"ExpressionStatement");return i.expression=t,i.directive=t.extra.rawValue,delete t.extra,i}},{key:"fillOptionalPropertiesForTSESLint",value:function(e){}},{key:"cloneEstreeStringLiteral",value:function(e){var t=e.start,i=e.end,r=e.loc,s=e.range,n=e.raw,a=e.value,o=Object.create(e.constructor.prototype);return o.type="Literal",o.start=t,o.end=i,o.loc=r,o.range=s,o.raw=n,o.value=a,o}},{key:"initFunction",value:function(e,i){w(t,"initFunction",this,3)([e,i]),e.expression=!1}},{key:"checkDeclaration",value:function(e){null!=e&&this.isObjectProperty(e)?this.checkDeclaration(e.value):w(t,"checkDeclaration",this,3)([e])}},{key:"getObjectOrClassMethodParams",value:function(e){return e.value.params}},{key:"isValidDirective",value:function(e){var t;return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"===typeof e.expression.value&&!(null!=(t=e.expression.extra)&&t.parenthesized)}},{key:"parseBlockBody",value:function(e,i,r,s,n){var a=this;w(t,"parseBlockBody",this,3)([e,i,r,s,n]);var o=e.directives.map((function(e){return a.directiveToStmt(e)}));e.body=o.concat(e.body),delete e.directives}},{key:"parsePrivateName",value:function(){var e=w(t,"parsePrivateName",this,3)([]);return this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(e):e}},{key:"convertPrivateNameToPrivateIdentifier",value:function(e){var i=w(t,"getPrivateNameSV",this,3)([e]);return e=e,delete e.id,e.name=i,this.castNodeTo(e,"PrivateIdentifier")}},{key:"isPrivateName",value:function(e){return this.getPluginOption("estree","classFeatures")?"PrivateIdentifier"===e.type:w(t,"isPrivateName",this,3)([e])}},{key:"getPrivateNameSV",value:function(e){return this.getPluginOption("estree","classFeatures")?e.name:w(t,"getPrivateNameSV",this,3)([e])}},{key:"parseLiteral",value:function(e,i){var r=w(t,"parseLiteral",this,3)([e,i]);return r.raw=r.extra.raw,delete r.extra,r}},{key:"parseFunctionBody",value:function(e,i){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];w(t,"parseFunctionBody",this,3)([e,i,r]),e.expression="BlockStatement"!==e.body.type}},{key:"parseMethod",value:function(e,i,r,s,n,a){var o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],l=this.startNode();l.kind=e.kind,l=w(t,"parseMethod",this,3)([l,i,r,s,n,a,o]),delete l.kind;var c=e.typeParameters;c&&(delete e.typeParameters,l.typeParameters=c,this.resetStartLocationFromNode(l,c));var h=this.castNodeTo(l,"FunctionExpression");return e.value=h,"ClassPrivateMethod"===a&&(e.computed=!1),"ObjectMethod"===a?("method"===e.kind&&(e.kind="init"),e.shorthand=!1,this.finishNode(e,"Property")):this.finishNode(e,"MethodDefinition")}},{key:"nameIsConstructor",value:function(e){return"Literal"===e.type?"constructor"===e.value:w(t,"nameIsConstructor",this,3)([e])}},{key:"parseClassProperty",value:function(){for(var e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];var s=w(t,"parseClassProperty",this,3)(i);return this.getPluginOption("estree","classFeatures")?(this.castNodeTo(s,"PropertyDefinition"),s):s}},{key:"parseClassPrivateProperty",value:function(){for(var e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];var s=w(t,"parseClassPrivateProperty",this,3)(i);return this.getPluginOption("estree","classFeatures")?(this.castNodeTo(s,"PropertyDefinition"),s.computed=!1,s):s}},{key:"parseClassAccessorProperty",value:function(e){var i=w(t,"parseClassAccessorProperty",this,3)([e]);return this.getPluginOption("estree","classFeatures")?(i.abstract&&this.hasPlugin("typescript")?(delete i.abstract,this.castNodeTo(i,"TSAbstractAccessorProperty")):this.castNodeTo(i,"AccessorProperty"),i):i}},{key:"parseObjectProperty",value:function(e,i,r,s){var n=w(t,"parseObjectProperty",this,3)([e,i,r,s]);return n&&(n.kind="init",this.castNodeTo(n,"Property")),n}},{key:"finishObjectProperty",value:function(e){return e.kind="init",this.finishNode(e,"Property")}},{key:"isValidLVal",value:function(e,i,r){return"Property"===e?"value":w(t,"isValidLVal",this,3)([e,i,r])}},{key:"isAssignable",value:function(e,i){return null!=e&&this.isObjectProperty(e)?this.isAssignable(e.value,i):w(t,"isAssignable",this,3)([e,i])}},{key:"toAssignable",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null!=e&&this.isObjectProperty(e)){var r=e.key,s=e.value;this.isPrivateName(r)&&this.classScope.usePrivateName(this.getPrivateNameSV(r),r.loc.start),this.toAssignable(s,i)}else w(t,"toAssignable",this,3)([e,i])}},{key:"toAssignableObjectExpressionProp",value:function(e,i,r){"Property"!==e.type||"get"!==e.kind&&"set"!==e.kind?"Property"===e.type&&e.method?this.raise(Z.PatternHasMethod,e.key):w(t,"toAssignableObjectExpressionProp",this,3)([e,i,r]):this.raise(Z.PatternHasAccessor,e.key)}},{key:"finishCallExpression",value:function(e,i){var r,s,n=w(t,"finishCallExpression",this,3)([e,i]);"Import"===n.callee.type?(this.castNodeTo(n,"ImportExpression"),n.source=n.arguments[0],n.options=null!=(r=n.arguments[1])?r:null,n.attributes=null!=(s=n.arguments[1])?s:null,delete n.arguments,delete n.callee):"OptionalCallExpression"===n.type?this.castNodeTo(n,"CallExpression"):n.optional=!1;return n}},{key:"toReferencedArguments",value:function(e){"ImportExpression"!==e.type&&w(t,"toReferencedArguments",this,3)([e])}},{key:"parseExport",value:function(e,i){var r=this.state.lastTokStartLoc,s=w(t,"parseExport",this,3)([e,i]);switch(s.type){case"ExportAllDeclaration":s.exported=null;break;case"ExportNamedDeclaration":1===s.specifiers.length&&"ExportNamespaceSpecifier"===s.specifiers[0].type&&(this.castNodeTo(s,"ExportAllDeclaration"),s.exported=s.specifiers[0].exported,delete s.specifiers);case"ExportDefaultDeclaration":var n,a=s.declaration;"ClassDeclaration"===(null==a?void 0:a.type)&&(null==(n=a.decorators)?void 0:n.length)>0&&a.start===s.start&&this.resetStartLocation(s,r);break}return s}},{key:"stopParseSubscript",value:function(e,i){var r=w(t,"stopParseSubscript",this,3)([e,i]);return i.optionalChainMember?this.estreeParseChainExpression(r,e.loc.end):r}},{key:"parseMember",value:function(e,i,r,s,n){var a=w(t,"parseMember",this,3)([e,i,r,s,n]);return"OptionalMemberExpression"===a.type?this.castNodeTo(a,"MemberExpression"):a.optional=!1,a}},{key:"isOptionalMemberExpression",value:function(e){return"ChainExpression"===e.type?"MemberExpression"===e.expression.type:w(t,"isOptionalMemberExpression",this,3)([e])}},{key:"hasPropertyAsPrivateName",value:function(e){return"ChainExpression"===e.type&&(e=e.expression),w(t,"hasPropertyAsPrivateName",this,3)([e])}},{key:"isObjectProperty",value:function(e){return"Property"===e.type&&"init"===e.kind&&!e.method}},{key:"isObjectMethod",value:function(e){return"Property"===e.type&&(e.method||"get"===e.kind||"set"===e.kind)}},{key:"castNodeTo",value:function(e,i){var r=w(t,"castNodeTo",this,3)([e,i]);return this.fillOptionalPropertiesForTSESLint(r),r}},{key:"cloneIdentifier",value:function(e){var i=w(t,"cloneIdentifier",this,3)([e]);return this.fillOptionalPropertiesForTSESLint(i),i}},{key:"cloneStringLiteral",value:function(e){return"Literal"===e.type?this.cloneEstreeStringLiteral(e):w(t,"cloneStringLiteral",this,3)([e])}},{key:"finishNodeAt",value:function(e,i,r){return se(w(t,"finishNodeAt",this,3)([e,i,r]))}},{key:"finishNode",value:function(e,i){var r=w(t,"finishNode",this,3)([e,i]);return this.fillOptionalPropertiesForTSESLint(r),r}},{key:"resetStartLocation",value:function(e,i){w(t,"resetStartLocation",this,3)([e,i]),se(e)}},{key:"resetEndLocation",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.lastTokEndLoc;w(t,"resetEndLocation",this,3)([e,i]),se(e)}}])}(e)},ae=L((function e(t,i){F(this,e),this.token=void 0,this.preserveSpace=void 0,this.token=t,this.preserveSpace=!!i})),oe={brace:new ae("{"),j_oTag:new ae("<tag"),j_cTag:new ae("</tag"),j_expr:new ae("<tag>...</tag>",!0)};oe.template=new ae("`",!0);var le=!0,ce=!0,he=!0,ue=!0,pe=!0,de=!0,fe=L((function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};F(this,e),this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.label=t,this.keyword=i.keyword,this.beforeExpr=!!i.beforeExpr,this.startsExpr=!!i.startsExpr,this.rightAssociative=!!i.rightAssociative,this.isLoop=!!i.isLoop,this.isAssign=!!i.isAssign,this.prefix=!!i.prefix,this.postfix=!!i.postfix,this.binop=null!=i.binop?i.binop:null,this.updateContext=null})),me=new Map;function ye(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.keyword=e;var i=Se(e,t);return me.set(e,i),i}function ve(e,t){return Se(e,{beforeExpr:le,binop:t})}var ge=-1,xe=[],be=[],Pe=[],ke=[],Te=[],we=[];function Se(e){var t,i,r,s,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return++ge,be.push(e),Pe.push(null!=(t=n.binop)?t:-1),ke.push(null!=(i=n.beforeExpr)&&i),Te.push(null!=(r=n.startsExpr)&&r),we.push(null!=(s=n.prefix)&&s),xe.push(new fe(e,n)),ge}function Ae(e){var t,i,r,s,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return++ge,me.set(e,ge),be.push(e),Pe.push(null!=(t=n.binop)?t:-1),ke.push(null!=(i=n.beforeExpr)&&i),Te.push(null!=(r=n.startsExpr)&&r),we.push(null!=(s=n.prefix)&&s),xe.push(new fe("name",n)),ge}var Ee={bracketL:Se("[",{beforeExpr:le,startsExpr:ce}),bracketHashL:Se("#[",{beforeExpr:le,startsExpr:ce}),bracketBarL:Se("[|",{beforeExpr:le,startsExpr:ce}),bracketR:Se("]"),bracketBarR:Se("|]"),braceL:Se("{",{beforeExpr:le,startsExpr:ce}),braceBarL:Se("{|",{beforeExpr:le,startsExpr:ce}),braceHashL:Se("#{",{beforeExpr:le,startsExpr:ce}),braceR:Se("}"),braceBarR:Se("|}"),parenL:Se("(",{beforeExpr:le,startsExpr:ce}),parenR:Se(")"),comma:Se(",",{beforeExpr:le}),semi:Se(";",{beforeExpr:le}),colon:Se(":",{beforeExpr:le}),doubleColon:Se("::",{beforeExpr:le}),dot:Se("."),question:Se("?",{beforeExpr:le}),questionDot:Se("?."),arrow:Se("=>",{beforeExpr:le}),template:Se("template"),ellipsis:Se("...",{beforeExpr:le}),backQuote:Se("`",{startsExpr:ce}),dollarBraceL:Se("${",{beforeExpr:le,startsExpr:ce}),templateTail:Se("...`",{startsExpr:ce}),templateNonTail:Se("...${",{beforeExpr:le,startsExpr:ce}),at:Se("@"),hash:Se("#",{startsExpr:ce}),interpreterDirective:Se("#!..."),eq:Se("=",{beforeExpr:le,isAssign:ue}),assign:Se("_=",{beforeExpr:le,isAssign:ue}),slashAssign:Se("_=",{beforeExpr:le,isAssign:ue}),xorAssign:Se("_=",{beforeExpr:le,isAssign:ue}),moduloAssign:Se("_=",{beforeExpr:le,isAssign:ue}),incDec:Se("++/--",{prefix:pe,postfix:de,startsExpr:ce}),bang:Se("!",{beforeExpr:le,prefix:pe,startsExpr:ce}),tilde:Se("~",{beforeExpr:le,prefix:pe,startsExpr:ce}),doubleCaret:Se("^^",{startsExpr:ce}),doubleAt:Se("@@",{startsExpr:ce}),pipeline:ve("|>",0),nullishCoalescing:ve("??",1),logicalOR:ve("||",1),logicalAND:ve("&&",2),bitwiseOR:ve("|",3),bitwiseXOR:ve("^",4),bitwiseAND:ve("&",5),equality:ve("==/!=/===/!==",6),lt:ve("</>/<=/>=",7),gt:ve("</>/<=/>=",7),relational:ve("</>/<=/>=",7),bitShift:ve("<</>>/>>>",8),bitShiftL:ve("<</>>/>>>",8),bitShiftR:ve("<</>>/>>>",8),plusMin:Se("+/-",{beforeExpr:le,binop:9,prefix:pe,startsExpr:ce}),modulo:Se("%",{binop:10,startsExpr:ce}),star:Se("*",{binop:10}),slash:ve("/",10),exponent:Se("**",{beforeExpr:le,binop:11,rightAssociative:!0}),_in:ye("in",{beforeExpr:le,binop:7}),_instanceof:ye("instanceof",{beforeExpr:le,binop:7}),_break:ye("break"),_case:ye("case",{beforeExpr:le}),_catch:ye("catch"),_continue:ye("continue"),_debugger:ye("debugger"),_default:ye("default",{beforeExpr:le}),_else:ye("else",{beforeExpr:le}),_finally:ye("finally"),_function:ye("function",{startsExpr:ce}),_if:ye("if"),_return:ye("return",{beforeExpr:le}),_switch:ye("switch"),_throw:ye("throw",{beforeExpr:le,prefix:pe,startsExpr:ce}),_try:ye("try"),_var:ye("var"),_const:ye("const"),_with:ye("with"),_new:ye("new",{beforeExpr:le,startsExpr:ce}),_this:ye("this",{startsExpr:ce}),_super:ye("super",{startsExpr:ce}),_class:ye("class",{startsExpr:ce}),_extends:ye("extends",{beforeExpr:le}),_export:ye("export"),_import:ye("import",{startsExpr:ce}),_null:ye("null",{startsExpr:ce}),_true:ye("true",{startsExpr:ce}),_false:ye("false",{startsExpr:ce}),_typeof:ye("typeof",{beforeExpr:le,prefix:pe,startsExpr:ce}),_void:ye("void",{beforeExpr:le,prefix:pe,startsExpr:ce}),_delete:ye("delete",{beforeExpr:le,prefix:pe,startsExpr:ce}),_do:ye("do",{isLoop:he,beforeExpr:le}),_for:ye("for",{isLoop:he}),_while:ye("while",{isLoop:he}),_as:Ae("as",{startsExpr:ce}),_assert:Ae("assert",{startsExpr:ce}),_async:Ae("async",{startsExpr:ce}),_await:Ae("await",{startsExpr:ce}),_defer:Ae("defer",{startsExpr:ce}),_from:Ae("from",{startsExpr:ce}),_get:Ae("get",{startsExpr:ce}),_let:Ae("let",{startsExpr:ce}),_meta:Ae("meta",{startsExpr:ce}),_of:Ae("of",{startsExpr:ce}),_sent:Ae("sent",{startsExpr:ce}),_set:Ae("set",{startsExpr:ce}),_source:Ae("source",{startsExpr:ce}),_static:Ae("static",{startsExpr:ce}),_using:Ae("using",{startsExpr:ce}),_yield:Ae("yield",{startsExpr:ce}),_asserts:Ae("asserts",{startsExpr:ce}),_checks:Ae("checks",{startsExpr:ce}),_exports:Ae("exports",{startsExpr:ce}),_global:Ae("global",{startsExpr:ce}),_implements:Ae("implements",{startsExpr:ce}),_intrinsic:Ae("intrinsic",{startsExpr:ce}),_infer:Ae("infer",{startsExpr:ce}),_is:Ae("is",{startsExpr:ce}),_mixins:Ae("mixins",{startsExpr:ce}),_proto:Ae("proto",{startsExpr:ce}),_require:Ae("require",{startsExpr:ce}),_satisfies:Ae("satisfies",{startsExpr:ce}),_keyof:Ae("keyof",{startsExpr:ce}),_readonly:Ae("readonly",{startsExpr:ce}),_unique:Ae("unique",{startsExpr:ce}),_abstract:Ae("abstract",{startsExpr:ce}),_declare:Ae("declare",{startsExpr:ce}),_enum:Ae("enum",{startsExpr:ce}),_module:Ae("module",{startsExpr:ce}),_namespace:Ae("namespace",{startsExpr:ce}),_interface:Ae("interface",{startsExpr:ce}),_type:Ae("type",{startsExpr:ce}),_opaque:Ae("opaque",{startsExpr:ce}),name:Se("name",{startsExpr:ce}),placeholder:Se("%%",{startsExpr:ce}),string:Se("string",{startsExpr:ce}),num:Se("num",{startsExpr:ce}),bigint:Se("bigint",{startsExpr:ce}),decimal:Se("decimal",{startsExpr:ce}),regexp:Se("regexp",{startsExpr:ce}),privateName:Se("#name",{startsExpr:ce}),eof:Se("eof"),jsxName:Se("jsxName"),jsxText:Se("jsxText",{beforeExpr:le}),jsxTagStart:Se("jsxTagStart",{startsExpr:ce}),jsxTagEnd:Se("jsxTagEnd")};function Ce(e){return e>=93&&e<=133}function Ie(e){return e<=92}function Ne(e){return e>=58&&e<=133}function Oe(e){return e>=58&&e<=137}function Le(e){return ke[e]}function De(e){return Te[e]}function Me(e){return e>=29&&e<=33}function Fe(e){return e>=129&&e<=131}function Be(e){return e>=90&&e<=92}function _e(e){return e>=58&&e<=92}function je(e){return e>=39&&e<=59}function Re(e){return 34===e}function Ue(e){return we[e]}function Ve(e){return e>=121&&e<=123}function He(e){return e>=124&&e<=130}function ze(e){return be[e]}function qe(e){return Pe[e]}function Ke(e){return 57===e}function We(e){return e>=24&&e<=25}function Xe(e){return xe[e]}xe[8].updateContext=function(e){e.pop()},xe[5].updateContext=xe[7].updateContext=xe[23].updateContext=function(e){e.push(oe.brace)},xe[22].updateContext=function(e){e[e.length-1]===oe.template?e.pop():e.push(oe.template)},xe[143].updateContext=function(e){e.push(oe.j_expr,oe.j_oTag)};var Je="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲊᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟍꟐꟑꟓꟕ-Ƛꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",Ye="·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࢗ-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･",Ge=new RegExp("["+Je+"]"),$e=new RegExp("["+Je+Ye+"]");Je=Ye=null;var Qe=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],Ze=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function et(e,t){for(var i=65536,r=0,s=t.length;r<s;r+=2){if(i+=t[r],i>e)return!1;if(i+=t[r+1],i>=e)return!0}return!1}function tt(e){return e<65?36===e:e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&Ge.test(String.fromCharCode(e)):et(e,Qe)))}function it(e){return e<48?36===e:e<58||!(e<65)&&(e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&$e.test(String.fromCharCode(e)):et(e,Qe)||et(e,Ze))))}var rt={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},st=new Set(rt.keyword),nt=new Set(rt.strict),at=new Set(rt.strictBind);function ot(e,t){return t&&"await"===e||"enum"===e}function lt(e,t){return ot(e,t)||nt.has(e)}function ct(e){return at.has(e)}function ht(e,t){return lt(e,t)||ct(e)}function ut(e){return st.has(e)}function pt(e,t,i){return 64===e&&64===t&&tt(i)}var dt=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete","implements","interface","let","package","private","protected","public","static","yield","eval","arguments","enum","await"]);function ft(e){return dt.has(e)}var mt=L((function e(t){F(this,e),this.flags=0,this.names=new Map,this.firstLexicalName="",this.flags=t})),yt=function(){function e(t,i){F(this,e),this.parser=void 0,this.scopeStack=[],this.inModule=void 0,this.undefinedExports=new Map,this.parser=t,this.inModule=i}return L(e,[{key:"inTopLevel",get:function(){return(1&this.currentScope().flags)>0}},{key:"inFunction",get:function(){return(2&this.currentVarScopeFlags())>0}},{key:"allowSuper",get:function(){return(16&this.currentThisScopeFlags())>0}},{key:"allowDirectSuper",get:function(){return(32&this.currentThisScopeFlags())>0}},{key:"allowNewTarget",get:function(){return(512&this.currentThisScopeFlags())>0}},{key:"inClass",get:function(){return(64&this.currentThisScopeFlags())>0}},{key:"inClassAndNotInNonArrowFunction",get:function(){var e=this.currentThisScopeFlags();return(64&e)>0&&0===(2&e)}},{key:"inStaticBlock",get:function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e].flags;if(128&t)return!0;if(1731&t)return!1}}},{key:"inNonArrowFunction",get:function(){return(2&this.currentThisScopeFlags())>0}},{key:"inBareCaseStatement",get:function(){return(256&this.currentScope().flags)>0}},{key:"treatFunctionsAsVar",get:function(){return this.treatFunctionsAsVarInScope(this.currentScope())}},{key:"createScope",value:function(e){return new mt(e)}},{key:"enter",value:function(e){this.scopeStack.push(this.createScope(e))}},{key:"exit",value:function(){var e=this.scopeStack.pop();return e.flags}},{key:"treatFunctionsAsVarInScope",value:function(e){return!!(130&e.flags||!this.parser.inModule&&1&e.flags)}},{key:"declareName",value:function(e,t,i){var r=this.currentScope();if(8&t||16&t){this.checkRedeclarationInScope(r,e,t,i);var s=r.names.get(e)||0;16&t?s|=4:(r.firstLexicalName||(r.firstLexicalName=e),s|=2),r.names.set(e,s),8&t&&this.maybeExportDefined(r,e)}else if(4&t)for(var n=this.scopeStack.length-1;n>=0;--n)if(r=this.scopeStack[n],this.checkRedeclarationInScope(r,e,t,i),r.names.set(e,1|(r.names.get(e)||0)),this.maybeExportDefined(r,e),1667&r.flags)break;this.parser.inModule&&1&r.flags&&this.undefinedExports.delete(e)}},{key:"maybeExportDefined",value:function(e,t){this.parser.inModule&&1&e.flags&&this.undefinedExports.delete(t)}},{key:"checkRedeclarationInScope",value:function(e,t,i,r){this.isRedeclaredInScope(e,t,i)&&this.parser.raise(Z.VarRedeclaration,r,{identifierName:t})}},{key:"isRedeclaredInScope",value:function(e,t,i){if(!(1&i))return!1;if(8&i)return e.names.has(t);var r=e.names.get(t);return 16&i?(2&r)>0||!this.treatFunctionsAsVarInScope(e)&&(1&r)>0:(2&r)>0&&!(8&e.flags&&e.firstLexicalName===t)||!this.treatFunctionsAsVarInScope(e)&&(4&r)>0}},{key:"checkLocalExport",value:function(e){var t=e.name,i=this.scopeStack[0];i.names.has(t)||this.undefinedExports.set(t,e.loc.start)}},{key:"currentScope",value:function(){return this.scopeStack[this.scopeStack.length-1]}},{key:"currentVarScopeFlags",value:function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e].flags;if(1667&t)return t}}},{key:"currentThisScopeFlags",value:function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e].flags;if(1731&t&&!(4&t))return t}}}])}(),vt=function(e){function t(){var e;F(this,t);for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=b(this,t,[].concat(r)),e.declareFunctions=new Set,e}return C(t,e),L(t)}(mt),gt=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"createScope",value:function(e){return new vt(e)}},{key:"declareName",value:function(e,i,r){var s=this.currentScope();if(2048&i)return this.checkRedeclarationInScope(s,e,i,r),this.maybeExportDefined(s,e),void s.declareFunctions.add(e);w(t,"declareName",this,3)([e,i,r])}},{key:"isRedeclaredInScope",value:function(e,i,r){if(w(t,"isRedeclaredInScope",this,3)([e,i,r]))return!0;if(2048&r&&!e.declareFunctions.has(i)){var s=e.names.get(i);return(4&s)>0||(2&s)>0}return!1}},{key:"checkLocalExport",value:function(e){this.scopeStack[0].declareFunctions.has(e.name)||w(t,"checkLocalExport",this,3)([e])}}])}(yt),xt=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),bt=Q(n||(n=N(["flow"])))({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:function(e){var t=e.reservedType;return"Cannot overwrite reserved type ".concat(t,".")},DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:function(e){var t=e.memberName,i=e.enumName;return"Boolean enum members need to be initialized. Use either `".concat(t," = true,` or `").concat(t," = false,` in enum `").concat(i,"`.")},EnumDuplicateMemberName:function(e){var t=e.memberName,i=e.enumName;return"Enum member names need to be unique, but the name `".concat(t,"` has already been used before in enum `").concat(i,"`.")},EnumInconsistentMemberValues:function(e){var t=e.enumName;return"Enum `".concat(t,"` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.")},EnumInvalidExplicitType:function(e){var t=e.invalidEnumType,i=e.enumName;return"Enum type `".concat(t,"` is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `").concat(i,"`.")},EnumInvalidExplicitTypeUnknownSupplied:function(e){var t=e.enumName;return"Supplied enum type is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `".concat(t,"`.")},EnumInvalidMemberInitializerPrimaryType:function(e){var t=e.enumName,i=e.memberName,r=e.explicitType;return"Enum `".concat(t,"` has type `").concat(r,"`, so the initializer of `").concat(i,"` needs to be a ").concat(r," literal.")},EnumInvalidMemberInitializerSymbolType:function(e){var t=e.enumName,i=e.memberName;return"Symbol enum members cannot be initialized. Use `".concat(i,",` in enum `").concat(t,"`.")},EnumInvalidMemberInitializerUnknownType:function(e){var t=e.enumName,i=e.memberName;return"The enum member initializer for `".concat(i,"` needs to be a literal (either a boolean, number, or string) in enum `").concat(t,"`.")},EnumInvalidMemberName:function(e){var t=e.enumName,i=e.memberName,r=e.suggestion;return"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `".concat(i,"`, consider using `").concat(r,"`, in enum `").concat(t,"`.")},EnumNumberMemberNotInitialized:function(e){var t=e.enumName,i=e.memberName;return"Number enum members need to be initialized, e.g. `".concat(i," = 1` in enum `").concat(t,"`.")},EnumStringMemberInconsistentlyInitialized:function(e){var t=e.enumName;return"String enum members need to consistently either all use initializers, or use no initializers, in enum `".concat(t,"`.")},GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` or `typeof` keyword.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",PatternIsOptional:Object.assign({message:"A binding pattern parameter cannot be optional in an implementation signature."},{reasonCode:"OptionalBindingPattern"}),SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:function(e){var t=e.reservedType;return"Unexpected reserved type ".concat(t,".")},UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:function(e){var t=e.unsupportedExportKind,i=e.suggestion;return"`declare export ".concat(t,"` is not supported. Use `").concat(i,"` instead.")},UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."});function Pt(e){return"DeclareExportAllDeclaration"===e.type||"DeclareExportDeclaration"===e.type&&(!e.declaration||"TypeAlias"!==e.declaration.type&&"InterfaceDeclaration"!==e.declaration.type)}function kt(e){return"type"===e.importKind||"typeof"===e.importKind}var Tt={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"};function wt(e,t){for(var i=[],r=[],s=0;s<e.length;s++)(t(e[s],s,e)?i:r).push(e[s]);return[i,r]}var St=/\*?\s*@((?:no)?flow)\b/,At=function(e){return function(e){function t(){var e;F(this,t);for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=b(this,t,[].concat(r)),e.flowPragma=void 0,e}return C(t,e),L(t,[{key:"getScopeHandler",value:function(){return gt}},{key:"shouldParseTypes",value:function(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}},{key:"finishToken",value:function(e,i){134!==e&&13!==e&&28!==e&&void 0===this.flowPragma&&(this.flowPragma=null),w(t,"finishToken",this,3)([e,i])}},{key:"addComment",value:function(e){if(void 0===this.flowPragma){var i=St.exec(e.value);if(i)if("flow"===i[1])this.flowPragma="flow";else{if("noflow"!==i[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}else;}w(t,"addComment",this,3)([e])}},{key:"flowParseTypeInitialiser",value:function(e){var t=this.state.inType;this.state.inType=!0,this.expect(e||14);var i=this.flowParseType();return this.state.inType=t,i}},{key:"flowParsePredicate",value:function(){var e=this.startNode(),i=this.state.startLoc;return this.next(),this.expectContextual(110),this.state.lastTokStartLoc.index>i.index+1&&this.raise(bt.UnexpectedSpaceBetweenModuloChecks,i),this.eat(10)?(e.value=w(t,"parseExpression",this,3)([]),this.expect(11),this.finishNode(e,"DeclaredPredicate")):this.finishNode(e,"InferredPredicate")}},{key:"flowParseTypeAndPredicateInitialiser",value:function(){var e=this.state.inType;this.state.inType=!0,this.expect(14);var t=null,i=null;return this.match(54)?(this.state.inType=e,i=this.flowParsePredicate()):(t=this.flowParseType(),this.state.inType=e,this.match(54)&&(i=this.flowParsePredicate())),[t,i]}},{key:"flowParseDeclareClass",value:function(e){return this.next(),this.flowParseInterfaceish(e,!0),this.finishNode(e,"DeclareClass")}},{key:"flowParseDeclareFunction",value:function(e){this.next();var t=e.id=this.parseIdentifier(),i=this.startNode(),r=this.startNode();this.match(47)?i.typeParameters=this.flowParseTypeParameterDeclaration():i.typeParameters=null,this.expect(10);var s=this.flowParseFunctionTypeParams();i.params=s.params,i.rest=s.rest,i.this=s._this,this.expect(11);var n=this.flowParseTypeAndPredicateInitialiser(),a=f(n,2);return i.returnType=a[0],e.predicate=a[1],r.typeAnnotation=this.finishNode(i,"FunctionTypeAnnotation"),t.typeAnnotation=this.finishNode(r,"TypeAnnotation"),this.resetEndLocation(t),this.semicolon(),this.scope.declareName(e.id.name,2048,e.id.loc.start),this.finishNode(e,"DeclareFunction")}},{key:"flowParseDeclare",value:function(e,t){return this.match(80)?this.flowParseDeclareClass(e):this.match(68)?this.flowParseDeclareFunction(e):this.match(74)?this.flowParseDeclareVariable(e):this.eatContextual(127)?this.match(16)?this.flowParseDeclareModuleExports(e):(t&&this.raise(bt.NestedDeclareModule,this.state.lastTokStartLoc),this.flowParseDeclareModule(e)):this.isContextual(130)?this.flowParseDeclareTypeAlias(e):this.isContextual(131)?this.flowParseDeclareOpaqueType(e):this.isContextual(129)?this.flowParseDeclareInterface(e):this.match(82)?this.flowParseDeclareExportDeclaration(e,t):void this.unexpected()}},{key:"flowParseDeclareVariable",value:function(e){return this.next(),e.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(e.id.name,5,e.id.loc.start),this.semicolon(),this.finishNode(e,"DeclareVariable")}},{key:"flowParseDeclareModule",value:function(e){var i=this;this.scope.enter(0),this.match(134)?e.id=w(t,"parseExprAtom",this,3)([]):e.id=this.parseIdentifier();var r=e.body=this.startNode(),s=r.body=[];this.expect(5);while(!this.match(8)){var n=this.startNode();this.match(83)?(this.next(),this.isContextual(130)||this.match(87)||this.raise(bt.InvalidNonTypeImportInDeclareModule,this.state.lastTokStartLoc),w(t,"parseImport",this,3)([n])):(this.expectContextual(125,bt.UnsupportedStatementInDeclareModule),n=this.flowParseDeclare(n,!0)),s.push(n)}this.scope.exit(),this.expect(8),this.finishNode(r,"BlockStatement");var a=null,o=!1;return s.forEach((function(e){Pt(e)?("CommonJS"===a&&i.raise(bt.AmbiguousDeclareModuleKind,e),a="ES"):"DeclareModuleExports"===e.type&&(o&&i.raise(bt.DuplicateDeclareModuleExports,e),"ES"===a&&i.raise(bt.AmbiguousDeclareModuleKind,e),a="CommonJS",o=!0)})),e.kind=a||"CommonJS",this.finishNode(e,"DeclareModule")}},{key:"flowParseDeclareExportDeclaration",value:function(e,t){if(this.expect(82),this.eat(65))return this.match(68)||this.match(80)?e.declaration=this.flowParseDeclare(this.startNode()):(e.declaration=this.flowParseType(),this.semicolon()),e.default=!0,this.finishNode(e,"DeclareExportDeclaration");if(this.match(75)||this.isLet()||(this.isContextual(130)||this.isContextual(129))&&!t){var i=this.state.value;throw this.raise(bt.UnsupportedDeclareExportKind,this.state.startLoc,{unsupportedExportKind:i,suggestion:Tt[i]})}return this.match(74)||this.match(68)||this.match(80)||this.isContextual(131)?(e.declaration=this.flowParseDeclare(this.startNode()),e.default=!1,this.finishNode(e,"DeclareExportDeclaration")):this.match(55)||this.match(5)||this.isContextual(129)||this.isContextual(130)||this.isContextual(131)?(e=this.parseExport(e,null),"ExportNamedDeclaration"===e.type?(e.default=!1,delete e.exportKind,this.castNodeTo(e,"DeclareExportDeclaration")):this.castNodeTo(e,"DeclareExportAllDeclaration")):void this.unexpected()}},{key:"flowParseDeclareModuleExports",value:function(e){return this.next(),this.expectContextual(111),e.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(e,"DeclareModuleExports")}},{key:"flowParseDeclareTypeAlias",value:function(e){this.next();var t=this.flowParseTypeAlias(e);return this.castNodeTo(t,"DeclareTypeAlias"),t}},{key:"flowParseDeclareOpaqueType",value:function(e){this.next();var t=this.flowParseOpaqueType(e,!0);return this.castNodeTo(t,"DeclareOpaqueType"),t}},{key:"flowParseDeclareInterface",value:function(e){return this.next(),this.flowParseInterfaceish(e,!1),this.finishNode(e,"DeclareInterface")}},{key:"flowParseInterfaceish",value:function(e,t){if(e.id=this.flowParseRestrictedIdentifier(!t,!0),this.scope.declareName(e.id.name,t?17:8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.extends=[],this.eat(81))do{e.extends.push(this.flowParseInterfaceExtends())}while(!t&&this.eat(12));if(t){if(e.implements=[],e.mixins=[],this.eatContextual(117))do{e.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(12));if(this.eatContextual(113))do{e.implements.push(this.flowParseInterfaceExtends())}while(this.eat(12))}e.body=this.flowParseObjectType({allowStatic:t,allowExact:!1,allowSpread:!1,allowProto:t,allowInexact:!1})}},{key:"flowParseInterfaceExtends",value:function(){var e=this.startNode();return e.id=this.flowParseQualifiedTypeIdentifier(),this.match(47)?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,this.finishNode(e,"InterfaceExtends")}},{key:"flowParseInterface",value:function(e){return this.flowParseInterfaceish(e,!1),this.finishNode(e,"InterfaceDeclaration")}},{key:"checkNotUnderscore",value:function(e){"_"===e&&this.raise(bt.UnexpectedReservedUnderscore,this.state.startLoc)}},{key:"checkReservedType",value:function(e,t,i){xt.has(e)&&this.raise(i?bt.AssignReservedType:bt.UnexpectedReservedType,t,{reservedType:e})}},{key:"flowParseRestrictedIdentifier",value:function(e,t){return this.checkReservedType(this.state.value,this.state.startLoc,t),this.parseIdentifier(e)}},{key:"flowParseTypeAlias",value:function(e){return e.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(e.id.name,8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.right=this.flowParseTypeInitialiser(29),this.semicolon(),this.finishNode(e,"TypeAlias")}},{key:"flowParseOpaqueType",value:function(e,t){return this.expectContextual(130),e.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(e.id.name,8201,e.id.loc.start),this.match(47)?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.supertype=null,this.match(14)&&(e.supertype=this.flowParseTypeInitialiser(14)),e.impltype=null,t||(e.impltype=this.flowParseTypeInitialiser(29)),this.semicolon(),this.finishNode(e,"OpaqueType")}},{key:"flowParseTypeParameter",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.state.startLoc,i=this.startNode(),r=this.flowParseVariance(),s=this.flowParseTypeAnnotatableIdentifier();return i.name=s.name,i.variance=r,i.bound=s.typeAnnotation,this.match(29)?(this.eat(29),i.default=this.flowParseType()):e&&this.raise(bt.MissingTypeParamDefault,t),this.finishNode(i,"TypeParameter")}},{key:"flowParseTypeParameterDeclaration",value:function(){var e=this.state.inType,t=this.startNode();t.params=[],this.state.inType=!0,this.match(47)||this.match(143)?this.next():this.unexpected();var i=!1;do{var r=this.flowParseTypeParameter(i);t.params.push(r),r.default&&(i=!0),this.match(48)||this.expect(12)}while(!this.match(48));return this.expect(48),this.state.inType=e,this.finishNode(t,"TypeParameterDeclaration")}},{key:"flowInTopLevelContext",value:function(e){if(this.curContext()===oe.brace)return e();var t=this.state.context;this.state.context=[t[0]];try{return e()}finally{this.state.context=t}}},{key:"flowParseTypeParameterInstantiationInExpression",value:function(){if(47===this.reScan_lt())return this.flowParseTypeParameterInstantiation()}},{key:"flowParseTypeParameterInstantiation",value:function(){var e=this,t=this.startNode(),i=this.state.inType;return this.state.inType=!0,t.params=[],this.flowInTopLevelContext((function(){e.expect(47);var i=e.state.noAnonFunctionType;e.state.noAnonFunctionType=!1;while(!e.match(48))t.params.push(e.flowParseType()),e.match(48)||e.expect(12);e.state.noAnonFunctionType=i})),this.state.inType=i,this.state.inType||this.curContext()!==oe.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(t,"TypeParameterInstantiation")}},{key:"flowParseTypeParameterInstantiationCallOrNew",value:function(){if(47===this.reScan_lt()){var e=this.startNode(),t=this.state.inType;e.params=[],this.state.inType=!0,this.expect(47);while(!this.match(48))e.params.push(this.flowParseTypeOrImplicitInstantiation()),this.match(48)||this.expect(12);return this.expect(48),this.state.inType=t,this.finishNode(e,"TypeParameterInstantiation")}}},{key:"flowParseInterfaceType",value:function(){var e=this.startNode();if(this.expectContextual(129),e.extends=[],this.eat(81))do{e.extends.push(this.flowParseInterfaceExtends())}while(this.eat(12));return e.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(e,"InterfaceTypeAnnotation")}},{key:"flowParseObjectPropertyKey",value:function(){return this.match(135)||this.match(134)?w(t,"parseExprAtom",this,3)([]):this.parseIdentifier(!0)}},{key:"flowParseObjectTypeIndexer",value:function(e,t,i){return e.static=t,14===this.lookahead().type?(e.id=this.flowParseObjectPropertyKey(),e.key=this.flowParseTypeInitialiser()):(e.id=null,e.key=this.flowParseType()),this.expect(3),e.value=this.flowParseTypeInitialiser(),e.variance=i,this.finishNode(e,"ObjectTypeIndexer")}},{key:"flowParseObjectTypeInternalSlot",value:function(e,t){return e.static=t,e.id=this.flowParseObjectPropertyKey(),this.expect(3),this.expect(3),this.match(47)||this.match(10)?(e.method=!0,e.optional=!1,e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start))):(e.method=!1,this.eat(17)&&(e.optional=!0),e.value=this.flowParseTypeInitialiser()),this.finishNode(e,"ObjectTypeInternalSlot")}},{key:"flowParseObjectTypeMethodish",value:function(e){e.params=[],e.rest=null,e.typeParameters=null,e.this=null,this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(10),this.match(78)&&(e.this=this.flowParseFunctionTypeParam(!0),e.this.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))e.params.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(e.rest=this.flowParseFunctionTypeParam(!1)),this.expect(11),e.returnType=this.flowParseTypeInitialiser(),this.finishNode(e,"FunctionTypeAnnotation")}},{key:"flowParseObjectTypeCallProperty",value:function(e,t){var i=this.startNode();return e.static=t,e.value=this.flowParseObjectTypeMethodish(i),this.finishNode(e,"ObjectTypeCallProperty")}},{key:"flowParseObjectType",value:function(e){var t=e.allowStatic,i=e.allowExact,r=e.allowSpread,s=e.allowProto,n=e.allowInexact,a=this.state.inType;this.state.inType=!0;var o,l,c=this.startNode();c.callProperties=[],c.properties=[],c.indexers=[],c.internalSlots=[];var h=!1;i&&this.match(6)?(this.expect(6),o=9,l=!0):(this.expect(5),o=8,l=!1),c.exact=l;while(!this.match(o)){var u=!1,p=null,d=null,f=this.startNode();if(s&&this.isContextual(118)){var m=this.lookahead();14!==m.type&&17!==m.type&&(this.next(),p=this.state.startLoc,t=!1)}if(t&&this.isContextual(106)){var y=this.lookahead();14!==y.type&&17!==y.type&&(this.next(),u=!0)}var v=this.flowParseVariance();if(this.eat(0))null!=p&&this.unexpected(p),this.eat(0)?(v&&this.unexpected(v.loc.start),c.internalSlots.push(this.flowParseObjectTypeInternalSlot(f,u))):c.indexers.push(this.flowParseObjectTypeIndexer(f,u,v));else if(this.match(10)||this.match(47))null!=p&&this.unexpected(p),v&&this.unexpected(v.loc.start),c.callProperties.push(this.flowParseObjectTypeCallProperty(f,u));else{var g="init";if(this.isContextual(99)||this.isContextual(104)){var x=this.lookahead();Oe(x.type)&&(g=this.state.value,this.next())}var b=this.flowParseObjectTypeProperty(f,u,p,v,g,r,null!=n?n:!l);null===b?(h=!0,d=this.state.lastTokStartLoc):c.properties.push(b)}this.flowObjectTypeSemicolon(),!d||this.match(8)||this.match(9)||this.raise(bt.UnexpectedExplicitInexactInObject,d)}this.expect(o),r&&(c.inexact=h);var P=this.finishNode(c,"ObjectTypeAnnotation");return this.state.inType=a,P}},{key:"flowParseObjectTypeProperty",value:function(e,t,i,r,s,n,a){if(this.eat(21)){var o=this.match(12)||this.match(13)||this.match(8)||this.match(9);return o?(n?a||this.raise(bt.InexactInsideExact,this.state.lastTokStartLoc):this.raise(bt.InexactInsideNonObject,this.state.lastTokStartLoc),r&&this.raise(bt.InexactVariance,r),null):(n||this.raise(bt.UnexpectedSpreadType,this.state.lastTokStartLoc),null!=i&&this.unexpected(i),r&&this.raise(bt.SpreadVariance,r),e.argument=this.flowParseType(),this.finishNode(e,"ObjectTypeSpreadProperty"))}e.key=this.flowParseObjectPropertyKey(),e.static=t,e.proto=null!=i,e.kind=s;var l=!1;return this.match(47)||this.match(10)?(e.method=!0,null!=i&&this.unexpected(i),r&&this.unexpected(r.loc.start),e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.loc.start)),"get"!==s&&"set"!==s||this.flowCheckGetterSetterParams(e),!n&&"constructor"===e.key.name&&e.value.this&&this.raise(bt.ThisParamBannedInConstructor,e.value.this)):("init"!==s&&this.unexpected(),e.method=!1,this.eat(17)&&(l=!0),e.value=this.flowParseTypeInitialiser(),e.variance=r),e.optional=l,this.finishNode(e,"ObjectTypeProperty")}},{key:"flowCheckGetterSetterParams",value:function(e){var t="get"===e.kind?0:1,i=e.value.params.length+(e.value.rest?1:0);e.value.this&&this.raise("get"===e.kind?bt.GetterMayNotHaveThisParam:bt.SetterMayNotHaveThisParam,e.value.this),i!==t&&this.raise("get"===e.kind?Z.BadGetterArity:Z.BadSetterArity,e),"set"===e.kind&&e.value.rest&&this.raise(Z.BadSetterRestParameter,e)}},{key:"flowObjectTypeSemicolon",value:function(){this.eat(13)||this.eat(12)||this.match(8)||this.match(9)||this.unexpected()}},{key:"flowParseQualifiedTypeIdentifier",value:function(e,t){null!=e||(e=this.state.startLoc);var i=t||this.flowParseRestrictedIdentifier(!0);while(this.eat(16)){var r=this.startNodeAt(e);r.qualification=i,r.id=this.flowParseRestrictedIdentifier(!0),i=this.finishNode(r,"QualifiedTypeIdentifier")}return i}},{key:"flowParseGenericType",value:function(e,t){var i=this.startNodeAt(e);return i.typeParameters=null,i.id=this.flowParseQualifiedTypeIdentifier(e,t),this.match(47)&&(i.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(i,"GenericTypeAnnotation")}},{key:"flowParseTypeofType",value:function(){var e=this.startNode();return this.expect(87),e.argument=this.flowParsePrimaryType(),this.finishNode(e,"TypeofTypeAnnotation")}},{key:"flowParseTupleType",value:function(){var e=this.startNode();e.types=[],this.expect(0);while(this.state.pos<this.length&&!this.match(3)){if(e.types.push(this.flowParseType()),this.match(3))break;this.expect(12)}return this.expect(3),this.finishNode(e,"TupleTypeAnnotation")}},{key:"flowParseFunctionTypeParam",value:function(e){var t=null,i=!1,r=null,s=this.startNode(),n=this.lookahead(),a=78===this.state.type;return 14===n.type||17===n.type?(a&&!e&&this.raise(bt.ThisParamMustBeFirst,s),t=this.parseIdentifier(a),this.eat(17)&&(i=!0,a&&this.raise(bt.ThisParamMayNotBeOptional,s)),r=this.flowParseTypeInitialiser()):r=this.flowParseType(),s.name=t,s.optional=i,s.typeAnnotation=r,this.finishNode(s,"FunctionTypeParam")}},{key:"reinterpretTypeAsFunctionTypeParam",value:function(e){var t=this.startNodeAt(e.loc.start);return t.name=null,t.optional=!1,t.typeAnnotation=e,this.finishNode(t,"FunctionTypeParam")}},{key:"flowParseFunctionTypeParams",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=null,i=null;this.match(78)&&(i=this.flowParseFunctionTypeParam(!0),i.name=null,this.match(11)||this.expect(12));while(!this.match(11)&&!this.match(21))e.push(this.flowParseFunctionTypeParam(!1)),this.match(11)||this.expect(12);return this.eat(21)&&(t=this.flowParseFunctionTypeParam(!1)),{params:e,rest:t,_this:i}}},{key:"flowIdentToTypeAnnotation",value:function(e,t,i){switch(i.name){case"any":return this.finishNode(t,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(t,"BooleanTypeAnnotation");case"mixed":return this.finishNode(t,"MixedTypeAnnotation");case"empty":return this.finishNode(t,"EmptyTypeAnnotation");case"number":return this.finishNode(t,"NumberTypeAnnotation");case"string":return this.finishNode(t,"StringTypeAnnotation");case"symbol":return this.finishNode(t,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(i.name),this.flowParseGenericType(e,i)}}},{key:"flowParsePrimaryType",value:function(){var e,i,r=this.state.startLoc,s=this.startNode(),n=!1,a=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case 6:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case 0:return this.state.noAnonFunctionType=!1,i=this.flowParseTupleType(),this.state.noAnonFunctionType=a,i;case 47:var o=this.startNode();return o.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(10),e=this.flowParseFunctionTypeParams(),o.params=e.params,o.rest=e.rest,o.this=e._this,this.expect(11),this.expect(19),o.returnType=this.flowParseType(),this.finishNode(o,"FunctionTypeAnnotation");case 10:var l=this.startNode();if(this.next(),!this.match(11)&&!this.match(21))if(Ce(this.state.type)||this.match(78)){var c=this.lookahead().type;n=17!==c&&14!==c}else n=!0;if(n){if(this.state.noAnonFunctionType=!1,i=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&19===this.lookahead().type))return this.expect(11),i;this.eat(12)}return e=i?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(i)]):this.flowParseFunctionTypeParams(),l.params=e.params,l.rest=e.rest,l.this=e._this,this.expect(11),this.expect(19),l.returnType=this.flowParseType(),l.typeParameters=null,this.finishNode(l,"FunctionTypeAnnotation");case 134:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case 85:case 86:return s.value=this.match(85),this.next(),this.finishNode(s,"BooleanLiteralTypeAnnotation");case 53:if("-"===this.state.value){if(this.next(),this.match(135))return this.parseLiteralAtNode(-this.state.value,"NumberLiteralTypeAnnotation",s);if(this.match(136))return this.parseLiteralAtNode(-this.state.value,"BigIntLiteralTypeAnnotation",s);throw this.raise(bt.UnexpectedSubtractionOperand,this.state.startLoc)}return void this.unexpected();case 135:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case 136:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case 88:return this.next(),this.finishNode(s,"VoidTypeAnnotation");case 84:return this.next(),this.finishNode(s,"NullLiteralTypeAnnotation");case 78:return this.next(),this.finishNode(s,"ThisTypeAnnotation");case 55:return this.next(),this.finishNode(s,"ExistsTypeAnnotation");case 87:return this.flowParseTypeofType();default:if(_e(this.state.type)){var h=ze(this.state.type);return this.next(),w(t,"createIdentifier",this,3)([s,h])}if(Ce(this.state.type))return this.isContextual(129)?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(r,s,this.parseIdentifier())}this.unexpected()}},{key:"flowParsePostfixType",value:function(){var e=this.state.startLoc,t=this.flowParsePrimaryType(),i=!1;while((this.match(0)||this.match(18))&&!this.canInsertSemicolon()){var r=this.startNodeAt(e),s=this.eat(18);i=i||s,this.expect(0),!s&&this.match(3)?(r.elementType=t,this.next(),t=this.finishNode(r,"ArrayTypeAnnotation")):(r.objectType=t,r.indexType=this.flowParseType(),this.expect(3),i?(r.optional=s,t=this.finishNode(r,"OptionalIndexedAccessType")):t=this.finishNode(r,"IndexedAccessType"))}return t}},{key:"flowParsePrefixType",value:function(){var e=this.startNode();return this.eat(17)?(e.typeAnnotation=this.flowParsePrefixType(),this.finishNode(e,"NullableTypeAnnotation")):this.flowParsePostfixType()}},{key:"flowParseAnonFunctionWithoutParens",value:function(){var e=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){var t=this.startNodeAt(e.loc.start);return t.params=[this.reinterpretTypeAsFunctionTypeParam(e)],t.rest=null,t.this=null,t.returnType=this.flowParseType(),t.typeParameters=null,this.finishNode(t,"FunctionTypeAnnotation")}return e}},{key:"flowParseIntersectionType",value:function(){var e=this.startNode();this.eat(45);var t=this.flowParseAnonFunctionWithoutParens();e.types=[t];while(this.eat(45))e.types.push(this.flowParseAnonFunctionWithoutParens());return 1===e.types.length?t:this.finishNode(e,"IntersectionTypeAnnotation")}},{key:"flowParseUnionType",value:function(){var e=this.startNode();this.eat(43);var t=this.flowParseIntersectionType();e.types=[t];while(this.eat(43))e.types.push(this.flowParseIntersectionType());return 1===e.types.length?t:this.finishNode(e,"UnionTypeAnnotation")}},{key:"flowParseType",value:function(){var e=this.state.inType;this.state.inType=!0;var t=this.flowParseUnionType();return this.state.inType=e,t}},{key:"flowParseTypeOrImplicitInstantiation",value:function(){if(132===this.state.type&&"_"===this.state.value){var e=this.state.startLoc,t=this.parseIdentifier();return this.flowParseGenericType(e,t)}return this.flowParseType()}},{key:"flowParseTypeAnnotation",value:function(){var e=this.startNode();return e.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(e,"TypeAnnotation")}},{key:"flowParseTypeAnnotatableIdentifier",value:function(e){var t=e?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(14)&&(t.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t)),t}},{key:"typeCastToParameter",value:function(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}},{key:"flowParseVariance",value:function(){var e=null;return this.match(53)?(e=this.startNode(),"+"===this.state.value?e.kind="plus":e.kind="minus",this.next(),this.finishNode(e,"Variance")):e}},{key:"parseFunctionBody",value:function(e,i){var r=this,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];i?this.forwardNoArrowParamsConversionAt(e,(function(){return w(t,"parseFunctionBody",r,3)([e,!0,s])})):w(t,"parseFunctionBody",this,3)([e,!1,s])}},{key:"parseFunctionBodyAndFinish",value:function(e,i){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.match(14)){var s=this.startNode(),n=this.flowParseTypeAndPredicateInitialiser(),a=f(n,2);s.typeAnnotation=a[0],e.predicate=a[1],e.returnType=s.typeAnnotation?this.finishNode(s,"TypeAnnotation"):null}return w(t,"parseFunctionBodyAndFinish",this,3)([e,i,r])}},{key:"parseStatementLike",value:function(e){if(this.state.strict&&this.isContextual(129)){var i=this.lookahead();if(Ne(i.type)){var r=this.startNode();return this.next(),this.flowParseInterface(r)}}else if(this.isContextual(126)){var s=this.startNode();return this.next(),this.flowParseEnumDeclaration(s)}var n=w(t,"parseStatementLike",this,3)([e]);return void 0!==this.flowPragma||this.isValidDirective(n)||(this.flowPragma=null),n}},{key:"parseExpressionStatement",value:function(e,i,r){if("Identifier"===i.type)if("declare"===i.name){if(this.match(80)||Ce(this.state.type)||this.match(68)||this.match(74)||this.match(82))return this.flowParseDeclare(e)}else if(Ce(this.state.type)){if("interface"===i.name)return this.flowParseInterface(e);if("type"===i.name)return this.flowParseTypeAlias(e);if("opaque"===i.name)return this.flowParseOpaqueType(e,!1)}return w(t,"parseExpressionStatement",this,3)([e,i,r])}},{key:"shouldParseExportDeclaration",value:function(){var e=this.state.type;return 126===e||Fe(e)?!this.state.containsEsc:w(t,"shouldParseExportDeclaration",this,3)([])}},{key:"isExportDefaultSpecifier",value:function(){var e=this.state.type;return 126===e||Fe(e)?this.state.containsEsc:w(t,"isExportDefaultSpecifier",this,3)([])}},{key:"parseExportDefaultExpression",value:function(){if(this.isContextual(126)){var e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return w(t,"parseExportDefaultExpression",this,3)([])}},{key:"parseConditional",value:function(e,t,i){var r=this;if(!this.match(17))return e;if(this.state.maybeInArrowParameters){var s=this.lookaheadCharCode();if(44===s||61===s||58===s||41===s)return this.setOptionalParametersError(i),e}this.expect(17);var n=this.state.clone(),a=this.state.noArrowAt,o=this.startNodeAt(t),l=this.tryParseConditionalConsequent(),c=l.consequent,u=l.failed,p=this.getArrowLikeExpressions(c),d=f(p,2),m=d[0],y=d[1];if(u||y.length>0){var v=h(a);if(y.length>0){this.state=n,this.state.noArrowAt=v;for(var g=0;g<y.length;g++)v.push(y[g].start);var x=this.tryParseConditionalConsequent();c=x.consequent,u=x.failed;var b=this.getArrowLikeExpressions(c),P=f(b,2);m=P[0],y=P[1]}if(u&&m.length>1&&this.raise(bt.AmbiguousConditionalArrow,n.startLoc),u&&1===m.length){this.state=n,v.push(m[0].start),this.state.noArrowAt=v;var k=this.tryParseConditionalConsequent();c=k.consequent,u=k.failed}}return this.getArrowLikeExpressions(c,!0),this.state.noArrowAt=a,this.expect(14),o.test=e,o.consequent=c,o.alternate=this.forwardNoArrowParamsConversionAt(o,(function(){return r.parseMaybeAssign(void 0,void 0)})),this.finishNode(o,"ConditionalExpression")}},{key:"tryParseConditionalConsequent",value:function(){this.state.noArrowParamsConversionAt.push(this.state.start);var e=this.parseMaybeAssignAllowIn(),t=!this.match(14);return this.state.noArrowParamsConversionAt.pop(),{consequent:e,failed:t}}},{key:"getArrowLikeExpressions",value:function(e,t){var i=this,r=[e],s=[];while(0!==r.length){var n=r.pop();"ArrowFunctionExpression"===n.type&&"BlockStatement"!==n.body.type?(n.typeParameters||!n.returnType?this.finishArrowValidation(n):s.push(n),r.push(n.body)):"ConditionalExpression"===n.type&&(r.push(n.consequent),r.push(n.alternate))}return t?(s.forEach((function(e){return i.finishArrowValidation(e)})),[s,[]]):wt(s,(function(e){return e.params.every((function(e){return i.isAssignable(e,!0)}))}))}},{key:"finishArrowValidation",value:function(e){var i;this.toAssignableList(e.params,null==(i=e.extra)?void 0:i.trailingCommaLoc,!1),this.scope.enter(518),w(t,"checkParams",this,3)([e,!1,!0]),this.scope.exit()}},{key:"forwardNoArrowParamsConversionAt",value:function(e,t){var i;return this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start))?(this.state.noArrowParamsConversionAt.push(this.state.start),i=t(),this.state.noArrowParamsConversionAt.pop()):i=t(),i}},{key:"parseParenItem",value:function(e,i){var r=w(t,"parseParenItem",this,3)([e,i]);if(this.eat(17)&&(r.optional=!0,this.resetEndLocation(e)),this.match(14)){var s=this.startNodeAt(i);return s.expression=r,s.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(s,"TypeCastExpression")}return r}},{key:"assertModuleNodeAllowed",value:function(e){"ImportDeclaration"===e.type&&("type"===e.importKind||"typeof"===e.importKind)||"ExportNamedDeclaration"===e.type&&"type"===e.exportKind||"ExportAllDeclaration"===e.type&&"type"===e.exportKind||w(t,"assertModuleNodeAllowed",this,3)([e])}},{key:"parseExportDeclaration",value:function(e){if(this.isContextual(130)){e.exportKind="type";var i=this.startNode();return this.next(),this.match(5)?(e.specifiers=this.parseExportSpecifiers(!0),w(t,"parseExportFrom",this,3)([e]),null):this.flowParseTypeAlias(i)}if(this.isContextual(131)){e.exportKind="type";var r=this.startNode();return this.next(),this.flowParseOpaqueType(r,!1)}if(this.isContextual(129)){e.exportKind="type";var s=this.startNode();return this.next(),this.flowParseInterface(s)}if(this.isContextual(126)){e.exportKind="value";var n=this.startNode();return this.next(),this.flowParseEnumDeclaration(n)}return w(t,"parseExportDeclaration",this,3)([e])}},{key:"eatExportStar",value:function(e){return!!w(t,"eatExportStar",this,3)([e])||!(!this.isContextual(130)||55!==this.lookahead().type)&&(e.exportKind="type",this.next(),this.next(),!0)}},{key:"maybeParseExportNamespaceSpecifier",value:function(e){var i=this.state.startLoc,r=w(t,"maybeParseExportNamespaceSpecifier",this,3)([e]);return r&&"type"===e.exportKind&&this.unexpected(i),r}},{key:"parseClassId",value:function(e,i,r){w(t,"parseClassId",this,3)([e,i,r]),this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration())}},{key:"parseClassMember",value:function(e,i,r){var s=this.state.startLoc;if(this.isContextual(125)){if(w(t,"parseClassMemberFromModifier",this,3)([e,i]))return;i.declare=!0}w(t,"parseClassMember",this,3)([e,i,r]),i.declare&&("ClassProperty"!==i.type&&"ClassPrivateProperty"!==i.type&&"PropertyDefinition"!==i.type?this.raise(bt.DeclareClassElement,s):i.value&&this.raise(bt.DeclareClassFieldInitializer,i.value))}},{key:"isIterator",value:function(e){return"iterator"===e||"asyncIterator"===e}},{key:"readIterator",value:function(){var e=w(t,"readWord1",this,3)([]),i="@@"+e;this.isIterator(e)&&this.state.inType||this.raise(Z.InvalidIdentifier,this.state.curPosition(),{identifierName:i}),this.finishToken(132,i)}},{key:"getTokenFromCode",value:function(e){var i=this.input.charCodeAt(this.state.pos+1);123===e&&124===i?this.finishOp(6,2):!this.state.inType||62!==e&&60!==e?this.state.inType&&63===e?46===i?this.finishOp(18,2):this.finishOp(17,1):pt(e,i,this.input.charCodeAt(this.state.pos+2))?(this.state.pos+=2,this.readIterator()):w(t,"getTokenFromCode",this,3)([e]):this.finishOp(62===e?48:47,1)}},{key:"isAssignable",value:function(e,i){return"TypeCastExpression"===e.type?this.isAssignable(e.expression,i):w(t,"isAssignable",this,3)([e,i])}},{key:"toAssignable",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];i||"AssignmentExpression"!==e.type||"TypeCastExpression"!==e.left.type||(e.left=this.typeCastToParameter(e.left)),w(t,"toAssignable",this,3)([e,i])}},{key:"toAssignableList",value:function(e,i,r){for(var s=0;s<e.length;s++){var n=e[s];"TypeCastExpression"===(null==n?void 0:n.type)&&(e[s]=this.typeCastToParameter(n))}w(t,"toAssignableList",this,3)([e,i,r])}},{key:"toReferencedList",value:function(e,t){for(var i=0;i<e.length;i++){var r,s=e[i];!s||"TypeCastExpression"!==s.type||null!=(r=s.extra)&&r.parenthesized||!(e.length>1)&&t||this.raise(bt.TypeCastInPattern,s.typeAnnotation)}return e}},{key:"parseArrayLike",value:function(e,i,r,s){var n=w(t,"parseArrayLike",this,3)([e,i,r,s]);return i&&!this.state.maybeInArrowParameters&&this.toReferencedList(n.elements),n}},{key:"isValidLVal",value:function(e,i,r){return"TypeCastExpression"===e||w(t,"isValidLVal",this,3)([e,i,r])}},{key:"parseClassProperty",value:function(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),w(t,"parseClassProperty",this,3)([e])}},{key:"parseClassPrivateProperty",value:function(e){return this.match(14)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),w(t,"parseClassPrivateProperty",this,3)([e])}},{key:"isClassMethod",value:function(){return this.match(47)||w(t,"isClassMethod",this,3)([])}},{key:"isClassProperty",value:function(){return this.match(14)||w(t,"isClassProperty",this,3)([])}},{key:"isNonstaticConstructor",value:function(e){return!this.match(14)&&w(t,"isNonstaticConstructor",this,3)([e])}},{key:"pushClassMethod",value:function(e,i,r,s,n,a){if(i.variance&&this.unexpected(i.variance.loc.start),delete i.variance,this.match(47)&&(i.typeParameters=this.flowParseTypeParameterDeclaration()),w(t,"pushClassMethod",this,3)([e,i,r,s,n,a]),i.params&&n){var o=i.params;o.length>0&&this.isThisParam(o[0])&&this.raise(bt.ThisParamBannedInConstructor,i)}else if("MethodDefinition"===i.type&&n&&i.value.params){var l=i.value.params;l.length>0&&this.isThisParam(l[0])&&this.raise(bt.ThisParamBannedInConstructor,i)}}},{key:"pushClassPrivateMethod",value:function(e,i,r,s){i.variance&&this.unexpected(i.variance.loc.start),delete i.variance,this.match(47)&&(i.typeParameters=this.flowParseTypeParameterDeclaration()),w(t,"pushClassPrivateMethod",this,3)([e,i,r,s])}},{key:"parseClassSuper",value:function(e){if(w(t,"parseClassSuper",this,3)([e]),e.superClass&&(this.match(47)||this.match(51))&&(e.superTypeParameters=this.flowParseTypeParameterInstantiationInExpression()),this.isContextual(113)){this.next();var i=e.implements=[];do{var r=this.startNode();r.id=this.flowParseRestrictedIdentifier(!0),this.match(47)?r.typeParameters=this.flowParseTypeParameterInstantiation():r.typeParameters=null,i.push(this.finishNode(r,"ClassImplements"))}while(this.eat(12))}}},{key:"checkGetterSetterParams",value:function(e){w(t,"checkGetterSetterParams",this,3)([e]);var i=this.getObjectOrClassMethodParams(e);if(i.length>0){var r=i[0];this.isThisParam(r)&&"get"===e.kind?this.raise(bt.GetterMayNotHaveThisParam,r):this.isThisParam(r)&&this.raise(bt.SetterMayNotHaveThisParam,r)}}},{key:"parsePropertyNamePrefixOperator",value:function(e){e.variance=this.flowParseVariance()}},{key:"parseObjPropValue",value:function(e,i,r,s,n,a,o){var l;e.variance&&this.unexpected(e.variance.loc.start),delete e.variance,this.match(47)&&!a&&(l=this.flowParseTypeParameterDeclaration(),this.match(10)||this.unexpected());var c=w(t,"parseObjPropValue",this,3)([e,i,r,s,n,a,o]);return l&&((c.value||c).typeParameters=l),c}},{key:"parseFunctionParamType",value:function(e){return this.eat(17)&&("Identifier"!==e.type&&this.raise(bt.PatternIsOptional,e),this.isThisParam(e)&&this.raise(bt.ThisParamMayNotBeOptional,e),e.optional=!0),this.match(14)?e.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(e)&&this.raise(bt.ThisParamAnnotationRequired,e),this.match(29)&&this.isThisParam(e)&&this.raise(bt.ThisParamNoDefault,e),this.resetEndLocation(e),e}},{key:"parseMaybeDefault",value:function(e,i){var r=w(t,"parseMaybeDefault",this,3)([e,i]);return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(bt.TypeBeforeInitializer,r.typeAnnotation),r}},{key:"checkImportReflection",value:function(e){w(t,"checkImportReflection",this,3)([e]),e.module&&"value"!==e.importKind&&this.raise(bt.ImportReflectionHasImportType,e.specifiers[0].loc.start)}},{key:"parseImportSpecifierLocal",value:function(e,t,i){t.local=kt(e)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),e.specifiers.push(this.finishImportSpecifier(t,i))}},{key:"isPotentialImportPhase",value:function(e){if(w(t,"isPotentialImportPhase",this,3)([e]))return!0;if(this.isContextual(130)){if(!e)return!0;var i=this.lookaheadCharCode();return 123===i||42===i}return!e&&this.isContextual(87)}},{key:"applyImportPhase",value:function(e,i,r,s){if(w(t,"applyImportPhase",this,3)([e,i,r,s]),i){if(!r&&this.match(65))return;e.exportKind="type"===r?r:"value"}else"type"===r&&this.match(55)&&this.unexpected(),e.importKind="type"===r||"typeof"===r?r:"value"}},{key:"parseImportSpecifier",value:function(e,t,i,r,s){var n=e.imported,a=null;"Identifier"===n.type&&("type"===n.name?a="type":"typeof"===n.name&&(a="typeof"));var o=!1;if(this.isContextual(93)&&!this.isLookaheadContextual("as")){var l=this.parseIdentifier(!0);null===a||Ne(this.state.type)?(e.imported=n,e.importKind=null,e.local=this.parseIdentifier()):(e.imported=l,e.importKind=a,e.local=this.cloneIdentifier(l))}else{if(null!==a&&Ne(this.state.type))e.imported=this.parseIdentifier(!0),e.importKind=a;else{if(t)throw this.raise(Z.ImportBindingIsString,e,{importName:n.value});e.imported=n,e.importKind=null}this.eatContextual(93)?e.local=this.parseIdentifier():(o=!0,e.local=this.cloneIdentifier(e.imported))}var c=kt(e);return i&&c&&this.raise(bt.ImportTypeShorthandOnlyInPureImport,e),(i||c)&&this.checkReservedType(e.local.name,e.local.loc.start,!0),!o||i||c||this.checkReservedWord(e.local.name,e.loc.start,!0,!0),this.finishImportSpecifier(e,"ImportSpecifier")}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case 78:return this.parseIdentifier(!0);default:return w(t,"parseBindingAtom",this,3)([])}}},{key:"parseFunctionParams",value:function(e,i){var r=e.kind;"get"!==r&&"set"!==r&&this.match(47)&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),w(t,"parseFunctionParams",this,3)([e,i])}},{key:"parseVarId",value:function(e,i){w(t,"parseVarId",this,3)([e,i]),this.match(14)&&(e.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(e,i){if(this.match(14)){var r=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,e.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=r}return w(t,"parseAsyncArrowFromCallExpression",this,3)([e,i])}},{key:"shouldParseAsyncArrow",value:function(){return this.match(14)||w(t,"shouldParseAsyncArrow",this,3)([])}},{key:"parseMaybeAssign",value:function(e,i){var r,s,n=this,a=null;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(a=this.state.clone(),s=this.tryParse((function(){return w(t,"parseMaybeAssign",n,3)([e,i])}),a),!s.error)return s.node;var o=this.state.context,l=o[o.length-1];l!==oe.j_oTag&&l!==oe.j_expr||o.pop()}if(null!=(r=s)&&r.error||this.match(47)){var c,h,u;a=a||this.state.clone();var p=this.tryParse((function(r){var s;u=n.flowParseTypeParameterDeclaration();var a=n.forwardNoArrowParamsConversionAt(u,(function(){var r=w(t,"parseMaybeAssign",n,3)([e,i]);return n.resetStartLocationFromNode(r,u),r}));null!=(s=a.extra)&&s.parenthesized&&r();var o=n.maybeUnwrapTypeCastExpression(a);return"ArrowFunctionExpression"!==o.type&&r(),o.typeParameters=u,n.resetStartLocationFromNode(o,u),a}),a),d=null;if(p.node&&"ArrowFunctionExpression"===this.maybeUnwrapTypeCastExpression(p.node).type){if(!p.error&&!p.aborted)return p.node.async&&this.raise(bt.UnexpectedTypeParameterBeforeAsyncArrowFunction,u),p.node;d=p.node}if(null!=(c=s)&&c.node)return this.state=s.failState,s.node;if(d)return this.state=p.failState,d;if(null!=(h=s)&&h.thrown)throw s.error;if(p.thrown)throw p.error;throw this.raise(bt.UnexpectedTokenAfterTypeParameter,u)}return w(t,"parseMaybeAssign",this,3)([e,i])}},{key:"parseArrow",value:function(e){var i=this;if(this.match(14)){var r=this.tryParse((function(){var t=i.state.noAnonFunctionType;i.state.noAnonFunctionType=!0;var r=i.startNode(),s=i.flowParseTypeAndPredicateInitialiser(),n=f(s,2);return r.typeAnnotation=n[0],e.predicate=n[1],i.state.noAnonFunctionType=t,i.canInsertSemicolon()&&i.unexpected(),i.match(19)||i.unexpected(),r}));if(r.thrown)return null;r.error&&(this.state=r.failState),e.returnType=r.node.typeAnnotation?this.finishNode(r.node,"TypeAnnotation"):null}return w(t,"parseArrow",this,3)([e])}},{key:"shouldParseArrow",value:function(e){return this.match(14)||w(t,"shouldParseArrow",this,3)([e])}},{key:"setArrowFunctionParameters",value:function(e,i){this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start))?e.params=i:w(t,"setArrowFunctionParameters",this,3)([e,i])}},{key:"checkParams",value:function(e,i,r){var s=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(!r||!this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(e.start))){for(var n=0;n<e.params.length;n++)this.isThisParam(e.params[n])&&n>0&&this.raise(bt.ThisParamMustBeFirst,e.params[n]);w(t,"checkParams",this,3)([e,i,r,s])}}},{key:"parseParenAndDistinguishExpression",value:function(e){return w(t,"parseParenAndDistinguishExpression",this,3)([e&&!this.state.noArrowAt.includes(this.sourceToOffsetPos(this.state.start))])}},{key:"parseSubscripts",value:function(e,i,r){var s=this;if("Identifier"===e.type&&"async"===e.name&&this.state.noArrowAt.includes(i.index)){this.next();var n=this.startNodeAt(i);n.callee=e,n.arguments=w(t,"parseCallExpressionArguments",this,3)([]),e=this.finishNode(n,"CallExpression")}else if("Identifier"===e.type&&"async"===e.name&&this.match(47)){var a=this.state.clone(),o=this.tryParse((function(e){return s.parseAsyncArrowWithTypeParameters(i)||e()}),a);if(!o.error&&!o.aborted)return o.node;var l=this.tryParse((function(){return w(t,"parseSubscripts",s,3)([e,i,r])}),a);if(l.node&&!l.error)return l.node;if(o.node)return this.state=o.failState,o.node;if(l.node)return this.state=l.failState,l.node;throw o.error||l.error}return w(t,"parseSubscripts",this,3)([e,i,r])}},{key:"parseSubscript",value:function(e,i,r,s){var n=this;if(this.match(18)&&this.isLookaheadToken_lt()){if(s.optionalChainMember=!0,r)return s.stop=!0,e;this.next();var a=this.startNodeAt(i);return a.callee=e,a.typeArguments=this.flowParseTypeParameterInstantiationInExpression(),this.expect(10),a.arguments=this.parseCallExpressionArguments(),a.optional=!0,this.finishCallExpression(a,!0)}if(!r&&this.shouldParseTypes()&&(this.match(47)||this.match(51))){var o=this.startNodeAt(i);o.callee=e;var l=this.tryParse((function(){return o.typeArguments=n.flowParseTypeParameterInstantiationCallOrNew(),n.expect(10),o.arguments=w(t,"parseCallExpressionArguments",n,3)([]),s.optionalChainMember&&(o.optional=!1),n.finishCallExpression(o,s.optionalChainMember)}));if(l.node)return l.error&&(this.state=l.failState),l.node}return w(t,"parseSubscript",this,3)([e,i,r,s])}},{key:"parseNewCallee",value:function(e){var i=this;w(t,"parseNewCallee",this,3)([e]);var r=null;this.shouldParseTypes()&&this.match(47)&&(r=this.tryParse((function(){return i.flowParseTypeParameterInstantiationCallOrNew()})).node),e.typeArguments=r}},{key:"parseAsyncArrowWithTypeParameters",value:function(e){var i=this.startNodeAt(e);if(this.parseFunctionParams(i,!1),this.parseArrow(i))return w(t,"parseArrowExpression",this,3)([i,void 0,!0])}},{key:"readToken_mult_modulo",value:function(e){var i=this.input.charCodeAt(this.state.pos+1);if(42===e&&47===i&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();w(t,"readToken_mult_modulo",this,3)([e])}},{key:"readToken_pipe_amp",value:function(e){var i=this.input.charCodeAt(this.state.pos+1);124!==e||125!==i?w(t,"readToken_pipe_amp",this,3)([e]):this.finishOp(9,2)}},{key:"parseTopLevel",value:function(e,i){var r=w(t,"parseTopLevel",this,3)([e,i]);return this.state.hasFlowComment&&this.raise(bt.UnterminatedFlowComment,this.state.curPosition()),r}},{key:"skipBlockComment",value:function(){if(!this.hasPlugin("flowComments")||!this.skipFlowComment())return w(t,"skipBlockComment",this,3)([this.state.hasFlowComment?"*-/":"*/"]);if(this.state.hasFlowComment)throw this.raise(bt.NestedFlowComment,this.state.startLoc);this.hasFlowCommentCompletion();var e=this.skipFlowComment();e&&(this.state.pos+=e,this.state.hasFlowComment=!0)}},{key:"skipFlowComment",value:function(){var e=this.state.pos,t=2;while([32,9].includes(this.input.charCodeAt(e+t)))t++;var i=this.input.charCodeAt(t+e),r=this.input.charCodeAt(t+e+1);return 58===i&&58===r?t+2:"flow-include"===this.input.slice(t+e,t+e+12)?t+12:58===i&&58!==r&&t}},{key:"hasFlowCommentCompletion",value:function(){var e=this.input.indexOf("*/",this.state.pos);if(-1===e)throw this.raise(Z.UnterminatedComment,this.state.curPosition())}},{key:"flowEnumErrorBooleanMemberNotInitialized",value:function(e,t){var i=t.enumName,r=t.memberName;this.raise(bt.EnumBooleanMemberNotInitialized,e,{memberName:r,enumName:i})}},{key:"flowEnumErrorInvalidMemberInitializer",value:function(e,t){return this.raise(t.explicitType?"symbol"===t.explicitType?bt.EnumInvalidMemberInitializerSymbolType:bt.EnumInvalidMemberInitializerPrimaryType:bt.EnumInvalidMemberInitializerUnknownType,e,t)}},{key:"flowEnumErrorNumberMemberNotInitialized",value:function(e,t){this.raise(bt.EnumNumberMemberNotInitialized,e,t)}},{key:"flowEnumErrorStringMemberInconsistentlyInitialized",value:function(e,t){this.raise(bt.EnumStringMemberInconsistentlyInitialized,e,t)}},{key:"flowEnumMemberInit",value:function(){var e=this,t=this.state.startLoc,i=function(){return e.match(12)||e.match(8)};switch(this.state.type){case 135:var r=this.parseNumericLiteral(this.state.value);return i()?{type:"number",loc:r.loc.start,value:r}:{type:"invalid",loc:t};case 134:var s=this.parseStringLiteral(this.state.value);return i()?{type:"string",loc:s.loc.start,value:s}:{type:"invalid",loc:t};case 85:case 86:var n=this.parseBooleanLiteral(this.match(85));return i()?{type:"boolean",loc:n.loc.start,value:n}:{type:"invalid",loc:t};default:return{type:"invalid",loc:t}}}},{key:"flowEnumMemberRaw",value:function(){var e=this.state.startLoc,t=this.parseIdentifier(!0),i=this.eat(29)?this.flowEnumMemberInit():{type:"none",loc:e};return{id:t,init:i}}},{key:"flowEnumCheckExplicitTypeMismatch",value:function(e,t,i){var r=t.explicitType;null!==r&&r!==i&&this.flowEnumErrorInvalidMemberInitializer(e,t)}},{key:"flowEnumMembers",value:function(e){var t=e.enumName,i=e.explicitType,r=new Set,s={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]},n=!1;while(!this.match(8)){if(this.eat(21)){n=!0;break}var a=this.startNode(),o=this.flowEnumMemberRaw(),l=o.id,c=o.init,h=l.name;if(""!==h){/^[a-z]/.test(h)&&this.raise(bt.EnumInvalidMemberName,l,{memberName:h,suggestion:h[0].toUpperCase()+h.slice(1),enumName:t}),r.has(h)&&this.raise(bt.EnumDuplicateMemberName,l,{memberName:h,enumName:t}),r.add(h);var u={enumName:t,explicitType:i,memberName:h};switch(a.id=l,c.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"boolean"),a.init=c.value,s.booleanMembers.push(this.finishNode(a,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"number"),a.init=c.value,s.numberMembers.push(this.finishNode(a,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(c.loc,u,"string"),a.init=c.value,s.stringMembers.push(this.finishNode(a,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(c.loc,u);case"none":switch(i){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(c.loc,u);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(c.loc,u);break;default:s.defaultedMembers.push(this.finishNode(a,"EnumDefaultedMember"))}}this.match(8)||this.expect(12)}}return{members:s,hasUnknownMembers:n}}},{key:"flowEnumStringMembers",value:function(e,t,i){var r=i.enumName;if(0===e.length)return t;if(0===t.length)return e;if(t.length>e.length){var s,n=c(e);try{for(n.s();!(s=n.n()).done;){var a=s.value;this.flowEnumErrorStringMemberInconsistentlyInitialized(a,{enumName:r})}}catch(u){n.e(u)}finally{n.f()}return t}var o,l=c(t);try{for(l.s();!(o=l.n()).done;){var h=o.value;this.flowEnumErrorStringMemberInconsistentlyInitialized(h,{enumName:r})}}catch(u){l.e(u)}finally{l.f()}return e}},{key:"flowEnumParseExplicitType",value:function(e){var t=e.enumName;if(!this.eatContextual(102))return null;if(!Ce(this.state.type))throw this.raise(bt.EnumInvalidExplicitTypeUnknownSupplied,this.state.startLoc,{enumName:t});var i=this.state.value;return this.next(),"boolean"!==i&&"number"!==i&&"string"!==i&&"symbol"!==i&&this.raise(bt.EnumInvalidExplicitType,this.state.startLoc,{enumName:t,invalidEnumType:i}),i}},{key:"flowEnumBody",value:function(e,t){var i=this,r=t.name,s=t.loc.start,n=this.flowEnumParseExplicitType({enumName:r});this.expect(5);var a=this.flowEnumMembers({enumName:r,explicitType:n}),o=a.members,l=a.hasUnknownMembers;switch(e.hasUnknownMembers=l,n){case"boolean":return e.explicitType=!0,e.members=o.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody");case"number":return e.explicitType=!0,e.members=o.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody");case"string":return e.explicitType=!0,e.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:r}),this.expect(8),this.finishNode(e,"EnumStringBody");case"symbol":return e.members=o.defaultedMembers,this.expect(8),this.finishNode(e,"EnumSymbolBody");default:var h=function(){return e.members=[],i.expect(8),i.finishNode(e,"EnumStringBody")};e.explicitType=!1;var u=o.booleanMembers.length,p=o.numberMembers.length,d=o.stringMembers.length,f=o.defaultedMembers.length;if(u||p||d||f){if(u||p){if(!p&&!d&&u>=f){var m,y=c(o.defaultedMembers);try{for(y.s();!(m=y.n()).done;){var v=m.value;this.flowEnumErrorBooleanMemberNotInitialized(v.loc.start,{enumName:r,memberName:v.id.name})}}catch(P){y.e(P)}finally{y.f()}return e.members=o.booleanMembers,this.expect(8),this.finishNode(e,"EnumBooleanBody")}if(!u&&!d&&p>=f){var g,x=c(o.defaultedMembers);try{for(x.s();!(g=x.n()).done;){var b=g.value;this.flowEnumErrorNumberMemberNotInitialized(b.loc.start,{enumName:r,memberName:b.id.name})}}catch(P){x.e(P)}finally{x.f()}return e.members=o.numberMembers,this.expect(8),this.finishNode(e,"EnumNumberBody")}return this.raise(bt.EnumInconsistentMemberValues,s,{enumName:r}),h()}return e.members=this.flowEnumStringMembers(o.stringMembers,o.defaultedMembers,{enumName:r}),this.expect(8),this.finishNode(e,"EnumStringBody")}return h()}}},{key:"flowParseEnumDeclaration",value:function(e){var t=this.parseIdentifier();return e.id=t,e.body=this.flowEnumBody(this.startNode(),t),this.finishNode(e,"EnumDeclaration")}},{key:"jsxParseOpeningElementAfterName",value:function(e){return this.shouldParseTypes()&&(this.match(47)||this.match(51))&&(e.typeArguments=this.flowParseTypeParameterInstantiationInExpression()),w(t,"jsxParseOpeningElementAfterName",this,3)([e])}},{key:"isLookaheadToken_lt",value:function(){var e=this.nextTokenStart();if(60===this.input.charCodeAt(e)){var t=this.input.charCodeAt(e+1);return 60!==t&&61!==t}return!1}},{key:"reScan_lt_gt",value:function(){var e=this.state.type;47===e?(this.state.pos-=1,this.readToken_lt()):48===e&&(this.state.pos-=1,this.readToken_gt())}},{key:"reScan_lt",value:function(){var e=this.state.type;return 51===e?(this.state.pos-=2,this.finishOp(47,1),47):e}},{key:"maybeUnwrapTypeCastExpression",value:function(e){return"TypeCastExpression"===e.type?e.expression:e}}])}(e)},Et={__proto__:null,quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},Ct=/\r\n|[\r\n\u2028\u2029]/,It=new RegExp(Ct.source,"g");function Nt(e){switch(e){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}function Ot(e,t,i){for(var r=t;r<i;r++)if(Nt(e.charCodeAt(r)))return!0;return!1}var Lt=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,Dt=/(?:[^\S\n\r\u2028\u2029]|\/\/.*|\/\*.*?\*\/)*/g;function Mt(e){switch(e){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}var Ft=Q(a||(a=N(["jsx"])))({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:function(e){var t=e.openingTagName;return"Expected corresponding JSX closing tag for <".concat(t,">.")},MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnexpectedToken:function(e){var t=e.unexpected,i=e.HTMLEntity;return"Unexpected token `".concat(t,"`. Did you mean `").concat(i,"` or `{'").concat(t,"'}`?")},UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"});function Bt(e){return!!e&&("JSXOpeningFragment"===e.type||"JSXClosingFragment"===e.type)}function _t(e){if("JSXIdentifier"===e.type)return e.name;if("JSXNamespacedName"===e.type)return e.namespace.name+":"+e.name.name;if("JSXMemberExpression"===e.type)return _t(e.object)+"."+_t(e.property);throw new Error("Node had unexpected type: "+e.type)}var jt=function(e){return function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"jsxReadToken",value:function(){for(var e="",i=this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(Ft.UnterminatedJsxContent,this.state.startLoc);var r=this.input.charCodeAt(this.state.pos);switch(r){case 60:case 123:return this.state.pos===this.state.start?void(60===r&&this.state.canStartJSXElement?(++this.state.pos,this.finishToken(143)):w(t,"getTokenFromCode",this,3)([r])):(e+=this.input.slice(i,this.state.pos),void this.finishToken(142,e));case 38:e+=this.input.slice(i,this.state.pos),e+=this.jsxReadEntity(),i=this.state.pos;break;case 62:case 125:default:Nt(r)?(e+=this.input.slice(i,this.state.pos),e+=this.jsxReadNewLine(!0),i=this.state.pos):++this.state.pos}}}},{key:"jsxReadNewLine",value:function(e){var t,i=this.input.charCodeAt(this.state.pos);return++this.state.pos,13===i&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,t=e?"\n":"\r\n"):t=String.fromCharCode(i),++this.state.curLine,this.state.lineStart=this.state.pos,t}},{key:"jsxReadString",value:function(e){for(var t="",i=++this.state.pos;;){if(this.state.pos>=this.length)throw this.raise(Z.UnterminatedString,this.state.startLoc);var r=this.input.charCodeAt(this.state.pos);if(r===e)break;38===r?(t+=this.input.slice(i,this.state.pos),t+=this.jsxReadEntity(),i=this.state.pos):Nt(r)?(t+=this.input.slice(i,this.state.pos),t+=this.jsxReadNewLine(!1),i=this.state.pos):++this.state.pos}t+=this.input.slice(i,this.state.pos++),this.finishToken(134,t)}},{key:"jsxReadEntity",value:function(){var e=++this.state.pos;if(35===this.codePointAtPos(this.state.pos)){++this.state.pos;var t=10;120===this.codePointAtPos(this.state.pos)&&(t=16,++this.state.pos);var i=this.readInt(t,void 0,!1,"bail");if(null!==i&&59===this.codePointAtPos(this.state.pos))return++this.state.pos,String.fromCodePoint(i)}else{var r=0,s=!1;while(r++<10&&this.state.pos<this.length&&!(s=59===this.codePointAtPos(this.state.pos)))++this.state.pos;if(s){var n=this.input.slice(e,this.state.pos),a=Et[n];if(++this.state.pos,a)return a}}return this.state.pos=e,"&"}},{key:"jsxReadWord",value:function(){var e,t=this.state.pos;do{e=this.input.charCodeAt(++this.state.pos)}while(it(e)||45===e);this.finishToken(141,this.input.slice(t,this.state.pos))}},{key:"jsxParseIdentifier",value:function(){var e=this.startNode();return this.match(141)?e.name=this.state.value:_e(this.state.type)?e.name=ze(this.state.type):this.unexpected(),this.next(),this.finishNode(e,"JSXIdentifier")}},{key:"jsxParseNamespacedName",value:function(){var e=this.state.startLoc,t=this.jsxParseIdentifier();if(!this.eat(14))return t;var i=this.startNodeAt(e);return i.namespace=t,i.name=this.jsxParseIdentifier(),this.finishNode(i,"JSXNamespacedName")}},{key:"jsxParseElementName",value:function(){var e=this.state.startLoc,t=this.jsxParseNamespacedName();if("JSXNamespacedName"===t.type)return t;while(this.eat(16)){var i=this.startNodeAt(e);i.object=t,i.property=this.jsxParseIdentifier(),t=this.finishNode(i,"JSXMemberExpression")}return t}},{key:"jsxParseAttributeValue",value:function(){var e;switch(this.state.type){case 5:return e=this.startNode(),this.setContext(oe.brace),this.next(),e=this.jsxParseExpressionContainer(e,oe.j_oTag),"JSXEmptyExpression"===e.expression.type&&this.raise(Ft.AttributeIsEmpty,e),e;case 143:case 134:return this.parseExprAtom();default:throw this.raise(Ft.UnsupportedJsxValue,this.state.startLoc)}}},{key:"jsxParseEmptyExpression",value:function(){var e=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(e,"JSXEmptyExpression",this.state.startLoc)}},{key:"jsxParseSpreadChild",value:function(e){return this.next(),e.expression=this.parseExpression(),this.setContext(oe.j_expr),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadChild")}},{key:"jsxParseExpressionContainer",value:function(e,t){if(this.match(8))e.expression=this.jsxParseEmptyExpression();else{var i=this.parseExpression();e.expression=i}return this.setContext(t),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXExpressionContainer")}},{key:"jsxParseAttribute",value:function(){var e=this.startNode();return this.match(5)?(this.setContext(oe.brace),this.next(),this.expect(21),e.argument=this.parseMaybeAssignAllowIn(),this.setContext(oe.j_oTag),this.state.canStartJSXElement=!0,this.expect(8),this.finishNode(e,"JSXSpreadAttribute")):(e.name=this.jsxParseNamespacedName(),e.value=this.eat(29)?this.jsxParseAttributeValue():null,this.finishNode(e,"JSXAttribute"))}},{key:"jsxParseOpeningElementAt",value:function(e){var t=this.startNodeAt(e);return this.eat(144)?this.finishNode(t,"JSXOpeningFragment"):(t.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(t))}},{key:"jsxParseOpeningElementAfterName",value:function(e){var t=[];while(!this.match(56)&&!this.match(144))t.push(this.jsxParseAttribute());return e.attributes=t,e.selfClosing=this.eat(56),this.expect(144),this.finishNode(e,"JSXOpeningElement")}},{key:"jsxParseClosingElementAt",value:function(e){var t=this.startNodeAt(e);return this.eat(144)?this.finishNode(t,"JSXClosingFragment"):(t.name=this.jsxParseElementName(),this.expect(144),this.finishNode(t,"JSXClosingElement"))}},{key:"jsxParseElementAt",value:function(e){var t=this.startNodeAt(e),i=[],r=this.jsxParseOpeningElementAt(e),s=null;if(!r.selfClosing){e:for(;;)switch(this.state.type){case 143:if(e=this.state.startLoc,this.next(),this.eat(56)){s=this.jsxParseClosingElementAt(e);break e}i.push(this.jsxParseElementAt(e));break;case 142:i.push(this.parseLiteral(this.state.value,"JSXText"));break;case 5:var n=this.startNode();this.setContext(oe.brace),this.next(),this.match(21)?i.push(this.jsxParseSpreadChild(n)):i.push(this.jsxParseExpressionContainer(n,oe.j_expr));break;default:this.unexpected()}Bt(r)&&!Bt(s)&&null!==s?this.raise(Ft.MissingClosingTagFragment,s):!Bt(r)&&Bt(s)?this.raise(Ft.MissingClosingTagElement,s,{openingTagName:_t(r.name)}):Bt(r)||Bt(s)||_t(s.name)!==_t(r.name)&&this.raise(Ft.MissingClosingTagElement,s,{openingTagName:_t(r.name)})}if(Bt(r)?(t.openingFragment=r,t.closingFragment=s):(t.openingElement=r,t.closingElement=s),t.children=i,this.match(47))throw this.raise(Ft.UnwrappedAdjacentJSXElements,this.state.startLoc);return Bt(r)?this.finishNode(t,"JSXFragment"):this.finishNode(t,"JSXElement")}},{key:"jsxParseElement",value:function(){var e=this.state.startLoc;return this.next(),this.jsxParseElementAt(e)}},{key:"setContext",value:function(e){var t=this.state.context;t[t.length-1]=e}},{key:"parseExprAtom",value:function(e){return this.match(143)?this.jsxParseElement():this.match(47)&&33!==this.input.charCodeAt(this.state.pos)?(this.replaceToken(143),this.jsxParseElement()):w(t,"parseExprAtom",this,3)([e])}},{key:"skipSpace",value:function(){var e=this.curContext();e.preserveSpace||w(t,"skipSpace",this,3)([])}},{key:"getTokenFromCode",value:function(e){var i=this.curContext();if(i!==oe.j_expr){if(i===oe.j_oTag||i===oe.j_cTag){if(tt(e))return void this.jsxReadWord();if(62===e)return++this.state.pos,void this.finishToken(144);if((34===e||39===e)&&i===oe.j_oTag)return void this.jsxReadString(e)}if(60===e&&this.state.canStartJSXElement&&33!==this.input.charCodeAt(this.state.pos+1))return++this.state.pos,void this.finishToken(143);w(t,"getTokenFromCode",this,3)([e])}else this.jsxReadToken()}},{key:"updateContext",value:function(e){var t=this.state,i=t.context,r=t.type;if(56===r&&143===e)i.splice(-2,2,oe.j_cTag),this.state.canStartJSXElement=!1;else if(143===r)i.push(oe.j_oTag);else if(144===r){var s=i[i.length-1];s===oe.j_oTag&&56===e||s===oe.j_cTag?(i.pop(),this.state.canStartJSXElement=i[i.length-1]===oe.j_expr):(this.setContext(oe.j_expr),this.state.canStartJSXElement=!0)}else this.state.canStartJSXElement=Le(r)}}])}(e)},Rt=function(e){function t(){var e;F(this,t);for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=b(this,t,[].concat(r)),e.tsNames=new Map,e}return C(t,e),L(t)}(mt),Ut=function(e){function t(){var e;F(this,t);for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=b(this,t,[].concat(r)),e.importsStack=[],e}return C(t,e),L(t,[{key:"createScope",value:function(e){return this.importsStack.push(new Set),new Rt(e)}},{key:"enter",value:function(e){1024===e&&this.importsStack.push(new Set),w(t,"enter",this,3)([e])}},{key:"exit",value:function(){var e=w(t,"exit",this,3)([]);return 1024===e&&this.importsStack.pop(),e}},{key:"hasImport",value:function(e,t){var i=this.importsStack.length;if(this.importsStack[i-1].has(e))return!0;if(!t&&i>1)for(var r=0;r<i-1;r++)if(this.importsStack[r].has(e))return!0;return!1}},{key:"declareName",value:function(e,i,r){if(4096&i)return this.hasImport(e,!0)&&this.parser.raise(Z.VarRedeclaration,r,{identifierName:e}),void this.importsStack[this.importsStack.length-1].add(e);var s=this.currentScope(),n=s.tsNames.get(e)||0;if(1024&i)return this.maybeExportDefined(s,e),void s.tsNames.set(e,16|n);w(t,"declareName",this,3)([e,i,r]),2&i&&(1&i||(this.checkRedeclarationInScope(s,e,i,r),this.maybeExportDefined(s,e)),n|=1),256&i&&(n|=2),512&i&&(n|=4),128&i&&(n|=8),n&&s.tsNames.set(e,n)}},{key:"isRedeclaredInScope",value:function(e,i,r){var s=e.tsNames.get(i);if((2&s)>0){if(256&r){var n=!!(512&r),a=(4&s)>0;return n!==a}return!0}return 128&r&&(8&s)>0?!!(2&e.names.get(i))&&!!(1&r):!!(2&r&&(1&s)>0)||w(t,"isRedeclaredInScope",this,3)([e,i,r])}},{key:"checkLocalExport",value:function(e){var i=e.name;if(!this.hasImport(i)){for(var r=this.scopeStack.length,s=r-1;s>=0;s--){var n=this.scopeStack[s],a=n.tsNames.get(i);if((1&a)>0||(16&a)>0)return}w(t,"checkLocalExport",this,3)([e])}}}])}(yt),Vt=function(){function e(){F(this,e),this.stacks=[]}return L(e,[{key:"enter",value:function(e){this.stacks.push(e)}},{key:"exit",value:function(){this.stacks.pop()}},{key:"currentFlags",value:function(){return this.stacks[this.stacks.length-1]}},{key:"hasAwait",get:function(){return(2&this.currentFlags())>0}},{key:"hasYield",get:function(){return(1&this.currentFlags())>0}},{key:"hasReturn",get:function(){return(4&this.currentFlags())>0}},{key:"hasIn",get:function(){return(8&this.currentFlags())>0}}])}();function Ht(e,t){return(e?2:0)|(t?1:0)}var zt=function(){function e(){F(this,e),this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}return L(e,[{key:"sourceToOffsetPos",value:function(e){return e+this.startIndex}},{key:"offsetToSourcePos",value:function(e){return e-this.startIndex}},{key:"hasPlugin",value:function(e){if("string"===typeof e)return this.plugins.has(e);var t=f(e,2),i=t[0],r=t[1];if(!this.hasPlugin(i))return!1;for(var s=this.plugins.get(i),n=0,a=Object.keys(r);n<a.length;n++){var o=a[n];if((null==s?void 0:s[o])!==r[o])return!1}return!0}},{key:"getPluginOption",value:function(e,t){var i;return null==(i=this.plugins.get(e))?void 0:i[t]}}])}();function qt(e,t){var i;void 0===e.trailingComments?e.trailingComments=t:(i=e.trailingComments).unshift.apply(i,h(t))}function Kt(e,t){var i;void 0===e.leadingComments?e.leadingComments=t:(i=e.leadingComments).unshift.apply(i,h(t))}function Wt(e,t){var i;void 0===e.innerComments?e.innerComments=t:(i=e.innerComments).unshift.apply(i,h(t))}function Xt(e,t,i){var r=null,s=t.length;while(null===r&&s>0)r=t[--s];null===r||r.start>i.start?Wt(e,i.comments):qt(r,i.comments)}var Jt=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"addComment",value:function(e){this.filename&&(e.loc.filename=this.filename);var t=this.state.commentsLen;this.comments.length!==t&&(this.comments.length=t),this.comments.push(e),this.state.commentsLen++}},{key:"processComment",value:function(e){var t=this.state.commentStack,i=t.length;if(0!==i){var r=i-1,s=t[r];s.start===e.end&&(s.leadingNode=e,r--);for(var n=e.start;r>=0;r--){var a=t[r],o=a.end;if(!(o>n)){o===n&&(a.trailingNode=e);break}a.containingNode=e,this.finalizeComment(a),t.splice(r,1)}}}},{key:"finalizeComment",value:function(e){var t,i=e.comments;if(null!==e.leadingNode||null!==e.trailingNode)null!==e.leadingNode&&qt(e.leadingNode,i),null!==e.trailingNode&&Kt(e.trailingNode,i);else{var r=e.containingNode,s=e.start;if(44===this.input.charCodeAt(this.offsetToSourcePos(s)-1))switch(r.type){case"ObjectExpression":case"ObjectPattern":case"RecordExpression":Xt(r,r.properties,e);break;case"CallExpression":case"OptionalCallExpression":Xt(r,r.arguments,e);break;case"ImportExpression":Xt(r,[r.source,null!=(t=r.options)?t:null],e);break;case"FunctionDeclaration":case"FunctionExpression":case"ArrowFunctionExpression":case"ObjectMethod":case"ClassMethod":case"ClassPrivateMethod":Xt(r,r.params,e);break;case"ArrayExpression":case"ArrayPattern":case"TupleExpression":Xt(r,r.elements,e);break;case"ExportNamedDeclaration":case"ImportDeclaration":Xt(r,r.specifiers,e);break;case"TSEnumDeclaration":Xt(r,r.members,e);break;case"TSEnumBody":Xt(r,r.members,e);break;default:Wt(r,i)}else Wt(r,i)}}},{key:"finalizeRemainingComments",value:function(){for(var e=this.state.commentStack,t=e.length-1;t>=0;t--)this.finalizeComment(e[t]);this.state.commentStack=[]}},{key:"resetPreviousNodeTrailingComments",value:function(e){var t=this.state.commentStack,i=t.length;if(0!==i){var r=t[i-1];r.leadingNode===e&&(r.leadingNode=null)}}},{key:"takeSurroundingComments",value:function(e,t,i){var r=this.state.commentStack,s=r.length;if(0!==s)for(var n=s-1;n>=0;n--){var a=r[n],o=a.end,l=a.start;if(l===i)a.leadingNode=e;else if(o===t)a.trailingNode=e;else if(o<t)break}}}])}(zt),Yt=function(){function e(){F(this,e),this.flags=1024,this.startIndex=void 0,this.curLine=void 0,this.lineStart=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.labels=[],this.commentsLen=0,this.commentStack=[],this.pos=0,this.type=140,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.context=[oe.brace],this.firstInvalidTemplateEscapePos=null,this.strictErrors=new Map,this.tokensLength=0}return L(e,[{key:"strict",get:function(){return(1&this.flags)>0},set:function(e){e?this.flags|=1:this.flags&=-2}},{key:"init",value:function(e){var t=e.strictMode,i=e.sourceType,r=e.startIndex,s=e.startLine,n=e.startColumn;this.strict=!1!==t&&(!0===t||"module"===i),this.startIndex=r,this.curLine=s,this.lineStart=-n,this.startLoc=this.endLoc=new _(s,n,r)}},{key:"maybeInArrowParameters",get:function(){return(2&this.flags)>0},set:function(e){e?this.flags|=2:this.flags&=-3}},{key:"inType",get:function(){return(4&this.flags)>0},set:function(e){e?this.flags|=4:this.flags&=-5}},{key:"noAnonFunctionType",get:function(){return(8&this.flags)>0},set:function(e){e?this.flags|=8:this.flags&=-9}},{key:"hasFlowComment",get:function(){return(16&this.flags)>0},set:function(e){e?this.flags|=16:this.flags&=-17}},{key:"isAmbientContext",get:function(){return(32&this.flags)>0},set:function(e){e?this.flags|=32:this.flags&=-33}},{key:"inAbstractClass",get:function(){return(64&this.flags)>0},set:function(e){e?this.flags|=64:this.flags&=-65}},{key:"inDisallowConditionalTypesContext",get:function(){return(128&this.flags)>0},set:function(e){e?this.flags|=128:this.flags&=-129}},{key:"soloAwait",get:function(){return(256&this.flags)>0},set:function(e){e?this.flags|=256:this.flags&=-257}},{key:"inFSharpPipelineDirectBody",get:function(){return(512&this.flags)>0},set:function(e){e?this.flags|=512:this.flags&=-513}},{key:"canStartJSXElement",get:function(){return(1024&this.flags)>0},set:function(e){e?this.flags|=1024:this.flags&=-1025}},{key:"containsEsc",get:function(){return(2048&this.flags)>0},set:function(e){e?this.flags|=2048:this.flags&=-2049}},{key:"hasTopLevelAwait",get:function(){return(4096&this.flags)>0},set:function(e){e?this.flags|=4096:this.flags&=-4097}},{key:"curPosition",value:function(){return new _(this.curLine,this.pos-this.lineStart,this.pos+this.startIndex)}},{key:"clone",value:function(){var t=new e;return t.flags=this.flags,t.startIndex=this.startIndex,t.curLine=this.curLine,t.lineStart=this.lineStart,t.startLoc=this.startLoc,t.endLoc=this.endLoc,t.errors=this.errors.slice(),t.potentialArrowAt=this.potentialArrowAt,t.noArrowAt=this.noArrowAt.slice(),t.noArrowParamsConversionAt=this.noArrowParamsConversionAt.slice(),t.topicContext=this.topicContext,t.labels=this.labels.slice(),t.commentsLen=this.commentsLen,t.commentStack=this.commentStack.slice(),t.pos=this.pos,t.type=this.type,t.value=this.value,t.start=this.start,t.end=this.end,t.lastTokEndLoc=this.lastTokEndLoc,t.lastTokStartLoc=this.lastTokStartLoc,t.context=this.context.slice(),t.firstInvalidTemplateEscapePos=this.firstInvalidTemplateEscapePos,t.strictErrors=this.strictErrors,t.tokensLength=this.tokensLength,t}}])}(),Gt=function(e){return e>=48&&e<=57},$t={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])},Qt={bin:function(e){return 48===e||49===e},oct:function(e){return e>=48&&e<=55},dec:function(e){return e>=48&&e<=57},hex:function(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}};function Zt(e,t,i,r,s,n){for(var a=i,o=r,l=s,c="",h=null,u=i,p=t.length;;){if(i>=p){n.unterminated(a,o,l),c+=t.slice(u,i);break}var d=t.charCodeAt(i);if(ei(e,d,t,i)){c+=t.slice(u,i);break}if(92===d){c+=t.slice(u,i);var f=ti(t,i,r,s,"template"===e,n);null!==f.ch||h?c+=f.ch:h={pos:i,lineStart:r,curLine:s},i=f.pos,r=f.lineStart,s=f.curLine,u=i}else 8232===d||8233===d?(++i,++s,r=i):10===d||13===d?"template"===e?(c+=t.slice(u,i)+"\n",++i,13===d&&10===t.charCodeAt(i)&&++i,++s,u=r=i):n.unterminated(a,o,l):++i}return{pos:i,str:c,firstInvalidLoc:h,lineStart:r,curLine:s,containsInvalid:!!h}}function ei(e,t,i,r){return"template"===e?96===t||36===t&&123===i.charCodeAt(r+1):t===("double"===e?34:39)}function ti(e,t,i,r,s,n){var a=!s;t++;var o=function(e){return{pos:t,ch:e,lineStart:i,curLine:r}},l=e.charCodeAt(t++);switch(l){case 110:return o("\n");case 114:return o("\r");case 120:var c,h=ii(e,t,i,r,2,!1,a,n);return c=h.code,t=h.pos,o(null===c?null:String.fromCharCode(c));case 117:var u,p=si(e,t,i,r,a,n);return u=p.code,t=p.pos,o(null===u?null:String.fromCodePoint(u));case 116:return o("\t");case 98:return o("\b");case 118:return o("\v");case 102:return o("\f");case 13:10===e.charCodeAt(t)&&++t;case 10:i=t,++r;case 8232:case 8233:return o("");case 56:case 57:if(s)return o(null);n.strictNumericEscape(t-1,i,r);default:if(l>=48&&l<=55){var d=t-1,f=/^[0-7]+/.exec(e.slice(d,t+2)),m=f[0],y=parseInt(m,8);y>255&&(m=m.slice(0,-1),y=parseInt(m,8)),t+=m.length-1;var v=e.charCodeAt(t);if("0"!==m||56===v||57===v){if(s)return o(null);n.strictNumericEscape(d,i,r)}return o(String.fromCharCode(y))}return o(String.fromCharCode(l))}}function ii(e,t,i,r,s,n,a,o){var l,c=t,h=ri(e,t,i,r,16,s,n,!1,o,!a);return l=h.n,t=h.pos,null===l&&(a?o.invalidEscapeSequence(c,i,r):t=c-1),{code:l,pos:t}}function ri(e,t,i,r,s,n,a,o,l,c){for(var h=t,u=16===s?$t.hex:$t.decBinOct,p=16===s?Qt.hex:10===s?Qt.dec:8===s?Qt.oct:Qt.bin,d=!1,f=0,m=0,y=null==n?1/0:n;m<y;++m){var v=e.charCodeAt(t),g=void 0;if(95!==v||"bail"===o){if(g=v>=97?v-97+10:v>=65?v-65+10:Gt(v)?v-48:1/0,g>=s){if(g<=9&&c)return{n:null,pos:t};if(g<=9&&l.invalidDigit(t,i,r,s))g=0;else{if(!a)break;g=0,d=!0}}++t,f=f*s+g}else{var x=e.charCodeAt(t-1),b=e.charCodeAt(t+1);if(o){if(Number.isNaN(b)||!p(b)||u.has(x)||u.has(b)){if(c)return{n:null,pos:t};l.unexpectedNumericSeparator(t,i,r)}}else{if(c)return{n:null,pos:t};l.numericSeparatorInEscapeSequence(t,i,r)}++t}}return t===h||null!=n&&t-h!==n||d?{n:null,pos:t}:{n:f,pos:t}}function si(e,t,i,r,s,n){var a,o=e.charCodeAt(t);if(123===o){++t;var l=ii(e,t,i,r,e.indexOf("}",t)-t,!0,s,n);if(a=l.code,t=l.pos,++t,null!==a&&a>1114111){if(!s)return{code:null,pos:t};n.invalidCodePoint(t,i,r)}}else{var c=ii(e,t,i,r,4,!1,s,n);a=c.code,t=c.pos}return{code:a,pos:t}}function ni(e,t,i){return new _(i,e-t,e)}var ai=new Set([103,109,115,105,121,117,100,118]),oi=L((function e(t){F(this,e);var i=t.startIndex||0;this.type=t.type,this.value=t.value,this.start=i+t.start,this.end=i+t.end,this.loc=new j(t.startLoc,t.endLoc)})),li=function(e){function t(e,i){var r;return F(this,t),r=b(this,t),r.isLookahead=void 0,r.tokens=[],r.errorHandlers_readInt={invalidDigit:function(e,t,i,s){return!!(2048&r.optionFlags)&&(r.raise(Z.InvalidDigit,ni(e,t,i),{radix:s}),!0)},numericSeparatorInEscapeSequence:r.errorBuilder(Z.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:r.errorBuilder(Z.UnexpectedNumericSeparator)},r.errorHandlers_readCodePoint=Object.assign({},r.errorHandlers_readInt,{invalidEscapeSequence:r.errorBuilder(Z.InvalidEscapeSequence),invalidCodePoint:r.errorBuilder(Z.InvalidCodePoint)}),r.errorHandlers_readStringContents_string=Object.assign({},r.errorHandlers_readCodePoint,{strictNumericEscape:function(e,t,i){r.recordStrictModeErrors(Z.StrictNumericEscape,ni(e,t,i))},unterminated:function(e,t,i){throw r.raise(Z.UnterminatedString,ni(e-1,t,i))}}),r.errorHandlers_readStringContents_template=Object.assign({},r.errorHandlers_readCodePoint,{strictNumericEscape:r.errorBuilder(Z.StrictNumericEscape),unterminated:function(e,t,i){throw r.raise(Z.UnterminatedTemplate,ni(e,t,i))}}),r.state=new Yt,r.state.init(e),r.input=i,r.length=i.length,r.comments=[],r.isLookahead=!1,r}return C(t,e),L(t,[{key:"pushToken",value:function(e){this.tokens.length=this.state.tokensLength,this.tokens.push(e),++this.state.tokensLength}},{key:"next",value:function(){this.checkKeywordEscapes(),256&this.optionFlags&&this.pushToken(new oi(this.state)),this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}},{key:"eat",value:function(e){return!!this.match(e)&&(this.next(),!0)}},{key:"match",value:function(e){return this.state.type===e}},{key:"createLookaheadState",value:function(e){return{pos:e.pos,value:null,type:e.type,start:e.start,end:e.end,context:[this.curContext()],inType:e.inType,startLoc:e.startLoc,lastTokEndLoc:e.lastTokEndLoc,curLine:e.curLine,lineStart:e.lineStart,curPosition:e.curPosition}}},{key:"lookahead",value:function(){var e=this.state;this.state=this.createLookaheadState(e),this.isLookahead=!0,this.nextToken(),this.isLookahead=!1;var t=this.state;return this.state=e,t}},{key:"nextTokenStart",value:function(){return this.nextTokenStartSince(this.state.pos)}},{key:"nextTokenStartSince",value:function(e){return Lt.lastIndex=e,Lt.test(this.input)?Lt.lastIndex:e}},{key:"lookaheadCharCode",value:function(){return this.lookaheadCharCodeSince(this.state.pos)}},{key:"lookaheadCharCodeSince",value:function(e){return this.input.charCodeAt(this.nextTokenStartSince(e))}},{key:"nextTokenInLineStart",value:function(){return this.nextTokenInLineStartSince(this.state.pos)}},{key:"nextTokenInLineStartSince",value:function(e){return Dt.lastIndex=e,Dt.test(this.input)?Dt.lastIndex:e}},{key:"lookaheadInLineCharCode",value:function(){return this.input.charCodeAt(this.nextTokenInLineStart())}},{key:"codePointAtPos",value:function(e){var t=this.input.charCodeAt(e);if(55296===(64512&t)&&++e<this.input.length){var i=this.input.charCodeAt(e);56320===(64512&i)&&(t=65536+((1023&t)<<10)+(1023&i))}return t}},{key:"setStrict",value:function(e){var t=this;this.state.strict=e,e&&(this.state.strictErrors.forEach((function(e){var i=f(e,2),r=i[0],s=i[1];return t.raise(r,s)})),this.state.strictErrors.clear())}},{key:"curContext",value:function(){return this.state.context[this.state.context.length-1]}},{key:"nextToken",value:function(){this.skipSpace(),this.state.start=this.state.pos,this.isLookahead||(this.state.startLoc=this.state.curPosition()),this.state.pos>=this.length?this.finishToken(140):this.getTokenFromCode(this.codePointAtPos(this.state.pos))}},{key:"skipBlockComment",value:function(e){var t;this.isLookahead||(t=this.state.curPosition());var i=this.state.pos,r=this.input.indexOf(e,i+2);if(-1===r)throw this.raise(Z.UnterminatedComment,this.state.curPosition());this.state.pos=r+e.length,It.lastIndex=i+2;while(It.test(this.input)&&It.lastIndex<=r)++this.state.curLine,this.state.lineStart=It.lastIndex;if(!this.isLookahead){var s={type:"CommentBlock",value:this.input.slice(i+2,r),start:this.sourceToOffsetPos(i),end:this.sourceToOffsetPos(r+e.length),loc:new j(t,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(s),s}}},{key:"skipLineComment",value:function(e){var t,i=this.state.pos;this.isLookahead||(t=this.state.curPosition());var r=this.input.charCodeAt(this.state.pos+=e);if(this.state.pos<this.length)while(!Nt(r)&&++this.state.pos<this.length)r=this.input.charCodeAt(this.state.pos);if(!this.isLookahead){var s=this.state.pos,n=this.input.slice(i+e,s),a={type:"CommentLine",value:n,start:this.sourceToOffsetPos(i),end:this.sourceToOffsetPos(s),loc:new j(t,this.state.curPosition())};return 256&this.optionFlags&&this.pushToken(a),a}}},{key:"skipSpace",value:function(){var e=this.state.pos,t=4096&this.optionFlags?[]:null;e:while(this.state.pos<this.length){var i=this.input.charCodeAt(this.state.pos);switch(i){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:var r=this.skipBlockComment("*/");void 0!==r&&(this.addComment(r),null==t||t.push(r));break;case 47:var s=this.skipLineComment(2);void 0!==s&&(this.addComment(s),null==t||t.push(s));break;default:break e}break;default:if(Mt(i))++this.state.pos;else if(45===i&&!this.inModule&&8192&this.optionFlags){var n=this.state.pos;if(45!==this.input.charCodeAt(n+1)||62!==this.input.charCodeAt(n+2)||!(0===e||this.state.lineStart>e))break e;var a=this.skipLineComment(3);void 0!==a&&(this.addComment(a),null==t||t.push(a))}else{if(60!==i||this.inModule||!(8192&this.optionFlags))break e;var o=this.state.pos;if(33!==this.input.charCodeAt(o+1)||45!==this.input.charCodeAt(o+2)||45!==this.input.charCodeAt(o+3))break e;var l=this.skipLineComment(4);void 0!==l&&(this.addComment(l),null==t||t.push(l))}}}if((null==t?void 0:t.length)>0){var c=this.state.pos,h={start:this.sourceToOffsetPos(e),end:this.sourceToOffsetPos(c),comments:t,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(h)}}},{key:"finishToken",value:function(e,t){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();var i=this.state.type;this.state.type=e,this.state.value=t,this.isLookahead||this.updateContext(i)}},{key:"replaceToken",value:function(e){this.state.type=e,this.updateContext()}},{key:"readToken_numberSign",value:function(){if(0!==this.state.pos||!this.readToken_interpreter()){var e=this.state.pos+1,t=this.codePointAtPos(e);if(t>=48&&t<=57)throw this.raise(Z.UnexpectedDigitAfterHash,this.state.curPosition());if(123===t||91===t&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),"bar"===this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(123===t?Z.RecordExpressionHashIncorrectStartSyntaxType:Z.TupleExpressionHashIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,123===t?this.finishToken(7):this.finishToken(1)}else tt(t)?(++this.state.pos,this.finishToken(139,this.readWord1(t))):92===t?(++this.state.pos,this.finishToken(139,this.readWord1())):this.finishOp(27,1)}}},{key:"readToken_dot",value:function(){var e=this.input.charCodeAt(this.state.pos+1);e>=48&&e<=57?this.readNumber(!0):46===e&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(21)):(++this.state.pos,this.finishToken(16))}},{key:"readToken_slash",value:function(){var e=this.input.charCodeAt(this.state.pos+1);61===e?this.finishOp(31,2):this.finishOp(56,1)}},{key:"readToken_interpreter",value:function(){if(0!==this.state.pos||this.length<2)return!1;var e=this.input.charCodeAt(this.state.pos+1);if(33!==e)return!1;var t=this.state.pos;this.state.pos+=1;while(!Nt(e)&&++this.state.pos<this.length)e=this.input.charCodeAt(this.state.pos);var i=this.input.slice(t+2,this.state.pos);return this.finishToken(28,i),!0}},{key:"readToken_mult_modulo",value:function(e){var t=42===e?55:54,i=1,r=this.input.charCodeAt(this.state.pos+1);42===e&&42===r&&(i++,r=this.input.charCodeAt(this.state.pos+2),t=57),61!==r||this.state.inType||(i++,t=37===e?33:30),this.finishOp(t,i)}},{key:"readToken_pipe_amp",value:function(e){var t=this.input.charCodeAt(this.state.pos+1);if(t!==e){if(124===e){if(62===t)return void this.finishOp(39,2);if(this.hasPlugin("recordAndTuple")&&125===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.RecordExpressionBarIncorrectEndSyntaxType,this.state.curPosition());return this.state.pos+=2,void this.finishToken(9)}if(this.hasPlugin("recordAndTuple")&&93===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.TupleExpressionBarIncorrectEndSyntaxType,this.state.curPosition());return this.state.pos+=2,void this.finishToken(4)}}61!==t?this.finishOp(124===e?43:45,1):this.finishOp(30,2)}else 61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(30,3):this.finishOp(124===e?41:42,2)}},{key:"readToken_caret",value:function(){var e=this.input.charCodeAt(this.state.pos+1);if(61!==e||this.state.inType)if(94===e&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"^^"}])){this.finishOp(37,2);var t=this.input.codePointAt(this.state.pos);94===t&&this.unexpected()}else this.finishOp(44,1);else this.finishOp(32,2)}},{key:"readToken_atSign",value:function(){var e=this.input.charCodeAt(this.state.pos+1);64===e&&this.hasPlugin(["pipelineOperator",{proposal:"hack",topicToken:"@@"}])?this.finishOp(38,2):this.finishOp(26,1)}},{key:"readToken_plus_min",value:function(e){var t=this.input.charCodeAt(this.state.pos+1);t!==e?61===t?this.finishOp(30,2):this.finishOp(53,1):this.finishOp(34,2)}},{key:"readToken_lt",value:function(){var e=this.state.pos,t=this.input.charCodeAt(e+1);if(60===t)return 61===this.input.charCodeAt(e+2)?void this.finishOp(30,3):void this.finishOp(51,2);61!==t?this.finishOp(47,1):this.finishOp(49,2)}},{key:"readToken_gt",value:function(){var e=this.state.pos,t=this.input.charCodeAt(e+1);if(62===t){var i=62===this.input.charCodeAt(e+2)?3:2;return 61===this.input.charCodeAt(e+i)?void this.finishOp(30,i+1):void this.finishOp(52,i)}61!==t?this.finishOp(48,1):this.finishOp(49,2)}},{key:"readToken_eq_excl",value:function(e){var t=this.input.charCodeAt(this.state.pos+1);if(61!==t)return 61===e&&62===t?(this.state.pos+=2,void this.finishToken(19)):void this.finishOp(61===e?29:35,1);this.finishOp(46,61===this.input.charCodeAt(this.state.pos+2)?3:2)}},{key:"readToken_question",value:function(){var e=this.input.charCodeAt(this.state.pos+1),t=this.input.charCodeAt(this.state.pos+2);63===e?61===t?this.finishOp(30,3):this.finishOp(40,2):46!==e||t>=48&&t<=57?(++this.state.pos,this.finishToken(17)):(this.state.pos+=2,this.finishToken(18))}},{key:"getTokenFromCode",value:function(e){switch(e){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(10);case 41:return++this.state.pos,void this.finishToken(11);case 59:return++this.state.pos,void this.finishToken(13);case 44:return++this.state.pos,void this.finishToken(12);case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.TupleExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(2)}else++this.state.pos,this.finishToken(0);return;case 93:return++this.state.pos,void this.finishToken(3);case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(Z.RecordExpressionBarIncorrectStartSyntaxType,this.state.curPosition());this.state.pos+=2,this.finishToken(6)}else++this.state.pos,this.finishToken(5);return;case 125:return++this.state.pos,void this.finishToken(8);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(15,2):(++this.state.pos,this.finishToken(14)));case 63:return void this.readToken_question();case 96:return void this.readTemplateToken();case 48:var t=this.input.charCodeAt(this.state.pos+1);if(120===t||88===t)return void this.readRadixNumber(16);if(111===t||79===t)return void this.readRadixNumber(8);if(98===t||66===t)return void this.readRadixNumber(2);case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(e);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(e);case 124:case 38:return void this.readToken_pipe_amp(e);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(e);case 60:return void this.readToken_lt();case 62:return void this.readToken_gt();case 61:case 33:return void this.readToken_eq_excl(e);case 126:return void this.finishOp(36,1);case 64:return void this.readToken_atSign();case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(tt(e))return void this.readWord(e)}throw this.raise(Z.InvalidOrUnexpectedToken,this.state.curPosition(),{unexpected:String.fromCodePoint(e)})}},{key:"finishOp",value:function(e,t){var i=this.input.slice(this.state.pos,this.state.pos+t);this.state.pos+=t,this.finishToken(e,i)}},{key:"readRegexp",value:function(){for(var e,t,i=this.state.startLoc,r=this.state.start+1,s=this.state.pos;;++s){if(s>=this.length)throw this.raise(Z.UnterminatedRegExp,R(i,1));var n=this.input.charCodeAt(s);if(Nt(n))throw this.raise(Z.UnterminatedRegExp,R(i,1));if(e)e=!1;else{if(91===n)t=!0;else if(93===n&&t)t=!1;else if(47===n&&!t)break;e=92===n}}var a=this.input.slice(r,s);++s;var o="",l=function(){return R(i,s+2-r)};while(s<this.length){var c=this.codePointAtPos(s),h=String.fromCharCode(c);if(ai.has(c))118===c?o.includes("u")&&this.raise(Z.IncompatibleRegExpUVFlags,l()):117===c&&o.includes("v")&&this.raise(Z.IncompatibleRegExpUVFlags,l()),o.includes(h)&&this.raise(Z.DuplicateRegExpFlags,l());else{if(!it(c)&&92!==c)break;this.raise(Z.MalformedRegExpFlags,l())}++s,o+=h}this.state.pos=s,this.finishToken(138,{pattern:a,flags:o})}},{key:"readInt",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],s=ri(this.input,this.state.pos,this.state.lineStart,this.state.curLine,e,t,i,r,this.errorHandlers_readInt,!1),n=s.n,a=s.pos;return this.state.pos=a,n}},{key:"readRadixNumber",value:function(e){var t=this.state.pos,i=this.state.curPosition(),r=!1;this.state.pos+=2;var s=this.readInt(e);null==s&&this.raise(Z.InvalidDigit,R(i,2),{radix:e});var n=this.input.charCodeAt(this.state.pos);if(110===n)++this.state.pos,r=!0;else if(109===n)throw this.raise(Z.InvalidDecimal,i);if(tt(this.codePointAtPos(this.state.pos)))throw this.raise(Z.NumberIdentifier,this.state.curPosition());if(r){var a=this.input.slice(t,this.state.pos).replace(/[_n]/g,"");this.finishToken(136,a)}else this.finishToken(135,s)}},{key:"readNumber",value:function(e){var t=this.state.pos,i=this.state.curPosition(),r=!1,s=!1,n=!1,a=!1;e||null!==this.readInt(10)||this.raise(Z.InvalidNumber,this.state.curPosition());var o=this.state.pos-t>=2&&48===this.input.charCodeAt(t);if(o){var l=this.input.slice(t,this.state.pos);if(this.recordStrictModeErrors(Z.StrictOctalLiteral,i),!this.state.strict){var c=l.indexOf("_");c>0&&this.raise(Z.ZeroDigitNumericSeparator,R(i,c))}a=o&&!/[89]/.test(l)}var h=this.input.charCodeAt(this.state.pos);if(46!==h||a||(++this.state.pos,this.readInt(10),r=!0,h=this.input.charCodeAt(this.state.pos)),69!==h&&101!==h||a||(h=this.input.charCodeAt(++this.state.pos),43!==h&&45!==h||++this.state.pos,null===this.readInt(10)&&this.raise(Z.InvalidOrMissingExponent,i),r=!0,n=!0,h=this.input.charCodeAt(this.state.pos)),110===h&&((r||o)&&this.raise(Z.InvalidBigIntLiteral,i),++this.state.pos,s=!0),109===h){this.expectPlugin("decimal",this.state.curPosition()),(n||o)&&this.raise(Z.InvalidDecimal,i),++this.state.pos;var u=!0}if(tt(this.codePointAtPos(this.state.pos)))throw this.raise(Z.NumberIdentifier,this.state.curPosition());var p=this.input.slice(t,this.state.pos).replace(/[_mn]/g,"");if(s)this.finishToken(136,p);else if(u)this.finishToken(137,p);else{var d=a?parseInt(p,8):parseFloat(p);this.finishToken(135,d)}}},{key:"readCodePoint",value:function(e){var t=si(this.input,this.state.pos,this.state.lineStart,this.state.curLine,e,this.errorHandlers_readCodePoint),i=t.code,r=t.pos;return this.state.pos=r,i}},{key:"readString",value:function(e){var t=Zt(34===e?"double":"single",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string),i=t.str,r=t.pos,s=t.curLine,n=t.lineStart;this.state.pos=r+1,this.state.lineStart=n,this.state.curLine=s,this.finishToken(134,i)}},{key:"readTemplateContinuation",value:function(){this.match(8)||this.unexpected(null,8),this.state.pos--,this.readTemplateToken()}},{key:"readTemplateToken",value:function(){var e=this.input[this.state.pos],t=Zt("template",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template),i=t.str,r=t.firstInvalidLoc,s=t.pos,n=t.curLine,a=t.lineStart;this.state.pos=s+1,this.state.lineStart=a,this.state.curLine=n,r&&(this.state.firstInvalidTemplateEscapePos=new _(r.curLine,r.pos-r.lineStart,this.sourceToOffsetPos(r.pos))),96===this.input.codePointAt(s)?this.finishToken(24,r?null:e+i+"`"):(this.state.pos++,this.finishToken(25,r?null:e+i+"${"))}},{key:"recordStrictModeErrors",value:function(e,t){var i=t.index;this.state.strict&&!this.state.strictErrors.has(i)?this.raise(e,t):this.state.strictErrors.set(i,[e,t])}},{key:"readWord1",value:function(e){this.state.containsEsc=!1;var t="",i=this.state.pos,r=this.state.pos;void 0!==e&&(this.state.pos+=e<=65535?1:2);while(this.state.pos<this.length){var s=this.codePointAtPos(this.state.pos);if(it(s))this.state.pos+=s<=65535?1:2;else{if(92!==s)break;this.state.containsEsc=!0,t+=this.input.slice(r,this.state.pos);var n=this.state.curPosition(),a=this.state.pos===i?tt:it;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(Z.MissingUnicodeEscape,this.state.curPosition()),r=this.state.pos-1;continue}++this.state.pos;var o=this.readCodePoint(!0);null!==o&&(a(o)||this.raise(Z.EscapedCharNotAnIdentifier,n),t+=String.fromCodePoint(o)),r=this.state.pos}}return t+this.input.slice(r,this.state.pos)}},{key:"readWord",value:function(e){var t=this.readWord1(e),i=me.get(t);void 0!==i?this.finishToken(i,ze(i)):this.finishToken(132,t)}},{key:"checkKeywordEscapes",value:function(){var e=this.state.type;_e(e)&&this.state.containsEsc&&this.raise(Z.InvalidEscapedReservedWord,this.state.startLoc,{reservedWord:ze(e)})}},{key:"raise",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t instanceof _?t:t.loc.start,s=e(r,i);if(!(2048&this.optionFlags))throw s;return this.isLookahead||this.state.errors.push(s),s}},{key:"raiseOverwrite",value:function(e,t){for(var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t instanceof _?t:t.loc.start,s=r.index,n=this.state.errors,a=n.length-1;a>=0;a--){var o=n[a];if(o.loc.index===s)return n[a]=e(r,i);if(o.loc.index<s)break}return this.raise(e,t,i)}},{key:"updateContext",value:function(e){}},{key:"unexpected",value:function(e,t){throw this.raise(Z.UnexpectedToken,null!=e?e:this.state.startLoc,{expected:t?ze(t):null})}},{key:"expectPlugin",value:function(e,t){if(this.hasPlugin(e))return!0;throw this.raise(Z.MissingPlugin,null!=t?t:this.state.startLoc,{missingPlugin:[e]})}},{key:"expectOnePlugin",value:function(e){var t=this;if(!e.some((function(e){return t.hasPlugin(e)})))throw this.raise(Z.MissingOneOfPlugins,this.state.startLoc,{missingPlugin:e})}},{key:"errorBuilder",value:function(e){var t=this;return function(i,r,s){t.raise(e,ni(i,r,s))}}}])}(Jt),ci=L((function e(){F(this,e),this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map})),hi=function(){function e(t){F(this,e),this.parser=void 0,this.stack=[],this.undefinedPrivateNames=new Map,this.parser=t}return L(e,[{key:"current",value:function(){return this.stack[this.stack.length-1]}},{key:"enter",value:function(){this.stack.push(new ci)}},{key:"exit",value:function(){for(var e=this.stack.pop(),t=this.current(),i=0,r=Array.from(e.undefinedPrivateNames);i<r.length;i++){var s=f(r[i],2),n=s[0],a=s[1];t?t.undefinedPrivateNames.has(n)||t.undefinedPrivateNames.set(n,a):this.parser.raise(Z.InvalidPrivateFieldResolution,a,{identifierName:n})}}},{key:"declarePrivateName",value:function(e,t,i){var r=this.current(),s=r.privateNames,n=r.loneAccessors,a=r.undefinedPrivateNames,o=s.has(e);if(3&t){var l=o&&n.get(e);if(l){var c=4&l,h=4&t,u=3&l,p=3&t;o=u===p||c!==h,o||n.delete(e)}else o||n.set(e,t)}o&&this.parser.raise(Z.PrivateNameRedeclaration,i,{identifierName:e}),s.add(e),a.delete(e)}},{key:"usePrivateName",value:function(e,t){var i,r,s=c(this.stack);try{for(s.s();!(r=s.n()).done;)if(i=r.value,i.privateNames.has(e))return}catch(n){s.e(n)}finally{s.f()}i?i.undefinedPrivateNames.set(e,t):this.parser.raise(Z.InvalidPrivateFieldResolution,t,{identifierName:e})}}])}(),ui=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;F(this,e),this.type=t}return L(e,[{key:"canBeArrowParameterDeclaration",value:function(){return 2===this.type||1===this.type}},{key:"isCertainlyParameterDeclaration",value:function(){return 3===this.type}}])}(),pi=function(e){function t(e){var i;return F(this,t),i=b(this,t,[e]),i.declarationErrors=new Map,i}return C(t,e),L(t,[{key:"recordDeclarationError",value:function(e,t){var i=t.index;this.declarationErrors.set(i,[e,t])}},{key:"clearDeclarationError",value:function(e){this.declarationErrors.delete(e)}},{key:"iterateErrors",value:function(e){this.declarationErrors.forEach(e)}}])}(ui),di=function(){function e(t){F(this,e),this.parser=void 0,this.stack=[new ui],this.parser=t}return L(e,[{key:"enter",value:function(e){this.stack.push(e)}},{key:"exit",value:function(){this.stack.pop()}},{key:"recordParameterInitializerError",value:function(e,t){var i=t.loc.start,r=this.stack,s=r.length-1,n=r[s];while(!n.isCertainlyParameterDeclaration()){if(!n.canBeArrowParameterDeclaration())return;n.recordDeclarationError(e,i),n=r[--s]}this.parser.raise(e,i)}},{key:"recordArrowParameterBindingError",value:function(e,t){var i=this.stack,r=i[i.length-1],s=t.loc.start;if(r.isCertainlyParameterDeclaration())this.parser.raise(e,s);else{if(!r.canBeArrowParameterDeclaration())return;r.recordDeclarationError(e,s)}}},{key:"recordAsyncArrowParametersError",value:function(e){var t=this.stack,i=t.length-1,r=t[i];while(r.canBeArrowParameterDeclaration())2===r.type&&r.recordDeclarationError(Z.AwaitBindingIdentifier,e),r=t[--i]}},{key:"validateAsPattern",value:function(){var e=this,t=this.stack,i=t[t.length-1];i.canBeArrowParameterDeclaration()&&i.iterateErrors((function(i){var r=f(i,2),s=r[0],n=r[1];e.parser.raise(s,n);var a=t.length-2,o=t[a];while(o.canBeArrowParameterDeclaration())o.clearDeclarationError(n.index),o=t[--a]}))}}])}();function fi(){return new ui(3)}function mi(){return new pi(1)}function yi(){return new pi(2)}function vi(){return new ui}var gi=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"addExtra",value:function(e,t,i){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(e){var s=e.extra;null==s&&(s={},e.extra=s),r?s[t]=i:Object.defineProperty(s,t,{enumerable:r,value:i})}}},{key:"isContextual",value:function(e){return this.state.type===e&&!this.state.containsEsc}},{key:"isUnparsedContextual",value:function(e,t){if(this.input.startsWith(t,e)){var i=this.input.charCodeAt(e+t.length);return!(it(i)||55296===(64512&i))}return!1}},{key:"isLookaheadContextual",value:function(e){var t=this.nextTokenStart();return this.isUnparsedContextual(t,e)}},{key:"eatContextual",value:function(e){return!!this.isContextual(e)&&(this.next(),!0)}},{key:"expectContextual",value:function(e,t){if(!this.eatContextual(e)){if(null!=t)throw this.raise(t,this.state.startLoc);this.unexpected(null,e)}}},{key:"canInsertSemicolon",value:function(){return this.match(140)||this.match(8)||this.hasPrecedingLineBreak()}},{key:"hasPrecedingLineBreak",value:function(){return Ot(this.input,this.offsetToSourcePos(this.state.lastTokEndLoc.index),this.state.start)}},{key:"hasFollowingLineBreak",value:function(){return Ot(this.input,this.state.end,this.nextTokenStart())}},{key:"isLineTerminator",value:function(){return this.eat(13)||this.canInsertSemicolon()}},{key:"semicolon",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(e?this.isLineTerminator():this.eat(13))||this.raise(Z.MissingSemicolon,this.state.lastTokEndLoc)}},{key:"expect",value:function(e,t){this.eat(e)||this.unexpected(t,e)}},{key:"tryParse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.clone(),i={node:null};try{var r=e((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;throw i.node=e,i}));if(this.state.errors.length>t.errors.length){var s=this.state;return this.state=t,this.state.tokensLength=s.tokensLength,{node:r,error:s.errors[t.errors.length],thrown:!1,aborted:!1,failState:s}}return{node:r,error:null,thrown:!1,aborted:!1,failState:null}}catch(a){var n=this.state;if(this.state=t,a instanceof SyntaxError)return{node:null,error:a,thrown:!0,aborted:!1,failState:n};if(a===i)return{node:i.node,error:null,thrown:!1,aborted:!0,failState:n};throw a}}},{key:"checkExpressionErrors",value:function(e,t){if(!e)return!1;var i=e.shorthandAssignLoc,r=e.doubleProtoLoc,s=e.privateKeyLoc,n=e.optionalParametersLoc,a=e.voidPatternLoc,o=!!i||!!r||!!n||!!s||!!a;if(!t)return o;null!=i&&this.raise(Z.InvalidCoverInitializedName,i),null!=r&&this.raise(Z.DuplicateProto,r),null!=s&&this.raise(Z.UnexpectedPrivateField,s),null!=n&&this.unexpected(n),null!=a&&this.raise(Z.InvalidCoverDiscardElement,a)}},{key:"isLiteralPropertyName",value:function(){return Oe(this.state.type)}},{key:"isPrivateName",value:function(e){return"PrivateName"===e.type}},{key:"getPrivateNameSV",value:function(e){return e.id.name}},{key:"hasPropertyAsPrivateName",value:function(e){return("MemberExpression"===e.type||"OptionalMemberExpression"===e.type)&&this.isPrivateName(e.property)}},{key:"isObjectProperty",value:function(e){return"ObjectProperty"===e.type}},{key:"isObjectMethod",value:function(e){return"ObjectMethod"===e.type}},{key:"initializeScopes",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"module"===this.options.sourceType,i=this.state.labels;this.state.labels=[];var r=this.exportedIdentifiers;this.exportedIdentifiers=new Set;var s=this.inModule;this.inModule=t;var n=this.scope,a=this.getScopeHandler();this.scope=new a(this,t);var o=this.prodParam;this.prodParam=new Vt;var l=this.classScope;this.classScope=new hi(this);var c=this.expressionScope;return this.expressionScope=new di(this),function(){e.state.labels=i,e.exportedIdentifiers=r,e.inModule=s,e.scope=n,e.prodParam=o,e.classScope=l,e.expressionScope=c}}},{key:"enterInitialScopes",value:function(){var e=0;(this.inModule||1&this.optionFlags)&&(e|=2),32&this.optionFlags&&(e|=1);var t=!this.inModule&&"commonjs"===this.options.sourceType;(t||2&this.optionFlags)&&(e|=4),this.prodParam.enter(e);var i=t?514:1;4&this.optionFlags&&(i|=512),this.scope.enter(i)}},{key:"checkDestructuringPrivate",value:function(e){var t=e.privateKeyLoc;null!==t&&this.expectPlugin("destructuringPrivate",t)}}])}(li),xi=L((function e(){F(this,e),this.shorthandAssignLoc=null,this.doubleProtoLoc=null,this.privateKeyLoc=null,this.optionalParametersLoc=null,this.voidPatternLoc=null})),bi=L((function e(t,i,r){F(this,e),this.type="",this.start=i,this.end=0,this.loc=new j(r),128&(null==t?void 0:t.optionFlags)&&(this.range=[i,0]),null!=t&&t.filename&&(this.loc.filename=t.filename)})),Pi=bi.prototype;Pi.__clone=function(){for(var e=new bi(void 0,this.start,this.loc.start),t=Object.keys(this),i=0,r=t.length;i<r;i++){var s=t[i];"leadingComments"!==s&&"trailingComments"!==s&&"innerComments"!==s&&(e[s]=this[s])}return e};var ki=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"startNode",value:function(){var e=this.state.startLoc;return new bi(this,e.index,e)}},{key:"startNodeAt",value:function(e){return new bi(this,e.index,e)}},{key:"startNodeAtNode",value:function(e){return this.startNodeAt(e.loc.start)}},{key:"finishNode",value:function(e,t){return this.finishNodeAt(e,t,this.state.lastTokEndLoc)}},{key:"finishNodeAt",value:function(e,t,i){return e.type=t,e.end=i.index,e.loc.end=i,128&this.optionFlags&&(e.range[1]=i.index),4096&this.optionFlags&&this.processComment(e),e}},{key:"resetStartLocation",value:function(e,t){e.start=t.index,e.loc.start=t,128&this.optionFlags&&(e.range[0]=t.index)}},{key:"resetEndLocation",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state.lastTokEndLoc;e.end=t.index,e.loc.end=t,128&this.optionFlags&&(e.range[1]=t.index)}},{key:"resetStartLocationFromNode",value:function(e,t){this.resetStartLocation(e,t.loc.start)}},{key:"castNodeTo",value:function(e,t){return e.type=t,e}},{key:"cloneIdentifier",value:function(e){var t=e.type,i=e.start,r=e.end,s=e.loc,n=e.range,a=e.name,o=Object.create(Pi);return o.type=t,o.start=i,o.end=r,o.loc=s,o.range=n,o.name=a,e.extra&&(o.extra=e.extra),o}},{key:"cloneStringLiteral",value:function(e){var t=e.type,i=e.start,r=e.end,s=e.loc,n=e.range,a=e.extra,o=Object.create(Pi);return o.type=t,o.start=i,o.end=r,o.loc=s,o.range=n,o.extra=a,o.value=e.value,o}}])}(gi),Ti=function(e){return"ParenthesizedExpression"===e.type?Ti(e.expression):e},wi=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"toAssignable",value:function(e){var t,i,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=void 0;switch(("ParenthesizedExpression"===e.type||null!=(t=e.extra)&&t.parenthesized)&&(s=Ti(e),r?"Identifier"===s.type?this.expressionScope.recordArrowParameterBindingError(Z.InvalidParenthesizedAssignment,e):"MemberExpression"===s.type||this.isOptionalMemberExpression(s)||this.raise(Z.InvalidParenthesizedAssignment,e):this.raise(Z.InvalidParenthesizedAssignment,e)),e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":case"VoidPattern":break;case"ObjectExpression":this.castNodeTo(e,"ObjectPattern");for(var n=0,a=e.properties.length,o=a-1;n<a;n++){var l,c=e.properties[n],h=n===o;this.toAssignableObjectExpressionProp(c,h,r),h&&"RestElement"===c.type&&null!=(l=e.extra)&&l.trailingCommaLoc&&this.raise(Z.RestTrailingComma,e.extra.trailingCommaLoc)}break;case"ObjectProperty":var u=e.key,p=e.value;this.isPrivateName(u)&&this.classScope.usePrivateName(this.getPrivateNameSV(u),u.loc.start),this.toAssignable(p,r);break;case"SpreadElement":throw new Error("Internal @babel/parser error (this is a bug, please report it). SpreadElement should be converted by .toAssignable's caller.");case"ArrayExpression":this.castNodeTo(e,"ArrayPattern"),this.toAssignableList(e.elements,null==(i=e.extra)?void 0:i.trailingCommaLoc,r);break;case"AssignmentExpression":"="!==e.operator&&this.raise(Z.MissingEqInAssignment,e.left.loc.end),this.castNodeTo(e,"AssignmentPattern"),delete e.operator,"VoidPattern"===e.left.type&&this.raise(Z.VoidPatternInitializer,e.left),this.toAssignable(e.left,r);break;case"ParenthesizedExpression":this.toAssignable(s,r);break}}},{key:"toAssignableObjectExpressionProp",value:function(e,t,i){if("ObjectMethod"===e.type)this.raise("get"===e.kind||"set"===e.kind?Z.PatternHasAccessor:Z.PatternHasMethod,e.key);else if("SpreadElement"===e.type){this.castNodeTo(e,"RestElement");var r=e.argument;this.checkToRestConversion(r,!1),this.toAssignable(r,i),t||this.raise(Z.RestTrailingComma,e)}else this.toAssignable(e,i)}},{key:"toAssignableList",value:function(e,t,i){for(var r=e.length-1,s=0;s<=r;s++){var n=e[s];n&&(this.toAssignableListItem(e,s,i),"RestElement"===n.type&&(s<r?this.raise(Z.RestTrailingComma,n):t&&this.raise(Z.RestTrailingComma,t)))}}},{key:"toAssignableListItem",value:function(e,t,i){var r=e[t];if("SpreadElement"===r.type){this.castNodeTo(r,"RestElement");var s=r.argument;this.checkToRestConversion(s,!0),this.toAssignable(s,i)}else this.toAssignable(r,i)}},{key:"isAssignable",value:function(e,t){var i=this;switch(e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":case"VoidPattern":return!0;case"ObjectExpression":var r=e.properties.length-1;return e.properties.every((function(e,t){return"ObjectMethod"!==e.type&&(t===r||"SpreadElement"!==e.type)&&i.isAssignable(e)}));case"ObjectProperty":return this.isAssignable(e.value);case"SpreadElement":return this.isAssignable(e.argument);case"ArrayExpression":return e.elements.every((function(e){return null===e||i.isAssignable(e)}));case"AssignmentExpression":return"="===e.operator;case"ParenthesizedExpression":return this.isAssignable(e.expression);case"MemberExpression":case"OptionalMemberExpression":return!t;default:return!1}}},{key:"toReferencedList",value:function(e,t){return e}},{key:"toReferencedListDeep",value:function(e,t){this.toReferencedList(e,t);var i,r=c(e);try{for(r.s();!(i=r.n()).done;){var s=i.value;"ArrayExpression"===(null==s?void 0:s.type)&&this.toReferencedListDeep(s.elements)}}catch(n){r.e(n)}finally{r.f()}}},{key:"parseSpread",value:function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssignAllowIn(e,void 0),this.finishNode(t,"SpreadElement")}},{key:"parseRestBinding",value:function(){var e=this.startNode();this.next();var t=this.parseBindingAtom();return"VoidPattern"===t.type&&this.raise(Z.UnexpectedVoidPattern,t),e.argument=t,this.finishNode(e,"RestElement")}},{key:"parseBindingAtom",value:function(){switch(this.state.type){case 0:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(3,93,1),this.finishNode(e,"ArrayPattern");case 5:return this.parseObjectLike(8,!0);case 88:return this.parseVoidPattern(null)}return this.parseIdentifier()}},{key:"parseBindingList",value:function(e,t,i){var r=1&i,s=[],n=!0;while(!this.eat(e))if(n?n=!1:this.expect(12),r&&this.match(12))s.push(null);else{if(this.eat(e))break;if(this.match(21)){var a=this.parseRestBinding();if((this.hasPlugin("flow")||2&i)&&(a=this.parseFunctionParamType(a)),s.push(a),!this.checkCommaAfterRest(t)){this.expect(e);break}}else{var o=[];if(2&i){this.match(26)&&this.hasPlugin("decorators")&&this.raise(Z.UnsupportedParameterDecorator,this.state.startLoc);while(this.match(26))o.push(this.parseDecorator())}s.push(this.parseBindingElement(i,o))}}return s}},{key:"parseBindingRestProperty",value:function(e){return this.next(),this.hasPlugin("discardBinding")&&this.match(88)?(e.argument=this.parseVoidPattern(null),this.raise(Z.UnexpectedVoidPattern,e.argument)):e.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(e,"RestElement")}},{key:"parseBindingProperty",value:function(){var e=this.state,t=e.type,i=e.startLoc;if(21===t)return this.parseBindingRestProperty(this.startNode());var r=this.startNode();return 139===t?(this.expectPlugin("destructuringPrivate",i),this.classScope.usePrivateName(this.state.value,i),r.key=this.parsePrivateName()):this.parsePropertyName(r),r.method=!1,this.parseObjPropValue(r,i,!1,!1,!0,!1)}},{key:"parseBindingElement",value:function(e,t){var i=this.parseMaybeDefault();(this.hasPlugin("flow")||2&e)&&this.parseFunctionParamType(i),t.length&&(i.decorators=t,this.resetStartLocationFromNode(i,t[0]));var r=this.parseMaybeDefault(i.loc.start,i);return r}},{key:"parseFunctionParamType",value:function(e){return e}},{key:"parseMaybeDefault",value:function(e,t){if(null!=e||(e=this.state.startLoc),t=null!=t?t:this.parseBindingAtom(),!this.eat(29))return t;var i=this.startNodeAt(e);return"VoidPattern"===t.type&&this.raise(Z.VoidPatternInitializer,t),i.left=t,i.right=this.parseMaybeAssignAllowIn(),this.finishNode(i,"AssignmentPattern")}},{key:"isValidLVal",value:function(e,t,i){switch(e){case"AssignmentPattern":return"left";case"RestElement":return"argument";case"ObjectProperty":return"value";case"ParenthesizedExpression":return"expression";case"ArrayPattern":return"elements";case"ObjectPattern":return"properties";case"VoidPattern":return!0}return!1}},{key:"isOptionalMemberExpression",value:function(e){return"OptionalMemberExpression"===e.type}},{key:"checkLVal",value:function(e,t){var i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:64,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],o=e.type;if(!this.isObjectMethod(e)){var l=this.isOptionalMemberExpression(e);if(l||"MemberExpression"===o)return l&&(this.expectPlugin("optionalChainingAssign",e.loc.start),"AssignmentExpression"!==t.type&&this.raise(Z.InvalidLhsOptionalChaining,e,{ancestor:t})),void(64!==r&&this.raise(Z.InvalidPropertyBindingPattern,e));if("Identifier"!==o){"VoidPattern"===o&&"CatchClause"===t.type&&this.raise(Z.VoidPatternCatchClauseParam,e);var h=this.isValidLVal(o,!(a||null!=(i=e.extra)&&i.parenthesized)&&"AssignmentExpression"===t.type,r);if(!0!==h)if(!1!==h){var u,p;if("string"===typeof h)u=h,p="ParenthesizedExpression"===o;else{var d=f(h,2);u=d[0],p=d[1]}var m="ArrayPattern"===o||"ObjectPattern"===o?{type:o}:t,y=e[u];if(Array.isArray(y)){var v,g=c(y);try{for(g.s();!(v=g.n()).done;){var x=v.value;x&&this.checkLVal(x,m,r,s,n,p)}}catch(k){g.e(k)}finally{g.f()}}else y&&this.checkLVal(y,m,r,s,n,p)}else{var b=64===r?Z.InvalidLhs:Z.InvalidLhsBinding;this.raise(b,e,{ancestor:t})}}else{this.checkIdentifier(e,r,n);var P=e.name;s&&(s.has(P)?this.raise(Z.ParamDupe,e):s.add(P))}}}},{key:"checkIdentifier",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.state.strict&&(i?ht(e.name,this.inModule):ct(e.name))&&(64===t?this.raise(Z.StrictEvalArguments,e,{referenceName:e.name}):this.raise(Z.StrictEvalArgumentsBinding,e,{bindingName:e.name})),8192&t&&"let"===e.name&&this.raise(Z.LetInLexicalBinding,e),64&t||this.declareNameFromIdentifier(e,t)}},{key:"declareNameFromIdentifier",value:function(e,t){this.scope.declareName(e.name,t,e.loc.start)}},{key:"checkToRestConversion",value:function(e,t){switch(e.type){case"ParenthesizedExpression":this.checkToRestConversion(e.expression,t);break;case"Identifier":case"MemberExpression":break;case"ArrayExpression":case"ObjectExpression":if(t)break;default:this.raise(Z.InvalidRestAssignmentPattern,e)}}},{key:"checkCommaAfterRest",value:function(e){return!!this.match(12)&&(this.raise(this.lookaheadCharCode()===e?Z.RestTrailingComma:Z.ElementAfterRest,this.state.startLoc),!0)}}])}(ki);function Si(e){if(null==e)throw new Error("Unexpected ".concat(e," value."));return e}function Ai(e){if(!e)throw new Error("Assert fail")}var Ei=Q(o||(o=N(["typescript"])))({AbstractMethodHasImplementation:function(e){var t=e.methodName;return"Method '".concat(t,"' cannot have an implementation because it is marked abstract.")},AbstractPropertyHasInitializer:function(e){var t=e.propertyName;return"Property '".concat(t,"' cannot have an initializer because it is marked abstract.")},AccessorCannotBeOptional:"An 'accessor' property cannot be declared optional.",AccessorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccessorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:function(e){var t=e.kind;return"'declare' is not allowed in ".concat(t,"ters.")},DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:function(e){var t=e.modifier;return"Accessibility modifier already seen: '".concat(t,"'.")},DuplicateModifier:function(e){var t=e.modifier;return"Duplicate modifier: '".concat(t,"'.")},EmptyHeritageClauseType:function(e){var t=e.token;return"'".concat(t,"' list cannot be empty.")},EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",ImportReflectionHasImportType:"An `import module` declaration can not use `type` modifier",IncompatibleModifiers:function(e){var t=e.modifiers;return"'".concat(t[0],"' modifier cannot be used with '").concat(t[1],"' modifier.")},IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:function(e){var t=e.modifier;return"Index signatures cannot have an accessibility modifier ('".concat(t,"').")},IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InitializerNotAllowedInAmbientContext:"Initializers are not allowed in ambient contexts.",InvalidHeritageClauseType:function(e){var t=e.token;return"'".concat(t,"' list can only include identifiers or qualified-names with optional type arguments.")},InvalidModifierOnAwaitUsingDeclaration:function(e){return"'".concat(e,"' modifier cannot appear on an await using declaration.")},InvalidModifierOnTypeMember:function(e){var t=e.modifier;return"'".concat(t,"' modifier cannot appear on a type member.")},InvalidModifierOnTypeParameter:function(e){var t=e.modifier;return"'".concat(t,"' modifier cannot appear on a type parameter.")},InvalidModifierOnTypeParameterPositions:function(e){var t=e.modifier;return"'".concat(t,"' modifier can only appear on a type parameter of a class, interface or type alias.")},InvalidModifierOnUsingDeclaration:function(e){return"'".concat(e,"' modifier cannot appear on a using declaration.")},InvalidModifiersOrder:function(e){var t=e.orderedModifiers;return"'".concat(t[0],"' modifier must precede '").concat(t[1],"' modifier.")},InvalidPropertyAccessAfterInstantiationExpression:"Invalid property access after an instantiation expression. You can either wrap the instantiation expression in parentheses, or delete the type arguments.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MissingInterfaceName:"'interface' declarations must be followed by an identifier.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifier:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:function(e){var t=e.modifier;return"Private elements cannot have an accessibility modifier ('".concat(t,"').")},ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",ReservedArrowTypeParam:"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.",ReservedTypeAssertion:"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.",SetAccessorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccessorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccessorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",SingleTypeParameterWithoutTrailingComma:function(e){var t=e.typeParameterName;return"Single type parameter ".concat(t," should have a trailing comma. Example usage: <").concat(t,",>.")},StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TupleOptionalAfterType:"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",TypeModifierIsUsedInTypeExports:"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.",TypeModifierIsUsedInTypeImports:"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:function(e){var t=e.type;return"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ".concat(t,".")},UsingDeclarationInAmbientContext:function(e){return"'".concat(e,"' declarations are not allowed in ambient contexts.")}});function Ci(e){switch(e){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}function Ii(e){return"private"===e||"public"===e||"protected"===e}function Ni(e){return"in"===e||"out"===e}var Oi=function(e){return function(e){function t(){var e;F(this,t);for(var i=arguments.length,r=new Array(i),s=0;s<i;s++)r[s]=arguments[s];return e=b(this,t,[].concat(r)),e.tsParseInOutModifiers=e.tsParseModifiers.bind(e,{allowedModifiers:["in","out"],disallowedModifiers:["const","public","private","protected","readonly","declare","abstract","override"],errorTemplate:Ei.InvalidModifierOnTypeParameter}),e.tsParseConstModifier=e.tsParseModifiers.bind(e,{allowedModifiers:["const"],disallowedModifiers:["in","out"],errorTemplate:Ei.InvalidModifierOnTypeParameterPositions}),e.tsParseInOutConstModifiers=e.tsParseModifiers.bind(e,{allowedModifiers:["in","out","const"],disallowedModifiers:["public","private","protected","readonly","declare","abstract","override"],errorTemplate:Ei.InvalidModifierOnTypeParameter}),e}return C(t,e),L(t,[{key:"getScopeHandler",value:function(){return Ut}},{key:"tsIsIdentifier",value:function(){return Ce(this.state.type)}},{key:"tsTokenCanFollowModifier",value:function(){return this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(139)||this.isLiteralPropertyName()}},{key:"tsNextTokenOnSameLineAndCanFollowModifier",value:function(){return this.next(),!this.hasPrecedingLineBreak()&&this.tsTokenCanFollowModifier()}},{key:"tsNextTokenCanFollowModifier",value:function(){return this.match(106)?(this.next(),this.tsTokenCanFollowModifier()):this.tsNextTokenOnSameLineAndCanFollowModifier()}},{key:"tsParseModifier",value:function(e,t,i){if(Ce(this.state.type)||58===this.state.type||75===this.state.type){var r=this.state.value;if(e.includes(r)){if(i&&this.match(106))return;if(t&&this.tsIsStartOfStaticBlocks())return;if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this)))return r}}}},{key:"tsParseModifiers",value:function(e,t){for(var i=this,r=e.allowedModifiers,s=e.disallowedModifiers,n=e.stopOnStartOfClassStaticBlock,a=e.errorTemplate,o=void 0===a?Ei.InvalidModifierOnTypeMember:a,l=function(e,r,s,n){r===s&&t[n]&&i.raise(Ei.InvalidModifiersOrder,e,{orderedModifiers:[s,n]})},c=function(e,r,s,n){(t[s]&&r===n||t[n]&&r===s)&&i.raise(Ei.IncompatibleModifiers,e,{modifiers:[s,n]})};;){var h=this.state.startLoc,u=this.tsParseModifier(r.concat(null!=s?s:[]),n,t.static);if(!u)break;Ii(u)?t.accessibility?this.raise(Ei.DuplicateAccessibilityModifier,h,{modifier:u}):(l(h,u,u,"override"),l(h,u,u,"static"),l(h,u,u,"readonly"),t.accessibility=u):Ni(u)?(t[u]&&this.raise(Ei.DuplicateModifier,h,{modifier:u}),t[u]=!0,l(h,u,"in","out")):(hasOwnProperty.call(t,u)?this.raise(Ei.DuplicateModifier,h,{modifier:u}):(l(h,u,"static","readonly"),l(h,u,"static","override"),l(h,u,"override","readonly"),l(h,u,"abstract","override"),c(h,u,"declare","override"),c(h,u,"static","abstract")),t[u]=!0),null!=s&&s.includes(u)&&this.raise(o,h,{modifier:u})}}},{key:"tsIsListTerminator",value:function(e){switch(e){case"EnumMembers":case"TypeMembers":return this.match(8);case"HeritageClauseElement":return this.match(5);case"TupleElementTypes":return this.match(3);case"TypeParametersOrArguments":return this.match(48)}}},{key:"tsParseList",value:function(e,t){var i=[];while(!this.tsIsListTerminator(e))i.push(t());return i}},{key:"tsParseDelimitedList",value:function(e,t,i){return Si(this.tsParseDelimitedListWorker(e,t,!0,i))}},{key:"tsParseDelimitedListWorker",value:function(e,t,i,r){for(var s=[],n=-1;;){if(this.tsIsListTerminator(e))break;n=-1;var a=t();if(null==a)return;if(s.push(a),!this.eat(12)){if(this.tsIsListTerminator(e))break;return void(i&&this.expect(12))}n=this.state.lastTokStartLoc.index}return r&&(r.value=n),s}},{key:"tsParseBracketedList",value:function(e,t,i,r,s){r||(i?this.expect(0):this.expect(47));var n=this.tsParseDelimitedList(e,t,s);return i?this.expect(3):this.expect(48),n}},{key:"tsParseImportType",value:function(){var e=this.startNode();return this.expect(83),this.expect(10),this.match(134)?e.argument=this.parseStringLiteral(this.state.value):(this.raise(Ei.UnsupportedImportTypeArgument,this.state.startLoc),e.argument=w(t,"parseExprAtom",this,3)([])),this.eat(12)?e.options=this.tsParseImportTypeOptions():e.options=null,this.expect(11),this.eat(16)&&(e.qualifier=this.tsParseEntityName(3)),this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSImportType")}},{key:"tsParseImportTypeOptions",value:function(){var e=this.startNode();this.expect(5);var t=this.startNode();return this.isContextual(76)?(t.method=!1,t.key=this.parseIdentifier(!0),t.computed=!1,t.shorthand=!1):this.unexpected(null,76),this.expect(14),t.value=this.tsParseImportTypeWithPropertyValue(),e.properties=[this.finishObjectProperty(t)],this.expect(8),this.finishNode(e,"ObjectExpression")}},{key:"tsParseImportTypeWithPropertyValue",value:function(){var e=this.startNode(),i=[];this.expect(5);while(!this.match(8)){var r=this.state.type;Ce(r)||134===r?i.push(w(t,"parsePropertyDefinition",this,3)([null])):this.unexpected(),this.eat(12)}return e.properties=i,this.next(),this.finishNode(e,"ObjectExpression")}},{key:"tsParseEntityName",value:function(e){var t;if(1&e&&this.match(78))if(2&e)t=this.parseIdentifier(!0);else{var i=this.startNode();this.next(),t=this.finishNode(i,"ThisExpression")}else t=this.parseIdentifier(!!(1&e));while(this.eat(16)){var r=this.startNodeAtNode(t);r.left=t,r.right=this.parseIdentifier(!!(1&e)),t=this.finishNode(r,"TSQualifiedName")}return t}},{key:"tsParseTypeReference",value:function(){var e=this.startNode();return e.typeName=this.tsParseEntityName(1),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeReference")}},{key:"tsParseThisTypePredicate",value:function(e){this.next();var t=this.startNodeAtNode(e);return t.parameterName=e,t.typeAnnotation=this.tsParseTypeAnnotation(!1),t.asserts=!1,this.finishNode(t,"TSTypePredicate")}},{key:"tsParseThisTypeNode",value:function(){var e=this.startNode();return this.next(),this.finishNode(e,"TSThisType")}},{key:"tsParseTypeQuery",value:function(){var e=this.startNode();return this.expect(87),this.match(83)?e.exprName=this.tsParseImportType():e.exprName=this.tsParseEntityName(3),!this.hasPrecedingLineBreak()&&this.match(47)&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeQuery")}},{key:"tsParseTypeParameter",value:function(e){var t=this.startNode();return e(t),t.name=this.tsParseTypeParameterName(),t.constraint=this.tsEatThenParseType(81),t.default=this.tsEatThenParseType(29),this.finishNode(t,"TSTypeParameter")}},{key:"tsTryParseTypeParameters",value:function(e){if(this.match(47))return this.tsParseTypeParameters(e)}},{key:"tsParseTypeParameters",value:function(e){var t=this.startNode();this.match(47)||this.match(143)?this.next():this.unexpected();var i={value:-1};return t.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this,e),!1,!0,i),0===t.params.length&&this.raise(Ei.EmptyTypeParameters,t),-1!==i.value&&this.addExtra(t,"trailingComma",i.value),this.finishNode(t,"TSTypeParameterDeclaration")}},{key:"tsFillSignature",value:function(e,t){var i=19===e,r="parameters",s="typeAnnotation";t.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier),this.expect(10),t[r]=this.tsParseBindingListForSignature(),(i||this.match(e))&&(t[s]=this.tsParseTypeOrTypePredicateAnnotation(e))}},{key:"tsParseBindingListForSignature",value:function(){var e,i=w(t,"parseBindingList",this,3)([11,41,2]),r=c(i);try{for(r.s();!(e=r.n()).done;){var s=e.value,n=s.type;"AssignmentPattern"!==n&&"TSParameterProperty"!==n||this.raise(Ei.UnsupportedSignatureParameterKind,s,{type:n})}}catch(a){r.e(a)}finally{r.f()}return i}},{key:"tsParseTypeMemberSemicolon",value:function(){this.eat(12)||this.isLineTerminator()||this.expect(13)}},{key:"tsParseSignatureMember",value:function(e,t){return this.tsFillSignature(14,t),this.tsParseTypeMemberSemicolon(),this.finishNode(t,e)}},{key:"tsIsUnambiguouslyIndexSignature",value:function(){return this.next(),!!Ce(this.state.type)&&(this.next(),this.match(14))}},{key:"tsTryParseIndexSignature",value:function(e){if(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))){this.expect(0);var t=this.parseIdentifier();t.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(t),this.expect(3),e.parameters=[t];var i=this.tsTryParseTypeAnnotation();return i&&(e.typeAnnotation=i),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSIndexSignature")}}},{key:"tsParsePropertyOrMethodSignature",value:function(e,t){if(this.eat(17)&&(e.optional=!0),this.match(10)||this.match(47)){t&&this.raise(Ei.ReadonlyForMethodSignature,e);var i=e;i.kind&&this.match(47)&&this.raise(Ei.AccessorCannotHaveTypeParameters,this.state.curPosition()),this.tsFillSignature(14,i),this.tsParseTypeMemberSemicolon();var r="parameters",s="typeAnnotation";if("get"===i.kind)i[r].length>0&&(this.raise(Z.BadGetterArity,this.state.curPosition()),this.isThisParam(i[r][0])&&this.raise(Ei.AccessorCannotDeclareThisParameter,this.state.curPosition()));else if("set"===i.kind){if(1!==i[r].length)this.raise(Z.BadSetterArity,this.state.curPosition());else{var n=i[r][0];this.isThisParam(n)&&this.raise(Ei.AccessorCannotDeclareThisParameter,this.state.curPosition()),"Identifier"===n.type&&n.optional&&this.raise(Ei.SetAccessorCannotHaveOptionalParameter,this.state.curPosition()),"RestElement"===n.type&&this.raise(Ei.SetAccessorCannotHaveRestParameter,this.state.curPosition())}i[s]&&this.raise(Ei.SetAccessorCannotHaveReturnType,i[s])}else i.kind="method";return this.finishNode(i,"TSMethodSignature")}var a=e;t&&(a.readonly=!0);var o=this.tsTryParseTypeAnnotation();return o&&(a.typeAnnotation=o),this.tsParseTypeMemberSemicolon(),this.finishNode(a,"TSPropertySignature")}},{key:"tsParseTypeMember",value:function(){var e=this.startNode();if(this.match(10)||this.match(47))return this.tsParseSignatureMember("TSCallSignatureDeclaration",e);if(this.match(77)){var i=this.startNode();return this.next(),this.match(10)||this.match(47)?this.tsParseSignatureMember("TSConstructSignatureDeclaration",e):(e.key=this.createIdentifier(i,"new"),this.tsParsePropertyOrMethodSignature(e,!1))}this.tsParseModifiers({allowedModifiers:["readonly"],disallowedModifiers:["declare","abstract","private","protected","public","static","override"]},e);var r=this.tsTryParseIndexSignature(e);return r||(w(t,"parsePropertyName",this,3)([e]),e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||!this.tsTokenCanFollowModifier()||(e.kind=e.key.name,w(t,"parsePropertyName",this,3)([e]),this.match(10)||this.match(47)||this.unexpected(null,10)),this.tsParsePropertyOrMethodSignature(e,!!e.readonly))}},{key:"tsParseTypeLiteral",value:function(){var e=this.startNode();return e.members=this.tsParseObjectTypeMembers(),this.finishNode(e,"TSTypeLiteral")}},{key:"tsParseObjectTypeMembers",value:function(){this.expect(5);var e=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(8),e}},{key:"tsIsStartOfMappedType",value:function(){return this.next(),this.eat(53)?this.isContextual(122):(this.isContextual(122)&&this.next(),!!this.match(0)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(58))))}},{key:"tsParseMappedType",value:function(){var e=this.startNode();this.expect(5),this.match(53)?(e.readonly=this.state.value,this.next(),this.expectContextual(122)):this.eatContextual(122)&&(e.readonly=!0),this.expect(0);var t=this.startNode();return t.name=this.tsParseTypeParameterName(),t.constraint=this.tsExpectThenParseType(58),e.typeParameter=this.finishNode(t,"TSTypeParameter"),e.nameType=this.eatContextual(93)?this.tsParseType():null,this.expect(3),this.match(53)?(e.optional=this.state.value,this.next(),this.expect(17)):this.eat(17)&&(e.optional=!0),e.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(8),this.finishNode(e,"TSMappedType")}},{key:"tsParseTupleType",value:function(){var e=this,t=this.startNode();t.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);var i=!1;return t.elementTypes.forEach((function(t){var r=t.type;!i||"TSRestType"===r||"TSOptionalType"===r||"TSNamedTupleMember"===r&&t.optional||e.raise(Ei.OptionalTypeBeforeRequired,t),i||(i="TSNamedTupleMember"===r&&t.optional||"TSOptionalType"===r)})),this.finishNode(t,"TSTupleType")}},{key:"tsParseTupleElementType",value:function(){var e,t,i,r,s,n=this.state.startLoc,a=this.eat(21),o=this.state.startLoc,l=Ne(this.state.type),c=l?this.lookaheadCharCode():null;if(58===c)e=!0,i=!1,t=this.parseIdentifier(!0),this.expect(14),r=this.tsParseType();else if(63===c){i=!0;var h=this.state.value,u=this.tsParseNonArrayType();58===this.lookaheadCharCode()?(e=!0,t=this.createIdentifier(this.startNodeAt(o),h),this.expect(17),this.expect(14),r=this.tsParseType()):(e=!1,r=u,this.expect(17))}else r=this.tsParseType(),i=this.eat(17),e=this.eat(14);if(e)t?(s=this.startNodeAt(o),s.optional=i,s.label=t,s.elementType=r,this.eat(17)&&(s.optional=!0,this.raise(Ei.TupleOptionalAfterType,this.state.lastTokStartLoc))):(s=this.startNodeAt(o),s.optional=i,this.raise(Ei.InvalidTupleMemberLabel,r),s.label=r,s.elementType=this.tsParseType()),r=this.finishNode(s,"TSNamedTupleMember");else if(i){var p=this.startNodeAt(o);p.typeAnnotation=r,r=this.finishNode(p,"TSOptionalType")}if(a){var d=this.startNodeAt(n);d.typeAnnotation=r,r=this.finishNode(d,"TSRestType")}return r}},{key:"tsParseParenthesizedType",value:function(){var e=this.startNode();return this.expect(10),e.typeAnnotation=this.tsParseType(),this.expect(11),this.finishNode(e,"TSParenthesizedType")}},{key:"tsParseFunctionOrConstructorType",value:function(e,t){var i=this,r=this.startNode();return"TSConstructorType"===e&&(r.abstract=!!t,t&&this.next(),this.next()),this.tsInAllowConditionalTypesContext((function(){return i.tsFillSignature(19,r)})),this.finishNode(r,e)}},{key:"tsParseLiteralTypeNode",value:function(){var e=this.startNode();switch(this.state.type){case 135:case 136:case 134:case 85:case 86:e.literal=w(t,"parseExprAtom",this,3)([]);break;default:this.unexpected()}return this.finishNode(e,"TSLiteralType")}},{key:"tsParseTemplateLiteralType",value:function(){var e=this.startNode();return e.literal=w(t,"parseTemplate",this,3)([!1]),this.finishNode(e,"TSLiteralType")}},{key:"parseTemplateSubstitution",value:function(){return this.state.inType?this.tsParseType():w(t,"parseTemplateSubstitution",this,3)([])}},{key:"tsParseThisTypeOrThisTypePredicate",value:function(){var e=this.tsParseThisTypeNode();return this.isContextual(116)&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(e):e}},{key:"tsParseNonArrayType",value:function(){switch(this.state.type){case 134:case 135:case 136:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if("-"===this.state.value){var e=this.startNode(),t=this.lookahead();return 135!==t.type&&136!==t.type&&this.unexpected(),e.literal=this.parseMaybeUnary(),this.finishNode(e,"TSLiteralType")}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:var i=this.state.type;if(Ce(i)||88===i||84===i){var r=88===i?"TSVoidKeyword":84===i?"TSNullKeyword":Ci(this.state.value);if(void 0!==r&&46!==this.lookaheadCharCode()){var s=this.startNode();return this.next(),this.finishNode(s,r)}return this.tsParseTypeReference()}}this.unexpected()}},{key:"tsParseArrayTypeOrHigher",value:function(){var e=this.state.startLoc,t=this.tsParseNonArrayType();while(!this.hasPrecedingLineBreak()&&this.eat(0))if(this.match(3)){var i=this.startNodeAt(e);i.elementType=t,this.expect(3),t=this.finishNode(i,"TSArrayType")}else{var r=this.startNodeAt(e);r.objectType=t,r.indexType=this.tsParseType(),this.expect(3),t=this.finishNode(r,"TSIndexedAccessType")}return t}},{key:"tsParseTypeOperator",value:function(){var e=this.startNode(),t=this.state.value;return this.next(),e.operator=t,e.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===t&&this.tsCheckTypeAnnotationForReadOnly(e),this.finishNode(e,"TSTypeOperator")}},{key:"tsCheckTypeAnnotationForReadOnly",value:function(e){switch(e.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(Ei.UnexpectedReadonly,e)}}},{key:"tsParseInferType",value:function(){var e=this,t=this.startNode();this.expectContextual(115);var i=this.startNode();return i.name=this.tsParseTypeParameterName(),i.constraint=this.tsTryParse((function(){return e.tsParseConstraintForInferType()})),t.typeParameter=this.finishNode(i,"TSTypeParameter"),this.finishNode(t,"TSInferType")}},{key:"tsParseConstraintForInferType",value:function(){var e=this;if(this.eat(81)){var t=this.tsInDisallowConditionalTypesContext((function(){return e.tsParseType()}));if(this.state.inDisallowConditionalTypesContext||!this.match(17))return t}}},{key:"tsParseTypeOperatorOrHigher",value:function(){var e=this,t=Ve(this.state.type)&&!this.state.containsEsc;return t?this.tsParseTypeOperator():this.isContextual(115)?this.tsParseInferType():this.tsInAllowConditionalTypesContext((function(){return e.tsParseArrayTypeOrHigher()}))}},{key:"tsParseUnionOrIntersectionType",value:function(e,t,i){var r=this.startNode(),s=this.eat(i),n=[];do{n.push(t())}while(this.eat(i));return 1!==n.length||s?(r.types=n,this.finishNode(r,e)):n[0]}},{key:"tsParseIntersectionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),45)}},{key:"tsParseUnionTypeOrHigher",value:function(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),43)}},{key:"tsIsStartOfFunctionType",value:function(){return!!this.match(47)||this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}},{key:"tsSkipParameterStart",value:function(){if(Ce(this.state.type)||this.match(78))return this.next(),!0;if(this.match(5)){var e=this.state.errors,i=e.length;try{return this.parseObjectLike(8,!0),e.length===i}catch(n){return!1}}if(this.match(0)){this.next();var r=this.state.errors,s=r.length;try{return w(t,"parseBindingList",this,3)([3,93,1]),r.length===s}catch(a){return!1}}return!1}},{key:"tsIsUnambiguouslyStartOfFunctionType",value:function(){if(this.next(),this.match(11)||this.match(21))return!0;if(this.tsSkipParameterStart()){if(this.match(14)||this.match(12)||this.match(17)||this.match(29))return!0;if(this.match(11)&&(this.next(),this.match(19)))return!0}return!1}},{key:"tsParseTypeOrTypePredicateAnnotation",value:function(e){var t=this;return this.tsInType((function(){var i=t.startNode();t.expect(e);var r=t.startNode(),s=!!t.tsTryParse(t.tsParseTypePredicateAsserts.bind(t));if(s&&t.match(78)){var n=t.tsParseThisTypeOrThisTypePredicate();return"TSThisType"===n.type?(r.parameterName=n,r.asserts=!0,r.typeAnnotation=null,n=t.finishNode(r,"TSTypePredicate")):(t.resetStartLocationFromNode(n,r),n.asserts=!0),i.typeAnnotation=n,t.finishNode(i,"TSTypeAnnotation")}var a=t.tsIsIdentifier()&&t.tsTryParse(t.tsParseTypePredicatePrefix.bind(t));if(!a)return s?(r.parameterName=t.parseIdentifier(),r.asserts=s,r.typeAnnotation=null,i.typeAnnotation=t.finishNode(r,"TSTypePredicate"),t.finishNode(i,"TSTypeAnnotation")):t.tsParseTypeAnnotation(!1,i);var o=t.tsParseTypeAnnotation(!1);return r.parameterName=a,r.typeAnnotation=o,r.asserts=s,i.typeAnnotation=t.finishNode(r,"TSTypePredicate"),t.finishNode(i,"TSTypeAnnotation")}))}},{key:"tsTryParseTypeOrTypePredicateAnnotation",value:function(){if(this.match(14))return this.tsParseTypeOrTypePredicateAnnotation(14)}},{key:"tsTryParseTypeAnnotation",value:function(){if(this.match(14))return this.tsParseTypeAnnotation()}},{key:"tsTryParseType",value:function(){return this.tsEatThenParseType(14)}},{key:"tsParseTypePredicatePrefix",value:function(){var e=this.parseIdentifier();if(this.isContextual(116)&&!this.hasPrecedingLineBreak())return this.next(),e}},{key:"tsParseTypePredicateAsserts",value:function(){if(109!==this.state.type)return!1;var e=this.state.containsEsc;return this.next(),!(!Ce(this.state.type)&&!this.match(78))&&(e&&this.raise(Z.InvalidEscapedReservedWord,this.state.lastTokStartLoc,{reservedWord:"asserts"}),!0)}},{key:"tsParseTypeAnnotation",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.startNode();return this.tsInType((function(){t&&e.expect(14),i.typeAnnotation=e.tsParseType()})),this.finishNode(i,"TSTypeAnnotation")}},{key:"tsParseType",value:function(){var e=this;Ai(this.state.inType);var t=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81))return t;var i=this.startNodeAtNode(t);return i.checkType=t,i.extendsType=this.tsInDisallowConditionalTypesContext((function(){return e.tsParseNonConditionalType()})),this.expect(17),i.trueType=this.tsInAllowConditionalTypesContext((function(){return e.tsParseType()})),this.expect(14),i.falseType=this.tsInAllowConditionalTypesContext((function(){return e.tsParseType()})),this.finishNode(i,"TSConditionalType")}},{key:"isAbstractConstructorSignature",value:function(){return this.isContextual(124)&&this.isLookaheadContextual("new")}},{key:"tsParseNonConditionalType",value:function(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(77)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}},{key:"tsParseTypeAssertion",value:function(){var e=this;this.getPluginOption("typescript","disallowAmbiguousJSXLike")&&this.raise(Ei.ReservedTypeAssertion,this.state.startLoc);var t=this.startNode();return t.typeAnnotation=this.tsInType((function(){return e.next(),e.match(75)?e.tsParseTypeReference():e.tsParseType()})),this.expect(48),t.expression=this.parseMaybeUnary(),this.finishNode(t,"TSTypeAssertion")}},{key:"tsParseHeritageClause",value:function(e){var t=this,i=this.state.startLoc,r=this.tsParseDelimitedList("HeritageClauseElement",(function(){var e=t.startNode();return e.expression=t.tsParseEntityName(3),t.match(47)&&(e.typeParameters=t.tsParseTypeArguments()),t.finishNode(e,"TSExpressionWithTypeArguments")}));return r.length||this.raise(Ei.EmptyHeritageClauseType,i,{token:e}),r}},{key:"tsParseInterfaceDeclaration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.hasFollowingLineBreak())return null;this.expectContextual(129),t.declare&&(e.declare=!0),Ce(this.state.type)?(e.id=this.parseIdentifier(),this.checkIdentifier(e.id,130)):(e.id=null,this.raise(Ei.MissingInterfaceName,this.state.startLoc)),e.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers),this.eat(81)&&(e.extends=this.tsParseHeritageClause("extends"));var i=this.startNode();return i.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),e.body=this.finishNode(i,"TSInterfaceBody"),this.finishNode(e,"TSInterfaceDeclaration")}},{key:"tsParseTypeAliasDeclaration",value:function(e){var t=this;return e.id=this.parseIdentifier(),this.checkIdentifier(e.id,2),e.typeAnnotation=this.tsInType((function(){if(e.typeParameters=t.tsTryParseTypeParameters(t.tsParseInOutModifiers),t.expect(29),t.isContextual(114)&&46!==t.lookaheadCharCode()){var i=t.startNode();return t.next(),t.finishNode(i,"TSIntrinsicKeyword")}return t.tsParseType()})),this.semicolon(),this.finishNode(e,"TSTypeAliasDeclaration")}},{key:"tsInTopLevelContext",value:function(e){if(this.curContext()===oe.brace)return e();var t=this.state.context;this.state.context=[t[0]];try{return e()}finally{this.state.context=t}}},{key:"tsInType",value:function(e){var t=this.state.inType;this.state.inType=!0;try{return e()}finally{this.state.inType=t}}},{key:"tsInDisallowConditionalTypesContext",value:function(e){var t=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!0;try{return e()}finally{this.state.inDisallowConditionalTypesContext=t}}},{key:"tsInAllowConditionalTypesContext",value:function(e){var t=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=!1;try{return e()}finally{this.state.inDisallowConditionalTypesContext=t}}},{key:"tsEatThenParseType",value:function(e){if(this.match(e))return this.tsNextThenParseType()}},{key:"tsExpectThenParseType",value:function(e){var t=this;return this.tsInType((function(){return t.expect(e),t.tsParseType()}))}},{key:"tsNextThenParseType",value:function(){var e=this;return this.tsInType((function(){return e.next(),e.tsParseType()}))}},{key:"tsParseEnumMember",value:function(){var e=this.startNode();return e.id=this.match(134)?w(t,"parseStringLiteral",this,3)([this.state.value]):this.parseIdentifier(!0),this.eat(29)&&(e.initializer=w(t,"parseMaybeAssignAllowIn",this,3)([])),this.finishNode(e,"TSEnumMember")}},{key:"tsParseEnumDeclaration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.const&&(e.const=!0),t.declare&&(e.declare=!0),this.expectContextual(126),e.id=this.parseIdentifier(),this.checkIdentifier(e.id,e.const?8971:8459),this.expect(5),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(e,"TSEnumDeclaration")}},{key:"tsParseEnumBody",value:function(){var e=this.startNode();return this.expect(5),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(8),this.finishNode(e,"TSEnumBody")}},{key:"tsParseModuleBlock",value:function(){var e=this.startNode();return this.scope.enter(0),this.expect(5),w(t,"parseBlockOrModuleBlockBody",this,3)([e.body=[],void 0,!0,8]),this.scope.exit(),this.finishNode(e,"TSModuleBlock")}},{key:"tsParseModuleOrNamespaceDeclaration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e.id=this.parseIdentifier(),t||this.checkIdentifier(e.id,1024),this.eat(16)){var i=this.startNode();this.tsParseModuleOrNamespaceDeclaration(i,!0),e.body=i}else this.scope.enter(1024),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(e,"TSModuleDeclaration")}},{key:"tsParseAmbientExternalModuleDeclaration",value:function(e){return this.isContextual(112)?(e.kind="global",e.global=!0,e.id=this.parseIdentifier()):this.match(134)?(e.kind="module",e.id=w(t,"parseStringLiteral",this,3)([this.state.value])):this.unexpected(),this.match(5)?(this.scope.enter(1024),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(e,"TSModuleDeclaration")}},{key:"tsParseImportEqualsDeclaration",value:function(e,t,i){e.isExport=i||!1,e.id=t||this.parseIdentifier(),this.checkIdentifier(e.id,4096),this.expect(29);var r=this.tsParseModuleReference();return"type"===e.importKind&&"TSExternalModuleReference"!==r.type&&this.raise(Ei.ImportAliasHasImportType,r),e.moduleReference=r,this.semicolon(),this.finishNode(e,"TSImportEqualsDeclaration")}},{key:"tsIsExternalModuleReference",value:function(){return this.isContextual(119)&&40===this.lookaheadCharCode()}},{key:"tsParseModuleReference",value:function(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(0)}},{key:"tsParseExternalModuleReference",value:function(){var e=this.startNode();return this.expectContextual(119),this.expect(10),this.match(134)||this.unexpected(),e.expression=w(t,"parseExprAtom",this,3)([]),this.expect(11),this.sawUnambiguousESM=!0,this.finishNode(e,"TSExternalModuleReference")}},{key:"tsLookAhead",value:function(e){var t=this.state.clone(),i=e();return this.state=t,i}},{key:"tsTryParseAndCatch",value:function(e){var t=this.tryParse((function(t){return e()||t()}));if(!t.aborted&&t.node)return t.error&&(this.state=t.failState),t.node}},{key:"tsTryParse",value:function(e){var t=this.state.clone(),i=e();if(void 0!==i&&!1!==i)return i;this.state=t}},{key:"tsTryParseDeclare",value:function(e){var i=this;if(!this.isLineTerminator()){var r=this.state.type;return this.tsInAmbientContext((function(){switch(r){case 68:return e.declare=!0,w(t,"parseFunctionStatement",i,3)([e,!1,!1]);case 80:return e.declare=!0,i.parseClass(e,!0,!1);case 126:return i.tsParseEnumDeclaration(e,{declare:!0});case 112:return i.tsParseAmbientExternalModuleDeclaration(e);case 100:if(i.state.containsEsc)return;case 75:case 74:return i.match(75)&&i.isLookaheadContextual("enum")?(i.expect(75),i.tsParseEnumDeclaration(e,{const:!0,declare:!0})):(e.declare=!0,i.parseVarStatement(e,i.state.value,!0));case 107:if(i.isUsing())return i.raise(Ei.InvalidModifierOnUsingDeclaration,i.state.startLoc,"declare"),e.declare=!0,i.parseVarStatement(e,"using",!0);break;case 96:if(i.isAwaitUsing())return i.raise(Ei.InvalidModifierOnAwaitUsingDeclaration,i.state.startLoc,"declare"),e.declare=!0,i.next(),i.parseVarStatement(e,"await using",!0);break;case 129:var s=i.tsParseInterfaceDeclaration(e,{declare:!0});if(s)return s;default:if(Ce(r))return i.tsParseDeclaration(e,i.state.value,!0,null)}}))}}},{key:"tsTryParseExportDeclaration",value:function(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0,null)}},{key:"tsParseExpressionStatement",value:function(e,t,i){switch(t.name){case"declare":var r=this.tsTryParseDeclare(e);return r&&(r.declare=!0),r;case"global":if(this.match(5)){this.scope.enter(1024),this.prodParam.enter(0);var s=e;return s.kind="global",e.global=!0,s.id=t,s.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(s,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(e,t.name,!1,i)}}},{key:"tsParseDeclaration",value:function(e,t,i,r){switch(t){case"abstract":if(this.tsCheckLineTerminator(i)&&(this.match(80)||Ce(this.state.type)))return this.tsParseAbstractDeclaration(e,r);break;case"module":if(this.tsCheckLineTerminator(i)){if(this.match(134))return this.tsParseAmbientExternalModuleDeclaration(e);if(Ce(this.state.type))return e.kind="module",this.tsParseModuleOrNamespaceDeclaration(e)}break;case"namespace":if(this.tsCheckLineTerminator(i)&&Ce(this.state.type))return e.kind="namespace",this.tsParseModuleOrNamespaceDeclaration(e);break;case"type":if(this.tsCheckLineTerminator(i)&&Ce(this.state.type))return this.tsParseTypeAliasDeclaration(e);break}}},{key:"tsCheckLineTerminator",value:function(e){return e?!this.hasFollowingLineBreak()&&(this.next(),!0):!this.isLineTerminator()}},{key:"tsTryParseGenericAsyncArrowFunction",value:function(e){var i=this;if(this.match(47)){var r=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;var s=this.tsTryParseAndCatch((function(){var r=i.startNodeAt(e);return r.typeParameters=i.tsParseTypeParameters(i.tsParseConstModifier),w(t,"parseFunctionParams",i,3)([r]),r.returnType=i.tsTryParseTypeOrTypePredicateAnnotation(),i.expect(19),r}));if(this.state.maybeInArrowParameters=r,s)return w(t,"parseArrowExpression",this,3)([s,null,!0])}}},{key:"tsParseTypeArgumentsInExpression",value:function(){if(47===this.reScan_lt())return this.tsParseTypeArguments()}},{key:"tsParseTypeArguments",value:function(){var e=this,t=this.startNode();return t.params=this.tsInType((function(){return e.tsInTopLevelContext((function(){return e.expect(47),e.tsParseDelimitedList("TypeParametersOrArguments",e.tsParseType.bind(e))}))})),0===t.params.length?this.raise(Ei.EmptyTypeArguments,t):this.state.inType||this.curContext()!==oe.brace||this.reScan_lt_gt(),this.expect(48),this.finishNode(t,"TSTypeParameterInstantiation")}},{key:"tsIsDeclarationStart",value:function(){return He(this.state.type)}},{key:"isExportDefaultSpecifier",value:function(){return!this.tsIsDeclarationStart()&&w(t,"isExportDefaultSpecifier",this,3)([])}},{key:"parseBindingElement",value:function(e,t){var i=t.length?t[0].loc.start:this.state.startLoc,r={};this.tsParseModifiers({allowedModifiers:["public","private","protected","override","readonly"]},r);var s=r.accessibility,n=r.override,a=r.readonly;4&e||!(s||a||n)||this.raise(Ei.UnexpectedParameterModifier,i);var o=this.parseMaybeDefault();2&e&&this.parseFunctionParamType(o);var l=this.parseMaybeDefault(o.loc.start,o);if(s||a||n){var c=this.startNodeAt(i);return t.length&&(c.decorators=t),s&&(c.accessibility=s),a&&(c.readonly=a),n&&(c.override=n),"Identifier"!==l.type&&"AssignmentPattern"!==l.type&&this.raise(Ei.UnsupportedParameterPropertyKind,c),c.parameter=l,this.finishNode(c,"TSParameterProperty")}return t.length&&(o.decorators=t),l}},{key:"isSimpleParameter",value:function(e){return"TSParameterProperty"===e.type&&w(t,"isSimpleParameter",this,3)([e.parameter])||w(t,"isSimpleParameter",this,3)([e])}},{key:"tsDisallowOptionalPattern",value:function(e){var t,i=c(e.params);try{for(i.s();!(t=i.n()).done;){var r=t.value;"Identifier"!==r.type&&r.optional&&!this.state.isAmbientContext&&this.raise(Ei.PatternIsOptional,r)}}catch(s){i.e(s)}finally{i.f()}}},{key:"setArrowFunctionParameters",value:function(e,i,r){w(t,"setArrowFunctionParameters",this,3)([e,i,r]),this.tsDisallowOptionalPattern(e)}},{key:"parseFunctionBodyAndFinish",value:function(e,i){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.match(14)&&(e.returnType=this.tsParseTypeOrTypePredicateAnnotation(14));var s="FunctionDeclaration"===i?"TSDeclareFunction":"ClassMethod"===i||"ClassPrivateMethod"===i?"TSDeclareMethod":void 0;return s&&!this.match(5)&&this.isLineTerminator()?this.finishNode(e,s):"TSDeclareFunction"===s&&this.state.isAmbientContext&&(this.raise(Ei.DeclareFunctionHasImplementation,e),e.declare)?w(t,"parseFunctionBodyAndFinish",this,3)([e,s,r]):(this.tsDisallowOptionalPattern(e),w(t,"parseFunctionBodyAndFinish",this,3)([e,i,r]))}},{key:"registerFunctionStatementId",value:function(e){!e.body&&e.id?this.checkIdentifier(e.id,1024):w(t,"registerFunctionStatementId",this,3)([e])}},{key:"tsCheckForInvalidTypeCasts",value:function(e){var t=this;e.forEach((function(e){"TSTypeCastExpression"===(null==e?void 0:e.type)&&t.raise(Ei.UnexpectedTypeAnnotation,e.typeAnnotation)}))}},{key:"toReferencedList",value:function(e,t){return this.tsCheckForInvalidTypeCasts(e),e}},{key:"parseArrayLike",value:function(e,i,r,s){var n=w(t,"parseArrayLike",this,3)([e,i,r,s]);return"ArrayExpression"===n.type&&this.tsCheckForInvalidTypeCasts(n.elements),n}},{key:"parseSubscript",value:function(e,i,r,s){var n=this;if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=!1,this.next();var a=this.startNodeAt(i);return a.expression=e,this.finishNode(a,"TSNonNullExpression")}var o=!1;if(this.match(18)&&60===this.lookaheadCharCode()){if(r)return s.stop=!0,e;s.optionalChainMember=o=!0,this.next()}if(this.match(47)||this.match(51)){var l,c=this.tsTryParseAndCatch((function(){if(!r&&n.atPossibleAsyncArrow(e)){var a=n.tsTryParseGenericAsyncArrowFunction(i);if(a)return a}var c=n.tsParseTypeArgumentsInExpression();if(c)if(!o||n.match(10)){if(We(n.state.type)){var h=w(t,"parseTaggedTemplateExpression",n,3)([e,i,s]);return h.typeParameters=c,h}if(!r&&n.eat(10)){var u=n.startNodeAt(i);return u.callee=e,u.arguments=n.parseCallExpressionArguments(),n.tsCheckForInvalidTypeCasts(u.arguments),u.typeParameters=c,s.optionalChainMember&&(u.optional=o),n.finishCallExpression(u,s.optionalChainMember)}var p=n.state.type;if(48!==p&&52!==p&&(10===p||!De(p)||n.hasPrecedingLineBreak())){var d=n.startNodeAt(i);return d.expression=e,d.typeParameters=c,n.finishNode(d,"TSInstantiationExpression")}}else l=n.state.curPosition()}));if(l&&this.unexpected(l,10),c)return"TSInstantiationExpression"===c.type&&((this.match(16)||this.match(18)&&40!==this.lookaheadCharCode())&&this.raise(Ei.InvalidPropertyAccessAfterInstantiationExpression,this.state.startLoc),this.match(16)||this.match(18)||(c.expression=w(t,"stopParseSubscript",this,3)([e,s]))),c}return w(t,"parseSubscript",this,3)([e,i,r,s])}},{key:"parseNewCallee",value:function(e){var i;w(t,"parseNewCallee",this,3)([e]);var r=e.callee;"TSInstantiationExpression"!==r.type||null!=(i=r.extra)&&i.parenthesized||(e.typeParameters=r.typeParameters,e.callee=r.expression)}},{key:"parseExprOp",value:function(e,i,r){var s,n=this;if(qe(58)>r&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(s=this.isContextual(120)))){var a=this.startNodeAt(i);return a.expression=e,a.typeAnnotation=this.tsInType((function(){return n.next(),n.match(75)?(s&&n.raise(Z.UnexpectedKeyword,n.state.startLoc,{keyword:"const"}),n.tsParseTypeReference()):n.tsParseType()})),this.finishNode(a,s?"TSSatisfiesExpression":"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(a,i,r)}return w(t,"parseExprOp",this,3)([e,i,r])}},{key:"checkReservedWord",value:function(e,i,r,s){this.state.isAmbientContext||w(t,"checkReservedWord",this,3)([e,i,r,s])}},{key:"checkImportReflection",value:function(e){w(t,"checkImportReflection",this,3)([e]),e.module&&"value"!==e.importKind&&this.raise(Ei.ImportReflectionHasImportType,e.specifiers[0].loc.start)}},{key:"checkDuplicateExports",value:function(){}},{key:"isPotentialImportPhase",value:function(e){if(w(t,"isPotentialImportPhase",this,3)([e]))return!0;if(this.isContextual(130)){var i=this.lookaheadCharCode();return e?123===i||42===i:61!==i}return!e&&this.isContextual(87)}},{key:"applyImportPhase",value:function(e,i,r,s){w(t,"applyImportPhase",this,3)([e,i,r,s]),i?e.exportKind="type"===r?"type":"value":e.importKind="type"===r||"typeof"===r?r:"value"}},{key:"parseImport",value:function(e){if(this.match(134))return e.importKind="value",w(t,"parseImport",this,3)([e]);var i;if(Ce(this.state.type)&&61===this.lookaheadCharCode())return e.importKind="value",this.tsParseImportEqualsDeclaration(e);if(this.isContextual(130)){var r=this.parseMaybeImportPhase(e,!1);if(61===this.lookaheadCharCode())return this.tsParseImportEqualsDeclaration(e,r);i=w(t,"parseImportSpecifiersAndAfter",this,3)([e,r])}else i=w(t,"parseImport",this,3)([e]);return"type"===i.importKind&&i.specifiers.length>1&&"ImportDefaultSpecifier"===i.specifiers[0].type&&this.raise(Ei.TypeImportCannotSpecifyDefaultAndNamed,i),i}},{key:"parseExport",value:function(e,i){if(this.match(83)){var r=e;this.next();var s=null;this.isContextual(130)&&this.isPotentialImportPhase(!1)?s=this.parseMaybeImportPhase(r,!1):r.importKind="value";var n=this.tsParseImportEqualsDeclaration(r,s,!0);return n}if(this.eat(29)){var a=e;return a.expression=w(t,"parseExpression",this,3)([]),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(a,"TSExportAssignment")}if(this.eatContextual(93)){var o=e;return this.expectContextual(128),o.id=this.parseIdentifier(),this.semicolon(),this.finishNode(o,"TSNamespaceExportDeclaration")}return w(t,"parseExport",this,3)([e,i])}},{key:"isAbstractClass",value:function(){return this.isContextual(124)&&this.isLookaheadContextual("class")}},{key:"parseExportDefaultExpression",value:function(){if(this.isAbstractClass()){var e=this.startNode();return this.next(),e.abstract=!0,this.parseClass(e,!0,!0)}if(this.match(129)){var i=this.tsParseInterfaceDeclaration(this.startNode());if(i)return i}return w(t,"parseExportDefaultExpression",this,3)([])}},{key:"parseVarStatement",value:function(e,i){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=this.state.isAmbientContext,n=w(t,"parseVarStatement",this,3)([e,i,r||s]);if(!s)return n;if(!e.declare&&("using"===i||"await using"===i))return this.raiseOverwrite(Ei.UsingDeclarationInAmbientContext,e,i),n;var a,o=c(n.declarations);try{for(o.s();!(a=o.n()).done;){var l=a.value,h=l.id,u=l.init;u&&("var"===i||"let"===i||h.typeAnnotation?this.raise(Ei.InitializerNotAllowedInAmbientContext,u):Di(u,this.hasPlugin("estree"))||this.raise(Ei.ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference,u))}}catch(p){o.e(p)}finally{o.f()}return n}},{key:"parseStatementContent",value:function(e,i){if(this.match(75)&&this.isLookaheadContextual("enum")){var r=this.startNode();return this.expect(75),this.tsParseEnumDeclaration(r,{const:!0})}if(this.isContextual(126))return this.tsParseEnumDeclaration(this.startNode());if(this.isContextual(129)){var s=this.tsParseInterfaceDeclaration(this.startNode());if(s)return s}return w(t,"parseStatementContent",this,3)([e,i])}},{key:"parseAccessModifier",value:function(){return this.tsParseModifier(["public","protected","private"])}},{key:"tsHasSomeModifiers",value:function(e,t){return t.some((function(t){return Ii(t)?e.accessibility===t:!!e[t]}))}},{key:"tsIsStartOfStaticBlocks",value:function(){return this.isContextual(106)&&123===this.lookaheadCharCode()}},{key:"parseClassMember",value:function(e,i,r){var s=this,n=["declare","private","public","protected","override","abstract","readonly","static"];this.tsParseModifiers({allowedModifiers:n,disallowedModifiers:["in","out"],stopOnStartOfClassStaticBlock:!0,errorTemplate:Ei.InvalidModifierOnTypeParameterPositions},i);var a=function(){s.tsIsStartOfStaticBlocks()?(s.next(),s.next(),s.tsHasSomeModifiers(i,n)&&s.raise(Ei.StaticBlockCannotHaveModifier,s.state.curPosition()),w(t,"parseClassStaticBlock",s,3)([e,i])):s.parseClassMemberWithIsStatic(e,i,r,!!i.static)};i.declare?this.tsInAmbientContext(a):a()}},{key:"parseClassMemberWithIsStatic",value:function(e,i,r,s){var n=this.tsTryParseIndexSignature(i);if(n)return e.body.push(n),i.abstract&&this.raise(Ei.IndexSignatureHasAbstract,i),i.accessibility&&this.raise(Ei.IndexSignatureHasAccessibility,i,{modifier:i.accessibility}),i.declare&&this.raise(Ei.IndexSignatureHasDeclare,i),void(i.override&&this.raise(Ei.IndexSignatureHasOverride,i));!this.state.inAbstractClass&&i.abstract&&this.raise(Ei.NonAbstractClassHasAbstractMethod,i),i.override&&(r.hadSuperClass||this.raise(Ei.OverrideNotInSubClass,i)),w(t,"parseClassMemberWithIsStatic",this,3)([e,i,r,s])}},{key:"parsePostMemberNameModifiers",value:function(e){var t=this.eat(17);t&&(e.optional=!0),e.readonly&&this.match(10)&&this.raise(Ei.ClassMethodHasReadonly,e),e.declare&&this.match(10)&&this.raise(Ei.ClassMethodHasDeclare,e)}},{key:"parseExpressionStatement",value:function(e,i,r){var s="Identifier"===i.type?this.tsParseExpressionStatement(e,i,r):void 0;return s||w(t,"parseExpressionStatement",this,3)([e,i,r])}},{key:"shouldParseExportDeclaration",value:function(){return!!this.tsIsDeclarationStart()||w(t,"shouldParseExportDeclaration",this,3)([])}},{key:"parseConditional",value:function(e,i,r){if(!this.match(17))return e;if(this.state.maybeInArrowParameters){var s=this.lookaheadCharCode();if(44===s||61===s||58===s||41===s)return this.setOptionalParametersError(r),e}return w(t,"parseConditional",this,3)([e,i,r])}},{key:"parseParenItem",value:function(e,i){var r=w(t,"parseParenItem",this,3)([e,i]);if(this.eat(17)&&(r.optional=!0,this.resetEndLocation(e)),this.match(14)){var s=this.startNodeAt(i);return s.expression=e,s.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(s,"TSTypeCastExpression")}return e}},{key:"parseExportDeclaration",value:function(e){var i=this;if(!this.state.isAmbientContext&&this.isContextual(125))return this.tsInAmbientContext((function(){return i.parseExportDeclaration(e)}));var r=this.state.startLoc,s=this.eatContextual(125);if(s&&(this.isContextual(125)||!this.shouldParseExportDeclaration()))throw this.raise(Ei.ExpectedAmbientAfterExportDeclare,this.state.startLoc);var n=Ce(this.state.type),a=n&&this.tsTryParseExportDeclaration()||w(t,"parseExportDeclaration",this,3)([e]);return a?(("TSInterfaceDeclaration"===a.type||"TSTypeAliasDeclaration"===a.type||s)&&(e.exportKind="type"),s&&"TSImportEqualsDeclaration"!==a.type&&(this.resetStartLocation(a,r),a.declare=!0),a):null}},{key:"parseClassId",value:function(e,i,r,s){if(i&&!r||!this.isContextual(113)){w(t,"parseClassId",this,3)([e,i,r,e.declare?1024:8331]);var n=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);n&&(e.typeParameters=n)}}},{key:"parseClassPropertyAnnotation",value:function(e){e.optional||(this.eat(35)?e.definite=!0:this.eat(17)&&(e.optional=!0));var t=this.tsTryParseTypeAnnotation();t&&(e.typeAnnotation=t)}},{key:"parseClassProperty",value:function(e){if(this.parseClassPropertyAnnotation(e),this.state.isAmbientContext&&(!e.readonly||e.typeAnnotation)&&this.match(29)&&this.raise(Ei.DeclareClassFieldHasInitializer,this.state.startLoc),e.abstract&&this.match(29)){var i=e.key;this.raise(Ei.AbstractPropertyHasInitializer,this.state.startLoc,{propertyName:"Identifier"!==i.type||e.computed?"[".concat(this.input.slice(this.offsetToSourcePos(i.start),this.offsetToSourcePos(i.end)),"]"):i.name})}return w(t,"parseClassProperty",this,3)([e])}},{key:"parseClassPrivateProperty",value:function(e){return e.abstract&&this.raise(Ei.PrivateElementHasAbstract,e),e.accessibility&&this.raise(Ei.PrivateElementHasAccessibility,e,{modifier:e.accessibility}),this.parseClassPropertyAnnotation(e),w(t,"parseClassPrivateProperty",this,3)([e])}},{key:"parseClassAccessorProperty",value:function(e){return this.parseClassPropertyAnnotation(e),e.optional&&this.raise(Ei.AccessorCannotBeOptional,e),w(t,"parseClassAccessorProperty",this,3)([e])}},{key:"pushClassMethod",value:function(e,i,r,s,n,a){var o=this.tsTryParseTypeParameters(this.tsParseConstModifier);o&&n&&this.raise(Ei.ConstructorHasTypeParameters,o);var l=i.declare,c=void 0!==l&&l,h=i.kind;!c||"get"!==h&&"set"!==h||this.raise(Ei.DeclareAccessor,i,{kind:h}),o&&(i.typeParameters=o),w(t,"pushClassMethod",this,3)([e,i,r,s,n,a])}},{key:"pushClassPrivateMethod",value:function(e,i,r,s){var n=this.tsTryParseTypeParameters(this.tsParseConstModifier);n&&(i.typeParameters=n),w(t,"pushClassPrivateMethod",this,3)([e,i,r,s])}},{key:"declareClassPrivateMethodInScope",value:function(e,i){"TSDeclareMethod"!==e.type&&("MethodDefinition"===e.type&&null==e.value.body||w(t,"declareClassPrivateMethodInScope",this,3)([e,i]))}},{key:"parseClassSuper",value:function(e){w(t,"parseClassSuper",this,3)([e]),e.superClass&&(this.match(47)||this.match(51))&&(e.superTypeParameters=this.tsParseTypeArgumentsInExpression()),this.eatContextual(113)&&(e.implements=this.tsParseHeritageClause("implements"))}},{key:"parseObjPropValue",value:function(e,i,r,s,n,a,o){var l=this.tsTryParseTypeParameters(this.tsParseConstModifier);return l&&(e.typeParameters=l),w(t,"parseObjPropValue",this,3)([e,i,r,s,n,a,o])}},{key:"parseFunctionParams",value:function(e,i){var r=this.tsTryParseTypeParameters(this.tsParseConstModifier);r&&(e.typeParameters=r),w(t,"parseFunctionParams",this,3)([e,i])}},{key:"parseVarId",value:function(e,i){w(t,"parseVarId",this,3)([e,i]),"Identifier"===e.id.type&&!this.hasPrecedingLineBreak()&&this.eat(35)&&(e.definite=!0);var r=this.tsTryParseTypeAnnotation();r&&(e.id.typeAnnotation=r,this.resetEndLocation(e.id))}},{key:"parseAsyncArrowFromCallExpression",value:function(e,i){return this.match(14)&&(e.returnType=this.tsParseTypeAnnotation()),w(t,"parseAsyncArrowFromCallExpression",this,3)([e,i])}},{key:"parseMaybeAssign",value:function(e,i){var r,s,n,a,o,l,c,h,u,p=this;if(this.hasPlugin("jsx")&&(this.match(143)||this.match(47))){if(l=this.state.clone(),c=this.tryParse((function(){return w(t,"parseMaybeAssign",p,3)([e,i])}),l),!c.error)return c.node;var d=this.state.context,f=d[d.length-1];f!==oe.j_oTag&&f!==oe.j_expr||d.pop()}if((null==(r=c)||!r.error)&&!this.match(47))return w(t,"parseMaybeAssign",this,3)([e,i]);l&&l!==this.state||(l=this.state.clone());var m=this.tryParse((function(r){var s,n;u=p.tsParseTypeParameters(p.tsParseConstModifier);var a=w(t,"parseMaybeAssign",p,3)([e,i]);return("ArrowFunctionExpression"!==a.type||null!=(s=a.extra)&&s.parenthesized)&&r(),0!==(null==(n=u)?void 0:n.params.length)&&p.resetStartLocationFromNode(a,u),a.typeParameters=u,a}),l);if(!m.error&&!m.aborted)return u&&this.reportReservedArrowTypeParam(u),m.node;if(!c&&(Ai(!this.hasPlugin("jsx")),h=this.tryParse((function(){return w(t,"parseMaybeAssign",p,3)([e,i])}),l),!h.error))return h.node;if(null!=(s=c)&&s.node)return this.state=c.failState,c.node;if(m.node)return this.state=m.failState,u&&this.reportReservedArrowTypeParam(u),m.node;if(null!=(n=h)&&n.node)return this.state=h.failState,h.node;throw(null==(a=c)?void 0:a.error)||m.error||(null==(o=h)?void 0:o.error)}},{key:"reportReservedArrowTypeParam",value:function(e){var t;1!==e.params.length||e.params[0].constraint||null!=(t=e.extra)&&t.trailingComma||!this.getPluginOption("typescript","disallowAmbiguousJSXLike")||this.raise(Ei.ReservedArrowTypeParam,e)}},{key:"parseMaybeUnary",value:function(e,i){return!this.hasPlugin("jsx")&&this.match(47)?this.tsParseTypeAssertion():w(t,"parseMaybeUnary",this,3)([e,i])}},{key:"parseArrow",value:function(e){var i=this;if(this.match(14)){var r=this.tryParse((function(e){var t=i.tsParseTypeOrTypePredicateAnnotation(14);return!i.canInsertSemicolon()&&i.match(19)||e(),t}));if(r.aborted)return;r.thrown||(r.error&&(this.state=r.failState),e.returnType=r.node)}return w(t,"parseArrow",this,3)([e])}},{key:"parseFunctionParamType",value:function(e){this.eat(17)&&(e.optional=!0);var t=this.tsTryParseTypeAnnotation();return t&&(e.typeAnnotation=t),this.resetEndLocation(e),e}},{key:"isAssignable",value:function(e,i){switch(e.type){case"TSTypeCastExpression":return this.isAssignable(e.expression,i);case"TSParameterProperty":return!0;default:return w(t,"isAssignable",this,3)([e,i])}}},{key:"toAssignable",value:function(e){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];switch(e.type){case"ParenthesizedExpression":this.toAssignableParenthesizedExpression(e,i);break;case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":i?this.expressionScope.recordArrowParameterBindingError(Ei.UnexpectedTypeCastInParameter,e):this.raise(Ei.UnexpectedTypeCastInParameter,e),this.toAssignable(e.expression,i);break;case"AssignmentExpression":i||"TSTypeCastExpression"!==e.left.type||(e.left=this.typeCastToParameter(e.left));default:w(t,"toAssignable",this,3)([e,i])}}},{key:"toAssignableParenthesizedExpression",value:function(e,i){switch(e.expression.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":this.toAssignable(e.expression,i);break;default:w(t,"toAssignable",this,3)([e,i])}}},{key:"checkToRestConversion",value:function(e,i){switch(e.type){case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":case"TSNonNullExpression":this.checkToRestConversion(e.expression,!1);break;default:w(t,"checkToRestConversion",this,3)([e,i])}}},{key:"isValidLVal",value:function(e,i,r){switch(e){case"TSTypeCastExpression":return!0;case"TSParameterProperty":return"parameter";case"TSNonNullExpression":return"expression";case"TSAsExpression":case"TSSatisfiesExpression":case"TSTypeAssertion":return(64!==r||!i)&&["expression",!0];default:return w(t,"isValidLVal",this,3)([e,i,r])}}},{key:"parseBindingAtom",value:function(){return 78===this.state.type?this.parseIdentifier(!0):w(t,"parseBindingAtom",this,3)([])}},{key:"parseMaybeDecoratorArguments",value:function(e,i){if(this.match(47)||this.match(51)){var r=this.tsParseTypeArgumentsInExpression();if(this.match(10)){var s=w(t,"parseMaybeDecoratorArguments",this,3)([e,i]);return s.typeParameters=r,s}this.unexpected(null,10)}return w(t,"parseMaybeDecoratorArguments",this,3)([e,i])}},{key:"checkCommaAfterRest",value:function(e){return this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===e?(this.next(),!1):w(t,"checkCommaAfterRest",this,3)([e])}},{key:"isClassMethod",value:function(){return this.match(47)||w(t,"isClassMethod",this,3)([])}},{key:"isClassProperty",value:function(){return this.match(35)||this.match(14)||w(t,"isClassProperty",this,3)([])}},{key:"parseMaybeDefault",value:function(e,i){var r=w(t,"parseMaybeDefault",this,3)([e,i]);return"AssignmentPattern"===r.type&&r.typeAnnotation&&r.right.start<r.typeAnnotation.start&&this.raise(Ei.TypeAnnotationAfterAssign,r.typeAnnotation),r}},{key:"getTokenFromCode",value:function(e){if(this.state.inType){if(62===e)return void this.finishOp(48,1);if(60===e)return void this.finishOp(47,1)}w(t,"getTokenFromCode",this,3)([e])}},{key:"reScan_lt_gt",value:function(){var e=this.state.type;47===e?(this.state.pos-=1,this.readToken_lt()):48===e&&(this.state.pos-=1,this.readToken_gt())}},{key:"reScan_lt",value:function(){var e=this.state.type;return 51===e?(this.state.pos-=2,this.finishOp(47,1),47):e}},{key:"toAssignableListItem",value:function(e,i,r){var s=e[i];"TSTypeCastExpression"===s.type&&(e[i]=this.typeCastToParameter(s)),w(t,"toAssignableListItem",this,3)([e,i,r])}},{key:"typeCastToParameter",value:function(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.loc.end),e.expression}},{key:"shouldParseArrow",value:function(e){var i=this;return this.match(14)?e.every((function(e){return i.isAssignable(e,!0)})):w(t,"shouldParseArrow",this,3)([e])}},{key:"shouldParseAsyncArrow",value:function(){return this.match(14)||w(t,"shouldParseAsyncArrow",this,3)([])}},{key:"canHaveLeadingDecorator",value:function(){return w(t,"canHaveLeadingDecorator",this,3)([])||this.isAbstractClass()}},{key:"jsxParseOpeningElementAfterName",value:function(e){var i=this;if(this.match(47)||this.match(51)){var r=this.tsTryParseAndCatch((function(){return i.tsParseTypeArgumentsInExpression()}));r&&(e.typeParameters=r)}return w(t,"jsxParseOpeningElementAfterName",this,3)([e])}},{key:"getGetterSetterExpectedParamCount",value:function(e){var i=w(t,"getGetterSetterExpectedParamCount",this,3)([e]),r=this.getObjectOrClassMethodParams(e),s=r[0],n=s&&this.isThisParam(s);return n?i+1:i}},{key:"parseCatchClauseParam",value:function(){var e=w(t,"parseCatchClauseParam",this,3)([]),i=this.tsTryParseTypeAnnotation();return i&&(e.typeAnnotation=i,this.resetEndLocation(e)),e}},{key:"tsInAmbientContext",value:function(e){var t=this.state,i=t.isAmbientContext,r=t.strict;this.state.isAmbientContext=!0,this.state.strict=!1;try{return e()}finally{this.state.isAmbientContext=i,this.state.strict=r}}},{key:"parseClass",value:function(e,i,r){var s=this.state.inAbstractClass;this.state.inAbstractClass=!!e.abstract;try{return w(t,"parseClass",this,3)([e,i,r])}finally{this.state.inAbstractClass=s}}},{key:"tsParseAbstractDeclaration",value:function(e,t){if(this.match(80))return e.abstract=!0,this.maybeTakeDecorators(t,this.parseClass(e,!0,!1));if(this.isContextual(129)){if(!this.hasFollowingLineBreak())return e.abstract=!0,this.raise(Ei.NonClassMethodPropertyHasAbstractModifier,e),this.tsParseInterfaceDeclaration(e)}else this.unexpected(null,80)}},{key:"parseMethod",value:function(e,i,r,s,n,a,o){var l=w(t,"parseMethod",this,3)([e,i,r,s,n,a,o]);if(l.abstract||"TSAbstractMethodDefinition"===l.type){var c=this.hasPlugin("estree"),h=c?l.value:l;if(h.body){var u=l.key;this.raise(Ei.AbstractMethodHasImplementation,l,{methodName:"Identifier"!==u.type||l.computed?"[".concat(this.input.slice(this.offsetToSourcePos(u.start),this.offsetToSourcePos(u.end)),"]"):u.name})}}return l}},{key:"tsParseTypeParameterName",value:function(){var e=this.parseIdentifier();return e.name}},{key:"shouldParseAsAmbientContext",value:function(){return!!this.getPluginOption("typescript","dts")}},{key:"parse",value:function(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),w(t,"parse",this,3)([])}},{key:"getExpression",value:function(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),w(t,"getExpression",this,3)([])}},{key:"parseExportSpecifier",value:function(e,i,r,s){return!i&&s?(this.parseTypeOnlyImportExportSpecifier(e,!1,r),this.finishNode(e,"ExportSpecifier")):(e.exportKind="value",w(t,"parseExportSpecifier",this,3)([e,i,r,s]))}},{key:"parseImportSpecifier",value:function(e,i,r,s,n){return!i&&s?(this.parseTypeOnlyImportExportSpecifier(e,!0,r),this.finishNode(e,"ImportSpecifier")):(e.importKind="value",w(t,"parseImportSpecifier",this,3)([e,i,r,s,r?4098:4096]))}},{key:"parseTypeOnlyImportExportSpecifier",value:function(e,t,i){var r,s=t?"imported":"local",n=t?"local":"exported",a=e[s],o=!1,l=!0,c=a.loc.start;if(this.isContextual(93)){var h=this.parseIdentifier();if(this.isContextual(93)){var u=this.parseIdentifier();Ne(this.state.type)?(o=!0,a=h,r=t?this.parseIdentifier():this.parseModuleExportName(),l=!1):(r=u,l=!1)}else Ne(this.state.type)?(l=!1,r=t?this.parseIdentifier():this.parseModuleExportName()):(o=!0,a=h)}else Ne(this.state.type)&&(o=!0,t?(a=this.parseIdentifier(!0),this.isContextual(93)||this.checkReservedWord(a.name,a.loc.start,!0,!0)):a=this.parseModuleExportName());o&&i&&this.raise(t?Ei.TypeModifierIsUsedInTypeImports:Ei.TypeModifierIsUsedInTypeExports,c),e[s]=a,e[n]=r;var p=t?"importKind":"exportKind";e[p]=o?"type":"value",l&&this.eatContextual(93)&&(e[n]=t?this.parseIdentifier():this.parseModuleExportName()),e[n]||(e[n]=this.cloneIdentifier(e[s])),t&&this.checkIdentifier(e[n],o?4098:4096)}},{key:"fillOptionalPropertiesForTSESLint",value:function(e){switch(e.type){case"ExpressionStatement":return void(null!=e.directive||(e.directive=void 0));case"RestElement":e.value=void 0;case"Identifier":case"ArrayPattern":case"AssignmentPattern":case"ObjectPattern":return null!=e.decorators||(e.decorators=[]),null!=e.optional||(e.optional=!1),void(null!=e.typeAnnotation||(e.typeAnnotation=void 0));case"TSParameterProperty":return null!=e.accessibility||(e.accessibility=void 0),null!=e.decorators||(e.decorators=[]),null!=e.override||(e.override=!1),null!=e.readonly||(e.readonly=!1),void(null!=e.static||(e.static=!1));case"TSEmptyBodyFunctionExpression":e.body=null;case"TSDeclareFunction":case"FunctionDeclaration":case"FunctionExpression":case"ClassMethod":case"ClassPrivateMethod":return null!=e.declare||(e.declare=!1),null!=e.returnType||(e.returnType=void 0),void(null!=e.typeParameters||(e.typeParameters=void 0));case"Property":return void(null!=e.optional||(e.optional=!1));case"TSMethodSignature":case"TSPropertySignature":null!=e.optional||(e.optional=!1);case"TSIndexSignature":return null!=e.accessibility||(e.accessibility=void 0),null!=e.readonly||(e.readonly=!1),void(null!=e.static||(e.static=!1));case"TSAbstractPropertyDefinition":case"PropertyDefinition":case"TSAbstractAccessorProperty":case"AccessorProperty":null!=e.declare||(e.declare=!1),null!=e.definite||(e.definite=!1),null!=e.readonly||(e.readonly=!1),null!=e.typeAnnotation||(e.typeAnnotation=void 0);case"TSAbstractMethodDefinition":case"MethodDefinition":return null!=e.accessibility||(e.accessibility=void 0),null!=e.decorators||(e.decorators=[]),null!=e.override||(e.override=!1),void(null!=e.optional||(e.optional=!1));case"ClassExpression":null!=e.id||(e.id=null);case"ClassDeclaration":return null!=e.abstract||(e.abstract=!1),null!=e.declare||(e.declare=!1),null!=e.decorators||(e.decorators=[]),null!=e.implements||(e.implements=[]),null!=e.superTypeArguments||(e.superTypeArguments=void 0),void(null!=e.typeParameters||(e.typeParameters=void 0));case"TSTypeAliasDeclaration":case"VariableDeclaration":return void(null!=e.declare||(e.declare=!1));case"VariableDeclarator":return void(null!=e.definite||(e.definite=!1));case"TSEnumDeclaration":return null!=e.const||(e.const=!1),void(null!=e.declare||(e.declare=!1));case"TSEnumMember":return void(null!=e.computed||(e.computed=!1));case"TSImportType":return null!=e.qualifier||(e.qualifier=null),void(null!=e.options||(e.options=null));case"TSInterfaceDeclaration":return null!=e.declare||(e.declare=!1),void(null!=e.extends||(e.extends=[]));case"TSModuleDeclaration":return null!=e.declare||(e.declare=!1),void(null!=e.global||(e.global="global"===e.kind));case"TSTypeParameter":return null!=e.const||(e.const=!1),null!=e.in||(e.in=!1),void(null!=e.out||(e.out=!1))}}}])}(e)};function Li(e){if("MemberExpression"!==e.type)return!1;var t=e.computed,i=e.property;return(!t||"StringLiteral"===i.type||!("TemplateLiteral"!==i.type||i.expressions.length>0))&&Bi(e.object)}function Di(e,t){var i,r=e.type;if(null!=(i=e.extra)&&i.parenthesized)return!1;if(t){if("Literal"===r){var s=e.value;if("string"===typeof s||"boolean"===typeof s)return!0}}else if("StringLiteral"===r||"BooleanLiteral"===r)return!0;return!(!Mi(e,t)&&!Fi(e,t))||("TemplateLiteral"===r&&0===e.expressions.length||!!Li(e))}function Mi(e,t){return t?"Literal"===e.type&&("number"===typeof e.value||"bigint"in e):"NumericLiteral"===e.type||"BigIntLiteral"===e.type}function Fi(e,t){if("UnaryExpression"===e.type){var i=e.operator,r=e.argument;if("-"===i&&Mi(r,t))return!0}return!1}function Bi(e){return"Identifier"===e.type||"MemberExpression"===e.type&&!e.computed&&Bi(e.object)}var _i=Q(l||(l=N(["placeholders"])))({ClassNameIsRequired:"A class name is required.",UnexpectedSpace:"Unexpected space in placeholder."}),ji=function(e){return function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"parsePlaceholder",value:function(e){if(this.match(133)){var i=this.startNode();return this.next(),this.assertNoSpace(),i.name=w(t,"parseIdentifier",this,3)([!0]),this.assertNoSpace(),this.expect(133),this.finishPlaceholder(i,e)}}},{key:"finishPlaceholder",value:function(e,t){var i=e;return i.expectedNode&&i.type||(i=this.finishNode(i,"Placeholder")),i.expectedNode=t,i}},{key:"getTokenFromCode",value:function(e){37===e&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(133,2):w(t,"getTokenFromCode",this,3)([e])}},{key:"parseExprAtom",value:function(e){return this.parsePlaceholder("Expression")||w(t,"parseExprAtom",this,3)([e])}},{key:"parseIdentifier",value:function(e){return this.parsePlaceholder("Identifier")||w(t,"parseIdentifier",this,3)([e])}},{key:"checkReservedWord",value:function(e,i,r,s){void 0!==e&&w(t,"checkReservedWord",this,3)([e,i,r,s])}},{key:"cloneIdentifier",value:function(e){var i=w(t,"cloneIdentifier",this,3)([e]);return"Placeholder"===i.type&&(i.expectedNode=e.expectedNode),i}},{key:"cloneStringLiteral",value:function(e){return"Placeholder"===e.type?this.cloneIdentifier(e):w(t,"cloneStringLiteral",this,3)([e])}},{key:"parseBindingAtom",value:function(){return this.parsePlaceholder("Pattern")||w(t,"parseBindingAtom",this,3)([])}},{key:"isValidLVal",value:function(e,i,r){return"Placeholder"===e||w(t,"isValidLVal",this,3)([e,i,r])}},{key:"toAssignable",value:function(e,i){e&&"Placeholder"===e.type&&"Expression"===e.expectedNode?e.expectedNode="Pattern":w(t,"toAssignable",this,3)([e,i])}},{key:"chStartsBindingIdentifier",value:function(e,i){if(w(t,"chStartsBindingIdentifier",this,3)([e,i]))return!0;var r=this.nextTokenStart();return 37===this.input.charCodeAt(r)&&37===this.input.charCodeAt(r+1)}},{key:"verifyBreakContinue",value:function(e,i){e.label&&"Placeholder"===e.label.type||w(t,"verifyBreakContinue",this,3)([e,i])}},{key:"parseExpressionStatement",value:function(e,i){var r;if("Placeholder"!==i.type||null!=(r=i.extra)&&r.parenthesized)return w(t,"parseExpressionStatement",this,3)([e,i]);if(this.match(14)){var s=e;return s.label=this.finishPlaceholder(i,"Identifier"),this.next(),s.body=w(t,"parseStatementOrSloppyAnnexBFunctionDeclaration",this,3)([]),this.finishNode(s,"LabeledStatement")}this.semicolon();var n=e;return n.name=i.name,this.finishPlaceholder(n,"Statement")}},{key:"parseBlock",value:function(e,i,r){return this.parsePlaceholder("BlockStatement")||w(t,"parseBlock",this,3)([e,i,r])}},{key:"parseFunctionId",value:function(e){return this.parsePlaceholder("Identifier")||w(t,"parseFunctionId",this,3)([e])}},{key:"parseClass",value:function(e,i,r){var s=i?"ClassDeclaration":"ClassExpression";this.next();var n=this.state.strict,a=this.parsePlaceholder("Identifier");if(a){if(!(this.match(81)||this.match(133)||this.match(5))){if(r||!i)return e.id=null,e.body=this.finishPlaceholder(a,"ClassBody"),this.finishNode(e,s);throw this.raise(_i.ClassNameIsRequired,this.state.startLoc)}e.id=a}else this.parseClassId(e,i,r);return w(t,"parseClassSuper",this,3)([e]),e.body=this.parsePlaceholder("ClassBody")||w(t,"parseClassBody",this,3)([!!e.superClass,n]),this.finishNode(e,s)}},{key:"parseExport",value:function(e,i){var r=this.parsePlaceholder("Identifier");if(!r)return w(t,"parseExport",this,3)([e,i]);var s=e;if(!this.isContextual(98)&&!this.match(12))return s.specifiers=[],s.source=null,s.declaration=this.finishPlaceholder(r,"Declaration"),this.finishNode(s,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");var n=this.startNode();return n.exported=r,s.specifiers=[this.finishNode(n,"ExportDefaultSpecifier")],w(t,"parseExport",this,3)([s,i])}},{key:"isExportDefaultSpecifier",value:function(){if(this.match(65)){var e=this.nextTokenStart();if(this.isUnparsedContextual(e,"from")&&this.input.startsWith(ze(133),this.nextTokenStartSince(e+4)))return!0}return w(t,"isExportDefaultSpecifier",this,3)([])}},{key:"maybeParseExportDefaultSpecifier",value:function(e,i){var r;return!(null==(r=e.specifiers)||!r.length)||w(t,"maybeParseExportDefaultSpecifier",this,3)([e,i])}},{key:"checkExport",value:function(e){var i=e.specifiers;null!=i&&i.length&&(e.specifiers=i.filter((function(e){return"Placeholder"===e.exported.type}))),w(t,"checkExport",this,3)([e]),e.specifiers=i}},{key:"parseImport",value:function(e){var i=this.parsePlaceholder("Identifier");if(!i)return w(t,"parseImport",this,3)([e]);if(e.specifiers=[],!this.isContextual(98)&&!this.match(12))return e.source=this.finishPlaceholder(i,"StringLiteral"),this.semicolon(),this.finishNode(e,"ImportDeclaration");var r=this.startNodeAtNode(i);if(r.local=i,e.specifiers.push(this.finishNode(r,"ImportDefaultSpecifier")),this.eat(12)){var s=this.maybeParseStarImportSpecifier(e);s||this.parseNamedImportSpecifiers(e)}return this.expectContextual(98),e.source=this.parseImportSource(),this.semicolon(),this.finishNode(e,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.parsePlaceholder("StringLiteral")||w(t,"parseImportSource",this,3)([])}},{key:"assertNoSpace",value:function(){this.state.start>this.offsetToSourcePos(this.state.lastTokEndLoc.index)&&this.raise(_i.UnexpectedSpace,this.state.lastTokEndLoc)}}])}(e)},Ri=function(e){return function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"parseV8Intrinsic",value:function(){if(this.match(54)){var e=this.state.startLoc,t=this.startNode();if(this.next(),Ce(this.state.type)){var i=this.parseIdentifierName(),r=this.createIdentifier(t,i);if(this.castNodeTo(r,"V8IntrinsicIdentifier"),this.match(10))return r}this.unexpected(e)}}},{key:"parseExprAtom",value:function(e){return this.parseV8Intrinsic()||w(t,"parseExprAtom",this,3)([e])}}])}(e)},Ui=["minimal","fsharp","hack","smart"],Vi=["^^","@@","^","%","#"];function Hi(e){if(e.has("decorators")){if(e.has("decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");var t=e.get("decorators").decoratorsBeforeExport;if(null!=t&&"boolean"!==typeof t)throw new Error("'decoratorsBeforeExport' must be a boolean, if specified.");var i=e.get("decorators").allowCallParenthesized;if(null!=i&&"boolean"!==typeof i)throw new Error("'allowCallParenthesized' must be a boolean.")}if(e.has("flow")&&e.has("typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(e.has("placeholders")&&e.has("v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(e.has("pipelineOperator")){var r,s=e.get("pipelineOperator").proposal;if(!Ui.includes(s)){var n=Ui.map((function(e){return'"'.concat(e,'"')})).join(", ");throw new Error('"pipelineOperator" requires "proposal" option whose value must be one of: '.concat(n,"."))}if("hack"===s){if(e.has("placeholders"))throw new Error("Cannot combine placeholders plugin and Hack-style pipes.");if(e.has("v8intrinsic"))throw new Error("Cannot combine v8intrinsic plugin and Hack-style pipes.");var a,o=e.get("pipelineOperator").topicToken;if(!Vi.includes(o)){var l=Vi.map((function(e){return'"'.concat(e,'"')})).join(", ");throw new Error('"pipelineOperator" in "proposal": "hack" mode also requires a "topicToken" option whose value must be one of: '.concat(l,"."))}if("#"===o&&"hash"===(null==(a=e.get("recordAndTuple"))?void 0:a.syntaxType))throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "hack", topicToken: "#" }]` and `'.concat(JSON.stringify(["recordAndTuple",e.get("recordAndTuple")]),"`."))}else if("smart"===s&&"hash"===(null==(r=e.get("recordAndTuple"))?void 0:r.syntaxType))throw new Error('Plugin conflict between `["pipelineOperator", { proposal: "smart" }]` and `'.concat(JSON.stringify(["recordAndTuple",e.get("recordAndTuple")]),"`."))}if(e.has("moduleAttributes")){if(e.has("deprecatedImportAssert")||e.has("importAssertions"))throw new Error("Cannot combine importAssertions, deprecatedImportAssert and moduleAttributes plugins.");var c=e.get("moduleAttributes").version;if("may-2020"!==c)throw new Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(e.has("importAssertions")&&e.has("deprecatedImportAssert"))throw new Error("Cannot combine importAssertions and deprecatedImportAssert plugins.");if(!e.has("deprecatedImportAssert")&&e.has("importAttributes")&&e.get("importAttributes").deprecatedAssertSyntax&&e.set("deprecatedImportAssert",{}),e.has("recordAndTuple")){var h=e.get("recordAndTuple").syntaxType;if(null!=h){var u=["hash","bar"];if(!u.includes(h))throw new Error("The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: "+u.map((function(e){return"'".concat(e,"'")})).join(", "))}}if(e.has("asyncDoExpressions")&&!e.has("doExpressions")){var p=new Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw p.missingPlugins="doExpressions",p}if(e.has("optionalChainingAssign")&&"2023-07"!==e.get("optionalChainingAssign").version)throw new Error("The 'optionalChainingAssign' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is '2023-07'.");if(e.has("discardBinding")&&"void"!==e.get("discardBinding").syntaxType)throw new Error("The 'discardBinding' plugin requires a 'syntaxType' option. Currently the only supported value is 'void'.")}var zi={estree:ne,jsx:jt,flow:At,typescript:Oi,v8intrinsic:Ri,placeholders:ji},qi=Object.keys(zi),Ki=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"checkProto",value:function(e,t,i,r){if("SpreadElement"===e.type||this.isObjectMethod(e)||e.computed||e.shorthand)return i;var s=e.key,n="Identifier"===s.type?s.name:s.value;return"__proto__"===n?t?(this.raise(Z.RecordNoProto,s),!0):(i&&(r?null===r.doubleProtoLoc&&(r.doubleProtoLoc=s.loc.start):this.raise(Z.DuplicateProto,s)),!0):i}},{key:"shouldExitDescending",value:function(e,t){return"ArrowFunctionExpression"===e.type&&this.offsetToSourcePos(e.start)===t}},{key:"getExpression",value:function(){if(this.enterInitialScopes(),this.nextToken(),this.match(140))throw this.raise(Z.ParseExpressionEmptyInput,this.state.startLoc);var e=this.parseExpression();if(!this.match(140))throw this.raise(Z.ParseExpressionExpectsEOF,this.state.startLoc,{unexpected:this.input.codePointAt(this.state.start)});return this.finalizeRemainingComments(),e.comments=this.comments,e.errors=this.state.errors,256&this.optionFlags&&(e.tokens=this.tokens),e}},{key:"parseExpression",value:function(e,t){var i=this;return e?this.disallowInAnd((function(){return i.parseExpressionBase(t)})):this.allowInAnd((function(){return i.parseExpressionBase(t)}))}},{key:"parseExpressionBase",value:function(e){var t=this.state.startLoc,i=this.parseMaybeAssign(e);if(this.match(12)){var r=this.startNodeAt(t);r.expressions=[i];while(this.eat(12))r.expressions.push(this.parseMaybeAssign(e));return this.toReferencedList(r.expressions),this.finishNode(r,"SequenceExpression")}return i}},{key:"parseMaybeAssignDisallowIn",value:function(e,t){var i=this;return this.disallowInAnd((function(){return i.parseMaybeAssign(e,t)}))}},{key:"parseMaybeAssignAllowIn",value:function(e,t){var i=this;return this.allowInAnd((function(){return i.parseMaybeAssign(e,t)}))}},{key:"setOptionalParametersError",value:function(e){e.optionalParametersLoc=this.state.startLoc}},{key:"parseMaybeAssign",value:function(e,t){var i,r=this.state.startLoc,s=this.isContextual(108);if(s&&this.prodParam.hasYield){this.next();var n=this.parseYield(r);return t&&(n=t.call(this,n,r)),n}e?i=!1:(e=new xi,i=!0);var a=this.state.type;(10===a||Ce(a))&&(this.state.potentialArrowAt=this.state.start);var o=this.parseMaybeConditional(e);if(t&&(o=t.call(this,o,r)),Me(this.state.type)){var l=this.startNodeAt(r),c=this.state.value;if(l.operator=c,this.match(29)){this.toAssignable(o,!0),l.left=o;var h=r.index;null!=e.doubleProtoLoc&&e.doubleProtoLoc.index>=h&&(e.doubleProtoLoc=null),null!=e.shorthandAssignLoc&&e.shorthandAssignLoc.index>=h&&(e.shorthandAssignLoc=null),null!=e.privateKeyLoc&&e.privateKeyLoc.index>=h&&(this.checkDestructuringPrivate(e),e.privateKeyLoc=null),null!=e.voidPatternLoc&&e.voidPatternLoc.index>=h&&(e.voidPatternLoc=null)}else l.left=o;return this.next(),l.right=this.parseMaybeAssign(),this.checkLVal(o,this.finishNode(l,"AssignmentExpression")),l}if(i&&this.checkExpressionErrors(e,!0),s){var u=this.state.type,p=this.hasPlugin("v8intrinsic")?De(u):De(u)&&!this.match(54);if(p&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(Z.YieldNotInGeneratorFunction,r),this.parseYield(r)}return o}},{key:"parseMaybeConditional",value:function(e){var t=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseExprOps(e);return this.shouldExitDescending(r,i)?r:this.parseConditional(r,t,e)}},{key:"parseConditional",value:function(e,t,i){if(this.eat(17)){var r=this.startNodeAt(t);return r.test=e,r.consequent=this.parseMaybeAssignAllowIn(),this.expect(14),r.alternate=this.parseMaybeAssign(),this.finishNode(r,"ConditionalExpression")}return e}},{key:"parseMaybeUnaryOrPrivate",value:function(e){return this.match(139)?this.parsePrivateName():this.parseMaybeUnary(e)}},{key:"parseExprOps",value:function(e){var t=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseMaybeUnaryOrPrivate(e);return this.shouldExitDescending(r,i)?r:this.parseExprOp(r,t,-1)}},{key:"parseExprOp",value:function(e,t,i){if(this.isPrivateName(e)){var r=this.getPrivateNameSV(e);(i>=qe(58)||!this.prodParam.hasIn||!this.match(58))&&this.raise(Z.PrivateInExpectedIn,e,{identifierName:r}),this.classScope.usePrivateName(r,e.loc.start)}var s=this.state.type;if(je(s)&&(this.prodParam.hasIn||!this.match(58))){var n=qe(s);if(n>i){if(39===s){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return e;this.checkPipelineAtInfixOperator(e,t)}var a=this.startNodeAt(t);a.left=e,a.operator=this.state.value;var o=41===s||42===s,l=40===s;if(l&&(n=qe(42)),this.next(),39===s&&this.hasPlugin(["pipelineOperator",{proposal:"minimal"}])&&96===this.state.type&&this.prodParam.hasAwait)throw this.raise(Z.UnexpectedAwaitAfterPipelineBody,this.state.startLoc);a.right=this.parseExprOpRightExpr(s,n);var c=this.finishNode(a,o||l?"LogicalExpression":"BinaryExpression"),h=this.state.type;if(l&&(41===h||42===h)||o&&40===h)throw this.raise(Z.MixingCoalesceWithLogical,this.state.startLoc);return this.parseExprOp(c,t,i)}}return e}},{key:"parseExprOpRightExpr",value:function(e,t){var i=this,r=this.state.startLoc;switch(e){case 39:switch(this.getPluginOption("pipelineOperator","proposal")){case"hack":return this.withTopicBindingContext((function(){return i.parseHackPipeBody()}));case"fsharp":return this.withSoloAwaitPermittingContext((function(){return i.parseFSharpPipelineBody(t)}))}if("smart"===this.getPluginOption("pipelineOperator","proposal"))return this.withTopicBindingContext((function(){if(i.prodParam.hasYield&&i.isContextual(108))throw i.raise(Z.PipeBodyIsTighter,i.state.startLoc);return i.parseSmartPipelineBodyInStyle(i.parseExprOpBaseRightExpr(e,t),r)}));default:return this.parseExprOpBaseRightExpr(e,t)}}},{key:"parseExprOpBaseRightExpr",value:function(e,t){var i=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),i,Ke(e)?t-1:t)}},{key:"parseHackPipeBody",value:function(){var e,t=this.state.startLoc,i=this.parseMaybeAssign(),r=X.has(i.type);return!r||null!=(e=i.extra)&&e.parenthesized||this.raise(Z.PipeUnparenthesizedBody,t,{type:i.type}),this.topicReferenceWasUsedInCurrentContext()||this.raise(Z.PipeTopicUnused,t),i}},{key:"checkExponentialAfterUnary",value:function(e){this.match(57)&&this.raise(Z.UnexpectedTokenUnaryExponentiation,e.argument)}},{key:"parseMaybeUnary",value:function(e,t){var i=this.state.startLoc,r=this.isContextual(96);if(r&&this.recordAwaitIfAllowed()){this.next();var s=this.parseAwait(i);return t||this.checkExponentialAfterUnary(s),s}var n=this.match(34),a=this.startNode();if(Ue(this.state.type)){a.operator=this.state.value,a.prefix=!0,this.match(72)&&this.expectPlugin("throwExpressions");var o=this.match(89);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),this.state.strict&&o){var l=a.argument;"Identifier"===l.type?this.raise(Z.StrictDelete,a):this.hasPropertyAsPrivateName(l)&&this.raise(Z.DeletePrivateField,a)}if(!n)return t||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}var c=this.parseUpdate(a,n,e);if(r){var h=this.state.type,u=this.hasPlugin("v8intrinsic")?De(h):De(h)&&!this.match(54);if(u&&!this.isAmbiguousPrefixOrIdentifier())return this.raiseOverwrite(Z.AwaitNotInAsyncContext,i),this.parseAwait(i)}return c}},{key:"parseUpdate",value:function(e,t,i){if(t){var r=e;return this.checkLVal(r.argument,this.finishNode(r,"UpdateExpression")),e}var s=this.state.startLoc,n=this.parseExprSubscripts(i);if(this.checkExpressionErrors(i,!1))return n;while(Re(this.state.type)&&!this.canInsertSemicolon()){var a=this.startNodeAt(s);a.operator=this.state.value,a.prefix=!1,a.argument=n,this.next(),this.checkLVal(n,n=this.finishNode(a,"UpdateExpression"))}return n}},{key:"parseExprSubscripts",value:function(e){var t=this.state.startLoc,i=this.state.potentialArrowAt,r=this.parseExprAtom(e);return this.shouldExitDescending(r,i)?r:this.parseSubscripts(r,t)}},{key:"parseSubscripts",value:function(e,t,i){var r={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(e),stop:!1};do{e=this.parseSubscript(e,t,i,r),r.maybeAsyncArrow=!1}while(!r.stop);return e}},{key:"parseSubscript",value:function(e,t,i,r){var s=this.state.type;if(!i&&15===s)return this.parseBind(e,t,i,r);if(We(s))return this.parseTaggedTemplateExpression(e,t,r);var n=!1;if(18===s){if(i&&(this.raise(Z.OptionalChainingNoNew,this.state.startLoc),40===this.lookaheadCharCode()))return this.stopParseSubscript(e,r);r.optionalChainMember=n=!0,this.next()}if(!i&&this.match(10))return this.parseCoverCallAndAsyncArrowHead(e,t,r,n);var a=this.eat(0);return a||n||this.eat(16)?this.parseMember(e,t,r,a,n):this.stopParseSubscript(e,r)}},{key:"stopParseSubscript",value:function(e,t){return t.stop=!0,e}},{key:"parseMember",value:function(e,t,i,r,s){var n=this.startNodeAt(t);return n.object=e,n.computed=r,r?(n.property=this.parseExpression(),this.expect(3)):this.match(139)?("Super"===e.type&&this.raise(Z.SuperPrivateField,t),this.classScope.usePrivateName(this.state.value,this.state.startLoc),n.property=this.parsePrivateName()):n.property=this.parseIdentifier(!0),i.optionalChainMember?(n.optional=s,this.finishNode(n,"OptionalMemberExpression")):this.finishNode(n,"MemberExpression")}},{key:"parseBind",value:function(e,t,i,r){var s=this.startNodeAt(t);return s.object=e,this.next(),s.callee=this.parseNoCallExpr(),r.stop=!0,this.parseSubscripts(this.finishNode(s,"BindExpression"),t,i)}},{key:"parseCoverCallAndAsyncArrowHead",value:function(e,t,i,r){var s=this.state.maybeInArrowParameters,n=null;this.state.maybeInArrowParameters=!0,this.next();var a=this.startNodeAt(t);a.callee=e;var o=i.maybeAsyncArrow,l=i.optionalChainMember;o&&(this.expressionScope.enter(yi()),n=new xi),l&&(a.optional=r),a.arguments=r?this.parseCallExpressionArguments():this.parseCallExpressionArguments("Super"!==e.type,a,n);var c=this.finishCallExpression(a,l);return o&&this.shouldParseAsyncArrow()&&!r?(i.stop=!0,this.checkDestructuringPrivate(n),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),c=this.parseAsyncArrowFromCallExpression(this.startNodeAt(t),c)):(o&&(this.checkExpressionErrors(n,!0),this.expressionScope.exit()),this.toReferencedArguments(c)),this.state.maybeInArrowParameters=s,c}},{key:"toReferencedArguments",value:function(e,t){this.toReferencedListDeep(e.arguments,t)}},{key:"parseTaggedTemplateExpression",value:function(e,t,i){var r=this.startNodeAt(t);return r.tag=e,r.quasi=this.parseTemplate(!0),i.optionalChainMember&&this.raise(Z.OptionalChainingNoTemplate,t),this.finishNode(r,"TaggedTemplateExpression")}},{key:"atPossibleAsyncArrow",value:function(e){return"Identifier"===e.type&&"async"===e.name&&this.state.lastTokEndLoc.index===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.offsetToSourcePos(e.start)===this.state.potentialArrowAt}},{key:"finishCallExpression",value:function(e,t){if("Import"===e.callee.type)if(0===e.arguments.length||e.arguments.length>2)this.raise(Z.ImportCallArity,e);else{var i,r=c(e.arguments);try{for(r.s();!(i=r.n()).done;){var s=i.value;"SpreadElement"===s.type&&this.raise(Z.ImportCallSpreadArgument,s)}}catch(n){r.e(n)}finally{r.f()}}return this.finishNode(e,t?"OptionalCallExpression":"CallExpression")}},{key:"parseCallExpressionArguments",value:function(e,t,i){var r=[],s=!0,n=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;while(!this.eat(11)){if(s)s=!1;else if(this.expect(12),this.match(11)){t&&this.addTrailingCommaExtraToNode(t),this.next();break}r.push(this.parseExprListItem(11,!1,i,e))}return this.state.inFSharpPipelineDirectBody=n,r}},{key:"shouldParseAsyncArrow",value:function(){return this.match(19)&&!this.canInsertSemicolon()}},{key:"parseAsyncArrowFromCallExpression",value:function(e,t){var i;return this.resetPreviousNodeTrailingComments(t),this.expect(19),this.parseArrowExpression(e,t.arguments,!0,null==(i=t.extra)?void 0:i.trailingCommaLoc),t.innerComments&&Wt(e,t.innerComments),t.callee.trailingComments&&Wt(e,t.callee.trailingComments),e}},{key:"parseNoCallExpr",value:function(){var e=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),e,!0)}},{key:"parseExprAtom",value:function(e){var t,i=null,r=this.state.type;switch(r){case 79:return this.parseSuper();case 83:return t=this.startNode(),this.next(),this.match(16)?this.parseImportMetaPropertyOrPhaseCall(t):this.match(10)?512&this.optionFlags?this.parseImportCall(t):this.finishNode(t,"Import"):(this.raise(Z.UnsupportedImport,this.state.lastTokStartLoc),this.finishNode(t,"Import"));case 78:return t=this.startNode(),this.next(),this.finishNode(t,"ThisExpression");case 90:return this.parseDo(this.startNode(),!1);case 56:case 31:return this.readRegexp(),this.parseRegExpLiteral(this.state.value);case 135:return this.parseNumericLiteral(this.state.value);case 136:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(!0);case 86:return this.parseBooleanLiteral(!1);case 10:var s=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(s);case 0:return this.parseArrayLike(3,!0,!1,e);case 5:return this.parseObjectLike(8,!1,!1,e);case 68:return this.parseFunctionOrFunctionSent();case 26:i=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(i,this.startNode()),!1);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(!1);case 15:t=this.startNode(),this.next(),t.object=null;var n=t.callee=this.parseNoCallExpr();if("MemberExpression"===n.type)return this.finishNode(t,"BindExpression");throw this.raise(Z.UnsupportedBind,n);case 139:return this.raise(Z.PrivateInExpectedIn,this.state.startLoc,{identifierName:this.state.value}),this.parsePrivateName();case 33:return this.parseTopicReferenceThenEqualsSign(54,"%");case 32:return this.parseTopicReferenceThenEqualsSign(44,"^");case 37:case 38:return this.parseTopicReference("hack");case 44:case 54:case 27:var a=this.getPluginOption("pipelineOperator","proposal");if(a)return this.parseTopicReference(a);this.unexpected();break;case 47:var o=this.input.codePointAt(this.nextTokenStart());tt(o)||62===o?this.expectOnePlugin(["jsx","flow","typescript"]):this.unexpected();break;default:if(137===r)return this.parseDecimalLiteral(this.state.value);if(2===r||1===r)return this.parseArrayLike(2===this.state.type?4:3,!1,!0);if(6===r||7===r)return this.parseObjectLike(6===this.state.type?9:8,!1,!0);if(Ce(r)){if(this.isContextual(127)&&123===this.lookaheadInLineCharCode())return this.parseModuleExpression();var l=this.state.potentialArrowAt===this.state.start,c=this.state.containsEsc,h=this.parseIdentifier();if(!c&&"async"===h.name&&!this.canInsertSemicolon()){var u=this.state.type;if(68===u)return this.resetPreviousNodeTrailingComments(h),this.next(),this.parseAsyncFunctionExpression(this.startNodeAtNode(h));if(Ce(u))return 61===this.lookaheadCharCode()?this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(h)):h;if(90===u)return this.resetPreviousNodeTrailingComments(h),this.parseDo(this.startNodeAtNode(h),!0)}return l&&this.match(19)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(this.startNodeAtNode(h),[h],!1)):h}this.unexpected()}}},{key:"parseTopicReferenceThenEqualsSign",value:function(e,t){var i=this.getPluginOption("pipelineOperator","proposal");if(i)return this.state.type=e,this.state.value=t,this.state.pos--,this.state.end--,this.state.endLoc=R(this.state.endLoc,-1),this.parseTopicReference(i);this.unexpected()}},{key:"parseTopicReference",value:function(e){var t=this.startNode(),i=this.state.startLoc,r=this.state.type;return this.next(),this.finishTopicReference(t,i,e,r)}},{key:"finishTopicReference",value:function(e,t,i,r){if(this.testTopicReferenceConfiguration(i,t,r))return"hack"===i?(this.topicReferenceIsAllowedInCurrentContext()||this.raise(Z.PipeTopicUnbound,t),this.registerTopicReference(),this.finishNode(e,"TopicReference")):(this.topicReferenceIsAllowedInCurrentContext()||this.raise(Z.PrimaryTopicNotAllowed,t),this.registerTopicReference(),this.finishNode(e,"PipelinePrimaryTopicReference"));throw this.raise(Z.PipeTopicUnconfiguredToken,t,{token:ze(r)})}},{key:"testTopicReferenceConfiguration",value:function(e,t,i){switch(e){case"hack":return this.hasPlugin(["pipelineOperator",{topicToken:ze(i)}]);case"smart":return 27===i;default:throw this.raise(Z.PipeTopicRequiresHackPipes,t)}}},{key:"parseAsyncArrowUnaryFunction",value:function(e){this.prodParam.enter(Ht(!0,this.prodParam.hasYield));var t=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(Z.LineTerminatorBeforeArrow,this.state.curPosition()),this.expect(19),this.parseArrowExpression(e,t,!0)}},{key:"parseDo",value:function(e,t){this.expectPlugin("doExpressions"),t&&this.expectPlugin("asyncDoExpressions"),e.async=t,this.next();var i=this.state.labels;return this.state.labels=[],t?(this.prodParam.enter(2),e.body=this.parseBlock(),this.prodParam.exit()):e.body=this.parseBlock(),this.state.labels=i,this.finishNode(e,"DoExpression")}},{key:"parseSuper",value:function(){var e=this.startNode();return this.next(),!this.match(10)||this.scope.allowDirectSuper||16&this.optionFlags?this.scope.allowSuper||16&this.optionFlags||this.raise(Z.UnexpectedSuper,e):this.raise(Z.SuperNotAllowed,e),this.match(10)||this.match(0)||this.match(16)||this.raise(Z.UnsupportedSuper,e),this.finishNode(e,"Super")}},{key:"parsePrivateName",value:function(){var e=this.startNode(),t=this.startNodeAt(R(this.state.startLoc,1)),i=this.state.value;return this.next(),e.id=this.createIdentifier(t,i),this.finishNode(e,"PrivateName")}},{key:"parseFunctionOrFunctionSent",value:function(){var e=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(16)){var t=this.createIdentifier(this.startNodeAtNode(e),"function");return this.next(),this.match(103)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected(),this.parseMetaProperty(e,t,"sent")}return this.parseFunction(e)}},{key:"parseMetaProperty",value:function(e,t,i){e.meta=t;var r=this.state.containsEsc;return e.property=this.parseIdentifier(!0),(e.property.name!==i||r)&&this.raise(Z.UnsupportedMetaProperty,e.property,{target:t.name,onlyValidPropertyName:i}),this.finishNode(e,"MetaProperty")}},{key:"parseImportMetaPropertyOrPhaseCall",value:function(e){if(this.next(),this.isContextual(105)||this.isContextual(97)){var t=this.isContextual(105);return this.expectPlugin(t?"sourcePhaseImports":"deferredImportEvaluation"),this.next(),e.phase=t?"source":"defer",this.parseImportCall(e)}var i=this.createIdentifierAt(this.startNodeAtNode(e),"import",this.state.lastTokStartLoc);return this.isContextual(101)&&(this.inModule||this.raise(Z.ImportMetaOutsideModule,i),this.sawUnambiguousESM=!0),this.parseMetaProperty(e,i,"meta")}},{key:"parseLiteralAtNode",value:function(e,t,i){return this.addExtra(i,"rawValue",e),this.addExtra(i,"raw",this.input.slice(this.offsetToSourcePos(i.start),this.state.end)),i.value=e,this.next(),this.finishNode(i,t)}},{key:"parseLiteral",value:function(e,t){var i=this.startNode();return this.parseLiteralAtNode(e,t,i)}},{key:"parseStringLiteral",value:function(e){return this.parseLiteral(e,"StringLiteral")}},{key:"parseNumericLiteral",value:function(e){return this.parseLiteral(e,"NumericLiteral")}},{key:"parseBigIntLiteral",value:function(e){return this.parseLiteral(e,"BigIntLiteral")}},{key:"parseDecimalLiteral",value:function(e){return this.parseLiteral(e,"DecimalLiteral")}},{key:"parseRegExpLiteral",value:function(e){var t=this.startNode();return this.addExtra(t,"raw",this.input.slice(this.offsetToSourcePos(t.start),this.state.end)),t.pattern=e.pattern,t.flags=e.flags,this.next(),this.finishNode(t,"RegExpLiteral")}},{key:"parseBooleanLiteral",value:function(e){var t=this.startNode();return t.value=e,this.next(),this.finishNode(t,"BooleanLiteral")}},{key:"parseNullLiteral",value:function(){var e=this.startNode();return this.next(),this.finishNode(e,"NullLiteral")}},{key:"parseParenAndDistinguishExpression",value:function(e){var t,i=this.state.startLoc;this.next(),this.expressionScope.enter(mi());var r=this.state.maybeInArrowParameters,s=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;var n,a,o=this.state.startLoc,l=[],c=new xi,h=!0;while(!this.match(11)){if(h)h=!1;else if(this.expect(12,null===c.optionalParametersLoc?null:c.optionalParametersLoc),this.match(11)){a=this.state.startLoc;break}if(this.match(21)){var u=this.state.startLoc;if(n=this.state.startLoc,l.push(this.parseParenItem(this.parseRestBinding(),u)),!this.checkCommaAfterRest(41))break}else l.push(this.parseMaybeAssignAllowInOrVoidPattern(11,c,this.parseParenItem))}var p=this.state.lastTokEndLoc;this.expect(11),this.state.maybeInArrowParameters=r,this.state.inFSharpPipelineDirectBody=s;var d=this.startNodeAt(i);return e&&this.shouldParseArrow(l)&&(d=this.parseArrow(d))?(this.checkDestructuringPrivate(c),this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(d,l,!1),d):(this.expressionScope.exit(),l.length||this.unexpected(this.state.lastTokStartLoc),a&&this.unexpected(a),n&&this.unexpected(n),this.checkExpressionErrors(c,!0),this.toReferencedListDeep(l,!0),l.length>1?(t=this.startNodeAt(o),t.expressions=l,this.finishNode(t,"SequenceExpression"),this.resetEndLocation(t,p)):t=l[0],this.wrapParenthesis(i,t))}},{key:"wrapParenthesis",value:function(e,t){if(!(1024&this.optionFlags))return this.addExtra(t,"parenthesized",!0),this.addExtra(t,"parenStart",e.index),this.takeSurroundingComments(t,e.index,this.state.lastTokEndLoc.index),t;var i=this.startNodeAt(e);return i.expression=t,this.finishNode(i,"ParenthesizedExpression")}},{key:"shouldParseArrow",value:function(e){return!this.canInsertSemicolon()}},{key:"parseArrow",value:function(e){if(this.eat(19))return e}},{key:"parseParenItem",value:function(e,t){return e}},{key:"parseNewOrNewTarget",value:function(){var e=this.startNode();if(this.next(),this.match(16)){var t=this.createIdentifier(this.startNodeAtNode(e),"new");this.next();var i=this.parseMetaProperty(e,t,"target");return this.scope.allowNewTarget||this.raise(Z.UnexpectedNewTarget,i),i}return this.parseNew(e)}},{key:"parseNew",value:function(e){if(this.parseNewCallee(e),this.eat(10)){var t=this.parseExprList(11);this.toReferencedList(t),e.arguments=t}else e.arguments=[];return this.finishNode(e,"NewExpression")}},{key:"parseNewCallee",value:function(e){var t=this.match(83),i=this.parseNoCallExpr();e.callee=i,!t||"Import"!==i.type&&"ImportExpression"!==i.type||this.raise(Z.ImportCallNotNewExpression,i)}},{key:"parseTemplateElement",value:function(e){var t=this.state,i=t.start,r=t.startLoc,s=t.end,n=t.value,a=i+1,o=this.startNodeAt(R(r,1));null===n&&(e||this.raise(Z.InvalidEscapeSequenceTemplate,R(this.state.firstInvalidTemplateEscapePos,1)));var l=this.match(24),c=l?-1:-2,h=s+c;o.value={raw:this.input.slice(a,h).replace(/\r\n?/g,"\n"),cooked:null===n?null:n.slice(1,c)},o.tail=l,this.next();var u=this.finishNode(o,"TemplateElement");return this.resetEndLocation(u,R(this.state.lastTokEndLoc,c)),u}},{key:"parseTemplate",value:function(e){var t=this.startNode(),i=this.parseTemplateElement(e),r=[i],s=[];while(!i.tail)s.push(this.parseTemplateSubstitution()),this.readTemplateContinuation(),r.push(i=this.parseTemplateElement(e));return t.expressions=s,t.quasis=r,this.finishNode(t,"TemplateLiteral")}},{key:"parseTemplateSubstitution",value:function(){return this.parseExpression()}},{key:"parseObjectLike",value:function(e,t,i,r){i&&this.expectPlugin("recordAndTuple");var s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;var n=!1,a=!0,o=this.startNode();o.properties=[],this.next();while(!this.match(e)){if(a)a=!1;else if(this.expect(12),this.match(e)){this.addTrailingCommaExtraToNode(o);break}var l=void 0;t?l=this.parseBindingProperty():(l=this.parsePropertyDefinition(r),n=this.checkProto(l,i,n,r)),i&&!this.isObjectProperty(l)&&"SpreadElement"!==l.type&&this.raise(Z.InvalidRecordProperty,l),l.shorthand&&this.addExtra(l,"shorthand",!0),o.properties.push(l)}this.next(),this.state.inFSharpPipelineDirectBody=s;var c="ObjectExpression";return t?c="ObjectPattern":i&&(c="RecordExpression"),this.finishNode(o,c)}},{key:"addTrailingCommaExtraToNode",value:function(e){this.addExtra(e,"trailingComma",this.state.lastTokStartLoc.index),this.addExtra(e,"trailingCommaLoc",this.state.lastTokStartLoc,!1)}},{key:"maybeAsyncOrAccessorProp",value:function(e){return!e.computed&&"Identifier"===e.key.type&&(this.isLiteralPropertyName()||this.match(0)||this.match(55))}},{key:"parsePropertyDefinition",value:function(e){var t=[];if(this.match(26)){this.hasPlugin("decorators")&&this.raise(Z.UnsupportedPropertyDecorator,this.state.startLoc);while(this.match(26))t.push(this.parseDecorator())}var i,r=this.startNode(),s=!1,n=!1;if(this.match(21))return t.length&&this.unexpected(),this.parseSpread();t.length&&(r.decorators=t,t=[]),r.method=!1,e&&(i=this.state.startLoc);var a=this.eat(55);this.parsePropertyNamePrefixOperator(r);var o=this.state.containsEsc;if(this.parsePropertyName(r,e),!a&&!o&&this.maybeAsyncOrAccessorProp(r)){var l=r.key,c=l.name;"async"!==c||this.hasPrecedingLineBreak()||(s=!0,this.resetPreviousNodeTrailingComments(l),a=this.eat(55),this.parsePropertyName(r)),"get"!==c&&"set"!==c||(n=!0,this.resetPreviousNodeTrailingComments(l),r.kind=c,this.match(55)&&(a=!0,this.raise(Z.AccessorIsGenerator,this.state.curPosition(),{kind:c}),this.next()),this.parsePropertyName(r))}return this.parseObjPropValue(r,i,a,s,!1,n,e)}},{key:"getGetterSetterExpectedParamCount",value:function(e){return"get"===e.kind?0:1}},{key:"getObjectOrClassMethodParams",value:function(e){return e.params}},{key:"checkGetterSetterParams",value:function(e){var t,i=this.getGetterSetterExpectedParamCount(e),r=this.getObjectOrClassMethodParams(e);r.length!==i&&this.raise("get"===e.kind?Z.BadGetterArity:Z.BadSetterArity,e),"set"===e.kind&&"RestElement"===(null==(t=r[r.length-1])?void 0:t.type)&&this.raise(Z.BadSetterRestParameter,e)}},{key:"parseObjectMethod",value:function(e,t,i,r,s){if(s){var n=this.parseMethod(e,t,!1,!1,!1,"ObjectMethod");return this.checkGetterSetterParams(n),n}if(i||t||this.match(10))return r&&this.unexpected(),e.kind="method",e.method=!0,this.parseMethod(e,t,i,!1,!1,"ObjectMethod")}},{key:"parseObjectProperty",value:function(e,t,i,r){if(e.shorthand=!1,this.eat(14))return e.value=i?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowInOrVoidPattern(8,r),this.finishObjectProperty(e);if(!e.computed&&"Identifier"===e.key.type){if(this.checkReservedWord(e.key.name,e.key.loc.start,!0,!1),i)e.value=this.parseMaybeDefault(t,this.cloneIdentifier(e.key));else if(this.match(29)){var s=this.state.startLoc;null!=r?null===r.shorthandAssignLoc&&(r.shorthandAssignLoc=s):this.raise(Z.InvalidCoverInitializedName,s),e.value=this.parseMaybeDefault(t,this.cloneIdentifier(e.key))}else e.value=this.cloneIdentifier(e.key);return e.shorthand=!0,this.finishObjectProperty(e)}}},{key:"finishObjectProperty",value:function(e){return this.finishNode(e,"ObjectProperty")}},{key:"parseObjPropValue",value:function(e,t,i,r,s,n,a){var o=this.parseObjectMethod(e,i,r,s,n)||this.parseObjectProperty(e,t,s,a);return o||this.unexpected(),o}},{key:"parsePropertyName",value:function(e,t){if(this.eat(0))e.computed=!0,e.key=this.parseMaybeAssignAllowIn(),this.expect(3);else{var i,r=this.state,s=r.type,n=r.value;if(Ne(s))i=this.parseIdentifier(!0);else switch(s){case 135:i=this.parseNumericLiteral(n);break;case 134:i=this.parseStringLiteral(n);break;case 136:i=this.parseBigIntLiteral(n);break;case 139:var a=this.state.startLoc;null!=t?null===t.privateKeyLoc&&(t.privateKeyLoc=a):this.raise(Z.UnexpectedPrivateField,a),i=this.parsePrivateName();break;default:if(137===s){i=this.parseDecimalLiteral(n);break}this.unexpected()}e.key=i,139!==s&&(e.computed=!1)}}},{key:"initFunction",value:function(e,t){e.id=null,e.generator=!1,e.async=t}},{key:"parseMethod",value:function(e,t,i,r,s,n){var a=arguments.length>6&&void 0!==arguments[6]&&arguments[6];this.initFunction(e,i),e.generator=t,this.scope.enter(530|(a?576:0)|(s?32:0)),this.prodParam.enter(Ht(i,e.generator)),this.parseFunctionParams(e,r);var o=this.parseFunctionBodyAndFinish(e,n,!0);return this.prodParam.exit(),this.scope.exit(),o}},{key:"parseArrayLike",value:function(e,t,i,r){i&&this.expectPlugin("recordAndTuple");var s=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;var n=this.startNode();return this.next(),n.elements=this.parseExprList(e,!i,r,n),this.state.inFSharpPipelineDirectBody=s,this.finishNode(n,i?"TupleExpression":"ArrayExpression")}},{key:"parseArrowExpression",value:function(e,t,i,r){this.scope.enter(518);var s=Ht(i,!1);!this.match(5)&&this.prodParam.hasIn&&(s|=8),this.prodParam.enter(s),this.initFunction(e,i);var n=this.state.maybeInArrowParameters;return t&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(e,t,r)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(e,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=n,this.finishNode(e,"ArrowFunctionExpression")}},{key:"setArrowFunctionParameters",value:function(e,t,i){this.toAssignableList(t,i,!1),e.params=t}},{key:"parseFunctionBodyAndFinish",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.parseFunctionBody(e,!1,i),this.finishNode(e,t)}},{key:"parseFunctionBody",value:function(e,t){var i=this,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=t&&!this.match(5);if(this.expressionScope.enter(vi()),s)e.body=this.parseMaybeAssign(),this.checkParams(e,!1,t,!1);else{var n=this.state.strict,a=this.state.labels;this.state.labels=[],this.prodParam.enter(4|this.prodParam.currentFlags()),e.body=this.parseBlock(!0,!1,(function(s){var a=!i.isSimpleParamList(e.params);s&&a&&i.raise(Z.IllegalLanguageModeDirective,"method"!==e.kind&&"constructor"!==e.kind||!e.key?e:e.key.loc.end);var o=!n&&i.state.strict;i.checkParams(e,!i.state.strict&&!t&&!r&&!a,t,o),i.state.strict&&e.id&&i.checkIdentifier(e.id,65,o)})),this.prodParam.exit(),this.state.labels=a}this.expressionScope.exit()}},{key:"isSimpleParameter",value:function(e){return"Identifier"===e.type}},{key:"isSimpleParamList",value:function(e){for(var t=0,i=e.length;t<i;t++)if(!this.isSimpleParameter(e[t]))return!1;return!0}},{key:"checkParams",value:function(e,t,i){var r,s=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],n=!t&&new Set,a={type:"FormalParameters"},o=c(e.params);try{for(o.s();!(r=o.n()).done;){var l=r.value;this.checkLVal(l,a,5,n,s)}}catch(h){o.e(h)}finally{o.f()}}},{key:"parseExprList",value:function(e,t,i,r){var s=[],n=!0;while(!this.eat(e)){if(n)n=!1;else if(this.expect(12),this.match(e)){r&&this.addTrailingCommaExtraToNode(r),this.next();break}s.push(this.parseExprListItem(e,t,i))}return s}},{key:"parseExprListItem",value:function(e,t,i,r){var s;if(this.match(12))t||this.raise(Z.UnexpectedToken,this.state.curPosition(),{unexpected:","}),s=null;else if(this.match(21)){var n=this.state.startLoc;s=this.parseParenItem(this.parseSpread(i),n)}else if(this.match(17)){this.expectPlugin("partialApplication"),r||this.raise(Z.UnexpectedArgumentPlaceholder,this.state.startLoc);var a=this.startNode();this.next(),s=this.finishNode(a,"ArgumentPlaceholder")}else s=this.parseMaybeAssignAllowInOrVoidPattern(e,i,this.parseParenItem);return s}},{key:"parseIdentifier",value:function(e){var t=this.startNode(),i=this.parseIdentifierName(e);return this.createIdentifier(t,i)}},{key:"createIdentifier",value:function(e,t){return e.name=t,e.loc.identifierName=t,this.finishNode(e,"Identifier")}},{key:"createIdentifierAt",value:function(e,t,i){return e.name=t,e.loc.identifierName=t,this.finishNodeAt(e,"Identifier",i)}},{key:"parseIdentifierName",value:function(e){var t,i=this.state,r=i.startLoc,s=i.type;Ne(s)?t=this.state.value:this.unexpected();var n=Ie(s);return e?n&&this.replaceToken(132):this.checkReservedWord(t,r,n,!1),this.next(),t}},{key:"checkReservedWord",value:function(e,t,i,r){if(!(e.length>10)&&ft(e))if(i&&ut(e))this.raise(Z.UnexpectedKeyword,t,{keyword:e});else{var s=this.state.strict?r?ht:lt:ot;if(s(e,this.inModule))this.raise(Z.UnexpectedReservedWord,t,{reservedWord:e});else if("yield"===e){if(this.prodParam.hasYield)return void this.raise(Z.YieldBindingIdentifier,t)}else if("await"===e){if(this.prodParam.hasAwait)return void this.raise(Z.AwaitBindingIdentifier,t);if(this.scope.inStaticBlock)return void this.raise(Z.AwaitBindingIdentifierInStaticBlock,t);this.expressionScope.recordAsyncArrowParametersError(t)}else if("arguments"===e&&this.scope.inClassAndNotInNonArrowFunction)return void this.raise(Z.ArgumentsInClass,t)}}},{key:"recordAwaitIfAllowed",value:function(){var e=this.prodParam.hasAwait;return e&&!this.scope.inFunction&&(this.state.hasTopLevelAwait=!0),e}},{key:"parseAwait",value:function(e){var t=this.startNodeAt(e);return this.expressionScope.recordParameterInitializerError(Z.AwaitExpressionFormalParameter,t),this.eat(55)&&this.raise(Z.ObsoleteAwaitStar,t),this.scope.inFunction||1&this.optionFlags||(this.isAmbiguousPrefixOrIdentifier()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(t.argument=this.parseMaybeUnary(null,!0)),this.finishNode(t,"AwaitExpression")}},{key:"isAmbiguousPrefixOrIdentifier",value:function(){if(this.hasPrecedingLineBreak())return!0;var e=this.state.type;return 53===e||10===e||0===e||We(e)||102===e&&!this.state.containsEsc||138===e||56===e||this.hasPlugin("v8intrinsic")&&54===e}},{key:"parseYield",value:function(e){var t=this.startNodeAt(e);this.expressionScope.recordParameterInitializerError(Z.YieldInParameter,t);var i=!1,r=null;if(!this.hasPrecedingLineBreak())switch(i=this.eat(55),this.state.type){case 13:case 140:case 8:case 11:case 3:case 9:case 14:case 12:if(!i)break;default:r=this.parseMaybeAssign()}return t.delegate=i,t.argument=r,this.finishNode(t,"YieldExpression")}},{key:"parseImportCall",value:function(e){if(this.next(),e.source=this.parseMaybeAssignAllowIn(),e.options=null,this.eat(12))if(this.match(11))this.addTrailingCommaExtraToNode(e.source);else if(e.options=this.parseMaybeAssignAllowIn(),this.eat(12)&&(this.addTrailingCommaExtraToNode(e.options),!this.match(11))){do{this.parseMaybeAssignAllowIn()}while(this.eat(12)&&!this.match(11));this.raise(Z.ImportCallArity,e)}return this.expect(11),this.finishNode(e,"ImportExpression")}},{key:"checkPipelineAtInfixOperator",value:function(e,t){this.hasPlugin(["pipelineOperator",{proposal:"smart"}])&&"SequenceExpression"===e.type&&this.raise(Z.PipelineHeadSequenceExpression,t)}},{key:"parseSmartPipelineBodyInStyle",value:function(e,t){if(this.isSimpleReference(e)){var i=this.startNodeAt(t);return i.callee=e,this.finishNode(i,"PipelineBareFunction")}var r=this.startNodeAt(t);return this.checkSmartPipeTopicBodyEarlyErrors(t),r.expression=e,this.finishNode(r,"PipelineTopicExpression")}},{key:"isSimpleReference",value:function(e){switch(e.type){case"MemberExpression":return!e.computed&&this.isSimpleReference(e.object);case"Identifier":return!0;default:return!1}}},{key:"checkSmartPipeTopicBodyEarlyErrors",value:function(e){if(this.match(19))throw this.raise(Z.PipelineBodyNoArrow,this.state.startLoc);this.topicReferenceWasUsedInCurrentContext()||this.raise(Z.PipelineTopicUnused,e)}},{key:"withTopicBindingContext",value:function(e){var t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}},{key:"withSmartMixTopicForbiddingContext",value:function(e){if(!this.hasPlugin(["pipelineOperator",{proposal:"smart"}]))return e();var t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}},{key:"withSoloAwaitPermittingContext",value:function(e){var t=this.state.soloAwait;this.state.soloAwait=!0;try{return e()}finally{this.state.soloAwait=t}}},{key:"allowInAnd",value:function(e){var t=this.prodParam.currentFlags(),i=8&~t;if(i){this.prodParam.enter(8|t);try{return e()}finally{this.prodParam.exit()}}return e()}},{key:"disallowInAnd",value:function(e){var t=this.prodParam.currentFlags(),i=8&t;if(i){this.prodParam.enter(-9&t);try{return e()}finally{this.prodParam.exit()}}return e()}},{key:"registerTopicReference",value:function(){this.state.topicContext.maxTopicIndex=0}},{key:"topicReferenceIsAllowedInCurrentContext",value:function(){return this.state.topicContext.maxNumOfResolvableTopics>=1}},{key:"topicReferenceWasUsedInCurrentContext",value:function(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}},{key:"parseFSharpPipelineBody",value:function(e){var t=this.state.startLoc;this.state.potentialArrowAt=this.state.start;var i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;var r=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),t,e);return this.state.inFSharpPipelineDirectBody=i,r}},{key:"parseModuleExpression",value:function(){this.expectPlugin("moduleBlocks");var e=this.startNode();this.next(),this.match(5)||this.unexpected(null,5);var t=this.startNodeAt(this.state.endLoc);this.next();var i=this.initializeScopes(!0);this.enterInitialScopes();try{e.body=this.parseProgram(t,8,"module")}finally{i()}return this.finishNode(e,"ModuleExpression")}},{key:"parseVoidPattern",value:function(e){this.expectPlugin("discardBinding");var t=this.startNode();return null!=e&&(e.voidPatternLoc=this.state.startLoc),this.next(),this.finishNode(t,"VoidPattern")}},{key:"parseMaybeAssignAllowInOrVoidPattern",value:function(e,t,i){if(null!=t&&this.match(88)){var r=this.lookaheadCharCode();if(44===r||r===(3===e?93:8===e?125:41)||61===r)return this.parseMaybeDefault(this.state.startLoc,this.parseVoidPattern(t))}return this.parseMaybeAssignAllowIn(t,i)}},{key:"parsePropertyNamePrefixOperator",value:function(e){}}])}(wi),Wi={kind:1},Xi={kind:2},Ji=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,Yi=new RegExp("in(?:stanceof)?","y");function Gi(e,t,i){for(var r=0;r<e.length;r++){var s=e[r],n=s.type;if("number"===typeof n){if(139===n){var a=s.loc,o=s.start,l=s.value,c=s.end,h=o+1,u=R(a.start,1);e.splice(r,1,new oi({type:Xe(27),value:"#",start:o,end:h,startLoc:a.start,endLoc:u}),new oi({type:Xe(132),value:l,start:h,end:c,startLoc:u,endLoc:a.end})),r++;continue}if(We(n)){var p=s.loc,d=s.start,f=s.value,m=s.end,y=d+1,v=R(p.start,1),g=void 0;g=96===t.charCodeAt(d-i)?new oi({type:Xe(22),value:"`",start:d,end:y,startLoc:p.start,endLoc:v}):new oi({type:Xe(8),value:"}",start:d,end:y,startLoc:p.start,endLoc:v});var x=void 0,b=void 0,P=void 0,k=void 0;24===n?(b=m-1,P=R(p.end,-1),x=null===f?null:f.slice(1,-1),k=new oi({type:Xe(22),value:"`",start:b,end:m,startLoc:P,endLoc:p.end})):(b=m-2,P=R(p.end,-2),x=null===f?null:f.slice(1,-2),k=new oi({type:Xe(23),value:"${",start:b,end:m,startLoc:P,endLoc:p.end})),e.splice(r,1,g,new oi({type:Xe(20),value:x,start:y,end:b,startLoc:v,endLoc:P}),k),r+=2;continue}s.type=Xe(n)}}return e}var $i=function(e){function t(){return F(this,t),b(this,t,arguments)}return C(t,e),L(t,[{key:"parseTopLevel",value:function(e,t){return e.program=this.parseProgram(t,140,"module"===this.options.sourceType?"module":"script"),e.comments=this.comments,256&this.optionFlags&&(e.tokens=Gi(this.tokens,this.input,this.startIndex)),this.finishNode(e,"File")}},{key:"parseProgram",value:function(e,t,i){if(e.sourceType=i,e.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(e,!0,!0,t),this.inModule){if(!(64&this.optionFlags)&&this.scope.undefinedExports.size>0)for(var r=0,s=Array.from(this.scope.undefinedExports);r<s.length;r++){var n=f(s[r],2),a=n[0],o=n[1];this.raise(Z.ModuleExportUndefined,o,{localName:a})}this.addExtra(e,"topLevelAwait",this.state.hasTopLevelAwait)}var l;return l=140===t?this.finishNode(e,"Program"):this.finishNodeAt(e,"Program",R(this.state.startLoc,-1)),l}},{key:"stmtToDirective",value:function(e){var t=this.castNodeTo(e,"Directive"),i=this.castNodeTo(e.expression,"DirectiveLiteral"),r=i.value,s=this.input.slice(this.offsetToSourcePos(i.start),this.offsetToSourcePos(i.end)),n=i.value=s.slice(1,-1);return this.addExtra(i,"raw",s),this.addExtra(i,"rawValue",n),this.addExtra(i,"expressionValue",r),t.value=i,delete e.expression,t}},{key:"parseInterpreterDirective",value:function(){if(!this.match(28))return null;var e=this.startNode();return e.value=this.state.value,this.next(),this.finishNode(e,"InterpreterDirective")}},{key:"isLet",value:function(){return!!this.isContextual(100)&&this.hasFollowingBindingAtom()}},{key:"isUsing",value:function(){if(!this.isContextual(107))return!1;var e=this.nextTokenInLineStart(),t=this.codePointAtPos(e);return this.chStartsBindingIdentifier(t,e)}},{key:"isForUsing",value:function(){if(!this.isContextual(107))return!1;var e=this.nextTokenInLineStart(),t=this.codePointAtPos(e);if(this.isUnparsedContextual(e,"of")){var i=this.lookaheadCharCodeSince(e+2);if(61!==i&&58!==i&&59!==i)return!1}return!(!this.chStartsBindingIdentifier(t,e)&&!this.isUnparsedContextual(e,"void"))}},{key:"isAwaitUsing",value:function(){if(!this.isContextual(96))return!1;var e=this.nextTokenInLineStart();if(this.isUnparsedContextual(e,"using")){e=this.nextTokenInLineStartSince(e+5);var t=this.codePointAtPos(e);if(this.chStartsBindingIdentifier(t,e))return!0}return!1}},{key:"chStartsBindingIdentifier",value:function(e,t){if(tt(e)){if(Yi.lastIndex=t,Yi.test(this.input)){var i=this.codePointAtPos(Yi.lastIndex);if(!it(i)&&92!==i)return!1}return!0}return 92===e}},{key:"chStartsBindingPattern",value:function(e){return 91===e||123===e}},{key:"hasFollowingBindingAtom",value:function(){var e=this.nextTokenStart(),t=this.codePointAtPos(e);return this.chStartsBindingPattern(t)||this.chStartsBindingIdentifier(t,e)}},{key:"hasInLineFollowingBindingIdentifierOrBrace",value:function(){var e=this.nextTokenInLineStart(),t=this.codePointAtPos(e);return 123===t||this.chStartsBindingIdentifier(t,e)}},{key:"allowsUsing",value:function(){return(this.scope.inModule||!this.scope.inTopLevel)&&!this.scope.inBareCaseStatement}},{key:"parseModuleItem",value:function(){return this.parseStatementLike(15)}},{key:"parseStatementListItem",value:function(){return this.parseStatementLike(6|(!this.options.annexB||this.state.strict?0:8))}},{key:"parseStatementOrSloppyAnnexBFunctionDeclaration",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=0;return this.options.annexB&&!this.state.strict&&(t|=4,e&&(t|=8)),this.parseStatementLike(t)}},{key:"parseStatement",value:function(){return this.parseStatementLike(0)}},{key:"parseStatementLike",value:function(e){var t=null;return this.match(26)&&(t=this.parseDecorators(!0)),this.parseStatementContent(e,t)}},{key:"parseStatementContent",value:function(e,t){var i=this.state.type,r=this.startNode(),s=!!(2&e),n=!!(4&e),a=1&e;switch(i){case 60:return this.parseBreakContinueStatement(r,!0);case 63:return this.parseBreakContinueStatement(r,!1);case 64:return this.parseDebuggerStatement(r);case 90:return this.parseDoWhileStatement(r);case 91:return this.parseForStatement(r);case 68:if(46===this.lookaheadCharCode())break;return n||this.raise(this.state.strict?Z.StrictFunction:this.options.annexB?Z.SloppyFunctionAnnexB:Z.SloppyFunction,this.state.startLoc),this.parseFunctionStatement(r,!1,!s&&n);case 80:return s||this.unexpected(),this.parseClass(this.maybeTakeDecorators(t,r),!0);case 69:return this.parseIfStatement(r);case 70:return this.parseReturnStatement(r);case 71:return this.parseSwitchStatement(r);case 72:return this.parseThrowStatement(r);case 73:return this.parseTryStatement(r);case 96:if(this.isAwaitUsing())return this.allowsUsing()?s?this.recordAwaitIfAllowed()||this.raise(Z.AwaitUsingNotInAsyncContext,r):this.raise(Z.UnexpectedLexicalDeclaration,r):this.raise(Z.UnexpectedUsingDeclaration,r),this.next(),this.parseVarStatement(r,"await using");break;case 107:if(this.state.containsEsc||!this.hasInLineFollowingBindingIdentifierOrBrace())break;return this.allowsUsing()?s||this.raise(Z.UnexpectedLexicalDeclaration,this.state.startLoc):this.raise(Z.UnexpectedUsingDeclaration,this.state.startLoc),this.parseVarStatement(r,"using");case 100:if(this.state.containsEsc)break;var o=this.nextTokenStart(),l=this.codePointAtPos(o);if(91!==l){if(!s&&this.hasFollowingLineBreak())break;if(!this.chStartsBindingIdentifier(l,o)&&123!==l)break}case 75:s||this.raise(Z.UnexpectedLexicalDeclaration,this.state.startLoc);case 74:var c=this.state.value;return this.parseVarStatement(r,c);case 92:return this.parseWhileStatement(r);case 76:return this.parseWithStatement(r);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(r);case 83:var h=this.lookaheadCharCode();if(40===h||46===h)break;case 82:var u;return 8&this.optionFlags||a||this.raise(Z.UnexpectedImportExport,this.state.startLoc),this.next(),u=83===i?this.parseImport(r):this.parseExport(r,t),this.assertModuleNodeAllowed(u),u;default:if(this.isAsyncFunction())return s||this.raise(Z.AsyncFunctionInSingleStatementContext,this.state.startLoc),this.next(),this.parseFunctionStatement(r,!0,!s&&n)}var p=this.state.value,d=this.parseExpression();return Ce(i)&&"Identifier"===d.type&&this.eat(14)?this.parseLabeledStatement(r,p,d,e):this.parseExpressionStatement(r,d,t)}},{key:"assertModuleNodeAllowed",value:function(e){8&this.optionFlags||this.inModule||this.raise(Z.ImportOutsideModule,e)}},{key:"decoratorsEnabledBeforeExport",value:function(){return!!this.hasPlugin("decorators-legacy")||this.hasPlugin("decorators")&&!1!==this.getPluginOption("decorators","decoratorsBeforeExport")}},{key:"maybeTakeDecorators",value:function(e,t,i){if(e){var r,s;if(null!=(r=t.decorators)&&r.length)"boolean"!==typeof this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(Z.DecoratorsBeforeAfterExport,t.decorators[0]),(s=t.decorators).unshift.apply(s,h(e));else t.decorators=e;this.resetStartLocationFromNode(t,e[0]),i&&this.resetStartLocationFromNode(i,t)}return t}},{key:"canHaveLeadingDecorator",value:function(){return this.match(80)}},{key:"parseDecorators",value:function(e){var t=[];do{t.push(this.parseDecorator())}while(this.match(26));if(this.match(82))e||this.unexpected(),this.decoratorsEnabledBeforeExport()||this.raise(Z.DecoratorExportClass,this.state.startLoc);else if(!this.canHaveLeadingDecorator())throw this.raise(Z.UnexpectedLeadingDecorator,this.state.startLoc);return t}},{key:"parseDecorator",value:function(){this.expectOnePlugin(["decorators","decorators-legacy"]);var e=this.startNode();if(this.next(),this.hasPlugin("decorators")){var t,i=this.state.startLoc;if(this.match(10)){var r=this.state.startLoc;this.next(),t=this.parseExpression(),this.expect(11),t=this.wrapParenthesis(r,t);var s=this.state.startLoc;e.expression=this.parseMaybeDecoratorArguments(t,r),!1===this.getPluginOption("decorators","allowCallParenthesized")&&e.expression!==t&&this.raise(Z.DecoratorArgumentsOutsideParentheses,s)}else{t=this.parseIdentifier(!1);while(this.eat(16)){var n=this.startNodeAt(i);n.object=t,this.match(139)?(this.classScope.usePrivateName(this.state.value,this.state.startLoc),n.property=this.parsePrivateName()):n.property=this.parseIdentifier(!0),n.computed=!1,t=this.finishNode(n,"MemberExpression")}e.expression=this.parseMaybeDecoratorArguments(t,i)}}else e.expression=this.parseExprSubscripts();return this.finishNode(e,"Decorator")}},{key:"parseMaybeDecoratorArguments",value:function(e,t){if(this.eat(10)){var i=this.startNodeAt(t);return i.callee=e,i.arguments=this.parseCallExpressionArguments(),this.toReferencedList(i.arguments),this.finishNode(i,"CallExpression")}return e}},{key:"parseBreakContinueStatement",value:function(e,t){return this.next(),this.isLineTerminator()?e.label=null:(e.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(e,t),this.finishNode(e,t?"BreakStatement":"ContinueStatement")}},{key:"verifyBreakContinue",value:function(e,t){var i;for(i=0;i<this.state.labels.length;++i){var r=this.state.labels[i];if(null==e.label||r.name===e.label.name){if(null!=r.kind&&(t||1===r.kind))break;if(e.label&&t)break}}if(i===this.state.labels.length){var s=t?"BreakStatement":"ContinueStatement";this.raise(Z.IllegalBreakContinue,e,{type:s})}}},{key:"parseDebuggerStatement",value:function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")}},{key:"parseHeaderExpression",value:function(){this.expect(10);var e=this.parseExpression();return this.expect(11),e}},{key:"parseDoWhileStatement",value:function(e){var t=this;return this.next(),this.state.labels.push(Wi),e.body=this.withSmartMixTopicForbiddingContext((function(){return t.parseStatement()})),this.state.labels.pop(),this.expect(92),e.test=this.parseHeaderExpression(),this.eat(13),this.finishNode(e,"DoWhileStatement")}},{key:"parseForStatement",value:function(e){this.next(),this.state.labels.push(Wi);var t=null;if(this.isContextual(96)&&this.recordAwaitIfAllowed()&&(t=this.state.startLoc,this.next()),this.scope.enter(0),this.expect(10),this.match(13))return null!==t&&this.unexpected(t),this.parseFor(e,null);var i=this.isContextual(100),r=this.isAwaitUsing(),s=r||this.isForUsing(),n=i&&this.hasFollowingBindingAtom()||s;if(this.match(74)||this.match(75)||n){var a,o=this.startNode();r?(a="await using",this.recordAwaitIfAllowed()||this.raise(Z.AwaitUsingNotInAsyncContext,this.state.startLoc),this.next()):a=this.state.value,this.next(),this.parseVar(o,!0,a);var l=this.finishNode(o,"VariableDeclaration"),c=this.match(58);return c&&s&&this.raise(Z.ForInUsing,l),(c||this.isContextual(102))&&1===l.declarations.length?this.parseForIn(e,l,t):(null!==t&&this.unexpected(t),this.parseFor(e,l))}var h=this.isContextual(95),u=new xi,p=this.parseExpression(!0,u),d=this.isContextual(102);if(d&&(i&&this.raise(Z.ForOfLet,p),null===t&&h&&"Identifier"===p.type&&this.raise(Z.ForOfAsync,p)),d||this.match(58)){this.checkDestructuringPrivate(u),this.toAssignable(p,!0);var f=d?"ForOfStatement":"ForInStatement";return this.checkLVal(p,{type:f}),this.parseForIn(e,p,t)}return this.checkExpressionErrors(u,!0),null!==t&&this.unexpected(t),this.parseFor(e,p)}},{key:"parseFunctionStatement",value:function(e,t,i){return this.next(),this.parseFunction(e,1|(i?2:0)|(t?8:0))}},{key:"parseIfStatement",value:function(e){return this.next(),e.test=this.parseHeaderExpression(),e.consequent=this.parseStatementOrSloppyAnnexBFunctionDeclaration(),e.alternate=this.eat(66)?this.parseStatementOrSloppyAnnexBFunctionDeclaration():null,this.finishNode(e,"IfStatement")}},{key:"parseReturnStatement",value:function(e){return this.prodParam.hasReturn||this.raise(Z.IllegalReturn,this.state.startLoc),this.next(),this.isLineTerminator()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")}},{key:"parseSwitchStatement",value:function(e){this.next(),e.discriminant=this.parseHeaderExpression();var t,i,r=e.cases=[];for(this.expect(5),this.state.labels.push(Xi),this.scope.enter(256);!this.match(8);)if(this.match(61)||this.match(65)){var s=this.match(61);t&&this.finishNode(t,"SwitchCase"),r.push(t=this.startNode()),t.consequent=[],this.next(),s?t.test=this.parseExpression():(i&&this.raise(Z.MultipleDefaultsInSwitch,this.state.lastTokStartLoc),i=!0,t.test=null),this.expect(14)}else t?t.consequent.push(this.parseStatementListItem()):this.unexpected();return this.scope.exit(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(e,"SwitchStatement")}},{key:"parseThrowStatement",value:function(e){return this.next(),this.hasPrecedingLineBreak()&&this.raise(Z.NewlineAfterThrow,this.state.lastTokEndLoc),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")}},{key:"parseCatchClauseParam",value:function(){var e=this.parseBindingAtom();return this.scope.enter(this.options.annexB&&"Identifier"===e.type?8:0),this.checkLVal(e,{type:"CatchClause"},9),e}},{key:"parseTryStatement",value:function(e){var t=this;if(this.next(),e.block=this.parseBlock(),e.handler=null,this.match(62)){var i=this.startNode();this.next(),this.match(10)?(this.expect(10),i.param=this.parseCatchClauseParam(),this.expect(11)):(i.param=null,this.scope.enter(0)),i.body=this.withSmartMixTopicForbiddingContext((function(){return t.parseBlock(!1,!1)})),this.scope.exit(),e.handler=this.finishNode(i,"CatchClause")}return e.finalizer=this.eat(67)?this.parseBlock():null,e.handler||e.finalizer||this.raise(Z.NoCatchOrFinally,e),this.finishNode(e,"TryStatement")}},{key:"parseVarStatement",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.next(),this.parseVar(e,!1,t,i),this.semicolon(),this.finishNode(e,"VariableDeclaration")}},{key:"parseWhileStatement",value:function(e){var t=this;return this.next(),e.test=this.parseHeaderExpression(),this.state.labels.push(Wi),e.body=this.withSmartMixTopicForbiddingContext((function(){return t.parseStatement()})),this.state.labels.pop(),this.finishNode(e,"WhileStatement")}},{key:"parseWithStatement",value:function(e){var t=this;return this.state.strict&&this.raise(Z.StrictWith,this.state.startLoc),this.next(),e.object=this.parseHeaderExpression(),e.body=this.withSmartMixTopicForbiddingContext((function(){return t.parseStatement()})),this.finishNode(e,"WithStatement")}},{key:"parseEmptyStatement",value:function(e){return this.next(),this.finishNode(e,"EmptyStatement")}},{key:"parseLabeledStatement",value:function(e,t,i,r){var s,n=c(this.state.labels);try{for(n.s();!(s=n.n()).done;){var a=s.value;a.name===t&&this.raise(Z.LabelRedeclaration,i,{labelName:t})}}catch(u){n.e(u)}finally{n.f()}for(var o=Be(this.state.type)?1:this.match(71)?2:null,l=this.state.labels.length-1;l>=0;l--){var h=this.state.labels[l];if(h.statementStart!==e.start)break;h.statementStart=this.sourceToOffsetPos(this.state.start),h.kind=o}return this.state.labels.push({name:t,kind:o,statementStart:this.sourceToOffsetPos(this.state.start)}),e.body=8&r?this.parseStatementOrSloppyAnnexBFunctionDeclaration(!0):this.parseStatement(),this.state.labels.pop(),e.label=i,this.finishNode(e,"LabeledStatement")}},{key:"parseExpressionStatement",value:function(e,t,i){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")}},{key:"parseBlock",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2?arguments[2]:void 0,r=this.startNode();return e&&this.state.strictErrors.clear(),this.expect(5),t&&this.scope.enter(0),this.parseBlockBody(r,e,!1,8,i),t&&this.scope.exit(),this.finishNode(r,"BlockStatement")}},{key:"isValidDirective",value:function(e){return"ExpressionStatement"===e.type&&"StringLiteral"===e.expression.type&&!e.expression.extra.parenthesized}},{key:"parseBlockBody",value:function(e,t,i,r,s){var n=e.body=[],a=e.directives=[];this.parseBlockOrModuleBlockBody(n,t?a:void 0,i,r,s)}},{key:"parseBlockOrModuleBlockBody",value:function(e,t,i,r,s){var n=this.state.strict,a=!1,o=!1;while(!this.match(r)){var l=i?this.parseModuleItem():this.parseStatementListItem();if(t&&!o){if(this.isValidDirective(l)){var c=this.stmtToDirective(l);t.push(c),a||"use strict"!==c.value.value||(a=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}e.push(l)}null==s||s.call(this,a),n||this.setStrict(!1),this.next()}},{key:"parseFor",value:function(e,t){var i=this;return e.init=t,this.semicolon(!1),e.test=this.match(13)?null:this.parseExpression(),this.semicolon(!1),e.update=this.match(11)?null:this.parseExpression(),this.expect(11),e.body=this.withSmartMixTopicForbiddingContext((function(){return i.parseStatement()})),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,"ForStatement")}},{key:"parseForIn",value:function(e,t,i){var r=this,s=this.match(58);return this.next(),s?null!==i&&this.unexpected(i):e.await=null!==i,"VariableDeclaration"!==t.type||null==t.declarations[0].init||s&&this.options.annexB&&!this.state.strict&&"var"===t.kind&&"Identifier"===t.declarations[0].id.type||this.raise(Z.ForInOfLoopInitializer,t,{type:s?"ForInStatement":"ForOfStatement"}),"AssignmentPattern"===t.type&&this.raise(Z.InvalidLhs,t,{ancestor:{type:"ForStatement"}}),e.left=t,e.right=s?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(11),e.body=this.withSmartMixTopicForbiddingContext((function(){return r.parseStatement()})),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,s?"ForInStatement":"ForOfStatement")}},{key:"parseVar",value:function(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=e.declarations=[];for(e.kind=i;;){var n=this.startNode();if(this.parseVarId(n,i),n.init=this.eat(29)?t?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():null,null!==n.init||r||("Identifier"===n.id.type||t&&(this.match(58)||this.isContextual(102))?"const"!==i&&"using"!==i&&"await using"!==i||this.match(58)||this.isContextual(102)||this.raise(Z.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:i}):this.raise(Z.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:"destructuring"})),s.push(this.finishNode(n,"VariableDeclarator")),!this.eat(12))break}return e}},{key:"parseVarId",value:function(e,t){var i=this.parseBindingAtom();"using"===t||"await using"===t?"ArrayPattern"!==i.type&&"ObjectPattern"!==i.type||this.raise(Z.UsingDeclarationHasBindingPattern,i.loc.start):"VoidPattern"===i.type&&this.raise(Z.UnexpectedVoidPattern,i.loc.start),this.checkLVal(i,{type:"VariableDeclarator"},"var"===t?5:8201),e.id=i}},{key:"parseAsyncFunctionExpression",value:function(e){return this.parseFunction(e,8)}},{key:"parseFunction",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=2&i,s=!!(1&i),n=s&&!(4&i),a=!!(8&i);this.initFunction(e,a),this.match(55)&&(r&&this.raise(Z.GeneratorInSingleStatementContext,this.state.startLoc),this.next(),e.generator=!0),s&&(e.id=this.parseFunctionId(n));var o=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(514),this.prodParam.enter(Ht(a,e.generator)),s||(e.id=this.parseFunctionId()),this.parseFunctionParams(e,!1),this.withSmartMixTopicForbiddingContext((function(){t.parseFunctionBodyAndFinish(e,s?"FunctionDeclaration":"FunctionExpression")})),this.prodParam.exit(),this.scope.exit(),s&&!r&&this.registerFunctionStatementId(e),this.state.maybeInArrowParameters=o,e}},{key:"parseFunctionId",value:function(e){return e||Ce(this.state.type)?this.parseIdentifier():null}},{key:"parseFunctionParams",value:function(e,t){this.expect(10),this.expressionScope.enter(fi()),e.params=this.parseBindingList(11,41,2|(t?4:0)),this.expressionScope.exit()}},{key:"registerFunctionStatementId",value:function(e){e.id&&this.scope.declareName(e.id.name,!this.options.annexB||this.state.strict||e.generator||e.async?this.scope.treatFunctionsAsVar?5:8201:17,e.id.loc.start)}},{key:"parseClass",value:function(e,t,i){this.next();var r=this.state.strict;return this.state.strict=!0,this.parseClassId(e,t,i),this.parseClassSuper(e),e.body=this.parseClassBody(!!e.superClass,r),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")}},{key:"isClassProperty",value:function(){return this.match(29)||this.match(13)||this.match(8)}},{key:"isClassMethod",value:function(){return this.match(10)}},{key:"nameIsConstructor",value:function(e){return"Identifier"===e.type&&"constructor"===e.name||"StringLiteral"===e.type&&"constructor"===e.value}},{key:"isNonstaticConstructor",value:function(e){return!e.computed&&!e.static&&this.nameIsConstructor(e.key)}},{key:"parseClassBody",value:function(e,t){var i=this;this.classScope.enter();var r={hadConstructor:!1,hadSuperClass:e},s=[],n=this.startNode();if(n.body=[],this.expect(5),this.withSmartMixTopicForbiddingContext((function(){while(!i.match(8))if(i.eat(13)){if(s.length>0)throw i.raise(Z.DecoratorSemicolon,i.state.lastTokEndLoc)}else if(i.match(26))s.push(i.parseDecorator());else{var e=i.startNode();s.length&&(e.decorators=s,i.resetStartLocationFromNode(e,s[0]),s=[]),i.parseClassMember(n,e,r),"constructor"===e.kind&&e.decorators&&e.decorators.length>0&&i.raise(Z.DecoratorConstructor,e)}})),this.state.strict=t,this.next(),s.length)throw this.raise(Z.TrailingDecorator,this.state.startLoc);return this.classScope.exit(),this.finishNode(n,"ClassBody")}},{key:"parseClassMemberFromModifier",value:function(e,t){var i=this.parseIdentifier(!0);if(this.isClassMethod()){var r=t;return r.kind="method",r.computed=!1,r.key=i,r.static=!1,this.pushClassMethod(e,r,!1,!1,!1,!1),!0}if(this.isClassProperty()){var s=t;return s.computed=!1,s.key=i,s.static=!1,e.body.push(this.parseClassProperty(s)),!0}return this.resetPreviousNodeTrailingComments(i),!1}},{key:"parseClassMember",value:function(e,t,i){var r=this.isContextual(106);if(r){if(this.parseClassMemberFromModifier(e,t))return;if(this.eat(5))return void this.parseClassStaticBlock(e,t)}this.parseClassMemberWithIsStatic(e,t,i,r)}},{key:"parseClassMemberWithIsStatic",value:function(e,t,i,r){var s=t,n=t,a=t,o=t,l=t,c=s,h=s;if(t.static=r,this.parsePropertyNamePrefixOperator(t),this.eat(55)){c.kind="method";var u=this.match(139);return this.parseClassElementName(c),this.parsePostMemberNameModifiers(c),u?void this.pushClassPrivateMethod(e,n,!0,!1):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsGenerator,s.key),void this.pushClassMethod(e,s,!0,!1,!1,!1))}var p=!this.state.containsEsc&&Ce(this.state.type),d=this.parseClassElementName(t),f=p?d.name:null,m=this.isPrivateName(d),y=this.state.startLoc;if(this.parsePostMemberNameModifiers(h),this.isClassMethod()){if(c.kind="method",m)return void this.pushClassPrivateMethod(e,n,!1,!1);var v=this.isNonstaticConstructor(s),g=!1;v&&(s.kind="constructor",i.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(Z.DuplicateConstructor,d),v&&this.hasPlugin("typescript")&&t.override&&this.raise(Z.OverrideOnConstructor,d),i.hadConstructor=!0,g=i.hadSuperClass),this.pushClassMethod(e,s,!1,!1,v,g)}else if(this.isClassProperty())m?this.pushClassPrivateProperty(e,o):this.pushClassProperty(e,a);else if("async"!==f||this.isLineTerminator())if("get"!==f&&"set"!==f||this.match(55)&&this.isLineTerminator())if("accessor"!==f||this.isLineTerminator())this.isLineTerminator()?m?this.pushClassPrivateProperty(e,o):this.pushClassProperty(e,a):this.unexpected();else{this.expectPlugin("decoratorAutoAccessors"),this.resetPreviousNodeTrailingComments(d);var x=this.match(139);this.parseClassElementName(a),this.pushClassAccessorProperty(e,l,x)}else{this.resetPreviousNodeTrailingComments(d),c.kind=f;var b=this.match(139);this.parseClassElementName(s),b?this.pushClassPrivateMethod(e,n,!1,!1):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsAccessor,s.key),this.pushClassMethod(e,s,!1,!1,!1,!1)),this.checkGetterSetterParams(s)}else{this.resetPreviousNodeTrailingComments(d);var P=this.eat(55);h.optional&&this.unexpected(y),c.kind="method";var k=this.match(139);this.parseClassElementName(c),this.parsePostMemberNameModifiers(h),k?this.pushClassPrivateMethod(e,n,P,!0):(this.isNonstaticConstructor(s)&&this.raise(Z.ConstructorIsAsync,s.key),this.pushClassMethod(e,s,P,!0,!1,!1))}}},{key:"parseClassElementName",value:function(e){var t=this.state,i=t.type,r=t.value;if(132!==i&&134!==i||!e.static||"prototype"!==r||this.raise(Z.StaticPrototype,this.state.startLoc),139===i){"constructor"===r&&this.raise(Z.ConstructorClassPrivateField,this.state.startLoc);var s=this.parsePrivateName();return e.key=s,s}return this.parsePropertyName(e),e.key}},{key:"parseClassStaticBlock",value:function(e,t){var i;this.scope.enter(720);var r=this.state.labels;this.state.labels=[],this.prodParam.enter(0);var s=t.body=[];this.parseBlockOrModuleBlockBody(s,void 0,!1,8),this.prodParam.exit(),this.scope.exit(),this.state.labels=r,e.body.push(this.finishNode(t,"StaticBlock")),null!=(i=t.decorators)&&i.length&&this.raise(Z.DecoratorStaticBlock,t)}},{key:"pushClassProperty",value:function(e,t){!t.computed&&this.nameIsConstructor(t.key)&&this.raise(Z.ConstructorClassField,t.key),e.body.push(this.parseClassProperty(t))}},{key:"pushClassPrivateProperty",value:function(e,t){var i=this.parseClassPrivateProperty(t);e.body.push(i),this.classScope.declarePrivateName(this.getPrivateNameSV(i.key),0,i.key.loc.start)}},{key:"pushClassAccessorProperty",value:function(e,t,i){i||t.computed||!this.nameIsConstructor(t.key)||this.raise(Z.ConstructorClassField,t.key);var r=this.parseClassAccessorProperty(t);e.body.push(r),i&&this.classScope.declarePrivateName(this.getPrivateNameSV(r.key),0,r.key.loc.start)}},{key:"pushClassMethod",value:function(e,t,i,r,s,n){e.body.push(this.parseMethod(t,i,r,s,n,"ClassMethod",!0))}},{key:"pushClassPrivateMethod",value:function(e,t,i,r){var s=this.parseMethod(t,i,r,!1,!1,"ClassPrivateMethod",!0);e.body.push(s);var n="get"===s.kind?s.static?6:2:"set"===s.kind?s.static?5:1:0;this.declareClassPrivateMethodInScope(s,n)}},{key:"declareClassPrivateMethodInScope",value:function(e,t){this.classScope.declarePrivateName(this.getPrivateNameSV(e.key),t,e.key.loc.start)}},{key:"parsePostMemberNameModifiers",value:function(e){}},{key:"parseClassPrivateProperty",value:function(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassPrivateProperty")}},{key:"parseClassProperty",value:function(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassProperty")}},{key:"parseClassAccessorProperty",value:function(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassAccessorProperty")}},{key:"parseInitializer",value:function(e){this.scope.enter(592),this.expressionScope.enter(vi()),this.prodParam.enter(0),e.value=this.eat(29)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}},{key:"parseClassId",value:function(e,t,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:8331;if(Ce(this.state.type))e.id=this.parseIdentifier(),t&&this.declareNameFromIdentifier(e.id,r);else{if(!i&&t)throw this.raise(Z.MissingClassName,this.state.startLoc);e.id=null}}},{key:"parseClassSuper",value:function(e){e.superClass=this.eat(81)?this.parseExprSubscripts():null}},{key:"parseExport",value:function(e,t){var i=this.parseMaybeImportPhase(e,!0),r=this.maybeParseExportDefaultSpecifier(e,i),s=!r||this.eat(12),n=s&&this.eatExportStar(e),a=n&&this.maybeParseExportNamespaceSpecifier(e),o=s&&(!a||this.eat(12)),l=r||n;if(n&&!a){if(r&&this.unexpected(),t)throw this.raise(Z.UnsupportedDecoratorExport,e);return this.parseExportFrom(e,!0),this.sawUnambiguousESM=!0,this.finishNode(e,"ExportAllDeclaration")}var c,h=this.maybeParseExportNamedSpecifiers(e);if(r&&s&&!n&&!h&&this.unexpected(null,5),a&&o&&this.unexpected(null,98),l||h){if(c=!1,t)throw this.raise(Z.UnsupportedDecoratorExport,e);this.parseExportFrom(e,l)}else c=this.maybeParseExportDeclaration(e);if(l||h||c){var u,p=e;if(this.checkExport(p,!0,!1,!!p.source),"ClassDeclaration"===(null==(u=p.declaration)?void 0:u.type))this.maybeTakeDecorators(t,p.declaration,p);else if(t)throw this.raise(Z.UnsupportedDecoratorExport,e);return this.sawUnambiguousESM=!0,this.finishNode(p,"ExportNamedDeclaration")}if(this.eat(65)){var d=e,f=this.parseExportDefaultExpression();if(d.declaration=f,"ClassDeclaration"===f.type)this.maybeTakeDecorators(t,f,d);else if(t)throw this.raise(Z.UnsupportedDecoratorExport,e);return this.checkExport(d,!0,!0),this.sawUnambiguousESM=!0,this.finishNode(d,"ExportDefaultDeclaration")}this.unexpected(null,5)}},{key:"eatExportStar",value:function(e){return this.eat(55)}},{key:"maybeParseExportDefaultSpecifier",value:function(e,t){if(t||this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom",null==t?void 0:t.loc.start);var i=t||this.parseIdentifier(!0),r=this.startNodeAtNode(i);return r.exported=i,e.specifiers=[this.finishNode(r,"ExportDefaultSpecifier")],!0}return!1}},{key:"maybeParseExportNamespaceSpecifier",value:function(e){if(this.isContextual(93)){var t;null!=(t=e).specifiers||(t.specifiers=[]);var i=this.startNodeAt(this.state.lastTokStartLoc);return this.next(),i.exported=this.parseModuleExportName(),e.specifiers.push(this.finishNode(i,"ExportNamespaceSpecifier")),!0}return!1}},{key:"maybeParseExportNamedSpecifiers",value:function(e){if(this.match(5)){var t,i=e;i.specifiers||(i.specifiers=[]);var r="type"===i.exportKind;return(t=i.specifiers).push.apply(t,h(this.parseExportSpecifiers(r))),i.source=null,this.hasPlugin("importAssertions")?i.assertions=[]:i.attributes=[],i.declaration=null,!0}return!1}},{key:"maybeParseExportDeclaration",value:function(e){return!!this.shouldParseExportDeclaration()&&(e.specifiers=[],e.source=null,this.hasPlugin("importAssertions")?e.assertions=[]:e.attributes=[],e.declaration=this.parseExportDeclaration(e),!0)}},{key:"isAsyncFunction",value:function(){if(!this.isContextual(95))return!1;var e=this.nextTokenInLineStart();return this.isUnparsedContextual(e,"function")}},{key:"parseExportDefaultExpression",value:function(){var e=this.startNode();if(this.match(68))return this.next(),this.parseFunction(e,5);if(this.isAsyncFunction())return this.next(),this.next(),this.parseFunction(e,13);if(this.match(80))return this.parseClass(e,!0,!0);if(this.match(26))return this.hasPlugin("decorators")&&!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(Z.DecoratorBeforeExport,this.state.startLoc),this.parseClass(this.maybeTakeDecorators(this.parseDecorators(!1),this.startNode()),!0,!0);if(this.match(75)||this.match(74)||this.isLet()||this.isUsing()||this.isAwaitUsing())throw this.raise(Z.UnsupportedDefaultExport,this.state.startLoc);var t=this.parseMaybeAssignAllowIn();return this.semicolon(),t}},{key:"parseExportDeclaration",value:function(e){if(this.match(80)){var t=this.parseClass(this.startNode(),!0,!1);return t}return this.parseStatementListItem()}},{key:"isExportDefaultSpecifier",value:function(){var e=this.state.type;if(Ce(e)){if(95===e&&!this.state.containsEsc||100===e)return!1;if((130===e||129===e)&&!this.state.containsEsc){var t=this.nextTokenStart(),i=this.input.charCodeAt(t);if(123===i||this.chStartsBindingIdentifier(i,t)&&!this.input.startsWith("from",t))return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(65))return!1;var r=this.nextTokenStart(),s=this.isUnparsedContextual(r,"from");if(44===this.input.charCodeAt(r)||Ce(this.state.type)&&s)return!0;if(this.match(65)&&s){var n=this.input.charCodeAt(this.nextTokenStartSince(r+4));return 34===n||39===n}return!1}},{key:"parseExportFrom",value:function(e,t){this.eatContextual(98)?(e.source=this.parseImportSource(),this.checkExport(e),this.maybeParseImportAttributes(e),this.checkJSONModuleImport(e)):t&&this.unexpected(),this.semicolon()}},{key:"shouldParseExportDeclaration",value:function(){var e=this.state.type;return 26===e&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))?(!0===this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(Z.DecoratorBeforeExport,this.state.startLoc),!0):this.isUsing()||this.isAwaitUsing()?(this.raise(Z.UsingDeclarationExport,this.state.startLoc),!0):74===e||75===e||68===e||80===e||this.isLet()||this.isAsyncFunction()}},{key:"checkExport",value:function(e,t,i,r){var s;if(t)if(i){if(this.checkDuplicateExports(e,"default"),this.hasPlugin("exportDefaultFrom")){var n,a=e.declaration;"Identifier"!==a.type||"from"!==a.name||a.end-a.start!==4||null!=(n=a.extra)&&n.parenthesized||this.raise(Z.ExportDefaultFromAsIdentifier,a)}}else if(null!=(s=e.specifiers)&&s.length){var o,l=c(e.specifiers);try{for(l.s();!(o=l.n()).done;){var h=o.value,u=h.exported,p="Identifier"===u.type?u.name:u.value;if(this.checkDuplicateExports(h,p),!r&&h.local){var d=h.local;"Identifier"!==d.type?this.raise(Z.ExportBindingIsString,h,{localName:d.value,exportName:p}):(this.checkReservedWord(d.name,d.loc.start,!0,!1),this.scope.checkLocalExport(d))}}}catch(x){l.e(x)}finally{l.f()}}else if(e.declaration){var f=e.declaration;if("FunctionDeclaration"===f.type||"ClassDeclaration"===f.type){var m=f.id;if(!m)throw new Error("Assertion failure");this.checkDuplicateExports(e,m.name)}else if("VariableDeclaration"===f.type){var y,v=c(f.declarations);try{for(v.s();!(y=v.n()).done;){var g=y.value;this.checkDeclaration(g.id)}}catch(x){v.e(x)}finally{v.f()}}}}},{key:"checkDeclaration",value:function(e){if("Identifier"===e.type)this.checkDuplicateExports(e,e.name);else if("ObjectPattern"===e.type){var t,i=c(e.properties);try{for(i.s();!(t=i.n()).done;){var r=t.value;this.checkDeclaration(r)}}catch(o){i.e(o)}finally{i.f()}}else if("ArrayPattern"===e.type){var s,n=c(e.elements);try{for(n.s();!(s=n.n()).done;){var a=s.value;a&&this.checkDeclaration(a)}}catch(o){n.e(o)}finally{n.f()}}else"ObjectProperty"===e.type?this.checkDeclaration(e.value):"RestElement"===e.type?this.checkDeclaration(e.argument):"AssignmentPattern"===e.type&&this.checkDeclaration(e.left)}},{key:"checkDuplicateExports",value:function(e,t){this.exportedIdentifiers.has(t)&&("default"===t?this.raise(Z.DuplicateDefaultExport,e):this.raise(Z.DuplicateExport,e,{exportName:t})),this.exportedIdentifiers.add(t)}},{key:"parseExportSpecifiers",value:function(e){var t=[],i=!0;this.expect(5);while(!this.eat(8)){if(i)i=!1;else if(this.expect(12),this.eat(8))break;var r=this.isContextual(130),s=this.match(134),n=this.startNode();n.local=this.parseModuleExportName(),t.push(this.parseExportSpecifier(n,s,e,r))}return t}},{key:"parseExportSpecifier",value:function(e,t,i,r){return this.eatContextual(93)?e.exported=this.parseModuleExportName():t?e.exported=this.cloneStringLiteral(e.local):e.exported||(e.exported=this.cloneIdentifier(e.local)),this.finishNode(e,"ExportSpecifier")}},{key:"parseModuleExportName",value:function(){if(this.match(134)){var e=this.parseStringLiteral(this.state.value),t=Ji.exec(e.value);return t&&this.raise(Z.ModuleExportNameHasLoneSurrogate,e,{surrogateCharCode:t[0].charCodeAt(0)}),e}return this.parseIdentifier(!0)}},{key:"isJSONModuleImport",value:function(e){return null!=e.assertions&&e.assertions.some((function(e){var t=e.key,i=e.value;return"json"===i.value&&("Identifier"===t.type?"type"===t.name:"type"===t.value)}))}},{key:"checkImportReflection",value:function(e){var t=e.specifiers,i=1===t.length?t[0].type:null;if("source"===e.phase)"ImportDefaultSpecifier"!==i&&this.raise(Z.SourcePhaseImportRequiresDefault,t[0].loc.start);else if("defer"===e.phase)"ImportNamespaceSpecifier"!==i&&this.raise(Z.DeferImportRequiresNamespace,t[0].loc.start);else if(e.module){var r;"ImportDefaultSpecifier"!==i&&this.raise(Z.ImportReflectionNotBinding,t[0].loc.start),(null==(r=e.assertions)?void 0:r.length)>0&&this.raise(Z.ImportReflectionHasAssertion,t[0].loc.start)}}},{key:"checkJSONModuleImport",value:function(e){if(this.isJSONModuleImport(e)&&"ExportAllDeclaration"!==e.type){var t=e.specifiers;if(null!=t){var i=t.find((function(e){var t;if("ExportSpecifier"===e.type?t=e.local:"ImportSpecifier"===e.type&&(t=e.imported),void 0!==t)return"Identifier"===t.type?"default"!==t.name:"default"!==t.value}));void 0!==i&&this.raise(Z.ImportJSONBindingNotDefault,i.loc.start)}}}},{key:"isPotentialImportPhase",value:function(e){return!e&&(this.isContextual(105)||this.isContextual(97)||this.isContextual(127))}},{key:"applyImportPhase",value:function(e,t,i,r){t||("module"===i?(this.expectPlugin("importReflection",r),e.module=!0):this.hasPlugin("importReflection")&&(e.module=!1),"source"===i?(this.expectPlugin("sourcePhaseImports",r),e.phase="source"):"defer"===i?(this.expectPlugin("deferredImportEvaluation",r),e.phase="defer"):this.hasPlugin("sourcePhaseImports")&&(e.phase=null))}},{key:"parseMaybeImportPhase",value:function(e,t){if(!this.isPotentialImportPhase(t))return this.applyImportPhase(e,t,null),null;var i=this.startNode(),r=this.parseIdentifierName(!0),s=this.state.type,n=Ne(s)?98!==s||102===this.lookaheadCharCode():12!==s;return n?(this.applyImportPhase(e,t,r,i.loc.start),null):(this.applyImportPhase(e,t,null),this.createIdentifier(i,r))}},{key:"isPrecedingIdImportPhase",value:function(e){var t=this.state.type;return Ce(t)?98!==t||102===this.lookaheadCharCode():12!==t}},{key:"parseImport",value:function(e){return this.match(134)?this.parseImportSourceAndAttributes(e):this.parseImportSpecifiersAndAfter(e,this.parseMaybeImportPhase(e,!1))}},{key:"parseImportSpecifiersAndAfter",value:function(e,t){e.specifiers=[];var i=this.maybeParseDefaultImportSpecifier(e,t),r=!i||this.eat(12),s=r&&this.maybeParseStarImportSpecifier(e);return r&&!s&&this.parseNamedImportSpecifiers(e),this.expectContextual(98),this.parseImportSourceAndAttributes(e)}},{key:"parseImportSourceAndAttributes",value:function(e){return null!=e.specifiers||(e.specifiers=[]),e.source=this.parseImportSource(),this.maybeParseImportAttributes(e),this.checkImportReflection(e),this.checkJSONModuleImport(e),this.semicolon(),this.sawUnambiguousESM=!0,this.finishNode(e,"ImportDeclaration")}},{key:"parseImportSource",value:function(){return this.match(134)||this.unexpected(),this.parseExprAtom()}},{key:"parseImportSpecifierLocal",value:function(e,t,i){t.local=this.parseIdentifier(),e.specifiers.push(this.finishImportSpecifier(t,i))}},{key:"finishImportSpecifier",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:8201;return this.checkLVal(e.local,{type:t},i),this.finishNode(e,t)}},{key:"parseImportAttributes",value:function(){this.expect(5);var e=[],t=new Set;do{if(this.match(8))break;var i=this.startNode(),r=this.state.value;if(t.has(r)&&this.raise(Z.ModuleAttributesWithDuplicateKeys,this.state.startLoc,{key:r}),t.add(r),this.match(134)?i.key=this.parseStringLiteral(r):i.key=this.parseIdentifier(!0),this.expect(14),!this.match(134))throw this.raise(Z.ModuleAttributeInvalidValue,this.state.startLoc);i.value=this.parseStringLiteral(this.state.value),e.push(this.finishNode(i,"ImportAttribute"))}while(this.eat(12));return this.expect(8),e}},{key:"parseModuleAttributes",value:function(){var e=[],t=new Set;do{var i=this.startNode();if(i.key=this.parseIdentifier(!0),"type"!==i.key.name&&this.raise(Z.ModuleAttributeDifferentFromType,i.key),t.has(i.key.name)&&this.raise(Z.ModuleAttributesWithDuplicateKeys,i.key,{key:i.key.name}),t.add(i.key.name),this.expect(14),!this.match(134))throw this.raise(Z.ModuleAttributeInvalidValue,this.state.startLoc);i.value=this.parseStringLiteral(this.state.value),e.push(this.finishNode(i,"ImportAttribute"))}while(this.eat(12));return e}},{key:"maybeParseImportAttributes",value:function(e){var t,i=!1;if(this.match(76)){if(this.hasPrecedingLineBreak()&&40===this.lookaheadCharCode())return;this.next(),this.hasPlugin("moduleAttributes")?(t=this.parseModuleAttributes(),this.addExtra(e,"deprecatedWithLegacySyntax",!0)):t=this.parseImportAttributes(),i=!0}else this.isContextual(94)&&!this.hasPrecedingLineBreak()?(this.hasPlugin("deprecatedImportAssert")||this.hasPlugin("importAssertions")||this.raise(Z.ImportAttributesUseAssert,this.state.startLoc),this.hasPlugin("importAssertions")||this.addExtra(e,"deprecatedAssertSyntax",!0),this.next(),t=this.parseImportAttributes()):t=[];!i&&this.hasPlugin("importAssertions")?e.assertions=t:e.attributes=t}},{key:"maybeParseDefaultImportSpecifier",value:function(e,t){if(t){var i=this.startNodeAtNode(t);return i.local=t,e.specifiers.push(this.finishImportSpecifier(i,"ImportDefaultSpecifier")),!0}return!!Ne(this.state.type)&&(this.parseImportSpecifierLocal(e,this.startNode(),"ImportDefaultSpecifier"),!0)}},{key:"maybeParseStarImportSpecifier",value:function(e){if(this.match(55)){var t=this.startNode();return this.next(),this.expectContextual(93),this.parseImportSpecifierLocal(e,t,"ImportNamespaceSpecifier"),!0}return!1}},{key:"parseNamedImportSpecifiers",value:function(e){var t=!0;this.expect(5);while(!this.eat(8)){if(t)t=!1;else{if(this.eat(14))throw this.raise(Z.DestructureNamedImport,this.state.startLoc);if(this.expect(12),this.eat(8))break}var i=this.startNode(),r=this.match(134),s=this.isContextual(130);i.imported=this.parseModuleExportName();var n=this.parseImportSpecifier(i,r,"type"===e.importKind||"typeof"===e.importKind,s,void 0);e.specifiers.push(n)}}},{key:"parseImportSpecifier",value:function(e,t,i,r,s){if(this.eatContextual(93))e.local=this.parseIdentifier();else{var n=e.imported;if(t)throw this.raise(Z.ImportBindingIsString,e,{importName:n.value});this.checkReservedWord(n.name,e.loc.start,!0,!0),e.local||(e.local=this.cloneIdentifier(n))}return this.finishImportSpecifier(e,"ImportSpecifier",s)}},{key:"isThisParam",value:function(e){return"Identifier"===e.type&&"this"===e.name}}])}(Ki),Qi=function(e){function t(e,i,r){var s;F(this,t),e=te(e),s=b(this,t,[e,i]),s.options=e,s.initializeScopes(),s.plugins=r,s.filename=e.sourceFilename,s.startIndex=e.startIndex;var n=0;return e.allowAwaitOutsideFunction&&(n|=1),e.allowReturnOutsideFunction&&(n|=2),e.allowImportExportEverywhere&&(n|=8),e.allowSuperOutsideMethod&&(n|=16),e.allowUndeclaredExports&&(n|=64),e.allowNewTargetOutsideFunction&&(n|=4),e.allowYieldOutsideFunction&&(n|=32),e.ranges&&(n|=128),e.tokens&&(n|=256),e.createImportExpressions&&(n|=512),e.createParenthesizedExpressions&&(n|=1024),e.errorRecovery&&(n|=2048),e.attachComment&&(n|=4096),e.annexB&&(n|=8192),s.optionFlags=n,s}return C(t,e),L(t,[{key:"getScopeHandler",value:function(){return yt}},{key:"parse",value:function(){this.enterInitialScopes();var e=this.startNode(),t=this.startNode();return this.nextToken(),e.errors=null,this.parseTopLevel(e,t),e.errors=this.state.errors,e.comments.length=this.state.commentsLen,e}}])}($i);function Zi(e,t){var i;if("unambiguous"!==(null==(i=t)?void 0:i.sourceType))return rr(t,e).parse();t=Object.assign({},t);try{t.sourceType="module";var r=rr(t,e),s=r.parse();if(r.sawUnambiguousESM)return s;if(r.ambiguousScriptDifferentAst)try{return t.sourceType="script",rr(t,e).parse()}catch(n){}else s.program.sourceType="script";return s}catch(a){try{return t.sourceType="script",rr(t,e).parse()}catch(o){}throw a}}function er(e,t){var i=rr(t,e);return i.options.strictMode&&(i.state.strict=!0),i.getExpression()}function tr(e){for(var t={},i=0,r=Object.keys(e);i<r.length;i++){var s=r[i];t[s]=Xe(e[s])}return t}var ir=tr(Ee);function rr(e,t){var i=Qi,r=new Map;if(null!=e&&e.plugins){var s,n=c(e.plugins);try{for(n.s();!(s=n.n()).done;){var a=s.value,o=void 0,l=void 0;if("string"===typeof a)o=a;else{var h=f(a,2);o=h[0],l=h[1]}r.has(o)||r.set(o,l||{})}}catch(u){n.e(u)}finally{n.f()}Hi(r),i=nr(r)}return new i(e,t,r)}var sr=new Map;function nr(e){var t,i=[],r=c(qi);try{for(r.s();!(t=r.n()).done;){var s=t.value;e.has(s)&&i.push(s)}}catch(u){r.e(u)}finally{r.f()}var n=i.join("|"),a=sr.get(n);if(!a){a=Qi;var o,l=c(i);try{for(l.s();!(o=l.n()).done;){var h=o.value;a=zi[h](a)}}catch(u){l.e(u)}finally{l.f()}sr.set(n,a)}return a}t.parse=Zi,t.parseExpression=er,t.tokTypes=ir},"28a0":function(e,t){"function"===typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var i=function(){};i.prototype=t.prototype,e.prototype=new i,e.prototype.constructor=e}},3022:function(e,t,i){(function(e){var r=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),i={},r=0;r<t.length;r++)i[t[r]]=Object.getOwnPropertyDescriptor(e,t[r]);return i},s=/%[sdj%]/g;t.format=function(e){if(!k(e)){for(var t=[],i=0;i<arguments.length;i++)t.push(o(arguments[i]));return t.join(" ")}i=1;for(var r=arguments,n=r.length,a=String(e).replace(s,(function(e){if("%%"===e)return"%";if(i>=n)return e;switch(e){case"%s":return String(r[i++]);case"%d":return Number(r[i++]);case"%j":try{return JSON.stringify(r[i++])}catch(t){return"[Circular]"}default:return e}})),l=r[i];i<n;l=r[++i])x(l)||!A(l)?a+=" "+l:a+=" "+o(l);return a},t.deprecate=function(i,r){if("undefined"!==typeof e&&!0===e.noDeprecation)return i;if("undefined"===typeof e)return function(){return t.deprecate(i,r).apply(this,arguments)};var s=!1;function n(){if(!s){if(e.throwDeprecation)throw new Error(r);e.traceDeprecation?console.trace(r):console.error(r),s=!0}return i.apply(this,arguments)}return n};var n,a={};function o(e,i){var r={seen:[],stylize:c};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),g(i)?r.showHidden=i:i&&t._extend(r,i),w(r.showHidden)&&(r.showHidden=!1),w(r.depth)&&(r.depth=2),w(r.colors)&&(r.colors=!1),w(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=l),u(r,e,r.depth)}function l(e,t){var i=o.styles[t];return i?"["+o.colors[i][0]+"m"+e+"["+o.colors[i][1]+"m":e}function c(e,t){return e}function h(e){var t={};return e.forEach((function(e,i){t[e]=!0})),t}function u(e,i,r){if(e.customInspect&&i&&I(i.inspect)&&i.inspect!==t.inspect&&(!i.constructor||i.constructor.prototype!==i)){var s=i.inspect(r,e);return k(s)||(s=u(e,s,r)),s}var n=p(e,i);if(n)return n;var a=Object.keys(i),o=h(a);if(e.showHidden&&(a=Object.getOwnPropertyNames(i)),C(i)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return d(i);if(0===a.length){if(I(i)){var l=i.name?": "+i.name:"";return e.stylize("[Function"+l+"]","special")}if(S(i))return e.stylize(RegExp.prototype.toString.call(i),"regexp");if(E(i))return e.stylize(Date.prototype.toString.call(i),"date");if(C(i))return d(i)}var c,g="",x=!1,b=["{","}"];if(v(i)&&(x=!0,b=["[","]"]),I(i)){var P=i.name?": "+i.name:"";g=" [Function"+P+"]"}return S(i)&&(g=" "+RegExp.prototype.toString.call(i)),E(i)&&(g=" "+Date.prototype.toUTCString.call(i)),C(i)&&(g=" "+d(i)),0!==a.length||x&&0!=i.length?r<0?S(i)?e.stylize(RegExp.prototype.toString.call(i),"regexp"):e.stylize("[Object]","special"):(e.seen.push(i),c=x?f(e,i,r,o,a):a.map((function(t){return m(e,i,r,o,t,x)})),e.seen.pop(),y(c,g,b)):b[0]+g+b[1]}function p(e,t){if(w(t))return e.stylize("undefined","undefined");if(k(t)){var i="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(i,"string")}return P(t)?e.stylize(""+t,"number"):g(t)?e.stylize(""+t,"boolean"):x(t)?e.stylize("null","null"):void 0}function d(e){return"["+Error.prototype.toString.call(e)+"]"}function f(e,t,i,r,s){for(var n=[],a=0,o=t.length;a<o;++a)F(t,String(a))?n.push(m(e,t,i,r,String(a),!0)):n.push("");return s.forEach((function(s){s.match(/^\d+$/)||n.push(m(e,t,i,r,s,!0))})),n}function m(e,t,i,r,s,n){var a,o,l;if(l=Object.getOwnPropertyDescriptor(t,s)||{value:t[s]},l.get?o=l.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):l.set&&(o=e.stylize("[Setter]","special")),F(r,s)||(a="["+s+"]"),o||(e.seen.indexOf(l.value)<0?(o=x(i)?u(e,l.value,null):u(e,l.value,i-1),o.indexOf("\n")>-1&&(o=n?o.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+o.split("\n").map((function(e){return"   "+e})).join("\n"))):o=e.stylize("[Circular]","special")),w(a)){if(n&&s.match(/^\d+$/))return o;a=JSON.stringify(""+s),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+o}function y(e,t,i){var r=e.reduce((function(e,t){return t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return r>60?i[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+i[1]:i[0]+t+" "+e.join(", ")+" "+i[1]}function v(e){return Array.isArray(e)}function g(e){return"boolean"===typeof e}function x(e){return null===e}function b(e){return null==e}function P(e){return"number"===typeof e}function k(e){return"string"===typeof e}function T(e){return"symbol"===typeof e}function w(e){return void 0===e}function S(e){return A(e)&&"[object RegExp]"===O(e)}function A(e){return"object"===typeof e&&null!==e}function E(e){return A(e)&&"[object Date]"===O(e)}function C(e){return A(e)&&("[object Error]"===O(e)||e instanceof Error)}function I(e){return"function"===typeof e}function N(e){return null===e||"boolean"===typeof e||"number"===typeof e||"string"===typeof e||"symbol"===typeof e||"undefined"===typeof e}function O(e){return Object.prototype.toString.call(e)}function L(e){return e<10?"0"+e.toString(10):e.toString(10)}t.debuglog=function(i){if(w(n)&&(n=Object({NODE_ENV:"production",VUE_APP_BASE_API:"https://adminapi.genconusantara.com",BASE_URL:"/"}).NODE_DEBUG||""),i=i.toUpperCase(),!a[i])if(new RegExp("\\b"+i+"\\b","i").test(n)){var r=e.pid;a[i]=function(){var e=t.format.apply(t,arguments);console.error("%s %d: %s",i,r,e)}}else a[i]=function(){};return a[i]},t.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},t.isArray=v,t.isBoolean=g,t.isNull=x,t.isNullOrUndefined=b,t.isNumber=P,t.isString=k,t.isSymbol=T,t.isUndefined=w,t.isRegExp=S,t.isObject=A,t.isDate=E,t.isError=C,t.isFunction=I,t.isPrimitive=N,t.isBuffer=i("d60a");var D=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function M(){var e=new Date,t=[L(e.getHours()),L(e.getMinutes()),L(e.getSeconds())].join(":");return[e.getDate(),D[e.getMonth()],t].join(" ")}function F(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){console.log("%s - %s",M(),t.format.apply(t,arguments))},t.inherits=i("28a0"),t._extend=function(e,t){if(!t||!A(t))return e;var i=Object.keys(t),r=i.length;while(r--)e[i[r]]=t[i[r]];return e};var B="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function _(e,t){if(!e){var i=new Error("Promise was rejected with a falsy value");i.reason=e,e=i}return t(e)}function j(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');function i(){for(var i=[],r=0;r<arguments.length;r++)i.push(arguments[r]);var s=i.pop();if("function"!==typeof s)throw new TypeError("The last argument must be of type Function");var n=this,a=function(){return s.apply(n,arguments)};t.apply(this,i).then((function(t){e.nextTick(a,null,t)}),(function(t){e.nextTick(_,t,a)}))}return Object.setPrototypeOf(i,Object.getPrototypeOf(t)),Object.defineProperties(i,r(t)),i}t.promisify=function(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');if(B&&e[B]){var t=e[B];if("function"!==typeof t)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,B,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,i,r=new Promise((function(e,r){t=e,i=r})),s=[],n=0;n<arguments.length;n++)s.push(arguments[n]);s.push((function(e,r){e?i(e):t(r)}));try{e.apply(this,s)}catch(a){i(a)}return r}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),B&&Object.defineProperty(t,B,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,r(e))},t.promisify.custom=B,t.callbackify=j}).call(this,i("4362"))},"7a1a":function(e,t,i){(function(e,i){i(t)})(0,(function(e){"use strict";function t(e,t,i,r){var s,n=!1,a=0;function o(){s&&clearTimeout(s)}function l(){o(),n=!0}function c(){for(var l=arguments.length,c=new Array(l),h=0;h<l;h++)c[h]=arguments[h];var u=this,p=Date.now()-a;function d(){a=Date.now(),i.apply(u,c)}function f(){s=void 0}n||(r&&!s&&d(),o(),void 0===r&&p>e?d():!0!==t&&(s=setTimeout(r?f:d,void 0===r?e-p:e)))}return"boolean"!==typeof t&&(r=i,i=t,t=void 0),c.cancel=l,c}function i(e,i,r){return void 0===r?t(e,i,!1):t(e,r,!1!==i)}e.debounce=i,e.throttle=t,Object.defineProperty(e,"__esModule",{value:!0})}))},aa47:function(e,t,i){"use strict";
/**!
 * Sortable 1.10.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function s(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function n(){return n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e},n.apply(this,arguments)}function a(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{},r=Object.keys(i);"function"===typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter((function(e){return Object.getOwnPropertyDescriptor(i,e).enumerable})))),r.forEach((function(t){s(e,t,i[t])}))}return e}function o(e,t){if(null==e)return{};var i,r,s={},n=Object.keys(e);for(r=0;r<n.length;r++)i=n[r],t.indexOf(i)>=0||(s[i]=e[i]);return s}function l(e,t){if(null==e)return{};var i,r,s=o(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)i=n[r],t.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(e,i)&&(s[i]=e[i])}return s}function c(e){return h(e)||u(e)||p()}function h(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}function u(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance")}i.r(t),i.d(t,"MultiDrag",(function(){return jt})),i.d(t,"Sortable",(function(){return Qe})),i.d(t,"Swap",(function(){return At}));var d="1.10.2";function f(e){if("undefined"!==typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var m=f(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),y=f(/Edge/i),v=f(/firefox/i),g=f(/safari/i)&&!f(/chrome/i)&&!f(/android/i),x=f(/iP(ad|od|hone)/i),b=f(/chrome/i)&&f(/android/i),P={capture:!1,passive:!1};function k(e,t,i){e.addEventListener(t,i,!m&&P)}function T(e,t,i){e.removeEventListener(t,i,!m&&P)}function w(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(i){return!1}return!1}}function S(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function A(e,t,i,r){if(e){i=i||document;do{if(null!=t&&(">"===t[0]?e.parentNode===i&&w(e,t):w(e,t))||r&&e===i)return e;if(e===i)break}while(e=S(e))}return null}var E,C=/\s+/g;function I(e,t,i){if(e&&t)if(e.classList)e.classList[i?"add":"remove"](t);else{var r=(" "+e.className+" ").replace(C," ").replace(" "+t+" "," ");e.className=(r+(i?" "+t:"")).replace(C," ")}}function N(e,t,i){var r=e&&e.style;if(r){if(void 0===i)return document.defaultView&&document.defaultView.getComputedStyle?i=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(i=e.currentStyle),void 0===t?i:i[t];t in r||-1!==t.indexOf("webkit")||(t="-webkit-"+t),r[t]=i+("string"===typeof i?"":"px")}}function O(e,t){var i="";if("string"===typeof e)i=e;else do{var r=N(e,"transform");r&&"none"!==r&&(i=r+" "+i)}while(!t&&(e=e.parentNode));var s=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return s&&new s(i)}function L(e,t,i){if(e){var r=e.getElementsByTagName(t),s=0,n=r.length;if(i)for(;s<n;s++)i(r[s],s);return r}return[]}function D(){var e=document.scrollingElement;return e||document.documentElement}function M(e,t,i,r,s){if(e.getBoundingClientRect||e===window){var n,a,o,l,c,h,u;if(e!==window&&e!==D()?(n=e.getBoundingClientRect(),a=n.top,o=n.left,l=n.bottom,c=n.right,h=n.height,u=n.width):(a=0,o=0,l=window.innerHeight,c=window.innerWidth,h=window.innerHeight,u=window.innerWidth),(t||i)&&e!==window&&(s=s||e.parentNode,!m))do{if(s&&s.getBoundingClientRect&&("none"!==N(s,"transform")||i&&"static"!==N(s,"position"))){var p=s.getBoundingClientRect();a-=p.top+parseInt(N(s,"border-top-width")),o-=p.left+parseInt(N(s,"border-left-width")),l=a+n.height,c=o+n.width;break}}while(s=s.parentNode);if(r&&e!==window){var d=O(s||e),f=d&&d.a,y=d&&d.d;d&&(a/=y,o/=f,u/=f,h/=y,l=a+h,c=o+u)}return{top:a,left:o,bottom:l,right:c,width:u,height:h}}}function F(e,t,i){var r=V(e,!0),s=M(e)[t];while(r){var n=M(r)[i],a=void 0;if(a="top"===i||"left"===i?s>=n:s<=n,!a)return r;if(r===D())break;r=V(r,!1)}return!1}function B(e,t,i){var r=0,s=0,n=e.children;while(s<n.length){if("none"!==n[s].style.display&&n[s]!==Qe.ghost&&n[s]!==Qe.dragged&&A(n[s],i.draggable,e,!1)){if(r===t)return n[s];r++}s++}return null}function _(e,t){var i=e.lastElementChild;while(i&&(i===Qe.ghost||"none"===N(i,"display")||t&&!w(i,t)))i=i.previousElementSibling;return i||null}function j(e,t){var i=0;if(!e||!e.parentNode)return-1;while(e=e.previousElementSibling)"TEMPLATE"===e.nodeName.toUpperCase()||e===Qe.clone||t&&!w(e,t)||i++;return i}function R(e){var t=0,i=0,r=D();if(e)do{var s=O(e),n=s.a,a=s.d;t+=e.scrollLeft*n,i+=e.scrollTop*a}while(e!==r&&(e=e.parentNode));return[t,i]}function U(e,t){for(var i in e)if(e.hasOwnProperty(i))for(var r in t)if(t.hasOwnProperty(r)&&t[r]===e[i][r])return Number(i);return-1}function V(e,t){if(!e||!e.getBoundingClientRect)return D();var i=e,r=!1;do{if(i.clientWidth<i.scrollWidth||i.clientHeight<i.scrollHeight){var s=N(i);if(i.clientWidth<i.scrollWidth&&("auto"==s.overflowX||"scroll"==s.overflowX)||i.clientHeight<i.scrollHeight&&("auto"==s.overflowY||"scroll"==s.overflowY)){if(!i.getBoundingClientRect||i===document.body)return D();if(r||t)return i;r=!0}}}while(i=i.parentNode);return D()}function H(e,t){if(e&&t)for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e}function z(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function q(e,t){return function(){if(!E){var i=arguments,r=this;1===i.length?e.call(r,i[0]):e.apply(r,i),E=setTimeout((function(){E=void 0}),t)}}}function K(){clearTimeout(E),E=void 0}function W(e,t,i){e.scrollLeft+=t,e.scrollTop+=i}function X(e){var t=window.Polymer,i=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):i?i(e).clone(!0)[0]:e.cloneNode(!0)}function J(e,t){N(e,"position","absolute"),N(e,"top",t.top),N(e,"left",t.left),N(e,"width",t.width),N(e,"height",t.height)}function Y(e){N(e,"position",""),N(e,"top",""),N(e,"left",""),N(e,"width",""),N(e,"height","")}var G="Sortable"+(new Date).getTime();function $(){var e,t=[];return{captureAnimationState:function(){if(t=[],this.options.animation){var e=[].slice.call(this.el.children);e.forEach((function(e){if("none"!==N(e,"display")&&e!==Qe.ghost){t.push({target:e,rect:M(e)});var i=a({},t[t.length-1].rect);if(e.thisAnimationDuration){var r=O(e,!0);r&&(i.top-=r.f,i.left-=r.e)}e.fromRect=i}}))}},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(U(t,{target:e}),1)},animateAll:function(i){var r=this;if(!this.options.animation)return clearTimeout(e),void("function"===typeof i&&i());var s=!1,n=0;t.forEach((function(e){var t=0,i=e.target,a=i.fromRect,o=M(i),l=i.prevFromRect,c=i.prevToRect,h=e.rect,u=O(i,!0);u&&(o.top-=u.f,o.left-=u.e),i.toRect=o,i.thisAnimationDuration&&z(l,o)&&!z(a,o)&&(h.top-o.top)/(h.left-o.left)===(a.top-o.top)/(a.left-o.left)&&(t=Z(h,l,c,r.options)),z(o,a)||(i.prevFromRect=a,i.prevToRect=o,t||(t=r.options.animation),r.animate(i,h,o,t)),t&&(s=!0,n=Math.max(n,t),clearTimeout(i.animationResetTimer),i.animationResetTimer=setTimeout((function(){i.animationTime=0,i.prevFromRect=null,i.fromRect=null,i.prevToRect=null,i.thisAnimationDuration=null}),t),i.thisAnimationDuration=t)})),clearTimeout(e),s?e=setTimeout((function(){"function"===typeof i&&i()}),n):"function"===typeof i&&i(),t=[]},animate:function(e,t,i,r){if(r){N(e,"transition",""),N(e,"transform","");var s=O(this.el),n=s&&s.a,a=s&&s.d,o=(t.left-i.left)/(n||1),l=(t.top-i.top)/(a||1);e.animatingX=!!o,e.animatingY=!!l,N(e,"transform","translate3d("+o+"px,"+l+"px,0)"),Q(e),N(e,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),N(e,"transform","translate3d(0,0,0)"),"number"===typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){N(e,"transition",""),N(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),r)}}}}function Q(e){return e.offsetWidth}function Z(e,t,i,r){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))*r.animation}var ee=[],te={initializeByDefault:!0},ie={mount:function(e){for(var t in te)te.hasOwnProperty(t)&&!(t in e)&&(e[t]=te[t]);ee.push(e)},pluginEvent:function(e,t,i){var r=this;this.eventCanceled=!1,i.cancel=function(){r.eventCanceled=!0};var s=e+"Global";ee.forEach((function(r){t[r.pluginName]&&(t[r.pluginName][s]&&t[r.pluginName][s](a({sortable:t},i)),t.options[r.pluginName]&&t[r.pluginName][e]&&t[r.pluginName][e](a({sortable:t},i)))}))},initializePlugins:function(e,t,i,r){for(var s in ee.forEach((function(r){var s=r.pluginName;if(e.options[s]||r.initializeByDefault){var a=new r(e,t,e.options);a.sortable=e,a.options=e.options,e[s]=a,n(i,a.defaults)}})),e.options)if(e.options.hasOwnProperty(s)){var a=this.modifyOption(e,s,e.options[s]);"undefined"!==typeof a&&(e.options[s]=a)}},getEventProperties:function(e,t){var i={};return ee.forEach((function(r){"function"===typeof r.eventProperties&&n(i,r.eventProperties.call(t[r.pluginName],e))})),i},modifyOption:function(e,t,i){var r;return ee.forEach((function(s){e[s.pluginName]&&s.optionListeners&&"function"===typeof s.optionListeners[t]&&(r=s.optionListeners[t].call(e[s.pluginName],i))})),r}};function re(e){var t=e.sortable,i=e.rootEl,r=e.name,s=e.targetEl,n=e.cloneEl,o=e.toEl,l=e.fromEl,c=e.oldIndex,h=e.newIndex,u=e.oldDraggableIndex,p=e.newDraggableIndex,d=e.originalEvent,f=e.putSortable,v=e.extraEventProperties;if(t=t||i&&i[G],t){var g,x=t.options,b="on"+r.charAt(0).toUpperCase()+r.substr(1);!window.CustomEvent||m||y?(g=document.createEvent("Event"),g.initEvent(r,!0,!0)):g=new CustomEvent(r,{bubbles:!0,cancelable:!0}),g.to=o||i,g.from=l||i,g.item=s||i,g.clone=n,g.oldIndex=c,g.newIndex=h,g.oldDraggableIndex=u,g.newDraggableIndex=p,g.originalEvent=d,g.pullMode=f?f.lastPutMode:void 0;var P=a({},v,ie.getEventProperties(r,t));for(var k in P)g[k]=P[k];i&&i.dispatchEvent(g),x[b]&&x[b].call(t,g)}}var se=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=i.evt,s=l(i,["evt"]);ie.pluginEvent.bind(Qe)(e,t,a({dragEl:ae,parentEl:oe,ghostEl:le,rootEl:ce,nextEl:he,lastDownEl:ue,cloneEl:pe,cloneHidden:de,dragStarted:Ae,putSortable:xe,activeSortable:Qe.active,originalEvent:r,oldIndex:fe,oldDraggableIndex:ye,newIndex:me,newDraggableIndex:ve,hideGhostForTarget:Je,unhideGhostForTarget:Ye,cloneNowHidden:function(){de=!0},cloneNowShown:function(){de=!1},dispatchSortableEvent:function(e){ne({sortable:t,name:e,originalEvent:r})}},s))};function ne(e){re(a({putSortable:xe,cloneEl:pe,targetEl:ae,rootEl:ce,oldIndex:fe,oldDraggableIndex:ye,newIndex:me,newDraggableIndex:ve},e))}var ae,oe,le,ce,he,ue,pe,de,fe,me,ye,ve,ge,xe,be,Pe,ke,Te,we,Se,Ae,Ee,Ce,Ie,Ne,Oe=!1,Le=!1,De=[],Me=!1,Fe=!1,Be=[],_e=!1,je=[],Re="undefined"!==typeof document,Ue=x,Ve=y||m?"cssFloat":"float",He=Re&&!b&&!x&&"draggable"in document.createElement("div"),ze=function(){if(Re){if(m)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),qe=function(e,t){var i=N(e),r=parseInt(i.width)-parseInt(i.paddingLeft)-parseInt(i.paddingRight)-parseInt(i.borderLeftWidth)-parseInt(i.borderRightWidth),s=B(e,0,t),n=B(e,1,t),a=s&&N(s),o=n&&N(n),l=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+M(s).width,c=o&&parseInt(o.marginLeft)+parseInt(o.marginRight)+M(n).width;if("flex"===i.display)return"column"===i.flexDirection||"column-reverse"===i.flexDirection?"vertical":"horizontal";if("grid"===i.display)return i.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(s&&a["float"]&&"none"!==a["float"]){var h="left"===a["float"]?"left":"right";return!n||"both"!==o.clear&&o.clear!==h?"horizontal":"vertical"}return s&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||l>=r&&"none"===i[Ve]||n&&"none"===i[Ve]&&l+c>r)?"vertical":"horizontal"},Ke=function(e,t,i){var r=i?e.left:e.top,s=i?e.right:e.bottom,n=i?e.width:e.height,a=i?t.left:t.top,o=i?t.right:t.bottom,l=i?t.width:t.height;return r===a||s===o||r+n/2===a+l/2},We=function(e,t){var i;return De.some((function(r){if(!_(r)){var s=M(r),n=r[G].options.emptyInsertThreshold,a=e>=s.left-n&&e<=s.right+n,o=t>=s.top-n&&t<=s.bottom+n;return n&&a&&o?i=r:void 0}})),i},Xe=function(e){function t(e,i){return function(r,s,n,a){var o=r.options.group.name&&s.options.group.name&&r.options.group.name===s.options.group.name;if(null==e&&(i||o))return!0;if(null==e||!1===e)return!1;if(i&&"clone"===e)return e;if("function"===typeof e)return t(e(r,s,n,a),i)(r,s,n,a);var l=(i?r:s).options.group.name;return!0===e||"string"===typeof e&&e===l||e.join&&e.indexOf(l)>-1}}var i={},s=e.group;s&&"object"==r(s)||(s={name:s}),i.name=s.name,i.checkPull=t(s.pull,!0),i.checkPut=t(s.put),i.revertClone=s.revertClone,e.group=i},Je=function(){!ze&&le&&N(le,"display","none")},Ye=function(){!ze&&le&&N(le,"display","")};Re&&document.addEventListener("click",(function(e){if(Le)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Le=!1,!1}),!0);var Ge=function(e){if(ae){e=e.touches?e.touches[0]:e;var t=We(e.clientX,e.clientY);if(t){var i={};for(var r in e)e.hasOwnProperty(r)&&(i[r]=e[r]);i.target=i.rootEl=t,i.preventDefault=void 0,i.stopPropagation=void 0,t[G]._onDragOver(i)}}},$e=function(e){ae&&ae.parentNode[G]._isOutsideThisEl(e.target)};function Qe(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=n({},t),e[G]=this;var i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return qe(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Qe.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var r in ie.initializePlugins(this,e,i),i)!(r in t)&&(t[r]=i[r]);for(var s in Xe(t),this)"_"===s.charAt(0)&&"function"===typeof this[s]&&(this[s]=this[s].bind(this));this.nativeDraggable=!t.forceFallback&&He,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?k(e,"pointerdown",this._onTapStart):(k(e,"mousedown",this._onTapStart),k(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(k(e,"dragover",this),k(e,"dragenter",this)),De.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),n(this,$())}function Ze(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.cancelable&&e.preventDefault()}function et(e,t,i,r,s,n,a,o){var l,c,h=e[G],u=h.options.onMove;return!window.CustomEvent||m||y?(l=document.createEvent("Event"),l.initEvent("move",!0,!0)):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=e,l.dragged=i,l.draggedRect=r,l.related=s||t,l.relatedRect=n||M(t),l.willInsertAfter=o,l.originalEvent=a,e.dispatchEvent(l),u&&(c=u.call(h,l,a)),c}function tt(e){e.draggable=!1}function it(){_e=!1}function rt(e,t,i){var r=M(_(i.el,i.options.draggable)),s=10;return t?e.clientX>r.right+s||e.clientX<=r.right&&e.clientY>r.bottom&&e.clientX>=r.left:e.clientX>r.right&&e.clientY>r.top||e.clientX<=r.right&&e.clientY>r.bottom+s}function st(e,t,i,r,s,n,a,o){var l=r?e.clientY:e.clientX,c=r?i.height:i.width,h=r?i.top:i.left,u=r?i.bottom:i.right,p=!1;if(!a)if(o&&Ie<c*s){if(!Me&&(1===Ce?l>h+c*n/2:l<u-c*n/2)&&(Me=!0),Me)p=!0;else if(1===Ce?l<h+Ie:l>u-Ie)return-Ce}else if(l>h+c*(1-s)/2&&l<u-c*(1-s)/2)return nt(t);return p=p||a,p&&(l<h+c*n/2||l>u-c*n/2)?l>h+c/2?1:-1:0}function nt(e){return j(ae)<j(e)?1:-1}function at(e){var t=e.tagName+e.className+e.src+e.href+e.textContent,i=t.length,r=0;while(i--)r+=t.charCodeAt(i);return r.toString(36)}function ot(e){je.length=0;var t=e.getElementsByTagName("input"),i=t.length;while(i--){var r=t[i];r.checked&&je.push(r)}}function lt(e){return setTimeout(e,0)}function ct(e){return clearTimeout(e)}Qe.prototype={constructor:Qe,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(Ee=null)},_getDirection:function(e,t){return"function"===typeof this.options.direction?this.options.direction.call(this,e,t,ae):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,i=this.el,r=this.options,s=r.preventOnFilter,n=e.type,a=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,o=(a||e).target,l=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||o,c=r.filter;if(ot(i),!ae&&!(/mousedown|pointerdown/.test(n)&&0!==e.button||r.disabled)&&!l.isContentEditable&&(o=A(o,r.draggable,i,!1),(!o||!o.animated)&&ue!==o)){if(fe=j(o),ye=j(o,r.draggable),"function"===typeof c){if(c.call(this,e,o,this))return ne({sortable:t,rootEl:l,name:"filter",targetEl:o,toEl:i,fromEl:i}),se("filter",t,{evt:e}),void(s&&e.cancelable&&e.preventDefault())}else if(c&&(c=c.split(",").some((function(r){if(r=A(l,r.trim(),i,!1),r)return ne({sortable:t,rootEl:r,name:"filter",targetEl:o,fromEl:i,toEl:i}),se("filter",t,{evt:e}),!0})),c))return void(s&&e.cancelable&&e.preventDefault());r.handle&&!A(l,r.handle,i,!1)||this._prepareDragStart(e,a,o)}}},_prepareDragStart:function(e,t,i){var r,s=this,n=s.el,a=s.options,o=n.ownerDocument;if(i&&!ae&&i.parentNode===n){var l=M(i);if(ce=n,ae=i,oe=ae.parentNode,he=ae.nextSibling,ue=i,ge=a.group,Qe.dragged=ae,be={target:ae,clientX:(t||e).clientX,clientY:(t||e).clientY},we=be.clientX-l.left,Se=be.clientY-l.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,ae.style["will-change"]="all",r=function(){se("delayEnded",s,{evt:e}),Qe.eventCanceled?s._onDrop():(s._disableDelayedDragEvents(),!v&&s.nativeDraggable&&(ae.draggable=!0),s._triggerDragStart(e,t),ne({sortable:s,name:"choose",originalEvent:e}),I(ae,a.chosenClass,!0))},a.ignore.split(",").forEach((function(e){L(ae,e.trim(),tt)})),k(o,"dragover",Ge),k(o,"mousemove",Ge),k(o,"touchmove",Ge),k(o,"mouseup",s._onDrop),k(o,"touchend",s._onDrop),k(o,"touchcancel",s._onDrop),v&&this.nativeDraggable&&(this.options.touchStartThreshold=4,ae.draggable=!0),se("delayStart",this,{evt:e}),!a.delay||a.delayOnTouchOnly&&!t||this.nativeDraggable&&(y||m))r();else{if(Qe.eventCanceled)return void this._onDrop();k(o,"mouseup",s._disableDelayedDrag),k(o,"touchend",s._disableDelayedDrag),k(o,"touchcancel",s._disableDelayedDrag),k(o,"mousemove",s._delayedDragTouchMoveHandler),k(o,"touchmove",s._delayedDragTouchMoveHandler),a.supportPointer&&k(o,"pointermove",s._delayedDragTouchMoveHandler),s._dragStartTimer=setTimeout(r,a.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){ae&&tt(ae),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;T(e,"mouseup",this._disableDelayedDrag),T(e,"touchend",this._disableDelayedDrag),T(e,"touchcancel",this._disableDelayedDrag),T(e,"mousemove",this._delayedDragTouchMoveHandler),T(e,"touchmove",this._delayedDragTouchMoveHandler),T(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?k(document,"pointermove",this._onTouchMove):k(document,t?"touchmove":"mousemove",this._onTouchMove):(k(ae,"dragend",this),k(ce,"dragstart",this._onDragStart));try{document.selection?lt((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(i){}},_dragStarted:function(e,t){if(Oe=!1,ce&&ae){se("dragStarted",this,{evt:t}),this.nativeDraggable&&k(document,"dragover",$e);var i=this.options;!e&&I(ae,i.dragClass,!1),I(ae,i.ghostClass,!0),Qe.active=this,e&&this._appendGhost(),ne({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(Pe){this._lastX=Pe.clientX,this._lastY=Pe.clientY,Je();var e=document.elementFromPoint(Pe.clientX,Pe.clientY),t=e;while(e&&e.shadowRoot){if(e=e.shadowRoot.elementFromPoint(Pe.clientX,Pe.clientY),e===t)break;t=e}if(ae.parentNode[G]._isOutsideThisEl(e),t)do{if(t[G]){var i=void 0;if(i=t[G]._onDragOver({clientX:Pe.clientX,clientY:Pe.clientY,target:e,rootEl:t}),i&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Ye()}},_onTouchMove:function(e){if(be){var t=this.options,i=t.fallbackTolerance,r=t.fallbackOffset,s=e.touches?e.touches[0]:e,n=le&&O(le,!0),a=le&&n&&n.a,o=le&&n&&n.d,l=Ue&&Ne&&R(Ne),c=(s.clientX-be.clientX+r.x)/(a||1)+(l?l[0]-Be[0]:0)/(a||1),h=(s.clientY-be.clientY+r.y)/(o||1)+(l?l[1]-Be[1]:0)/(o||1);if(!Qe.active&&!Oe){if(i&&Math.max(Math.abs(s.clientX-this._lastX),Math.abs(s.clientY-this._lastY))<i)return;this._onDragStart(e,!0)}if(le){n?(n.e+=c-(ke||0),n.f+=h-(Te||0)):n={a:1,b:0,c:0,d:1,e:c,f:h};var u="matrix(".concat(n.a,",").concat(n.b,",").concat(n.c,",").concat(n.d,",").concat(n.e,",").concat(n.f,")");N(le,"webkitTransform",u),N(le,"mozTransform",u),N(le,"msTransform",u),N(le,"transform",u),ke=c,Te=h,Pe=s}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!le){var e=this.options.fallbackOnBody?document.body:ce,t=M(ae,!0,Ue,!0,e),i=this.options;if(Ue){Ne=e;while("static"===N(Ne,"position")&&"none"===N(Ne,"transform")&&Ne!==document)Ne=Ne.parentNode;Ne!==document.body&&Ne!==document.documentElement?(Ne===document&&(Ne=D()),t.top+=Ne.scrollTop,t.left+=Ne.scrollLeft):Ne=D(),Be=R(Ne)}le=ae.cloneNode(!0),I(le,i.ghostClass,!1),I(le,i.fallbackClass,!0),I(le,i.dragClass,!0),N(le,"transition",""),N(le,"transform",""),N(le,"box-sizing","border-box"),N(le,"margin",0),N(le,"top",t.top),N(le,"left",t.left),N(le,"width",t.width),N(le,"height",t.height),N(le,"opacity","0.8"),N(le,"position",Ue?"absolute":"fixed"),N(le,"zIndex","100000"),N(le,"pointerEvents","none"),Qe.ghost=le,e.appendChild(le),N(le,"transform-origin",we/parseInt(le.style.width)*100+"% "+Se/parseInt(le.style.height)*100+"%")}},_onDragStart:function(e,t){var i=this,r=e.dataTransfer,s=i.options;se("dragStart",this,{evt:e}),Qe.eventCanceled?this._onDrop():(se("setupClone",this),Qe.eventCanceled||(pe=X(ae),pe.draggable=!1,pe.style["will-change"]="",this._hideClone(),I(pe,this.options.chosenClass,!1),Qe.clone=pe),i.cloneId=lt((function(){se("clone",i),Qe.eventCanceled||(i.options.removeCloneOnHide||ce.insertBefore(pe,ae),i._hideClone(),ne({sortable:i,name:"clone"}))})),!t&&I(ae,s.dragClass,!0),t?(Le=!0,i._loopId=setInterval(i._emulateDragOver,50)):(T(document,"mouseup",i._onDrop),T(document,"touchend",i._onDrop),T(document,"touchcancel",i._onDrop),r&&(r.effectAllowed="move",s.setData&&s.setData.call(i,r,ae)),k(document,"drop",i),N(ae,"transform","translateZ(0)")),Oe=!0,i._dragStartId=lt(i._dragStarted.bind(i,t,e)),k(document,"selectstart",i),Ae=!0,g&&N(document.body,"user-select","none"))},_onDragOver:function(e){var t,i,r,s,n=this.el,o=e.target,l=this.options,c=l.group,h=Qe.active,u=ge===c,p=l.sort,d=xe||h,f=this,m=!1;if(!_e){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),o=A(o,l.draggable,n,!0),L("dragOver"),Qe.eventCanceled)return m;if(ae.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||f._ignoreWhileAnimating===o)return B(!1);if(Le=!1,h&&!l.disabled&&(u?p||(r=!ce.contains(ae)):xe===this||(this.lastPutMode=ge.checkPull(this,h,ae,e))&&c.checkPut(this,h,ae,e))){if(s="vertical"===this._getDirection(e,o),t=M(ae),L("dragOverValid"),Qe.eventCanceled)return m;if(r)return oe=ce,D(),this._hideClone(),L("revert"),Qe.eventCanceled||(he?ce.insertBefore(ae,he):ce.appendChild(ae)),B(!0);var y=_(n,l.draggable);if(!y||rt(e,s,this)&&!y.animated){if(y===ae)return B(!1);if(y&&n===e.target&&(o=y),o&&(i=M(o)),!1!==et(ce,n,ae,t,o,i,e,!!o))return D(),n.appendChild(ae),oe=n,R(),B(!0)}else if(o.parentNode===n){i=M(o);var v,g,x=0,b=ae.parentNode!==n,P=!Ke(ae.animated&&ae.toRect||t,o.animated&&o.toRect||i,s),k=s?"top":"left",T=F(o,"top","top")||F(ae,"top","top"),w=T?T.scrollTop:void 0;if(Ee!==o&&(v=i[k],Me=!1,Fe=!P&&l.invertSwap||b),x=st(e,o,i,s,P?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Fe,Ee===o),0!==x){var S=j(ae);do{S-=x,g=oe.children[S]}while(g&&("none"===N(g,"display")||g===le))}if(0===x||g===o)return B(!1);Ee=o,Ce=x;var E=o.nextElementSibling,C=!1;C=1===x;var O=et(ce,n,ae,t,o,i,e,C);if(!1!==O)return 1!==O&&-1!==O||(C=1===O),_e=!0,setTimeout(it,30),D(),C&&!E?n.appendChild(ae):o.parentNode.insertBefore(ae,C?E:o),T&&W(T,0,w-T.scrollTop),oe=ae.parentNode,void 0===v||Fe||(Ie=Math.abs(v-M(o)[k])),R(),B(!0)}if(n.contains(ae))return B(!1)}return!1}function L(l,c){se(l,f,a({evt:e,isOwner:u,axis:s?"vertical":"horizontal",revert:r,dragRect:t,targetRect:i,canSort:p,fromSortable:d,target:o,completed:B,onMove:function(i,r){return et(ce,n,ae,t,i,M(i),e,r)},changed:R},c))}function D(){L("dragOverAnimationCapture"),f.captureAnimationState(),f!==d&&d.captureAnimationState()}function B(t){return L("dragOverCompleted",{insertion:t}),t&&(u?h._hideClone():h._showClone(f),f!==d&&(I(ae,xe?xe.options.ghostClass:h.options.ghostClass,!1),I(ae,l.ghostClass,!0)),xe!==f&&f!==Qe.active?xe=f:f===Qe.active&&xe&&(xe=null),d===f&&(f._ignoreWhileAnimating=o),f.animateAll((function(){L("dragOverAnimationComplete"),f._ignoreWhileAnimating=null})),f!==d&&(d.animateAll(),d._ignoreWhileAnimating=null)),(o===ae&&!ae.animated||o===n&&!o.animated)&&(Ee=null),l.dragoverBubble||e.rootEl||o===document||(ae.parentNode[G]._isOutsideThisEl(e.target),!t&&Ge(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),m=!0}function R(){me=j(ae),ve=j(ae,l.draggable),ne({sortable:f,name:"change",toEl:n,newIndex:me,newDraggableIndex:ve,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){T(document,"mousemove",this._onTouchMove),T(document,"touchmove",this._onTouchMove),T(document,"pointermove",this._onTouchMove),T(document,"dragover",Ge),T(document,"mousemove",Ge),T(document,"touchmove",Ge)},_offUpEvents:function(){var e=this.el.ownerDocument;T(e,"mouseup",this._onDrop),T(e,"touchend",this._onDrop),T(e,"pointerup",this._onDrop),T(e,"touchcancel",this._onDrop),T(document,"selectstart",this)},_onDrop:function(e){var t=this.el,i=this.options;me=j(ae),ve=j(ae,i.draggable),se("drop",this,{evt:e}),oe=ae&&ae.parentNode,me=j(ae),ve=j(ae,i.draggable),Qe.eventCanceled||(Oe=!1,Fe=!1,Me=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ct(this.cloneId),ct(this._dragStartId),this.nativeDraggable&&(T(document,"drop",this),T(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),g&&N(document.body,"user-select",""),N(ae,"transform",""),e&&(Ae&&(e.cancelable&&e.preventDefault(),!i.dropBubble&&e.stopPropagation()),le&&le.parentNode&&le.parentNode.removeChild(le),(ce===oe||xe&&"clone"!==xe.lastPutMode)&&pe&&pe.parentNode&&pe.parentNode.removeChild(pe),ae&&(this.nativeDraggable&&T(ae,"dragend",this),tt(ae),ae.style["will-change"]="",Ae&&!Oe&&I(ae,xe?xe.options.ghostClass:this.options.ghostClass,!1),I(ae,this.options.chosenClass,!1),ne({sortable:this,name:"unchoose",toEl:oe,newIndex:null,newDraggableIndex:null,originalEvent:e}),ce!==oe?(me>=0&&(ne({rootEl:oe,name:"add",toEl:oe,fromEl:ce,originalEvent:e}),ne({sortable:this,name:"remove",toEl:oe,originalEvent:e}),ne({rootEl:oe,name:"sort",toEl:oe,fromEl:ce,originalEvent:e}),ne({sortable:this,name:"sort",toEl:oe,originalEvent:e})),xe&&xe.save()):me!==fe&&me>=0&&(ne({sortable:this,name:"update",toEl:oe,originalEvent:e}),ne({sortable:this,name:"sort",toEl:oe,originalEvent:e})),Qe.active&&(null!=me&&-1!==me||(me=fe,ve=ye),ne({sortable:this,name:"end",toEl:oe,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){se("nulling",this),ce=ae=oe=le=he=pe=ue=de=be=Pe=Ae=me=ve=fe=ye=Ee=Ce=xe=ge=Qe.dragged=Qe.ghost=Qe.clone=Qe.active=null,je.forEach((function(e){e.checked=!0})),je.length=ke=Te=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":ae&&(this._onDragOver(e),Ze(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e,t=[],i=this.el.children,r=0,s=i.length,n=this.options;r<s;r++)e=i[r],A(e,n.draggable,this.el,!1)&&t.push(e.getAttribute(n.dataIdAttr)||at(e));return t},sort:function(e){var t={},i=this.el;this.toArray().forEach((function(e,r){var s=i.children[r];A(s,this.options.draggable,i,!1)&&(t[e]=s)}),this),e.forEach((function(e){t[e]&&(i.removeChild(t[e]),i.appendChild(t[e]))}))},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return A(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var i=this.options;if(void 0===t)return i[e];var r=ie.modifyOption(this,e,t);i[e]="undefined"!==typeof r?r:t,"group"===e&&Xe(i)},destroy:function(){se("destroy",this);var e=this.el;e[G]=null,T(e,"mousedown",this._onTapStart),T(e,"touchstart",this._onTapStart),T(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(T(e,"dragover",this),T(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),De.splice(De.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!de){if(se("hideClone",this),Qe.eventCanceled)return;N(pe,"display","none"),this.options.removeCloneOnHide&&pe.parentNode&&pe.parentNode.removeChild(pe),de=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(de){if(se("showClone",this),Qe.eventCanceled)return;ce.contains(ae)&&!this.options.group.revertClone?ce.insertBefore(pe,ae):he?ce.insertBefore(pe,he):ce.appendChild(pe),this.options.group.revertClone&&this.animate(ae,pe),N(pe,"display",""),de=!1}}else this._hideClone()}},Re&&k(document,"touchmove",(function(e){(Qe.active||Oe)&&e.cancelable&&e.preventDefault()})),Qe.utils={on:k,off:T,css:N,find:L,is:function(e,t){return!!A(e,t,e,!1)},extend:H,throttle:q,closest:A,toggleClass:I,clone:X,index:j,nextTick:lt,cancelNextTick:ct,detectDirection:qe,getChild:B},Qe.get=function(e){return e[G]},Qe.mount=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Qe.utils=a({},Qe.utils,e.utils)),ie.mount(e)}))},Qe.create=function(e,t){return new Qe(e,t)},Qe.version=d;var ht,ut,pt,dt,ft,mt,yt=[],vt=!1;function gt(){function e(){for(var e in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"===typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?k(document,"dragover",this._handleAutoScroll):this.options.supportPointer?k(document,"pointermove",this._handleFallbackAutoScroll):t.touches?k(document,"touchmove",this._handleFallbackAutoScroll):k(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?T(document,"dragover",this._handleAutoScroll):(T(document,"pointermove",this._handleFallbackAutoScroll),T(document,"touchmove",this._handleFallbackAutoScroll),T(document,"mousemove",this._handleFallbackAutoScroll)),bt(),xt(),K()},nulling:function(){ft=ut=ht=vt=mt=pt=dt=null,yt.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var i=this,r=(e.touches?e.touches[0]:e).clientX,s=(e.touches?e.touches[0]:e).clientY,n=document.elementFromPoint(r,s);if(ft=e,t||y||m||g){kt(e,this.options,n,t);var a=V(n,!0);!vt||mt&&r===pt&&s===dt||(mt&&bt(),mt=setInterval((function(){var n=V(document.elementFromPoint(r,s),!0);n!==a&&(a=n,xt()),kt(e,i.options,n,t)}),10),pt=r,dt=s)}else{if(!this.options.bubbleScroll||V(n,!0)===D())return void xt();kt(e,this.options,V(n,!1),!1)}}},n(e,{pluginName:"scroll",initializeByDefault:!0})}function xt(){yt.forEach((function(e){clearInterval(e.pid)})),yt=[]}function bt(){clearInterval(mt)}var Pt,kt=q((function(e,t,i,r){if(t.scroll){var s,n=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,o=t.scrollSensitivity,l=t.scrollSpeed,c=D(),h=!1;ut!==i&&(ut=i,xt(),ht=t.scroll,s=t.scrollFn,!0===ht&&(ht=V(i,!0)));var u=0,p=ht;do{var d=p,f=M(d),m=f.top,y=f.bottom,v=f.left,g=f.right,x=f.width,b=f.height,P=void 0,k=void 0,T=d.scrollWidth,w=d.scrollHeight,S=N(d),A=d.scrollLeft,E=d.scrollTop;d===c?(P=x<T&&("auto"===S.overflowX||"scroll"===S.overflowX||"visible"===S.overflowX),k=b<w&&("auto"===S.overflowY||"scroll"===S.overflowY||"visible"===S.overflowY)):(P=x<T&&("auto"===S.overflowX||"scroll"===S.overflowX),k=b<w&&("auto"===S.overflowY||"scroll"===S.overflowY));var C=P&&(Math.abs(g-n)<=o&&A+x<T)-(Math.abs(v-n)<=o&&!!A),I=k&&(Math.abs(y-a)<=o&&E+b<w)-(Math.abs(m-a)<=o&&!!E);if(!yt[u])for(var O=0;O<=u;O++)yt[O]||(yt[O]={});yt[u].vx==C&&yt[u].vy==I&&yt[u].el===d||(yt[u].el=d,yt[u].vx=C,yt[u].vy=I,clearInterval(yt[u].pid),0==C&&0==I||(h=!0,yt[u].pid=setInterval(function(){r&&0===this.layer&&Qe.active._onTouchMove(ft);var t=yt[this.layer].vy?yt[this.layer].vy*l:0,i=yt[this.layer].vx?yt[this.layer].vx*l:0;"function"===typeof s&&"continue"!==s.call(Qe.dragged.parentNode[G],i,t,e,ft,yt[this.layer].el)||W(yt[this.layer].el,i,t)}.bind({layer:u}),24))),u++}while(t.bubbleScroll&&p!==c&&(p=V(p,!1)));vt=h}}),30),Tt=function(e){var t=e.originalEvent,i=e.putSortable,r=e.dragEl,s=e.activeSortable,n=e.dispatchSortableEvent,a=e.hideGhostForTarget,o=e.unhideGhostForTarget;if(t){var l=i||s;a();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,h=document.elementFromPoint(c.clientX,c.clientY);o(),l&&!l.el.contains(h)&&(n("spill"),this.onSpill({dragEl:r,putSortable:i}))}};function wt(){}function St(){}function At(){function e(){this.defaults={swapClass:"sortable-swap-highlight"}}return e.prototype={dragStart:function(e){var t=e.dragEl;Pt=t},dragOverValid:function(e){var t=e.completed,i=e.target,r=e.onMove,s=e.activeSortable,n=e.changed,a=e.cancel;if(s.options.swap){var o=this.sortable.el,l=this.options;if(i&&i!==o){var c=Pt;!1!==r(i)?(I(i,l.swapClass,!0),Pt=i):Pt=null,c&&c!==Pt&&I(c,l.swapClass,!1)}n(),t(!0),a()}},drop:function(e){var t=e.activeSortable,i=e.putSortable,r=e.dragEl,s=i||this.sortable,n=this.options;Pt&&I(Pt,n.swapClass,!1),Pt&&(n.swap||i&&i.options.swap)&&r!==Pt&&(s.captureAnimationState(),s!==t&&t.captureAnimationState(),Et(r,Pt),s.animateAll(),s!==t&&t.animateAll())},nulling:function(){Pt=null}},n(e,{pluginName:"swap",eventProperties:function(){return{swapItem:Pt}}})}function Et(e,t){var i,r,s=e.parentNode,n=t.parentNode;s&&n&&!s.isEqualNode(t)&&!n.isEqualNode(e)&&(i=j(e),r=j(t),s.isEqualNode(n)&&i<r&&r++,s.insertBefore(t,s.children[i]),n.insertBefore(e,n.children[r]))}wt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,i=e.putSortable;this.sortable.captureAnimationState(),i&&i.captureAnimationState();var r=B(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(t,r):this.sortable.el.appendChild(t),this.sortable.animateAll(),i&&i.animateAll()},drop:Tt},n(wt,{pluginName:"revertOnSpill"}),St.prototype={onSpill:function(e){var t=e.dragEl,i=e.putSortable,r=i||this.sortable;r.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),r.animateAll()},drop:Tt},n(St,{pluginName:"removeOnSpill"});var Ct,It,Nt,Ot,Lt,Dt=[],Mt=[],Ft=!1,Bt=!1,_t=!1;function jt(){function e(e){for(var t in this)"_"===t.charAt(0)&&"function"===typeof this[t]&&(this[t]=this[t].bind(this));e.options.supportPointer?k(document,"pointerup",this._deselectMultiDrag):(k(document,"mouseup",this._deselectMultiDrag),k(document,"touchend",this._deselectMultiDrag)),k(document,"keydown",this._checkKeyDown),k(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(t,i){var r="";Dt.length&&It===e?Dt.forEach((function(e,t){r+=(t?", ":"")+e.textContent})):r=i.textContent,t.setData("Text",r)}}}return e.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(e){var t=e.dragEl;Nt=t},delayEnded:function(){this.isMultiDrag=~Dt.indexOf(Nt)},setupClone:function(e){var t=e.sortable,i=e.cancel;if(this.isMultiDrag){for(var r=0;r<Dt.length;r++)Mt.push(X(Dt[r])),Mt[r].sortableIndex=Dt[r].sortableIndex,Mt[r].draggable=!1,Mt[r].style["will-change"]="",I(Mt[r],this.options.selectedClass,!1),Dt[r]===Nt&&I(Mt[r],this.options.chosenClass,!1);t._hideClone(),i()}},clone:function(e){var t=e.sortable,i=e.rootEl,r=e.dispatchSortableEvent,s=e.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||Dt.length&&It===t&&(Ut(!0,i),r("clone"),s()))},showClone:function(e){var t=e.cloneNowShown,i=e.rootEl,r=e.cancel;this.isMultiDrag&&(Ut(!1,i),Mt.forEach((function(e){N(e,"display","")})),t(),Lt=!1,r())},hideClone:function(e){var t=this,i=(e.sortable,e.cloneNowHidden),r=e.cancel;this.isMultiDrag&&(Mt.forEach((function(e){N(e,"display","none"),t.options.removeCloneOnHide&&e.parentNode&&e.parentNode.removeChild(e)})),i(),Lt=!0,r())},dragStartGlobal:function(e){e.sortable;!this.isMultiDrag&&It&&It.multiDrag._deselectMultiDrag(),Dt.forEach((function(e){e.sortableIndex=j(e)})),Dt=Dt.sort((function(e,t){return e.sortableIndex-t.sortableIndex})),_t=!0},dragStarted:function(e){var t=this,i=e.sortable;if(this.isMultiDrag){if(this.options.sort&&(i.captureAnimationState(),this.options.animation)){Dt.forEach((function(e){e!==Nt&&N(e,"position","absolute")}));var r=M(Nt,!1,!0,!0);Dt.forEach((function(e){e!==Nt&&J(e,r)})),Bt=!0,Ft=!0}i.animateAll((function(){Bt=!1,Ft=!1,t.options.animation&&Dt.forEach((function(e){Y(e)})),t.options.sort&&Vt()}))}},dragOver:function(e){var t=e.target,i=e.completed,r=e.cancel;Bt&&~Dt.indexOf(t)&&(i(!1),r())},revert:function(e){var t=e.fromSortable,i=e.rootEl,r=e.sortable,s=e.dragRect;Dt.length>1&&(Dt.forEach((function(e){r.addAnimationState({target:e,rect:Bt?M(e):s}),Y(e),e.fromRect=s,t.removeAnimationState(e)})),Bt=!1,Rt(!this.options.removeCloneOnHide,i))},dragOverCompleted:function(e){var t=e.sortable,i=e.isOwner,r=e.insertion,s=e.activeSortable,n=e.parentEl,a=e.putSortable,o=this.options;if(r){if(i&&s._hideClone(),Ft=!1,o.animation&&Dt.length>1&&(Bt||!i&&!s.options.sort&&!a)){var l=M(Nt,!1,!0,!0);Dt.forEach((function(e){e!==Nt&&(J(e,l),n.appendChild(e))})),Bt=!0}if(!i)if(Bt||Vt(),Dt.length>1){var c=Lt;s._showClone(t),s.options.animation&&!Lt&&c&&Mt.forEach((function(e){s.addAnimationState({target:e,rect:Ot}),e.fromRect=Ot,e.thisAnimationDuration=null}))}else s._showClone(t)}},dragOverAnimationCapture:function(e){var t=e.dragRect,i=e.isOwner,r=e.activeSortable;if(Dt.forEach((function(e){e.thisAnimationDuration=null})),r.options.animation&&!i&&r.multiDrag.isMultiDrag){Ot=n({},t);var s=O(Nt,!0);Ot.top-=s.f,Ot.left-=s.e}},dragOverAnimationComplete:function(){Bt&&(Bt=!1,Vt())},drop:function(e){var t=e.originalEvent,i=e.rootEl,r=e.parentEl,s=e.sortable,n=e.dispatchSortableEvent,a=e.oldIndex,o=e.putSortable,l=o||this.sortable;if(t){var c=this.options,h=r.children;if(!_t)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),I(Nt,c.selectedClass,!~Dt.indexOf(Nt)),~Dt.indexOf(Nt))Dt.splice(Dt.indexOf(Nt),1),Ct=null,re({sortable:s,rootEl:i,name:"deselect",targetEl:Nt,originalEvt:t});else{if(Dt.push(Nt),re({sortable:s,rootEl:i,name:"select",targetEl:Nt,originalEvt:t}),t.shiftKey&&Ct&&s.el.contains(Ct)){var u,p,d=j(Ct),f=j(Nt);if(~d&&~f&&d!==f)for(f>d?(p=d,u=f):(p=f,u=d+1);p<u;p++)~Dt.indexOf(h[p])||(I(h[p],c.selectedClass,!0),Dt.push(h[p]),re({sortable:s,rootEl:i,name:"select",targetEl:h[p],originalEvt:t}))}else Ct=Nt;It=l}if(_t&&this.isMultiDrag){if((r[G].options.sort||r!==i)&&Dt.length>1){var m=M(Nt),y=j(Nt,":not(."+this.options.selectedClass+")");if(!Ft&&c.animation&&(Nt.thisAnimationDuration=null),l.captureAnimationState(),!Ft&&(c.animation&&(Nt.fromRect=m,Dt.forEach((function(e){if(e.thisAnimationDuration=null,e!==Nt){var t=Bt?M(e):m;e.fromRect=t,l.addAnimationState({target:e,rect:t})}}))),Vt(),Dt.forEach((function(e){h[y]?r.insertBefore(e,h[y]):r.appendChild(e),y++})),a===j(Nt))){var v=!1;Dt.forEach((function(e){e.sortableIndex===j(e)||(v=!0)})),v&&n("update")}Dt.forEach((function(e){Y(e)})),l.animateAll()}It=l}(i===r||o&&"clone"!==o.lastPutMode)&&Mt.forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)}))}},nullingGlobal:function(){this.isMultiDrag=_t=!1,Mt.length=0},destroyGlobal:function(){this._deselectMultiDrag(),T(document,"pointerup",this._deselectMultiDrag),T(document,"mouseup",this._deselectMultiDrag),T(document,"touchend",this._deselectMultiDrag),T(document,"keydown",this._checkKeyDown),T(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(e){if(("undefined"===typeof _t||!_t)&&It===this.sortable&&(!e||!A(e.target,this.options.draggable,this.sortable.el,!1))&&(!e||0===e.button))while(Dt.length){var t=Dt[0];I(t,this.options.selectedClass,!1),Dt.shift(),re({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:t,originalEvt:e})}},_checkKeyDown:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(e){e.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},n(e,{pluginName:"multiDrag",utils:{select:function(e){var t=e.parentNode[G];t&&t.options.multiDrag&&!~Dt.indexOf(e)&&(It&&It!==t&&(It.multiDrag._deselectMultiDrag(),It=t),I(e,t.options.selectedClass,!0),Dt.push(e))},deselect:function(e){var t=e.parentNode[G],i=Dt.indexOf(e);t&&t.options.multiDrag&&~i&&(I(e,t.options.selectedClass,!1),Dt.splice(i,1))}},eventProperties:function(){var e=this,t=[],i=[];return Dt.forEach((function(r){var s;t.push({multiDragElement:r,index:r.sortableIndex}),s=Bt&&r!==Nt?-1:Bt?j(r,":not(."+e.options.selectedClass+")"):j(r),i.push({multiDragElement:r,index:s})})),{items:c(Dt),clones:[].concat(Mt),oldIndicies:t,newIndicies:i}},optionListeners:{multiDragKey:function(e){return e=e.toLowerCase(),"ctrl"===e?e="Control":e.length>1&&(e=e.charAt(0).toUpperCase()+e.substr(1)),e}}})}function Rt(e,t){Dt.forEach((function(i,r){var s=t.children[i.sortableIndex+(e?Number(r):0)];s?t.insertBefore(i,s):t.appendChild(i)}))}function Ut(e,t){Mt.forEach((function(i,r){var s=t.children[i.sortableIndex+(e?Number(r):0)];s?t.insertBefore(i,s):t.appendChild(i)}))}function Vt(){Dt.forEach((function(e){e!==Nt&&e.parentNode&&e.parentNode.removeChild(e)}))}Qe.mount(new gt),Qe.mount(St,wt),t["default"]=Qe},b311:function(e,t,i){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(t,i){e.exports=i()})(0,(function(){return function(){var e={686:function(e,t,i){"use strict";i.d(t,{default:function(){return L}});var r=i(279),s=i.n(r),n=i(370),a=i.n(n),o=i(817),l=i.n(o);function c(e){try{return document.execCommand(e)}catch(t){return!1}}var h=function(e){var t=l()(e);return c("cut"),t},u=h;function p(e){var t="rtl"===document.documentElement.getAttribute("dir"),i=document.createElement("textarea");i.style.fontSize="12pt",i.style.border="0",i.style.padding="0",i.style.margin="0",i.style.position="absolute",i.style[t?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return i.style.top="".concat(r,"px"),i.setAttribute("readonly",""),i.value=e,i}var d=function(e,t){var i=p(e);t.container.appendChild(i);var r=l()(i);return c("copy"),i.remove(),r},f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},i="";return"string"===typeof e?i=d(e,t):e instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===e||void 0===e?void 0:e.type)?i=d(e.value,t):(i=l()(e),c("copy")),i},m=f;function y(e){return y="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y(e)}var v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,i=void 0===t?"copy":t,r=e.container,s=e.target,n=e.text;if("copy"!==i&&"cut"!==i)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==s){if(!s||"object"!==y(s)||1!==s.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===i&&s.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===i&&(s.hasAttribute("readonly")||s.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return n?m(n,{container:r}):s?"cut"===i?u(s):m(s,{container:r}):void 0},g=v;function x(e){return x="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},x(e)}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function P(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function k(e,t,i){return t&&P(e.prototype,t),i&&P(e,i),e}function T(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&w(e,t)}function w(e,t){return w=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},w(e,t)}function S(e){var t=C();return function(){var i,r=I(e);if(t){var s=I(this).constructor;i=Reflect.construct(r,arguments,s)}else i=r.apply(this,arguments);return A(this,i)}}function A(e,t){return!t||"object"!==x(t)&&"function"!==typeof t?E(e):t}function E(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function C(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function I(e){return I=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},I(e)}function N(e,t){var i="data-clipboard-".concat(e);if(t.hasAttribute(i))return t.getAttribute(i)}var O=function(e){T(i,e);var t=S(i);function i(e,r){var s;return b(this,i),s=t.call(this),s.resolveOptions(r),s.listenClick(e),s}return k(i,[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof e.action?e.action:this.defaultAction,this.target="function"===typeof e.target?e.target:this.defaultTarget,this.text="function"===typeof e.text?e.text:this.defaultText,this.container="object"===x(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=a()(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,i=this.action(t)||"copy",r=g({action:i,container:this.container,target:this.target(t),text:this.text(t)});this.emit(r?"success":"error",{action:i,text:r,trigger:t,clearSelection:function(){t&&t.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return N("action",e)}},{key:"defaultTarget",value:function(e){var t=N("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return N("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return m(e,t)}},{key:"cut",value:function(e){return u(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"===typeof e?[e]:e,i=!!document.queryCommandSupported;return t.forEach((function(e){i=i&&!!document.queryCommandSupported(e)})),i}}]),i}(s()),L=O},828:function(e){var t=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var i=Element.prototype;i.matches=i.matchesSelector||i.mozMatchesSelector||i.msMatchesSelector||i.oMatchesSelector||i.webkitMatchesSelector}function r(e,i){while(e&&e.nodeType!==t){if("function"===typeof e.matches&&e.matches(i))return e;e=e.parentNode}}e.exports=r},438:function(e,t,i){var r=i(828);function s(e,t,i,r,s){var n=a.apply(this,arguments);return e.addEventListener(i,n,s),{destroy:function(){e.removeEventListener(i,n,s)}}}function n(e,t,i,r,n){return"function"===typeof e.addEventListener?s.apply(null,arguments):"function"===typeof i?s.bind(null,document).apply(null,arguments):("string"===typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return s(e,t,i,r,n)})))}function a(e,t,i,s){return function(i){i.delegateTarget=r(i.target,t),i.delegateTarget&&s.call(e,i)}}e.exports=n},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var i=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===i||"[object HTMLCollection]"===i)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"===typeof e||e instanceof String},t.fn=function(e){var t=Object.prototype.toString.call(e);return"[object Function]"===t}},370:function(e,t,i){var r=i(879),s=i(438);function n(e,t,i){if(!e&&!t&&!i)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(i))throw new TypeError("Third argument must be a Function");if(r.node(e))return a(e,t,i);if(r.nodeList(e))return o(e,t,i);if(r.string(e))return l(e,t,i);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(e,t,i){return e.addEventListener(t,i),{destroy:function(){e.removeEventListener(t,i)}}}function o(e,t,i){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,i)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,i)}))}}}function l(e,t,i){return s(document.body,e,t,i)}e.exports=n},817:function(e){function t(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var i=e.hasAttribute("readonly");i||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),i||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),s=document.createRange();s.selectNodeContents(e),r.removeAllRanges(),r.addRange(s),t=r.toString()}return t}e.exports=t},279:function(e){function t(){}t.prototype={on:function(e,t,i){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:i}),this},once:function(e,t,i){var r=this;function s(){r.off(e,s),t.apply(i,arguments)}return s._=t,this.on(e,s,i)},emit:function(e){var t=[].slice.call(arguments,1),i=((this.e||(this.e={}))[e]||[]).slice(),r=0,s=i.length;for(r;r<s;r++)i[r].fn.apply(i[r].ctx,t);return this},off:function(e,t){var i=this.e||(this.e={}),r=i[e],s=[];if(r&&t)for(var n=0,a=r.length;n<a;n++)r[n].fn!==t&&r[n].fn._!==t&&s.push(r[n]);return s.length?i[e]=s:delete i[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function i(r){if(t[r])return t[r].exports;var s=t[r]={exports:{}};return e[r](s,s.exports,i),s.exports}return function(){i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,{a:t}),t}}(),function(){i.d=function(e,t){for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),i(686)}().default}))},b76a:function(e,t,i){(function(t,r){e.exports=r(i("aa47"))})("undefined"!==typeof self&&self,(function(e){return function(e){var t={};function i(r){if(t[r])return t[r].exports;var s=t[r]={i:r,l:!1,exports:{}};return e[r].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=e,i.c=t,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)i.d(r,s,function(t){return e[t]}.bind(null,s));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s="fb15")}({"01f9":function(e,t,i){"use strict";var r=i("2d00"),s=i("5ca1"),n=i("2aba"),a=i("32e9"),o=i("84f2"),l=i("41a0"),c=i("7f20"),h=i("38fd"),u=i("2b4c")("iterator"),p=!([].keys&&"next"in[].keys()),d="@@iterator",f="keys",m="values",y=function(){return this};e.exports=function(e,t,i,v,g,x,b){l(i,t,v);var P,k,T,w=function(e){if(!p&&e in C)return C[e];switch(e){case f:return function(){return new i(this,e)};case m:return function(){return new i(this,e)}}return function(){return new i(this,e)}},S=t+" Iterator",A=g==m,E=!1,C=e.prototype,I=C[u]||C[d]||g&&C[g],N=I||w(g),O=g?A?w("entries"):N:void 0,L="Array"==t&&C.entries||I;if(L&&(T=h(L.call(new e)),T!==Object.prototype&&T.next&&(c(T,S,!0),r||"function"==typeof T[u]||a(T,u,y))),A&&I&&I.name!==m&&(E=!0,N=function(){return I.call(this)}),r&&!b||!p&&!E&&C[u]||a(C,u,N),o[t]=N,o[S]=y,g)if(P={values:A?N:w(m),keys:x?N:w(f),entries:O},b)for(k in P)k in C||n(C,k,P[k]);else s(s.P+s.F*(p||E),t,P);return P}},"02f4":function(e,t,i){var r=i("4588"),s=i("be13");e.exports=function(e){return function(t,i){var n,a,o=String(s(t)),l=r(i),c=o.length;return l<0||l>=c?e?"":void 0:(n=o.charCodeAt(l),n<55296||n>56319||l+1===c||(a=o.charCodeAt(l+1))<56320||a>57343?e?o.charAt(l):n:e?o.slice(l,l+2):a-56320+(n-55296<<10)+65536)}}},"0390":function(e,t,i){"use strict";var r=i("02f4")(!0);e.exports=function(e,t,i){return t+(i?r(e,t).length:1)}},"0bfb":function(e,t,i){"use strict";var r=i("cb7c");e.exports=function(){var e=r(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},"0d58":function(e,t,i){var r=i("ce10"),s=i("e11e");e.exports=Object.keys||function(e){return r(e,s)}},1495:function(e,t,i){var r=i("86cc"),s=i("cb7c"),n=i("0d58");e.exports=i("9e1e")?Object.defineProperties:function(e,t){s(e);var i,a=n(t),o=a.length,l=0;while(o>l)r.f(e,i=a[l++],t[i]);return e}},"214f":function(e,t,i){"use strict";i("b0c5");var r=i("2aba"),s=i("32e9"),n=i("79e5"),a=i("be13"),o=i("2b4c"),l=i("520a"),c=o("species"),h=!n((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),u=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var i="ab".split(e);return 2===i.length&&"a"===i[0]&&"b"===i[1]}();e.exports=function(e,t,i){var p=o(e),d=!n((function(){var t={};return t[p]=function(){return 7},7!=""[e](t)})),f=d?!n((function(){var t=!1,i=/a/;return i.exec=function(){return t=!0,null},"split"===e&&(i.constructor={},i.constructor[c]=function(){return i}),i[p](""),!t})):void 0;if(!d||!f||"replace"===e&&!h||"split"===e&&!u){var m=/./[p],y=i(a,p,""[e],(function(e,t,i,r,s){return t.exec===l?d&&!s?{done:!0,value:m.call(t,i,r)}:{done:!0,value:e.call(i,t,r)}:{done:!1}})),v=y[0],g=y[1];r(String.prototype,e,v),s(RegExp.prototype,p,2==t?function(e,t){return g.call(e,this,t)}:function(e){return g.call(e,this)})}}},"230e":function(e,t,i){var r=i("d3f4"),s=i("7726").document,n=r(s)&&r(s.createElement);e.exports=function(e){return n?s.createElement(e):{}}},"23c6":function(e,t,i){var r=i("2d95"),s=i("2b4c")("toStringTag"),n="Arguments"==r(function(){return arguments}()),a=function(e,t){try{return e[t]}catch(i){}};e.exports=function(e){var t,i,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(i=a(t=Object(e),s))?i:n?r(t):"Object"==(o=r(t))&&"function"==typeof t.callee?"Arguments":o}},2621:function(e,t){t.f=Object.getOwnPropertySymbols},"2aba":function(e,t,i){var r=i("7726"),s=i("32e9"),n=i("69a8"),a=i("ca5a")("src"),o=i("fa5b"),l="toString",c=(""+o).split(l);i("8378").inspectSource=function(e){return o.call(e)},(e.exports=function(e,t,i,o){var l="function"==typeof i;l&&(n(i,"name")||s(i,"name",t)),e[t]!==i&&(l&&(n(i,a)||s(i,a,e[t]?""+e[t]:c.join(String(t)))),e===r?e[t]=i:o?e[t]?e[t]=i:s(e,t,i):(delete e[t],s(e,t,i)))})(Function.prototype,l,(function(){return"function"==typeof this&&this[a]||o.call(this)}))},"2aeb":function(e,t,i){var r=i("cb7c"),s=i("1495"),n=i("e11e"),a=i("613b")("IE_PROTO"),o=function(){},l="prototype",c=function(){var e,t=i("230e")("iframe"),r=n.length,s="<",a=">";t.style.display="none",i("fab2").appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write(s+"script"+a+"document.F=Object"+s+"/script"+a),e.close(),c=e.F;while(r--)delete c[l][n[r]];return c()};e.exports=Object.create||function(e,t){var i;return null!==e?(o[l]=r(e),i=new o,o[l]=null,i[a]=e):i=c(),void 0===t?i:s(i,t)}},"2b4c":function(e,t,i){var r=i("5537")("wks"),s=i("ca5a"),n=i("7726").Symbol,a="function"==typeof n,o=e.exports=function(e){return r[e]||(r[e]=a&&n[e]||(a?n:s)("Symbol."+e))};o.store=r},"2d00":function(e,t){e.exports=!1},"2d95":function(e,t){var i={}.toString;e.exports=function(e){return i.call(e).slice(8,-1)}},"2fdb":function(e,t,i){"use strict";var r=i("5ca1"),s=i("d2c8"),n="includes";r(r.P+r.F*i("5147")(n),"String",{includes:function(e){return!!~s(this,e,n).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},"32e9":function(e,t,i){var r=i("86cc"),s=i("4630");e.exports=i("9e1e")?function(e,t,i){return r.f(e,t,s(1,i))}:function(e,t,i){return e[t]=i,e}},"38fd":function(e,t,i){var r=i("69a8"),s=i("4bf8"),n=i("613b")("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=s(e),r(e,n)?e[n]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},"41a0":function(e,t,i){"use strict";var r=i("2aeb"),s=i("4630"),n=i("7f20"),a={};i("32e9")(a,i("2b4c")("iterator"),(function(){return this})),e.exports=function(e,t,i){e.prototype=r(a,{next:s(1,i)}),n(e,t+" Iterator")}},"456d":function(e,t,i){var r=i("4bf8"),s=i("0d58");i("5eda")("keys",(function(){return function(e){return s(r(e))}}))},4588:function(e,t){var i=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:i)(e)}},4630:function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"4bf8":function(e,t,i){var r=i("be13");e.exports=function(e){return Object(r(e))}},5147:function(e,t,i){var r=i("2b4c")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(i){try{return t[r]=!1,!"/./"[e](t)}catch(s){}}return!0}},"520a":function(e,t,i){"use strict";var r=i("0bfb"),s=RegExp.prototype.exec,n=String.prototype.replace,a=s,o="lastIndex",l=function(){var e=/a/,t=/b*/g;return s.call(e,"a"),s.call(t,"a"),0!==e[o]||0!==t[o]}(),c=void 0!==/()??/.exec("")[1],h=l||c;h&&(a=function(e){var t,i,a,h,u=this;return c&&(i=new RegExp("^"+u.source+"$(?!\\s)",r.call(u))),l&&(t=u[o]),a=s.call(u,e),l&&a&&(u[o]=u.global?a.index+a[0].length:t),c&&a&&a.length>1&&n.call(a[0],i,(function(){for(h=1;h<arguments.length-2;h++)void 0===arguments[h]&&(a[h]=void 0)})),a}),e.exports=a},"52a7":function(e,t){t.f={}.propertyIsEnumerable},5537:function(e,t,i){var r=i("8378"),s=i("7726"),n="__core-js_shared__",a=s[n]||(s[n]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:r.version,mode:i("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"5ca1":function(e,t,i){var r=i("7726"),s=i("8378"),n=i("32e9"),a=i("2aba"),o=i("9b43"),l="prototype",c=function(e,t,i){var h,u,p,d,f=e&c.F,m=e&c.G,y=e&c.S,v=e&c.P,g=e&c.B,x=m?r:y?r[t]||(r[t]={}):(r[t]||{})[l],b=m?s:s[t]||(s[t]={}),P=b[l]||(b[l]={});for(h in m&&(i=t),i)u=!f&&x&&void 0!==x[h],p=(u?x:i)[h],d=g&&u?o(p,r):v&&"function"==typeof p?o(Function.call,p):p,x&&a(x,h,p,e&c.U),b[h]!=p&&n(b,h,d),v&&P[h]!=p&&(P[h]=p)};r.core=s,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},"5eda":function(e,t,i){var r=i("5ca1"),s=i("8378"),n=i("79e5");e.exports=function(e,t){var i=(s.Object||{})[e]||Object[e],a={};a[e]=t(i),r(r.S+r.F*n((function(){i(1)})),"Object",a)}},"5f1b":function(e,t,i){"use strict";var r=i("23c6"),s=RegExp.prototype.exec;e.exports=function(e,t){var i=e.exec;if("function"===typeof i){var n=i.call(e,t);if("object"!==typeof n)throw new TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==r(e))throw new TypeError("RegExp#exec called on incompatible receiver");return s.call(e,t)}},"613b":function(e,t,i){var r=i("5537")("keys"),s=i("ca5a");e.exports=function(e){return r[e]||(r[e]=s(e))}},"626a":function(e,t,i){var r=i("2d95");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},6762:function(e,t,i){"use strict";var r=i("5ca1"),s=i("c366")(!0);r(r.P,"Array",{includes:function(e){return s(this,e,arguments.length>1?arguments[1]:void 0)}}),i("9c6c")("includes")},6821:function(e,t,i){var r=i("626a"),s=i("be13");e.exports=function(e){return r(s(e))}},"69a8":function(e,t){var i={}.hasOwnProperty;e.exports=function(e,t){return i.call(e,t)}},"6a99":function(e,t,i){var r=i("d3f4");e.exports=function(e,t){if(!r(e))return e;var i,s;if(t&&"function"==typeof(i=e.toString)&&!r(s=i.call(e)))return s;if("function"==typeof(i=e.valueOf)&&!r(s=i.call(e)))return s;if(!t&&"function"==typeof(i=e.toString)&&!r(s=i.call(e)))return s;throw TypeError("Can't convert object to primitive value")}},7333:function(e,t,i){"use strict";var r=i("0d58"),s=i("2621"),n=i("52a7"),a=i("4bf8"),o=i("626a"),l=Object.assign;e.exports=!l||i("79e5")((function(){var e={},t={},i=Symbol(),r="abcdefghijklmnopqrst";return e[i]=7,r.split("").forEach((function(e){t[e]=e})),7!=l({},e)[i]||Object.keys(l({},t)).join("")!=r}))?function(e,t){var i=a(e),l=arguments.length,c=1,h=s.f,u=n.f;while(l>c){var p,d=o(arguments[c++]),f=h?r(d).concat(h(d)):r(d),m=f.length,y=0;while(m>y)u.call(d,p=f[y++])&&(i[p]=d[p])}return i}:l},7726:function(e,t){var i=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=i)},"77f1":function(e,t,i){var r=i("4588"),s=Math.max,n=Math.min;e.exports=function(e,t){return e=r(e),e<0?s(e+t,0):n(e,t)}},"79e5":function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},"7f20":function(e,t,i){var r=i("86cc").f,s=i("69a8"),n=i("2b4c")("toStringTag");e.exports=function(e,t,i){e&&!s(e=i?e:e.prototype,n)&&r(e,n,{configurable:!0,value:t})}},8378:function(e,t){var i=e.exports={version:"2.6.5"};"number"==typeof __e&&(__e=i)},"84f2":function(e,t){e.exports={}},"86cc":function(e,t,i){var r=i("cb7c"),s=i("c69a"),n=i("6a99"),a=Object.defineProperty;t.f=i("9e1e")?Object.defineProperty:function(e,t,i){if(r(e),t=n(t,!0),r(i),s)try{return a(e,t,i)}catch(o){}if("get"in i||"set"in i)throw TypeError("Accessors not supported!");return"value"in i&&(e[t]=i.value),e}},"9b43":function(e,t,i){var r=i("d8e8");e.exports=function(e,t,i){if(r(e),void 0===t)return e;switch(i){case 1:return function(i){return e.call(t,i)};case 2:return function(i,r){return e.call(t,i,r)};case 3:return function(i,r,s){return e.call(t,i,r,s)}}return function(){return e.apply(t,arguments)}}},"9c6c":function(e,t,i){var r=i("2b4c")("unscopables"),s=Array.prototype;void 0==s[r]&&i("32e9")(s,r,{}),e.exports=function(e){s[r][e]=!0}},"9def":function(e,t,i){var r=i("4588"),s=Math.min;e.exports=function(e){return e>0?s(r(e),9007199254740991):0}},"9e1e":function(e,t,i){e.exports=!i("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},a352:function(t,i){t.exports=e},a481:function(e,t,i){"use strict";var r=i("cb7c"),s=i("4bf8"),n=i("9def"),a=i("4588"),o=i("0390"),l=i("5f1b"),c=Math.max,h=Math.min,u=Math.floor,p=/\$([$&`']|\d\d?|<[^>]*>)/g,d=/\$([$&`']|\d\d?)/g,f=function(e){return void 0===e?e:String(e)};i("214f")("replace",2,(function(e,t,i,m){return[function(r,s){var n=e(this),a=void 0==r?void 0:r[t];return void 0!==a?a.call(r,n,s):i.call(String(n),r,s)},function(e,t){var s=m(i,e,this,t);if(s.done)return s.value;var u=r(e),p=String(this),d="function"===typeof t;d||(t=String(t));var v=u.global;if(v){var g=u.unicode;u.lastIndex=0}var x=[];while(1){var b=l(u,p);if(null===b)break;if(x.push(b),!v)break;var P=String(b[0]);""===P&&(u.lastIndex=o(p,n(u.lastIndex),g))}for(var k="",T=0,w=0;w<x.length;w++){b=x[w];for(var S=String(b[0]),A=c(h(a(b.index),p.length),0),E=[],C=1;C<b.length;C++)E.push(f(b[C]));var I=b.groups;if(d){var N=[S].concat(E,A,p);void 0!==I&&N.push(I);var O=String(t.apply(void 0,N))}else O=y(S,p,A,E,I,t);A>=T&&(k+=p.slice(T,A)+O,T=A+S.length)}return k+p.slice(T)}];function y(e,t,r,n,a,o){var l=r+e.length,c=n.length,h=d;return void 0!==a&&(a=s(a),h=p),i.call(o,h,(function(i,s){var o;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(l);case"<":o=a[s.slice(1,-1)];break;default:var h=+s;if(0===h)return i;if(h>c){var p=u(h/10);return 0===p?i:p<=c?void 0===n[p-1]?s.charAt(1):n[p-1]+s.charAt(1):i}o=n[h-1]}return void 0===o?"":o}))}}))},aae3:function(e,t,i){var r=i("d3f4"),s=i("2d95"),n=i("2b4c")("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[n])?!!t:"RegExp"==s(e))}},ac6a:function(e,t,i){for(var r=i("cadf"),s=i("0d58"),n=i("2aba"),a=i("7726"),o=i("32e9"),l=i("84f2"),c=i("2b4c"),h=c("iterator"),u=c("toStringTag"),p=l.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},f=s(d),m=0;m<f.length;m++){var y,v=f[m],g=d[v],x=a[v],b=x&&x.prototype;if(b&&(b[h]||o(b,h,p),b[u]||o(b,u,v),l[v]=p,g))for(y in r)b[y]||n(b,y,r[y],!0)}},b0c5:function(e,t,i){"use strict";var r=i("520a");i("5ca1")({target:"RegExp",proto:!0,forced:r!==/./.exec},{exec:r})},be13:function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},c366:function(e,t,i){var r=i("6821"),s=i("9def"),n=i("77f1");e.exports=function(e){return function(t,i,a){var o,l=r(t),c=s(l.length),h=n(a,c);if(e&&i!=i){while(c>h)if(o=l[h++],o!=o)return!0}else for(;c>h;h++)if((e||h in l)&&l[h]===i)return e||h||0;return!e&&-1}}},c649:function(e,t,i){"use strict";(function(e){i.d(t,"c",(function(){return c})),i.d(t,"a",(function(){return o})),i.d(t,"b",(function(){return s})),i.d(t,"d",(function(){return l}));i("a481");function r(){return"undefined"!==typeof window?window.console:e.console}var s=r();function n(e){var t=Object.create(null);return function(i){var r=t[i];return r||(t[i]=e(i))}}var a=/-(\w)/g,o=n((function(e){return e.replace(a,(function(e,t){return t?t.toUpperCase():""}))}));function l(e){null!==e.parentElement&&e.parentElement.removeChild(e)}function c(e,t,i){var r=0===i?e.children[0]:e.children[i-1].nextSibling;e.insertBefore(t,r)}}).call(this,i("c8ba"))},c69a:function(e,t,i){e.exports=!i("9e1e")&&!i("79e5")((function(){return 7!=Object.defineProperty(i("230e")("div"),"a",{get:function(){return 7}}).a}))},c8ba:function(e,t){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(r){"object"===typeof window&&(i=window)}e.exports=i},ca5a:function(e,t){var i=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++i+r).toString(36))}},cadf:function(e,t,i){"use strict";var r=i("9c6c"),s=i("d53b"),n=i("84f2"),a=i("6821");e.exports=i("01f9")(Array,"Array",(function(e,t){this._t=a(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,i=this._i++;return!e||i>=e.length?(this._t=void 0,s(1)):s(0,"keys"==t?i:"values"==t?e[i]:[i,e[i]])}),"values"),n.Arguments=n.Array,r("keys"),r("values"),r("entries")},cb7c:function(e,t,i){var r=i("d3f4");e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},ce10:function(e,t,i){var r=i("69a8"),s=i("6821"),n=i("c366")(!1),a=i("613b")("IE_PROTO");e.exports=function(e,t){var i,o=s(e),l=0,c=[];for(i in o)i!=a&&r(o,i)&&c.push(i);while(t.length>l)r(o,i=t[l++])&&(~n(c,i)||c.push(i));return c}},d2c8:function(e,t,i){var r=i("aae3"),s=i("be13");e.exports=function(e,t,i){if(r(t))throw TypeError("String#"+i+" doesn't accept regex!");return String(s(e))}},d3f4:function(e,t){e.exports=function(e){return"object"===typeof e?null!==e:"function"===typeof e}},d53b:function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},d8e8:function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},e11e:function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},f559:function(e,t,i){"use strict";var r=i("5ca1"),s=i("9def"),n=i("d2c8"),a="startsWith",o=""[a];r(r.P+r.F*i("5147")(a),"String",{startsWith:function(e){var t=n(this,e,a),i=s(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return o?o.call(t,r,i):t.slice(i,i+r.length)===r}})},f6fd:function(e,t){(function(e){var t="currentScript",i=e.getElementsByTagName("script");t in e||Object.defineProperty(e,t,{get:function(){try{throw new Error}catch(r){var e,t=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(r.stack)||[!1])[1];for(e in i)if(i[e].src==t||"interactive"==i[e].readyState)return i[e];return null}}})})(document)},f751:function(e,t,i){var r=i("5ca1");r(r.S+r.F,"Object",{assign:i("7333")})},fa5b:function(e,t,i){e.exports=i("5537")("native-function-to-string",Function.toString)},fab2:function(e,t,i){var r=i("7726").document;e.exports=r&&r.documentElement},fb15:function(e,t,i){"use strict";var r;(i.r(t),"undefined"!==typeof window)&&(i("f6fd"),(r=window.document.currentScript)&&(r=r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(i.p=r[1]));i("f751"),i("f559"),i("ac6a"),i("cadf"),i("456d");function s(e){if(Array.isArray(e))return e}function n(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var i=[],r=!0,s=!1,n=void 0;try{for(var a,o=e[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)if(i.push(a.value),t&&i.length===t)break}catch(l){s=!0,n=l}finally{try{r||null==o["return"]||o["return"]()}finally{if(s)throw n}}return i}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function o(e,t){if(e){if("string"===typeof e)return a(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?a(e,t):void 0}}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){return s(e)||n(e,t)||o(e,t)||l()}i("6762"),i("2fdb");function h(e){if(Array.isArray(e))return a(e)}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e){return h(e)||u(e)||o(e)||p()}var f=i("a352"),m=i.n(f),y=i("c649");function v(e,t,i){return void 0===i||(e=e||{},e[t]=i),e}function g(e,t){return e.map((function(e){return e.elm})).indexOf(t)}function x(e,t,i,r){if(!e)return[];var s=e.map((function(e){return e.elm})),n=t.length-r,a=d(t).map((function(e,t){return t>=n?s.length:s.indexOf(e)}));return i?a.filter((function(e){return-1!==e})):a}function b(e,t){var i=this;this.$nextTick((function(){return i.$emit(e.toLowerCase(),t)}))}function P(e){var t=this;return function(i){null!==t.realList&&t["onDrag"+e](i),b.call(t,e,i)}}function k(e){return["transition-group","TransitionGroup"].includes(e)}function T(e){if(!e||1!==e.length)return!1;var t=c(e,1),i=t[0].componentOptions;return!!i&&k(i.tag)}function w(e,t,i){return e[i]||(t[i]?t[i]():void 0)}function S(e,t,i){var r=0,s=0,n=w(t,i,"header");n&&(r=n.length,e=e?[].concat(d(n),d(e)):d(n));var a=w(t,i,"footer");return a&&(s=a.length,e=e?[].concat(d(e),d(a)):d(a)),{children:e,headerOffset:r,footerOffset:s}}function A(e,t){var i=null,r=function(e,t){i=v(i,e,t)},s=Object.keys(e).filter((function(e){return"id"===e||e.startsWith("data-")})).reduce((function(t,i){return t[i]=e[i],t}),{});if(r("attrs",s),!t)return i;var n=t.on,a=t.props,o=t.attrs;return r("on",n),r("props",a),Object.assign(i.attrs,o),i}var E=["Start","Add","Remove","Update","End"],C=["Choose","Unchoose","Sort","Filter","Clone"],I=["Move"].concat(E,C).map((function(e){return"on"+e})),N=null,O={options:Object,list:{type:Array,required:!1,default:null},value:{type:Array,required:!1,default:null},noTransitionOnDrag:{type:Boolean,default:!1},clone:{type:Function,default:function(e){return e}},element:{type:String,default:"div"},tag:{type:String,default:null},move:{type:Function,default:null},componentData:{type:Object,required:!1,default:null}},L={name:"draggable",inheritAttrs:!1,props:O,data:function(){return{transitionMode:!1,noneFunctionalComponentMode:!1}},render:function(e){var t=this.$slots.default;this.transitionMode=T(t);var i=S(t,this.$slots,this.$scopedSlots),r=i.children,s=i.headerOffset,n=i.footerOffset;this.headerOffset=s,this.footerOffset=n;var a=A(this.$attrs,this.componentData);return e(this.getTag(),a,r)},created:function(){null!==this.list&&null!==this.value&&y["b"].error("Value and list props are mutually exclusive! Please set one or another."),"div"!==this.element&&y["b"].warn("Element props is deprecated please use tag props instead. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#element-props"),void 0!==this.options&&y["b"].warn("Options props is deprecated, add sortable options directly as vue.draggable item, or use v-bind. See https://github.com/SortableJS/Vue.Draggable/blob/master/documentation/migrate.md#options-props")},mounted:function(){var e=this;if(this.noneFunctionalComponentMode=this.getTag().toLowerCase()!==this.$el.nodeName.toLowerCase()&&!this.getIsFunctional(),this.noneFunctionalComponentMode&&this.transitionMode)throw new Error("Transition-group inside component is not supported. Please alter tag value or remove transition-group. Current tag value: ".concat(this.getTag()));var t={};E.forEach((function(i){t["on"+i]=P.call(e,i)})),C.forEach((function(i){t["on"+i]=b.bind(e,i)}));var i=Object.keys(this.$attrs).reduce((function(t,i){return t[Object(y["a"])(i)]=e.$attrs[i],t}),{}),r=Object.assign({},this.options,i,t,{onMove:function(t,i){return e.onDragMove(t,i)}});!("draggable"in r)&&(r.draggable=">*"),this._sortable=new m.a(this.rootContainer,r),this.computeIndexes()},beforeDestroy:function(){void 0!==this._sortable&&this._sortable.destroy()},computed:{rootContainer:function(){return this.transitionMode?this.$el.children[0]:this.$el},realList:function(){return this.list?this.list:this.value}},watch:{options:{handler:function(e){this.updateOptions(e)},deep:!0},$attrs:{handler:function(e){this.updateOptions(e)},deep:!0},realList:function(){this.computeIndexes()}},methods:{getIsFunctional:function(){var e=this._vnode.fnOptions;return e&&e.functional},getTag:function(){return this.tag||this.element},updateOptions:function(e){for(var t in e){var i=Object(y["a"])(t);-1===I.indexOf(i)&&this._sortable.option(i,e[t])}},getChildrenNodes:function(){if(this.noneFunctionalComponentMode)return this.$children[0].$slots.default;var e=this.$slots.default;return this.transitionMode?e[0].child.$slots.default:e},computeIndexes:function(){var e=this;this.$nextTick((function(){e.visibleIndexes=x(e.getChildrenNodes(),e.rootContainer.children,e.transitionMode,e.footerOffset)}))},getUnderlyingVm:function(e){var t=g(this.getChildrenNodes()||[],e);if(-1===t)return null;var i=this.realList[t];return{index:t,element:i}},getUnderlyingPotencialDraggableComponent:function(e){var t=e.__vue__;return t&&t.$options&&k(t.$options._componentTag)?t.$parent:!("realList"in t)&&1===t.$children.length&&"realList"in t.$children[0]?t.$children[0]:t},emitChanges:function(e){var t=this;this.$nextTick((function(){t.$emit("change",e)}))},alterList:function(e){if(this.list)e(this.list);else{var t=d(this.value);e(t),this.$emit("input",t)}},spliceList:function(){var e=arguments,t=function(t){return t.splice.apply(t,d(e))};this.alterList(t)},updatePosition:function(e,t){var i=function(i){return i.splice(t,0,i.splice(e,1)[0])};this.alterList(i)},getRelatedContextFromMoveEvent:function(e){var t=e.to,i=e.related,r=this.getUnderlyingPotencialDraggableComponent(t);if(!r)return{component:r};var s=r.realList,n={list:s,component:r};if(t!==i&&s&&r.getUnderlyingVm){var a=r.getUnderlyingVm(i);if(a)return Object.assign(a,n)}return n},getVmIndex:function(e){var t=this.visibleIndexes,i=t.length;return e>i-1?i:t[e]},getComponent:function(){return this.$slots.default[0].componentInstance},resetTransitionData:function(e){if(this.noTransitionOnDrag&&this.transitionMode){var t=this.getChildrenNodes();t[e].data=null;var i=this.getComponent();i.children=[],i.kept=void 0}},onDragStart:function(e){this.context=this.getUnderlyingVm(e.item),e.item._underlying_vm_=this.clone(this.context.element),N=e.item},onDragAdd:function(e){var t=e.item._underlying_vm_;if(void 0!==t){Object(y["d"])(e.item);var i=this.getVmIndex(e.newIndex);this.spliceList(i,0,t),this.computeIndexes();var r={element:t,newIndex:i};this.emitChanges({added:r})}},onDragRemove:function(e){if(Object(y["c"])(this.rootContainer,e.item,e.oldIndex),"clone"!==e.pullMode){var t=this.context.index;this.spliceList(t,1);var i={element:this.context.element,oldIndex:t};this.resetTransitionData(t),this.emitChanges({removed:i})}else Object(y["d"])(e.clone)},onDragUpdate:function(e){Object(y["d"])(e.item),Object(y["c"])(e.from,e.item,e.oldIndex);var t=this.context.index,i=this.getVmIndex(e.newIndex);this.updatePosition(t,i);var r={element:this.context.element,oldIndex:t,newIndex:i};this.emitChanges({moved:r})},updateProperty:function(e,t){e.hasOwnProperty(t)&&(e[t]+=this.headerOffset)},computeFutureIndex:function(e,t){if(!e.element)return 0;var i=d(t.to.children).filter((function(e){return"none"!==e.style["display"]})),r=i.indexOf(t.related),s=e.component.getVmIndex(r),n=-1!==i.indexOf(N);return n||!t.willInsertAfter?s:s+1},onDragMove:function(e,t){var i=this.move;if(!i||!this.realList)return!0;var r=this.getRelatedContextFromMoveEvent(e),s=this.context,n=this.computeFutureIndex(r,e);Object.assign(s,{futureIndex:n});var a=Object.assign({},e,{relatedContext:r,draggedContext:s});return i(a,t)},onDragEnd:function(){this.computeIndexes(),N=null}}};"undefined"!==typeof window&&"Vue"in window&&window.Vue.component("draggable",L);var D=L;t["default"]=D}})["default"]}))},d60a:function(e,t){e.exports=function(e){return e&&"object"===typeof e&&"function"===typeof e.copy&&"function"===typeof e.fill&&"function"===typeof e.readUInt8}}}]);