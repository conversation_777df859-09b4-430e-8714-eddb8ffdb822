(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e4893"],{9149:function(e,t){(function(){if(window.frameElement.id){var e=window.parent,t=e.$EDITORUI[window.frameElement.id.replace(/_iframe$/,"")],o=t.editor,a=e.UE,n=a.dom.domUtils,i=a.utils,r=(a.browser,a.ajax,function(e){return document.getElementById(e)});window.nowEditor={editor:o,dialog:t},i.loadFile(document,{href:o.options.themePath+o.options.theme+"/dialogbase.css?cache="+Math.random(),tag:"link",type:"text/css",rel:"stylesheet"});var s=o.getLang(t.className.split("-")[2]);s&&n.on(window,"load",(function(){var e=o.options.langPath+o.options.lang+"/images/";for(var t in s["static"]){var a=r(t);if(a){var d=a.tagName,c=s["static"][t];switch(c.src&&(c=i.extend({},c,!1),c.src=e+c.src),c.style&&(c=i.extend({},c,!1),c.style=c.style.replace(/url\s*\(/g,"url("+e)),d.toLowerCase()){case"var":a.parentNode.replaceChild(document.createTextNode(c),a);break;case"select":for(var l,w=a.options,p=0;l=w[p];)l.innerHTML=c.options[p++];for(var m in c)"options"!=m&&a.setAttribute(m,c[m]);break;default:n.setAttributes(a,c)}}}}))}})()}}]);