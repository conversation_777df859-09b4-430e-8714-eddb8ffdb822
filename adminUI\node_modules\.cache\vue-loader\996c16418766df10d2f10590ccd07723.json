{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue?vue&type=template&id=18ee2a91&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue", "mtime": 1754382949117}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754138277104}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"divBox relative\"},[_c('el-card',{staticClass:\"box-card\"},[_c('el-form',{ref:\"searchFormRef\",attrs:{\"model\":_vm.searchForm,\"rules\":_vm.searchRules,\"inline\":\"\",\"size\":\"small\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('affiliateProducts.priceRange')}},[_c('el-input',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":_vm.$t('affiliateProducts.minPrice')},model:{value:(_vm.searchForm.priceMin),callback:function ($$v) {_vm.$set(_vm.searchForm, \"priceMin\", $$v)},expression:\"searchForm.priceMin\"}}),_vm._v(\" \"),_c('span',{staticStyle:{\"margin\":\"0 8px\"}},[_vm._v(\"-\")]),_vm._v(\" \"),_c('el-input',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":_vm.$t('affiliateProducts.maxPrice')},model:{value:(_vm.searchForm.priceMax),callback:function ($$v) {_vm.$set(_vm.searchForm, \"priceMax\", $$v)},expression:\"searchForm.priceMax\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('affiliateProducts.commissionRange'),\"prop\":\"commissionRange\"}},[_c('el-input',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":_vm.$t('affiliateProducts.minCommission')},on:{\"blur\":function($event){return _vm.validateCommissionRate('commissionMin')},\"input\":function($event){return _vm.clearCommissionError('commissionMin')}},model:{value:(_vm.searchForm.commissionMin),callback:function ($$v) {_vm.$set(_vm.searchForm, \"commissionMin\", $$v)},expression:\"searchForm.commissionMin\"}}),_vm._v(\" \"),_c('span',{staticStyle:{\"margin\":\"0 8px\"}},[_vm._v(\"-\")]),_vm._v(\" \"),_c('el-input',{staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":_vm.$t('affiliateProducts.maxCommission')},on:{\"blur\":function($event){return _vm.validateCommissionRate('commissionMax')},\"input\":function($event){return _vm.clearCommissionError('commissionMax')}},model:{value:(_vm.searchForm.commissionMax),callback:function ($$v) {_vm.$set(_vm.searchForm, \"commissionMax\", $$v)},expression:\"searchForm.commissionMax\"}}),_vm._v(\" \"),(_vm.commissionErrors.commissionMin)?_c('div',{staticClass:\"commission-error\"},[_vm._v(\"\\n          \"+_vm._s(_vm.commissionErrors.commissionMin)+\"\\n        \")]):_vm._e(),_vm._v(\" \"),(_vm.commissionErrors.commissionMax)?_c('div',{staticClass:\"commission-error\"},[_vm._v(\"\\n          \"+_vm._s(_vm.commissionErrors.commissionMax)+\"\\n        \")]):_vm._e()],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('affiliateProducts.sort')}},[_c('el-select',{staticStyle:{\"width\":\"140px\"},model:{value:(_vm.searchForm.sortField),callback:function ($$v) {_vm.$set(_vm.searchForm, \"sortField\", $$v)},expression:\"searchForm.sortField\"}},[_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortCommissionRate'),\"value\":\"commission_rate\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortCommission'),\"value\":\"commission\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortPrice'),\"value\":\"product_sales_price\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortSales'),\"value\":\"units_sold\"}})],1),_vm._v(\" \"),_c('el-select',{staticStyle:{\"width\":\"80px\",\"margin-left\":\"8px\"},model:{value:(_vm.searchForm.sortOrder),callback:function ($$v) {_vm.$set(_vm.searchForm, \"sortOrder\", $$v)},expression:\"searchForm.sortOrder\"}},[_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortDesc'),\"value\":\"DESC\"}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":_vm.$t('affiliateProducts.sortAsc'),\"value\":\"ASC\"}})],1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":_vm.$t('affiliateProducts.keywords')}},[_c('el-input',{staticStyle:{\"width\":\"300px\"},attrs:{\"placeholder\":_vm.$t('affiliateProducts.addKeywordPlaceholder'),\"clearable\":\"\",\"disabled\":_vm.searchForm.keywords.length >= 20},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.addKeyword($event)}},model:{value:(_vm.currentKeyword),callback:function ($$v) {_vm.currentKeyword=$$v},expression:\"currentKeyword\"}},[_c('template',{slot:\"append\"},[_c('span',{staticClass:\"keyword-count\",class:{ 'keyword-count-warning': _vm.searchForm.keywords.length >= 18 }},[_vm._v(\"\\n              \"+_vm._s(_vm.searchForm.keywords.length)+\"/20\\n            \")])])],2),_vm._v(\" \"),(_vm.keywordError)?_c('div',{staticClass:\"keyword-error\",staticStyle:{\"margin-top\":\"4px\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.keywordError)+\"\\n        \")]):_vm._e()],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"search-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleSearch}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.query')))]),_vm._v(\" \"),_c('el-button',{on:{\"click\":_vm.handleReset}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.reset')))])],1),_vm._v(\" \"),(_vm.searchForm.keywords.length > 0)?_c('div',{staticClass:\"keywords-display-section\",staticStyle:{\"margin-top\":\"16px\"}},[_c('div',{staticClass:\"keywords-display-header\"},[_c('div',{staticClass:\"keywords-display-label\"},[_vm._v(_vm._s(_vm.$t('affiliateProducts.selectedKeywords')))]),_vm._v(\" \"),_c('el-button',{staticClass:\"clear-all-btn\",attrs:{\"size\":\"mini\",\"type\":\"text\",\"icon\":\"el-icon-delete\",\"title\":_vm.$t('affiliateProducts.clearAllKeywords')},on:{\"click\":_vm.clearAllKeywords}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('affiliateProducts.clearAll'))+\"\\n        \")])],1),_vm._v(\" \"),_c('div',{staticClass:\"keywords-display-container\"},_vm._l((_vm.searchForm.keywords),function(keyword,index){return _c('el-tag',{key:index,staticClass:\"keyword-display-tag\",attrs:{\"closable\":\"\"},on:{\"close\":function($event){return _vm.removeKeyword(index)}}},[_vm._v(\"\\n          \"+_vm._s(keyword)+\"\\n        \")])}),1)]):_vm._e()],1),_vm._v(\" \"),_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"12px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',[_vm._v(_vm._s(_vm.$t('affiliateProducts.listTitle')))]),_vm._v(\" \"),_c('el-button',{staticStyle:{\"float\":\"right\",\"padding\":\"3px 0\"},attrs:{\"type\":\"text\"},on:{\"click\":_vm.handleRefresh}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.refresh')))])],1),_vm._v(\" \"),(_vm.hasSearched && _vm.tableData.length > 0)?_c('div',{staticStyle:{\"margin-bottom\":\"15px\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"disabled\":_vm.selectedProducts.length === 0 || _vm.batchImporting},on:{\"click\":_vm.handleBatchImport}},[_vm._v(\"\\n        \"+_vm._s(_vm.batchImporting ? _vm.$t('affiliateProducts.batchImporting') : ((_vm.$t('affiliateProducts.batchImport')) + \" (\" + (_vm.selectedProducts.length) + \")\"))+\"\\n      \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"disabled\":_vm.selectedProducts.length === 0 || _vm.batchDeleting},on:{\"click\":_vm.handleBatchDelete}},[_vm._v(\"\\n        \"+_vm._s(_vm.batchDeleting ? _vm.$t('affiliateProducts.batchDeleting') : ((_vm.$t('affiliateProducts.batchDelete')) + \" (\" + (_vm.selectedProducts.length) + \")\"))+\"\\n      \")])],1):_vm._e(),_vm._v(\" \"),(!_vm.hasSearched && _vm.tableData.length === 0)?_c('div',{staticClass:\"empty-tip\"},[_c('el-empty',{attrs:{\"description\":_vm.$t('affiliateProducts.emptyTip')}})],1):_vm._e(),_vm._v(\" \"),_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"},{name:\"show\",rawName:\"v-show\",value:(_vm.hasSearched),expression:\"hasSearched\"}],ref:\"productTable\",attrs:{\"data\":_vm.tableData,\"size\":\"small\",\"header-cell-style\":{ fontWeight: 'bold' }},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"type\":\"index\",\"label\":_vm.$t('affiliateProducts.serialNumber'),\"width\":\"60\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.productImage'),\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-image',{staticStyle:{\"width\":\"60px\",\"height\":\"60px\",\"border-radius\":\"4px\"},attrs:{\"src\":scope.row.mainImageUrl,\"fit\":\"cover\",\"preview-src-list\":[scope.row.mainImageUrl]}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.productTitle'),\"min-width\":\"200\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-link',{attrs:{\"href\":scope.row.detailLink,\"target\":\"_blank\",\"type\":\"primary\"}},[_vm._v(\"\\n            \"+_vm._s(scope.row.title)+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.shop'),\"width\":\"120\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n          \"+_vm._s(scope.row.shop ? scope.row.shop.name : '-')+\"\\n        \")]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.originalPrice'),\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.originalPrice)?_c('span',[_vm._v(\"\\n            \"+_vm._s(_vm.formatPrice(scope.row.originalPrice))+\"\\n          \")]):_c('span',[_vm._v(\"-\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.salesPrice'),\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.salesPrice)?_c('span',[_vm._v(\"\\n            \"+_vm._s(_vm.formatPrice(scope.row.salesPrice))+\"\\n          \")]):_c('span',[_vm._v(\"-\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.commissionRate'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.commission)?_c('span',[_vm._v(\"\\n            \"+_vm._s(_vm.formatCommissionRate(scope.row.commission.rate))+\"%\\n          \")]):_c('span',[_vm._v(\"-\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.commissionAmount'),\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.commission)?_c('span',[_vm._v(\"\\n            \"+_vm._s(scope.row.commission.amount)+\" \"+_vm._s(scope.row.commission.currency)+\"\\n          \")]):_c('span',[_vm._v(\"-\")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.unitsSold'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n          \"+_vm._s(scope.row.unitsSold || 0)+\"\\n        \")]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.inventoryStatus'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.hasInventory ? 'success' : 'danger',\"size\":\"mini\"}},[_vm._v(\"\\n            \"+_vm._s(scope.row.hasInventory ? _vm.$t('affiliateProducts.hasInventory') : _vm.$t('affiliateProducts.noInventory'))+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.saleRegion'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\"\\n          \"+_vm._s(scope.row.saleRegion || '-')+\"\\n        \")]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.importStatus'),\"width\":\"80\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.isImported ? 'success' : 'info',\"size\":\"mini\"}},[_vm._v(\"\\n            \"+_vm._s(scope.row.isImported ? _vm.$t('affiliateProducts.imported') : _vm.$t('affiliateProducts.notImported'))+\"\\n          \")])]}}])}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":_vm.$t('affiliateProducts.action'),\"width\":\"180\",\"fixed\":\"right\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":scope.row.isImported ? 'success' : 'primary',\"size\":\"mini\",\"disabled\":scope.row.importing || scope.row.isImported},on:{\"click\":function($event){return _vm.handleImportProduct(scope.row)}}},[_vm._v(\"\\n            \"+_vm._s(scope.row.importing ? _vm.$t('affiliateProducts.importing') : (scope.row.isImported ? _vm.$t('affiliateProducts.imported') : _vm.$t('affiliateProducts.import')))+\"\\n          \")]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"mini\",\"disabled\":scope.row.deleting},on:{\"click\":function($event){return _vm.handleDeleteProduct(scope.row)}}},[_vm._v(\"\\n            \"+_vm._s(scope.row.deleting ? _vm.$t('affiliateProducts.deleting') : _vm.$t('affiliateProducts.delete'))+\"\\n          \")])]}}])})],1),_vm._v(\" \"),(_vm.hasSearched)?_c('div',{staticClass:\"pagination-container\",staticStyle:{\"margin-top\":\"20px\",\"text-align\":\"center\"}},[_c('el-button',{attrs:{\"disabled\":!_vm.hasPrevPage,\"size\":\"small\"},on:{\"click\":_vm.handlePrevPage}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.prevPage')))]),_vm._v(\" \"),_c('el-button',{attrs:{\"disabled\":!_vm.hasNextPage,\"size\":\"small\"},on:{\"click\":_vm.handleNextPage}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.nextPage')))]),_vm._v(\" \"),_c('span',{staticStyle:{\"margin-left\":\"20px\"}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('affiliateProducts.pageSize'))+\"\\n        \"),_c('el-select',{staticStyle:{\"width\":\"80px\"},attrs:{\"size\":\"mini\"},on:{\"change\":_vm.handleSearch},model:{value:(_vm.searchForm.pageSize),callback:function ($$v) {_vm.$set(_vm.searchForm, \"pageSize\", $$v)},expression:\"searchForm.pageSize\"}},[_c('el-option',{attrs:{\"label\":\"10\",\"value\":10}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":\"20\",\"value\":20}}),_vm._v(\" \"),_c('el-option',{attrs:{\"label\":\"50\",\"value\":50}})],1)],1),_vm._v(\" \"),_c('span',{staticStyle:{\"margin-left\":\"20px\"}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('affiliateProducts.totalCount', { count: _vm.totalCount }))+\"\\n      \")])],1):_vm._e()],1),_vm._v(\" \"),_c('el-dialog',{attrs:{\"title\":_vm.currentImportProduct ? _vm.$t('affiliateProducts.importSingle') : _vm.$t('affiliateProducts.importBatch'),\"visible\":_vm.importDialogVisible,\"width\":\"500px\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.importDialogVisible=$event}}},[(_vm.currentImportProduct)?_c('div',[_c('p',[_c('strong',[_vm._v(_vm._s(_vm.$t('affiliateProducts.productTitle'))+\"：\")]),_vm._v(_vm._s(_vm.currentImportProduct.title))]),_vm._v(\" \"),_c('p',[_c('strong',[_vm._v(_vm._s(_vm.$t('product.productId'))+\"：\")]),_vm._v(_vm._s(_vm.currentImportProduct.id))])]):_c('div',[_c('p',[_c('strong',[_vm._v(_vm._s(_vm.$t('affiliateProducts.selectedCount')))]),_vm._v(_vm._s(_vm.selectedProducts.length))])]),_vm._v(\" \"),_c('div',{staticStyle:{\"margin\":\"20px 0\",\"padding\":\"15px\",\"background-color\":\"#f0f9ff\",\"border\":\"1px solid #b3d8ff\",\"border-radius\":\"4px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eff\",\"margin-right\":\"8px\"}}),_vm._v(\" \"),_c('span',{staticStyle:{\"color\":\"#409eff\"}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.brandAutoDetect')))])]),_vm._v(\" \"),_c('div',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.importDialogVisible = false}}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.cancel')))]),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.confirmImport}},[_vm._v(_vm._s(_vm.$t('affiliateProducts.confirmImport')))])],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}