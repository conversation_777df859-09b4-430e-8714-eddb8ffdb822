{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\src\\views\\app\\home.vue", "mtime": 1754301662333}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\babel.config.js", "mtime": 1754066064000}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1754138265873}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754138271542}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754138270609}, {"path": "C:\\Users\\<USER>\\Desktop\\project\\genco\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754138273929}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { productLstApi, productUpdateApi } from \"@/api/store\";\nimport { batchPutOn, batchPutoff, updateProductInfo } from \"@/api/brand\";\nexport default {\n  name: \"AppHome\",\n  data: function data() {\n    return {\n      statusOptions: [{\n        value: -1,\n        label: this.$t(\"all\")\n      }, {\n        value: 1,\n        label: this.$t(\"online\")\n      }, {\n        value: 0,\n        label: this.$t(\"offline\")\n      }],\n      form: {\n        keywords: \"\",\n        page: 1,\n        limit: 20,\n        type: \"1\",\n        isShow: \"\"\n      },\n      tableData: [],\n      levelList: [],\n      levelData: [],\n      loading: false\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    onChangeType: function onChangeType() {\n      this.getList();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.form.page = 1;\n      this.loading = true;\n      productLstApi(this.form).then(function (res) {\n        _this2.tableData = res.list;\n        _this2.form.total = res.total;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.form.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.form.limit = index;\n      this.getList();\n    },\n    resetForm: function resetForm() {\n      this.form.keywords = \"\";\n      this.form.page = 1;\n      this.form.limit = 20;\n      this.getList();\n    },\n    formatTime: function formatTime(t) {\n      var date = new Date(t * 1000);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, \"0\");\n      var day = String(date.getDate()).padStart(2, \"0\");\n      var hours = String(date.getHours()).padStart(2, \"0\");\n      var minutes = String(date.getMinutes()).padStart(2, \"0\");\n      var seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    handleUpdate: function handleUpdate(row, type) {\n      var _this = this;\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: this.$t(\"brand.confirm\"),\n        cancelButtonText: this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        var params = {\n          ids: [row.id]\n        };\n        if (row.isShow) {\n          batchPutoff(params).then(function (res) {\n            _this.getList();\n          });\n        } else {\n          batchPutOn(params).then(function (res) {\n            _this.getList();\n          });\n        }\n      });\n    },\n    formatRate: function formatRate(s) {\n      return parseInt(s * 10000) / 100 + \"%\";\n    },\n    handleDelete: function handleDelete(row) {\n      var _this = this;\n      var item = {\n        id: row.id\n      };\n      if (this.form.type == 1) {\n        item['isHot'] = false;\n      } else if (this.form.type == 2) {\n        item['isBenefit'] = false;\n      } else if (this.form.type == 3) {\n        item['isBest'] = false;\n      }\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: this.$t(\"brand.confirm\"),\n        cancelButtonText: this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        _this.loading = true;\n        updateProductInfo(item).then(function (res) {\n          _this.getList();\n        }).catch(function (res) {});\n      });\n    }\n  }\n};", null]}